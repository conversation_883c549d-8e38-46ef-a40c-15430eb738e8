// Test script to simulate RLS policy behavior
const { Client } = require('pg');

async function main() {
    // Create a new PostgreSQL client
    const client = new Client({
        connectionString: '*****************************************************************************/postgres'
    });

    try {
        // Connect to the database
        await client.connect();
        console.log('Connected to Supabase PostgreSQL database');

        // Get a list of existing tenants
        console.log('Fetching existing tenants from the database...');
        const tenantsQuery = `
            SELECT DISTINCT tenant_id
            FROM legal_templates
            ORDER BY tenant_id;
        `;
        const tenantsResult = await client.query(tenantsQuery);
        console.log('Available tenant IDs:');
        tenantsResult.rows.forEach((row, index) => {
            console.log(`${index + 1}. ${row.tenant_id}`);
        });

        // Get a list of existing roles
        console.log('\nSimulating different roles to check RLS policies:');
        const testRoles = ['partner', 'attorney', 'staff', 'client', 'authenticated'];

        // Test each role and tenant combination
        for (const role of testRoles) {
            console.log(`\n=== Testing role: ${role} ===`);

            for (const tenant of tenantsResult.rows) {
                const tenantId = tenant.tenant_id;
                console.log(`\nTenant ID: ${tenantId}`);

                // Set up the security context as if we were a user with this role and tenant
                await client.query(`
                    SET LOCAL ROLE authenticated;
                    SELECT set_config('request.jwt.claims',
                        '{"role": "${role}", "tenant_id": "${tenantId}"}',
                        true);
                `);

                // Try to access legal_templates with the simulated user
                try {
                    const { rows } = await client.query(`
                        SELECT COUNT(*) FROM legal_templates
                        WHERE tenant_id = '${tenantId}';
                    `);

                    console.log(`Access result: SUCCESS - Found ${rows[0].count} templates`);

                    // Check specific RLS expressions
                    const rlsCheckQuery = `
                        SELECT
                            ((tenant_id = '${tenantId}' OR tenant_id = '00000000-0000-0000-0000-000000000000')
                            AND '${role}' = ANY(ARRAY['partner', 'attorney', 'staff'])) AS select_access,
                            ((tenant_id = '${tenantId}')
                            AND '${role}' = ANY(ARRAY['partner', 'attorney'])) AS update_access,
                            ('${role}' = 'partner') AS insert_access
                        FROM legal_templates
                        LIMIT 1;
                    `;

                    const rlsCheck = await client.query(rlsCheckQuery);
                    if (rlsCheck.rows.length > 0) {
                        console.log('RLS policy check:');
                        console.log(`- SELECT access: ${rlsCheck.rows[0].select_access}`);
                        console.log(`- UPDATE access: ${rlsCheck.rows[0].update_access}`);
                        console.log(`- INSERT access: ${rlsCheck.rows[0].insert_access}`);
                    }
                } catch (error) {
                    console.log(`Access result: DENIED - ${error.message}`);
                }
            }
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        // Close the database connection
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

main();
