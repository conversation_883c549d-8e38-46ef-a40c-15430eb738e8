from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from copilotkit import CopilotKitRemoteEndpoint
from copilotkit.integrations.fastapi import add_fastapi_endpoint
import logging
import json

from config import settings, logger as config_logger

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="LangGraph API",
    description="FastAPI server for LangGraph with CopilotKit integration",
    version="0.1.0",
    debug=settings.DEBUG,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize CopilotKit
remote_endpoint = CopilotKitRemoteEndpoint()

# Add CopilotKit endpoint
add_fastapi_endpoint(app, remote_endpoint, "/copilotkit")

@app.get("/health")
async def health_check():
    """
    Health check endpoint to verify the server is running.

    Returns:
        dict: Status information
    """
    return {
        "status": "ok",
        "config": "loaded",
        "version": "0.1.0"
    }

@app.get("/config/check")
async def config_check():
    """
    Check if all required configuration is present without exposing sensitive values.

    Returns:
        dict: Configuration status for each component
    """
    config_status = {
        "openai": bool(settings.OPENAI_API_KEY),
        "pinecone": all([
            settings.PINECONE_API_KEY,
            settings.PINECONE_ENVIRONMENT,
            settings.PINECONE_INDEX_NAME
        ]),
        "supabase": all([
            settings.SUPABASE_URL,
            settings.SUPABASE_KEY
        ]),
        "jwt": bool(settings.JWT_SECRET),
        "copilotkit": bool(settings.CPK_ENDPOINT_SECRET),
        "database": all([
            settings.DB_HOST,
            settings.DB_PORT,
            settings.DB_NAME,
            settings.DB_USER,
            settings.DB_PASSWORD
        ]),
        "langsmith": all([
            settings.LANGSMITH_API_KEY,
            settings.LANGSMITH_PROJECT
        ]) if settings.LANGSMITH_API_KEY else None,
        "voyage": bool(settings.VOYAGE_API_KEY) if settings.VOYAGE_API_KEY else None,
        "validation_warnings": len(settings.validate())
    }
    return config_status

@app.get("/config/report")
async def config_report(include_sensitive: bool = False):
    """
    Generate a configuration report for debugging.

    Args:
        include_sensitive: Whether to include masked sensitive values

    Returns:
        dict: Configuration report
    """
    return settings.get_config_report(include_sensitive)

@app.on_event("startup")
async def startup_event():
    """
    Validate configuration on startup and perform any necessary initialization.
    """
    logger.info("Server starting up...")

    # Validation is already performed in config.py, but we log it here for clarity
    validation_errors = settings.validate()
    if validation_errors:
        logger.warning(f"Configuration validation warnings: {len(validation_errors)}")
        for error in validation_errors:
            logger.warning(f"- {error}")
    else:
        logger.info("Configuration validation passed")

    # Log server information
    logger.info(f"Server version: 0.1.0")
    logger.info(f"Server running on: {settings.HOST}:{settings.PORT}")
    logger.info(f"Debug mode: {settings.DEBUG}")
    logger.info(f"Environment ready")

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting server on {settings.HOST}:{settings.PORT}")
    uvicorn.run(app, host=settings.HOST, port=settings.PORT)
