# Website Prospect Integration - Complete Implementation

## Files to Create in Your Website Repo

### 1. API Route: `/app/api/prospects/signup/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Validation schema
const prospectSignupSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phone: z.string().optional(),
  signupSource: z.enum(['website', 'landing_page', 'referral', 'social_media', 'advertisement', 'other']).default('website'),
  signupPage: z.string().optional(),
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
  utmContent: z.string().optional(),
  utmTerm: z.string().optional(),
  practiceAreaInterest: z.array(z.enum(['personal_injury', 'criminal_defense', 'family_law'])).default([]),
  caseUrgency: z.enum(['immediate', 'within_month', 'within_quarter', 'planning_ahead']).optional(),
  estimatedCaseValue: z.enum(['under_10k', '10k_50k', '50k_100k', 'over_100k', 'unknown']).optional(),
  newsletterSubscribed: z.boolean().default(true),
  marketingConsent: z.boolean().default(false),
  communicationPreferences: z.object({
    email: z.boolean().default(true),
    sms: z.boolean().default(false),
    phone: z.boolean().default(false),
  }).default({ email: true, sms: false, phone: false }),
  gdprConsent: z.boolean(),
  turnstileToken: z.string().optional(),
});

// Create Supabase client
function createServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { autoRefreshToken: false, persistSession: false } }
  );
}

// Verify Turnstile token
async function verifyTurnstileToken(token: string, ip: string): Promise<boolean> {
  if (!process.env.TURNSTILE_SECRET_KEY) {
    console.warn('Turnstile secret key not configured');
    return true;
  }
  
  try {
    const formData = new URLSearchParams();
    formData.append('secret', process.env.TURNSTILE_SECRET_KEY);
    formData.append('response', token);
    formData.append('remoteip', ip);
    
    const result = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      body: formData,
    });
    
    const data = await result.json();
    return data.success === true;
  } catch (error) {
    console.error('Turnstile verification error:', error);
    return false;
  }
}

// Get client IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) return forwarded.split(',')[0].trim();
  if (realIP) return realIP;
  return request.ip || 'unknown';
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = prospectSignupSchema.parse(body);
    
    // Get client info
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const referrer = request.headers.get('referer') || null;
    
    // Verify Turnstile if provided
    if (validatedData.turnstileToken) {
      const isValid = await verifyTurnstileToken(validatedData.turnstileToken, ip);
      if (!isValid) {
        return NextResponse.json({ error: 'Invalid captcha token' }, { status: 400 });
      }
    }
    
    // GDPR consent required
    if (!validatedData.gdprConsent) {
      return NextResponse.json({ error: 'GDPR consent is required' }, { status: 400 });
    }
    
    const supabase = createServiceClient();
    
    // Check existing prospect
    const { data: existingProspect } = await supabase
      .from('prospects')
      .select('id, status, email_verified')
      .eq('email', validatedData.email)
      .maybeSingle();
    
    // Update existing or create new
    if (existingProspect && existingProspect.status === 'active') {
      const { error } = await supabase
        .from('prospects')
        .update({
          first_name: validatedData.firstName,
          last_name: validatedData.lastName,
          phone: validatedData.phone,
          newsletter_subscribed: validatedData.newsletterSubscribed,
          marketing_consent: validatedData.marketingConsent,
          communication_preferences: validatedData.communicationPreferences,
          practice_area_interest: validatedData.practiceAreaInterest,
          case_urgency: validatedData.caseUrgency,
          estimated_case_value: validatedData.estimatedCaseValue,
          updated_at: new Date().toISOString(),
        })
        .eq('email', validatedData.email);
      
      if (error) {
        console.error('Error updating prospect:', error);
        return NextResponse.json({ error: 'Failed to update signup' }, { status: 500 });
      }
      
      return NextResponse.json({
        success: true,
        message: 'Preferences updated successfully',
        prospectId: existingProspect.id,
        emailVerified: existingProspect.email_verified,
      });
    }
    
    // Create new prospect
    const prospectData = {
      email: validatedData.email,
      first_name: validatedData.firstName,
      last_name: validatedData.lastName,
      phone: validatedData.phone,
      signup_source: validatedData.signupSource,
      signup_page: validatedData.signupPage,
      utm_source: validatedData.utmSource,
      utm_medium: validatedData.utmMedium,
      utm_campaign: validatedData.utmCampaign,
      utm_content: validatedData.utmContent,
      utm_term: validatedData.utmTerm,
      newsletter_subscribed: validatedData.newsletterSubscribed,
      marketing_consent: validatedData.marketingConsent,
      communication_preferences: validatedData.communicationPreferences,
      practice_area_interest: validatedData.practiceAreaInterest,
      case_urgency: validatedData.caseUrgency,
      estimated_case_value: validatedData.estimatedCaseValue,
      gdpr_consent: validatedData.gdprConsent,
      gdpr_consent_date: new Date().toISOString(),
      gdpr_consent_ip: ip,
      gdpr_consent_user_agent: userAgent,
      data_retention_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      ip_address: ip,
      user_agent: userAgent,
      referrer_url: referrer,
      timezone: 'UTC', // You can enhance this with actual timezone detection
    };
    
    const { data: newProspect, error } = await supabase
      .from('prospects')
      .insert(prospectData)
      .select('id, email')
      .single();
    
    if (error) {
      console.error('Error creating prospect:', error);
      if (error.code === '23505') {
        return NextResponse.json({ error: 'Email already registered' }, { status: 409 });
      }
      return NextResponse.json({ error: 'Failed to create signup' }, { status: 500 });
    }
    
    // Log signup interaction
    await supabase.from('prospect_interactions').insert({
      prospect_id: newProspect.id,
      interaction_type: 'newsletter_signup',
      subject: 'Initial signup',
      metadata: {
        signup_source: validatedData.signupSource,
        practice_areas: validatedData.practiceAreaInterest,
        utm_data: {
          source: validatedData.utmSource,
          medium: validatedData.utmMedium,
          campaign: validatedData.utmCampaign,
        }
      },
      ip_address: ip,
      user_agent: userAgent,
      referrer_url: referrer,
    });
    
    return NextResponse.json({
      success: true,
      message: 'Signup successful',
      prospectId: newProspect.id,
      emailVerified: false,
    });
    
  } catch (error) {
    console.error('Prospect signup error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### 2. Enhanced Form Component: `/components/ProspectSignupForm.tsx`

```typescript
'use client';

import { useState } from 'react';
import { Turnstile } from '@marsidev/react-turnstile';

interface FormData {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  practiceAreaInterest: string[];
  caseUrgency: string;
  gdprConsent: boolean;
}

export function ProspectSignupForm({ 
  className = "",
  title = "Join Our Waitlist",
  subtitle = "Get notified when we launch"
}) {
  const [formData, setFormData] = useState<FormData>({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    practiceAreaInterest: [],
    caseUrgency: '',
    gdprConsent: false,
  });
  
  const [turnstileToken, setTurnstileToken] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');

  // Capture UTM parameters
  const getUtmParams = () => {
    if (typeof window === 'undefined') return {};
    
    const urlParams = new URLSearchParams(window.location.search);
    return {
      utmSource: urlParams.get('utm_source'),
      utmMedium: urlParams.get('utm_medium'),
      utmCampaign: urlParams.get('utm_campaign'),
      utmContent: urlParams.get('utm_content'),
      utmTerm: urlParams.get('utm_term'),
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.gdprConsent) {
      setMessage('Please accept the privacy policy to continue.');
      setMessageType('error');
      return;
    }

    setIsSubmitting(true);
    setMessage('');

    try {
      const response = await fetch('/api/prospects/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          signupSource: 'website',
          signupPage: window.location.pathname,
          ...getUtmParams(),
          turnstileToken,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMessage('Thank you! Please check your email to verify your address.');
        setMessageType('success');
        // Reset form
        setFormData({
          email: '', firstName: '', lastName: '', phone: '',
          practiceAreaInterest: [], caseUrgency: '', gdprConsent: false,
        });
        setTurnstileToken('');
      } else {
        setMessage(result.error || 'Something went wrong. Please try again.');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
      setMessageType('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={className}>
      {title && <h2 className="text-2xl font-bold mb-2">{title}</h2>}
      {subtitle && <p className="text-gray-600 mb-6">{subtitle}</p>}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="First Name *"
            required
            value={formData.firstName}
            onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <input
            type="text"
            placeholder="Last Name *"
            required
            value={formData.lastName}
            onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <input
          type="email"
          placeholder="Email Address *"
          required
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />

        <input
          type="tel"
          placeholder="Phone Number (Optional)"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Practice Areas of Interest
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {[
              { value: 'personal_injury', label: 'Personal Injury' },
              { value: 'criminal_defense', label: 'Criminal Defense' },
              { value: 'family_law', label: 'Family Law' }
            ].map((area) => (
              <label key={area.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.practiceAreaInterest.includes(area.value)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setFormData({
                        ...formData,
                        practiceAreaInterest: [...formData.practiceAreaInterest, area.value]
                      });
                    } else {
                      setFormData({
                        ...formData,
                        practiceAreaInterest: formData.practiceAreaInterest.filter(a => a !== area.value)
                      });
                    }
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">{area.label}</span>
              </label>
            ))}
          </div>
        </div>

        <select
          value={formData.caseUrgency}
          onChange={(e) => setFormData({ ...formData, caseUrgency: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">How soon do you need legal assistance?</option>
          <option value="immediate">Immediately</option>
          <option value="within_month">Within a month</option>
          <option value="within_quarter">Within 3 months</option>
          <option value="planning_ahead">Just planning ahead</option>
        </select>

        <label className="flex items-start space-x-3">
          <input
            type="checkbox"
            required
            checked={formData.gdprConsent}
            onChange={(e) => setFormData({ ...formData, gdprConsent: e.target.checked })}
            className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">
            I consent to receive marketing communications and agree to the{' '}
            <a href="/privacy-policy" className="text-blue-600 hover:underline">
              Privacy Policy
            </a>. *
          </span>
        </label>

        <Turnstile
          siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
          onSuccess={setTurnstileToken}
          onError={() => setTurnstileToken('')}
          onExpire={() => setTurnstileToken('')}
        />

        <button
          type="submit"
          disabled={isSubmitting || !turnstileToken}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {isSubmitting ? 'Joining Waitlist...' : 'Join Waitlist'}
        </button>

        {message && (
          <div className={`text-sm p-3 rounded ${
            messageType === 'success' 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {message}
          </div>
        )}
      </form>
    </div>
  );
}
```

### 3. Package Dependencies

Add to your website's `package.json`:

```bash
npm install @supabase/supabase-js @marsidev/react-turnstile zod
```

### 4. Environment Variables

Update your `.env.local`:

```bash
# Existing
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA7TWjsvWoeed9DQ
TURNSTILE_SECRET_KEY=your_turnstile_secret
RESEND_API_KEY=your_resend_key

# Add these
NEXT_PUBLIC_SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_SITE_URL=https://www.ailexlaw.com
```

## 🔄 Migration Strategy

**Phase 1 (Now)**: Website handles prospects directly
**Phase 2 (Later)**: Switch to PI Lawyer AI backend by changing one environment variable

```bash
# Phase 1: Direct integration
# Use local API routes

# Phase 2: Switch to backend
NEXT_PUBLIC_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app
# Then update form to use external API
```

### 5. Email Verification API: `/app/api/prospects/verify-email/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import { z } from 'zod';

const sendVerificationSchema = z.object({
  email: z.string().email(),
});

const verifyEmailSchema = z.object({
  token: z.string().min(1),
});

function createServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { autoRefreshToken: false, persistSession: false } }
  );
}

const resend = new Resend(process.env.RESEND_API_KEY);

async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  try {
    const verificationUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/verify-email?token=${token}`;

    const { error } = await resend.emails.send({
      from: '<EMAIL>',
      to: [email],
      subject: 'Verify your email address - AiLex Law',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">Welcome to AiLex Law!</h1>
            <p style="color: #6b7280; font-size: 16px;">Please verify your email address to complete your signup</p>
          </div>

          <div style="background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
            <p style="color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
              Thank you for joining our waitlist! We're excited to keep you updated on our AI-powered legal solutions.
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}"
                 style="background-color: #2563eb; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600; font-size: 16px;">
                Verify Email Address
              </a>
            </div>

            <p style="color: #6b7280; font-size: 14px; text-align: center;">
              Or copy and paste this link into your browser:
            </p>
            <p style="word-break: break-all; color: #6b7280; font-size: 14px; text-align: center; background-color: #f3f4f6; padding: 10px; border-radius: 4px;">
              ${verificationUrl}
            </p>
          </div>

          <div style="text-align: center; color: #6b7280; font-size: 14px;">
            <p>This verification link will expire in 24 hours.</p>
            <p>If you didn't sign up for AiLex Law, you can safely ignore this email.</p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Resend error:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    return false;
  }
}

// POST: Send verification email
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { email } = sendVerificationSchema.parse(body);

    const supabase = createServiceClient();

    const { data: prospect } = await supabase
      .from('prospects')
      .select('id, email_verified, status')
      .eq('email', email)
      .eq('status', 'active')
      .is('deleted_at', null)
      .maybeSingle();

    if (!prospect) {
      return NextResponse.json({ error: 'Email not found' }, { status: 404 });
    }

    if (prospect.email_verified) {
      return NextResponse.json({ error: 'Email already verified' }, { status: 400 });
    }

    // Generate token
    const token = Buffer.from(crypto.getRandomValues(new Uint8Array(32))).toString('base64url');

    const { error } = await supabase
      .from('prospects')
      .update({
        email_verification_token: token,
        email_verification_sent_at: new Date().toISOString(),
      })
      .eq('email', email);

    if (error) {
      return NextResponse.json({ error: 'Failed to generate token' }, { status: 500 });
    }

    const emailSent = await sendVerificationEmail(email, token);

    if (!emailSent) {
      return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Verification email sent' });

  } catch (error) {
    console.error('Send verification error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT: Verify email with token
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { token } = verifyEmailSchema.parse(body);

    const supabase = createServiceClient();

    const { data: prospect, error } = await supabase
      .from('prospects')
      .select('id, email, first_name, last_name')
      .eq('email_verification_token', token)
      .gte('email_verification_sent_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .eq('email_verified', false)
      .eq('status', 'active')
      .is('deleted_at', null)
      .maybeSingle();

    if (error || !prospect) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 400 });
    }

    const { error: updateError } = await supabase
      .from('prospects')
      .update({
        email_verified: true,
        email_verified_at: new Date().toISOString(),
        email_verification_token: null,
      })
      .eq('id', prospect.id);

    if (updateError) {
      return NextResponse.json({ error: 'Verification failed' }, { status: 500 });
    }

    // Log verification
    await supabase.from('prospect_interactions').insert({
      prospect_id: prospect.id,
      interaction_type: 'email_verified',
      subject: 'Email verified',
      metadata: { verified_at: new Date().toISOString() },
    });

    return NextResponse.json({
      success: true,
      message: 'Email verified successfully',
      prospect: {
        id: prospect.id,
        email: prospect.email,
        firstName: prospect.first_name,
        lastName: prospect.last_name,
      },
    });

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### 6. Email Verification Page: `/app/verify-email/page.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [prospectData, setProspectData] = useState<any>(null);

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setMessage('Invalid verification link. Please check your email for the correct link.');
      return;
    }

    const verifyEmail = async () => {
      try {
        const response = await fetch('/api/prospects/verify-email', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token }),
        });

        const result = await response.json();

        if (result.success) {
          setStatus('success');
          setMessage('Your email has been verified successfully!');
          setProspectData(result.prospect);
        } else {
          setStatus('error');
          setMessage(result.error || 'Verification failed. The link may be expired or invalid.');
        }
      } catch (error) {
        setStatus('error');
        setMessage('Network error during verification. Please try again.');
      }
    };

    verifyEmail();
  }, [token]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Email Verification</h1>

          {status === 'loading' && (
            <div className="space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">Verifying your email address...</p>
            </div>
          )}

          {status === 'success' && (
            <div className="space-y-4">
              <div className="text-6xl">✅</div>
              <div className="space-y-2">
                <p className="text-green-600 font-medium">{message}</p>
                {prospectData && (
                  <p className="text-gray-600">
                    Welcome, {prospectData.firstName}! You're now on our waitlist.
                  </p>
                )}
              </div>
              <div className="space-y-3 pt-4">
                <Link
                  href="/"
                  className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Return to Homepage
                </Link>
                <p className="text-sm text-gray-500">
                  We'll keep you updated on our progress and notify you when we launch.
                </p>
              </div>
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-4">
              <div className="text-6xl">❌</div>
              <div className="space-y-2">
                <p className="text-red-600 font-medium">{message}</p>
                <p className="text-gray-600 text-sm">
                  If you continue to have issues, please contact our support team.
                </p>
              </div>
              <div className="space-y-3 pt-4">
                <Link
                  href="/"
                  className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Return to Homepage
                </Link>
                <Link
                  href="/contact"
                  className="block text-blue-600 hover:underline text-sm"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
```

### 7. Update Package Dependencies

```bash
npm install @supabase/supabase-js @marsidev/react-turnstile zod resend
```

## 🔄 Migration Strategy

**Phase 1 (Now)**: Website handles prospects directly
- Use the API routes above in your website
- Prospects stored in same Supabase database
- Email verification handled by website

**Phase 2 (Later)**: Switch to PI Lawyer AI backend
- Change `NEXT_PUBLIC_PI_LAWYER_API_URL` environment variable
- Update form component to use external API
- All existing prospect data remains intact

This approach gives you immediate prospect collection capability while maintaining flexibility for future migration!
