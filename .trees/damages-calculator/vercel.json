{"buildCommand": "cd frontend && pnpm build", "installCommand": "pnpm install", "outputDirectory": "frontend/.next", "functions": {"frontend/src/app/api/**/*.ts": {"runtime": "@vercel/node@3.0.0", "maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-API-Key"}]}], "rewrites": [{"source": "/api/copilotkit/:path*", "destination": "/api/copilotkit/:path*"}]}