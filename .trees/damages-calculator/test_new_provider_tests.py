#!/usr/bin/env python3
"""
Test runner for the new provider token tests.
"""

import asyncio
import sys
import os
from unittest.mock import patch, MagicMock
import pytest

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_new_tests():
    """Run the new provider token tests."""
    print("🚀 Running new provider token tests...\n")
    
    # Mock the problematic imports to avoid circular dependencies
    mock_modules = {
        'backend.agents.interactive.calendar_crud.providers': MagicMock(),
        'backend.agents.interactive.calendar_crud.providers.google': MagicMock(),
        'backend.agents.interactive.calendar_crud.providers.calendly': MagicMock(),
    }
    
    with patch.dict('sys.modules', mock_modules):
        # Run our new tests
        result = pytest.main([
            '-v',
            'tests/providers/test_google_token_fetch.py',
            'tests/providers/test_calendly_token_fetch.py',
            '--tb=short'
        ])
        
        return result

if __name__ == "__main__":
    result = run_new_tests()
    if result == 0:
        print("\n✅ All new provider token tests passed!")
    else:
        print(f"\n❌ Some tests failed (exit code: {result})")
    sys.exit(result)
