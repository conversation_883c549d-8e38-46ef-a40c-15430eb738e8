# \!/usr/bin/env python3
"""
Debug script for Supabase client
"""

import os
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")


def main():
    print("Testing Supabase client")
    print(f"URL: {SUPABASE_URL}")
    print(f"Key length: {len(SUPABASE_KEY) if SUPABASE_KEY else 'None'}")

    try:
        # Create client - new interface
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

        print(f"\nClient type: {type(supabase)}")

        # Test setting schema
        try:
            # Try to set schema
            print("\nTrying to set schema:")

            # Check if we can configure schema
            if hasattr(supabase, "schema"):
                print("Setting schema to 'tenants'")
                supabase.schema("tenants")

            # These are the parameters used in the test
            params = {
                "p_client_data": {
                    "first_name": "Test",
                    "last_name": "Client",
                    "client_type": "individual",
                    "status": "active",
                    "email": "<EMAIL>",
                    "phone_primary": "************",
                },
                "p_case_data": {
                    "title": "Test Case",
                    "description": "Test case",
                    "practice_area": "personal_injury",
                    "case_type": "auto_accident",
                    "status": "active",
                },
                "p_other_parties": [],
                "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",
                "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",
            }

            # Try with plain function name
            print("\nTrying rpc with plain function name...")
            try:
                response = supabase.rpc("create_client_intake", params)
                print(f"Response type: {type(response)}")
                result = response.execute()
                print(f"Result: {result}")
            except Exception as e:
                print(f"Error with plain function name: {e}")

            # Try with PostgREST schema format (schema.function)
            print("\nTrying rpc with schema.function format...")
            try:
                response = supabase.rpc("tenants.create_client_intake", params)
                print(f"Response type: {type(response)}")
                result = response.execute()
                print(f"Result: {result}")
            except Exception as e:
                print(f"Error with schema.function format: {e}")

            # Try with table() approach
            print("\nTrying with table() approach...")
            try:
                # First try to access the schema
                from_schema = supabase.from_("tenants")
                print(f"From schema type: {type(from_schema)}")

                # Then try to call the function
                response = from_schema.rpc("create_client_intake", params)
                print(f"Response type: {type(response)}")
                result = response.execute()
                print(f"Result: {result}")
            except Exception as e:
                print(f"Error with table() approach: {e}")

        except Exception as e:
            print(f"Error testing schema: {e}")

    except Exception as e:
        print(f"Error creating client: {e}")


if __name__ == "__main__":
    main()
