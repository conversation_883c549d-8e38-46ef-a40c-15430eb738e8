// Use the pg module to connect directly to Supabase
const { Client } = require('pg');

async function main() {
    // Create a new PostgreSQL client
    const client = new Client({
        connectionString: '*****************************************************************************/postgres'
    });

    try {
        // Connect to the database
        await client.connect();
        console.log('Connected to Supabase PostgreSQL database');

        // Query to get legal_templates table schema
        const schemaQuery = `
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'legal_templates'
            ORDER BY ordinal_position;
        `;

        const schemaResult = await client.query(schemaQuery);
        console.log('Schema information:');
        console.log(JSON.stringify(schemaResult.rows, null, 2));

        // Query to check RLS policies
        const rlsQuery = `
            SELECT
                tablename,
                policyname,
                cmd,
                permissive,
                roles,
                qual::text as check_expression,
                with_check::text as with_check_expression
            FROM pg_policies
            WHERE tablename = 'legal_templates';
        `;

        const rlsResult = await client.query(rlsQuery);
        console.log('\nRLS Policies:');
        console.log(JSON.stringify(rlsResult.rows, null, 2));

        // Query to check table contents (sample)
        const contentQuery = `
            SELECT id, name, tenant_id, is_active, created_at
            FROM legal_templates
            LIMIT 5;
        `;

        const contentResult = await client.query(contentQuery);
        console.log('\nTable Contents (Sample):');
        console.log(JSON.stringify(contentResult.rows, null, 2));

    } catch (error) {
        console.error('Error:', error);
    } finally {
        // Close the database connection
        await client.end();
        console.log('Database connection closed');
    }
}

main();
