"""
Test script for running the Supervisor Agent standalone
"""

import os
import asyncio
from langchain.schema import HumanMessage
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from pydantic import BaseModel
from typing import Dict, List, Optional, Any

# Import the supervisor agent
from pi_lawyer.agents.supervisor_agent import app as supervisor_app
from pi_lawyer.agents.supervisor_agent import SupervisorState

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class SupervisorRequest(BaseModel):
    message: str
    thread_id: Optional[str] = None


class SupervisorResponse(BaseModel):
    threadId: str
    messages: List[Dict[str, Any]]
    routing: Optional[Dict[str, Any]] = None


@app.post("/api/supervisor", response_model=SupervisorResponse)
async def handle_supervisor_request(request: SupervisorRequest):
    """
    Handle requests through the supervisor agent
    """
    user_input = request.message
    thread_id = request.thread_id

    if not thread_id:
        # Start a new conversation
        state = SupervisorState()
        state.messages.append(HumanMessage(content=user_input))

        # Run the graph
        result = await supervisor_app["graph"].ainvoke(state)

        # Save the state
        thread_id = supervisor_app["memory"].save(result)
    else:
        # Continue an existing conversation
        state = supervisor_app["memory"].load(thread_id)
        state.messages.append(HumanMessage(content=user_input))

        # Run the graph
        result = await supervisor_app["graph"].ainvoke(state)

        # Update the saved state
        supervisor_app["memory"].save(result, thread_id)

    # Format messages for the response
    formatted_messages = []
    for msg in result.messages:
        formatted_messages.append(
            {
                "role": "assistant" if msg.type == "ai" else "user",
                "content": msg.content,
            }
        )

    # Format the routing information
    routing_info = None
    if result.routing:
        routing_info = {
            "agent_type": result.routing["agent_type"],
            "confidence": result.routing["confidence"],
            "rationale": result.routing["rationale"],
        }

    return SupervisorResponse(
        threadId=thread_id, messages=formatted_messages, routing=routing_info
    )


@app.get("/health")
async def health_check():
    return {"status": "ok"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
