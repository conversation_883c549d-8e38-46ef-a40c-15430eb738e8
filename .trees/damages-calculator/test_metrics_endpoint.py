#!/usr/bin/env python3
"""
Simple script to test the metrics endpoint functionality.

This script can be used to manually verify that the metrics endpoint
works correctly and returns the expected Prometheus metrics.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_metrics_endpoint():
    """Test the metrics endpoint manually."""
    try:
        print("Testing metrics endpoint functionality...\n")
        
        # Import metrics components
        from backend.metrics import (
            calendar_api_requests_total,
            calendar_api_response_time,
            get_metrics_output
        )
        
        print("✓ Successfully imported metrics components")
        
        # Simulate some calendar API traffic
        print("\n📊 Simulating calendar API traffic...")
        
        # Google provider requests
        calendar_api_requests_total.labels(
            provider="google",
            endpoint="connect",
            status="success"
        ).inc(5)
        
        calendar_api_requests_total.labels(
            provider="google",
            endpoint="callback",
            status="success"
        ).inc(3)
        
        calendar_api_requests_total.labels(
            provider="google",
            endpoint="events",
            status="error"
        ).inc(1)
        
        # Microsoft provider requests
        calendar_api_requests_total.labels(
            provider="microsoft",
            endpoint="connect",
            status="success"
        ).inc(2)
        
        calendar_api_requests_total.labels(
            provider="microsoft",
            endpoint="callback",
            status="error"
        ).inc(1)
        
        # Calendly provider requests
        calendar_api_requests_total.labels(
            provider="calendly",
            endpoint="webhook",
            status="success"
        ).inc(10)
        
        # Response times
        calendar_api_response_time.labels(provider="google").observe(0.2)
        calendar_api_response_time.labels(provider="google").observe(0.5)
        calendar_api_response_time.labels(provider="google").observe(1.1)
        
        calendar_api_response_time.labels(provider="microsoft").observe(0.8)
        calendar_api_response_time.labels(provider="microsoft").observe(2.3)
        
        calendar_api_response_time.labels(provider="calendly").observe(0.1)
        calendar_api_response_time.labels(provider="calendly").observe(0.3)
        
        print("✓ Generated sample metrics data")
        
        # Generate metrics output
        print("\n📈 Generating metrics output...")
        output = get_metrics_output()
        output_str = output.decode('utf-8')
        
        print(f"✓ Generated {len(output)} bytes of metrics data")
        
        # Display the metrics
        print("\n" + "="*60)
        print("PROMETHEUS METRICS OUTPUT")
        print("="*60)
        print(output_str)
        print("="*60)
        
        # Verify key metrics are present
        print("\n🔍 Verifying metrics content...")
        
        checks = [
            ("calendar_api_requests_total", "Calendar request counter"),
            ("calendar_api_response_time", "Calendar response time histogram"),
            ("Calendar CRUD requests", "Counter help text"),
            ("Latency of calendar provider calls", "Histogram help text"),
            ('provider="google"', "Google provider label"),
            ('provider="microsoft"', "Microsoft provider label"),
            ('provider="calendly"', "Calendly provider label"),
            ('endpoint="connect"', "Connect endpoint label"),
            ('status="success"', "Success status label"),
            ('status="error"', "Error status label"),
        ]
        
        for check, description in checks:
            if check in output_str:
                print(f"✓ Found {description}")
            else:
                print(f"✗ Missing {description}")
                return False
        
        print(f"\n🎉 All checks passed! Metrics endpoint is working correctly.")
        print(f"\n💡 To test with curl:")
        print(f"   curl http://localhost:8000/metrics")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_metrics_endpoint()
    sys.exit(0 if success else 1)
