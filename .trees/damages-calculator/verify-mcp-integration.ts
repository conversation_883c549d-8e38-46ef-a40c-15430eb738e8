#!/usr/bin/env ts-node

/**
 * MCP Integration Verification Script
 *
 * This script verifies that all MCP components are properly integrated
 * and TypeScript compilation is working correctly.
 */

const fs = require('fs');
const path = require('path');

interface VerificationResult {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
}

class McpIntegrationVerifier {
  private results: VerificationResult[] = [];

  /**
   * Run all verification checks
   */
  async verify(): Promise<void> {
    console.log('🔍 Starting MCP Integration Verification...\n');

    // Check file structure
    this.checkFileStructure();
    
    // Check TypeScript compilation
    await this.checkTypeScriptCompilation();
    
    // Check environment configuration
    this.checkEnvironmentConfig();
    
    // Check package dependencies
    this.checkPackageDependencies();
    
    // Print results
    this.printResults();
  }

  /**
   * Check that all required files exist
   */
  private checkFileStructure(): void {
    const requiredFiles = [
      'packages/mcp-client/src/index.ts',
      'packages/mcp-client/src/types.ts',
      'packages/mcp-client/package.json',
      'packages/mcp-client/tsconfig.json',
      'infra/terraform/mcp-secret.tf',
      'infra/terraform/api-key-rbac.tf',
      'apps/backend/src/middleware/mcpMetrics.ts',
      'apps/backend/src/services/mcpService.ts',
      'scripts/rotate-mcp-key.ts',
      'tests/mcp.e2e.spec.ts',
      'README_MCP.md',
      '.env.example'
    ];

    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        this.addResult(file, 'PASS', 'File exists');
      } else {
        this.addResult(file, 'FAIL', 'File missing');
      }
    }
  }

  /**
   * Check TypeScript compilation
   */
  private async checkTypeScriptCompilation(): Promise<void> {
    try {
      // Check MCP client compilation
      const mcpClientPath = 'packages/mcp-client/dist';
      if (fs.existsSync(mcpClientPath)) {
        const distFiles = fs.readdirSync(mcpClientPath);
        if (distFiles.includes('index.js') && distFiles.includes('index.d.ts')) {
          this.addResult('MCP Client Build', 'PASS', 'TypeScript compilation successful');
        } else {
          this.addResult('MCP Client Build', 'FAIL', 'Missing compiled files');
        }
      } else {
        this.addResult('MCP Client Build', 'FAIL', 'Dist directory not found');
      }

      // Check if TypeScript can compile the main files
      const tsFiles = [
        'packages/mcp-client/src/index.ts',
        'apps/backend/src/middleware/mcpMetrics.ts',
        'apps/backend/src/services/mcpService.ts'
      ];

      for (const file of tsFiles) {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          if (content.includes('export') || content.includes('import')) {
            this.addResult(`${file} Syntax`, 'PASS', 'Valid TypeScript syntax');
          } else {
            this.addResult(`${file} Syntax`, 'WARN', 'No imports/exports found');
          }
        }
      }
    } catch (error) {
      this.addResult('TypeScript Compilation', 'FAIL', `Error: ${error}`);
    }
  }

  /**
   * Check environment configuration
   */
  private checkEnvironmentConfig(): void {
    try {
      const envExample = fs.readFileSync('.env.example', 'utf8');
      
      const requiredVars = [
        'MCP_RULES_BASE',
        'MCP_API_KEY'
      ];

      for (const varName of requiredVars) {
        if (envExample.includes(varName)) {
          this.addResult(`Env Var: ${varName}`, 'PASS', 'Found in .env.example');
        } else {
          this.addResult(`Env Var: ${varName}`, 'FAIL', 'Missing from .env.example');
        }
      }
    } catch (error) {
      this.addResult('Environment Config', 'FAIL', `Error reading .env.example: ${error}`);
    }
  }

  /**
   * Check package dependencies
   */
  private checkPackageDependencies(): void {
    try {
      // Check main package.json
      const mainPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const requiredDeps = [
        '@google-cloud/apikeys',
        '@google-cloud/secret-manager',
        'commander',
        'google-auth-library'
      ];

      for (const dep of requiredDeps) {
        if (mainPackage.dependencies?.[dep] || mainPackage.devDependencies?.[dep]) {
          this.addResult(`Dependency: ${dep}`, 'PASS', 'Found in package.json');
        } else {
          this.addResult(`Dependency: ${dep}`, 'FAIL', 'Missing from package.json');
        }
      }

      // Check MCP client package.json
      const mcpPackage = JSON.parse(fs.readFileSync('packages/mcp-client/package.json', 'utf8'));
      if (mcpPackage.dependencies?.['node-fetch']) {
        this.addResult('MCP Client: node-fetch', 'PASS', 'Found in mcp-client package.json');
      } else {
        this.addResult('MCP Client: node-fetch', 'FAIL', 'Missing from mcp-client package.json');
      }

    } catch (error) {
      this.addResult('Package Dependencies', 'FAIL', `Error: ${error}`);
    }
  }

  /**
   * Add a verification result
   */
  private addResult(component: string, status: 'PASS' | 'FAIL' | 'WARN', message: string): void {
    this.results.push({ component, status, message });
  }

  /**
   * Print verification results
   */
  private printResults(): void {
    console.log('\n📊 Verification Results:\n');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warned = this.results.filter(r => r.status === 'WARN').length;

    for (const result of this.results) {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${icon} ${result.component}: ${result.message}`);
    }

    console.log(`\n📈 Summary: ${passed} passed, ${failed} failed, ${warned} warnings`);
    
    if (failed === 0) {
      console.log('\n🎉 MCP Integration verification completed successfully!');
      console.log('\n🚀 Next Steps:');
      console.log('1. Deploy Terraform infrastructure: cd infra/terraform && terraform apply');
      console.log('2. Set environment variables in your deployment');
      console.log('3. Test the integration with: npm run mcp:rotate-key -- --help');
      console.log('4. Integrate mcpService into your case creation flow');
    } else {
      console.log('\n⚠️  Some verification checks failed. Please review and fix the issues above.');
      process.exit(1);
    }
  }
}

// Run verification if this script is executed directly
if (require.main === module) {
  const verifier = new McpIntegrationVerifier();
  verifier.verify().catch(error => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });
}
