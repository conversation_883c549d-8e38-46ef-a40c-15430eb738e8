#!/usr/bin/env node

/**
 * Verify Migration Progress
 * Test that our migrated tables are accessible and working
 */

const { createClient } = require('@supabase/supabase-js');

// Staging database configuration
const STAGING_URL = 'https://btwaueeckvylrlrnbvgt.supabase.co';
const STAGING_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0d2F1ZWVja3Z5bHJscm5idmd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxNzMxNjIsImV4cCI6MjA2MDc0OTE2Mn0.06bTSo_WpKBjtXmgmMYKd0cYAOoi-xhydEjNZbo2GYk';

// Tables we've migrated so far
const MIGRATED_TABLES = [
  // Batch 1: Core tables (already done)
  { schema: 'tenants', table: 'subscription_plans', description: 'Subscription plans (already existed)' },
  { schema: 'tenants', table: 'users', description: 'User management' },
  { schema: 'tenants', table: 'firms', description: 'Law firm data' },
  { schema: 'tenants', table: 'matters', description: 'Case/matter management' },
  { schema: 'tenants', table: 'clients', description: 'Client management' },
  { schema: 'tenants', table: 'tasks', description: 'Task management' },

  // Batch 2: Business logic (just added)
  { schema: 'tenants', table: 'user_roles', description: 'Role-based access control' },
  { schema: 'tenants', table: 'tenant_subscriptions', description: 'Active subscriptions' },
  { schema: 'tenants', table: 'tenant_quotas', description: 'Usage limits and quotas' },

  // Batch 3: Legal data (just added)
  { schema: 'public', table: 'documents', description: 'Document management' },
  { schema: 'public', table: 'cases', description: 'Legal case database' },
  { schema: 'public', table: 'people', description: 'Legal entities/people' },
  { schema: 'public', table: 'jurisdictions', description: 'Legal jurisdictions' },
  { schema: 'public', table: 'legal_templates', description: 'Document templates' },

  // Batch 4: Advanced features (just added)
  { schema: 'tenants', table: 'activities', description: 'Activity tracking' },
  { schema: 'tenants', table: 'notifications', description: 'Notification system' },
  { schema: 'tenants', table: 'authored_documents', description: 'Document authoring' },
  { schema: 'tenants', table: 'client_documents', description: 'Client document management' },
  { schema: 'tenants', table: 'deadlines', description: 'Deadline management' },
  { schema: 'public', table: 'system_settings', description: 'Application configuration' },

  // Batch 5: Case management & workflow (just added)
  { schema: 'tenants', table: 'assignments', description: 'Matter assignments' },
  { schema: 'tenants', table: 'case_clients', description: 'Case-client relationships' },
  { schema: 'tenants', table: 'case_documents', description: 'Case document management' },
  { schema: 'tenants', table: 'events', description: 'Calendar events & meetings' },
  { schema: 'tenants', table: 'parties', description: 'Legal parties management' },
  { schema: 'tenants', table: 'conflict_checks', description: 'Conflict of interest checks' },
  { schema: 'tenants', table: 'document_categories', description: 'Document categorization' },

  // Batch 6: Advanced features & AI (just added)
  { schema: 'tenants', table: 'bookings', description: 'Calendar bookings & appointments' },
  { schema: 'tenants', table: 'insights', description: 'AI-generated insights' },
  { schema: 'tenants', table: 'legal_rules', description: 'Legal rule engine' },
  { schema: 'tenants', table: 'jurisdictions', description: 'Tenant-specific jurisdictions' },
  { schema: 'public', table: 'opinions', description: 'Legal opinions' },
  { schema: 'public', table: 'citations', description: 'Legal citations' },
  { schema: 'tenants', table: 'intake_events', description: 'Client intake tracking' },
  { schema: 'tenants', table: 'resource_usage', description: 'Usage tracking & analytics' },
  { schema: 'tenants', table: 'ml_models', description: 'ML model configurations' },

  // Batch 7: Enterprise features & compliance (just added)
  { schema: 'tenants', table: 'briefing_configs', description: 'Daily briefing configurations' },
  { schema: 'tenants', table: 'case_parties', description: 'Case-party relationships' },
  { schema: 'tenants', table: 'access_logs', description: 'Security & audit logs' },
  { schema: 'public', table: 'chunks', description: 'Document chunks for search' },
  { schema: 'tenants', table: 'countries', description: 'Country configurations' },
  { schema: 'tenants', table: 'plan_pricing', description: 'Multi-currency pricing' },
  { schema: 'tenants', table: 'subscription_pricing', description: 'Subscription pricing tiers' },
  { schema: 'tenants', table: 'document_chunks', description: 'Tenant document chunks' },
  { schema: 'public', table: 'prospects', description: 'Lead management & marketing' },

  // Batch 8: Advanced subscription & workflow (just added)
  { schema: 'tenants', table: 'subscription_addons', description: 'Subscription add-on products' },
  { schema: 'tenants', table: 'tenant_addons', description: 'Active tenant add-ons' },
  { schema: 'tenants', table: 'task_deadlines', description: 'Task deadline calculations' },
  { schema: 'tenants', table: 'task_history', description: 'Task change history' },
  { schema: 'tenants', table: 'notification_schedules', description: 'Scheduled notifications' },
  { schema: 'tenants', table: 'quota_adjustments', description: 'Quota adjustments & overrides' },
  { schema: 'public', table: 'blog_articles', description: 'Blog content management' },
  { schema: 'public', table: 'blog_categories', description: 'Blog categorization' },
  { schema: 'tenants', table: 'regional_features', description: 'Regional feature availability' },

  // Batch 9: Advanced AI & document management (just added)
  { schema: 'tenants', table: 'briefing_deliveries', description: 'Briefing delivery tracking' },
  { schema: 'tenants', table: 'activity_feedback', description: 'Activity feedback & analytics' },
  { schema: 'tenants', table: 'insight_feedback', description: 'AI insight feedback' },
  { schema: 'tenants', table: 'insight_templates', description: 'AI insight templates' },
  { schema: 'tenants', table: 'document_history', description: 'Document version history' },
  { schema: 'tenants', table: 'document_highlights', description: 'Document annotations' },
  { schema: 'tenants', table: 'document_summary_feedback', description: 'Document summary feedback' },
  { schema: 'public', table: 'court_systems', description: 'Court system hierarchy' },
  { schema: 'public', table: 'template_categories', description: 'Template categorization' },

  // Batch 10: Document collaboration & legal calendar (just added)
  { schema: 'tenants', table: 'authored_document_approvals', description: 'Document approval workflow' },
  { schema: 'tenants', table: 'authored_document_collaborators', description: 'Real-time collaboration' },
  { schema: 'tenants', table: 'authored_document_comments', description: 'Document commenting system' },
  { schema: 'tenants', table: 'authored_document_editors', description: 'Document editor permissions' },
  { schema: 'tenants', table: 'authored_document_signatures', description: 'Digital signature workflow' },
  { schema: 'tenants', table: 'authored_document_templates', description: 'Custom document templates' },
  { schema: 'tenants', table: 'court_holidays', description: 'Court holiday calendar' },
  { schema: 'tenants', table: 'holiday_calendars', description: 'Holiday calendar system' },
  { schema: 'tenants', table: 'firm_jurisdictions', description: 'Firm jurisdiction subscriptions' },

  // Batch 11: Advanced billing & AI features (just added)
  { schema: 'tenants', table: 'global_trial_settings', description: 'Global trial configuration' },
  { schema: 'tenants', table: 'tax_calculations', description: 'Tax calculation records' },
  { schema: 'tenants', table: 'tax_config', description: 'Tax configuration by region' },
  { schema: 'tenants', table: 'trial_extension_history', description: 'Trial extension tracking' },
  { schema: 'tenants', table: 'voice_agent_configs', description: 'Voice AI agent configuration' },
  { schema: 'public', table: 'case_people', description: 'Case-person relationships' },
  { schema: 'public', table: 'citation_network_metrics', description: 'Citation network analysis' },
  { schema: 'public', table: 'blog_markets', description: 'Multi-market blog system' },

  // Batch 12: Security, MFA & Payment schemas (just added)
  { schema: 'security', table: 'auth_audit', description: 'Authentication audit logs' },
  { schema: 'security', table: 'alerts', description: 'Security alerts system' },
  { schema: 'security', table: 'events', description: 'Security events tracking' },
  { schema: 'security', table: 'device_fingerprints', description: 'Device fingerprinting' },
  { schema: 'security', table: 'token_usage', description: 'Token usage tracking' },
  { schema: 'mfa_enhancements', table: 'superadmin_mfa_config', description: 'Superadmin MFA configuration' },
  { schema: 'mfa_enhancements', table: 'totp_metadata', description: 'TOTP metadata & backup codes' },
  { schema: 'payment_methods', table: 'sepa_mandates', description: 'SEPA payment mandates' },

  // Batch 13: Audit & Error Reporting schemas (just added)
  { schema: 'audit', table: 'log', description: 'Comprehensive audit logging' },
  { schema: 'error_reporting', table: 'error_logs', description: 'Error tracking and monitoring' },

  // Batch 14: Data Retention & Advanced Security (just added)
  { schema: 'data_retention', table: 'retention_policies', description: 'Data retention policy management' },
  { schema: 'data_retention', table: 'retention_events', description: 'Data retention event tracking' },
  { schema: 'security', table: 'anomalies', description: 'Security anomaly detection' },
  { schema: 'security', table: 'tenant_usage_metrics', description: 'Tenant usage metrics tracking' },
  { schema: 'mfa_enhancements', table: 'superadmin_mfa_sessions', description: 'MFA session management' },
  { schema: 'mfa_enhancements', table: 'totp_backup_codes', description: 'TOTP backup code management' },
  { schema: 'mfa_enhancements', table: 'security_events', description: 'MFA security event logging' },

  // Batch 15: Prospect management & interactions (just added)
  { schema: 'public', table: 'prospect_interactions', description: 'Prospect interaction tracking' },

  // Batch 16: Advanced entitlements & error aggregation (just added)
  { schema: 'tenants', table: 'tenant_entitlements', description: 'Tenant feature entitlements' },
  { schema: 'error_reporting', table: 'error_aggregates', description: 'Error aggregation and analytics' },

  // Batch 17: Legal holds, consent management & performance metrics (just added)
  { schema: 'data_retention', table: 'legal_holds', description: 'Legal hold management' },
  { schema: 'public', table: 'user_consent', description: 'User consent tracking' },
  { schema: 'error_reporting', table: 'performance_metrics', description: 'Performance monitoring' }
];

async function verifyMigration() {
  console.log('🔍 VERIFYING MIGRATION PROGRESS\n');
  
  const supabase = createClient(STAGING_URL, STAGING_ANON_KEY);
  
  let successCount = 0;
  let failCount = 0;
  
  for (const { schema, table, description } of MIGRATED_TABLES) {
    console.log(`Testing: ${schema}.${table} (${description})`);
    
    try {
      // Test basic table access
      const { data, error, count } = await supabase
        .schema(schema)
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`  ❌ Error: ${error.message}`);
        failCount++;
      } else {
        console.log(`  ✅ Success: Table accessible (${count || 0} rows)`);
        successCount++;
      }
      
    } catch (err) {
      console.log(`  ❌ Exception: ${err.message}`);
      failCount++;
    }
  }
  
  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('======================');
  console.log(`✅ Successful: ${successCount}/${MIGRATED_TABLES.length} tables`);
  console.log(`❌ Failed: ${failCount}/${MIGRATED_TABLES.length} tables`);
  
  const percentage = Math.round((successCount / MIGRATED_TABLES.length) * 100);
  console.log(`📈 Success Rate: ${percentage}%`);
  
  if (successCount === MIGRATED_TABLES.length) {
    console.log('\n🎉 ALL MIGRATED TABLES ARE ACCESSIBLE!');
    console.log('✅ Basic database connectivity working');
    console.log('✅ Schema exposure working correctly');
    console.log('✅ Table structures created successfully');
    
    console.log('\n🚀 READY FOR NEXT PHASE:');
    console.log('1. Continue migrating more tables');
    console.log('2. Test application functionality');
    console.log('3. Add sample data for testing');
  } else {
    console.log('\n⚠️  Some tables are not accessible');
    console.log('Check permissions and table creation');
  }
  
  // Test subscription plans specifically (should have data)
  console.log('\n🧪 TESTING SUBSCRIPTION PLANS DATA:');
  try {
    const { data: plans, error } = await supabase
      .schema('tenants')
      .from('subscription_plans')
      .select('name, code, base_price_monthly')
      .eq('is_active', true);
    
    if (error) {
      console.log(`❌ Subscription plans error: ${error.message}`);
    } else {
      console.log(`✅ Found ${plans.length} active subscription plans:`);
      plans.forEach(plan => {
        console.log(`  - ${plan.name} (${plan.code}): $${plan.base_price_monthly}/month`);
      });
    }
  } catch (err) {
    console.log(`❌ Subscription plans exception: ${err.message}`);
  }
  
  return successCount === MIGRATED_TABLES.length;
}

// Run verification
verifyMigration()
  .then(success => {
    if (success) {
      console.log('\n✅ Migration verification passed!');
      process.exit(0);
    } else {
      console.log('\n❌ Migration verification failed!');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('\n💥 Verification failed:', err.message);
    process.exit(1);
  });
