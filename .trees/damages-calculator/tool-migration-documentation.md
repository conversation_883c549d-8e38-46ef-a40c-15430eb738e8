# AiLex AG-UI Tool Migration Documentation

## Overview

This document provides detailed information about the tool migration process from CopilotKit v1.x to CopilotKit v2.x with AG-UI protocol in the AiLex system. It covers the implementation details, architectural decisions, and usage examples for migrated tools.

## Table of Contents

1. [Introduction](#introduction)
2. [Tool Migration Strategy](#tool-migration-strategy)
3. [Action Compatibility Layer](#action-compatibility-layer)
4. [Migrated Tools](#migrated-tools)
5. [Schema Validation](#schema-validation)
6. [Handling Parameter Changes](#handling-parameter-changes)
7. [Usage Examples](#usage-examples)
8. [Testing Migrated Tools](#testing-migrated-tools)
9. [Best Practices](#best-practices)

## Introduction

As part of the AG-UI migration, all CopilotKit tools (previously known as "actions") needed to be updated to use the new AG-UI protocol format. This migration involved:

- Converting the parameter schema format
- Adding proper JSON Schema validation
- Creating compatibility layers for progressive rollout
- Improving error handling and response formatting

## Tool Migration Strategy

The migration followed a phased approach:

1. Create a compatibility layer for tools to support both legacy and AG-UI formats
2. Migrate high-priority tools (intake, document handling)
3. Update components to use the migrated tools
4. Add comprehensive validation and error handling
5. Test all tools to ensure proper functionality

## Action Compatibility Layer

The heart of the tool migration is the action compatibility layer, which allows tools to work in both the legacy and AG-UI formats simultaneously. This enables a gradual rollout with feature flagging.

### Key Components

- `ActionConfigLegacy`: Interface for legacy CopilotKit v1.x tools
- `ActionConfigAGUI`: Interface for the new AG-UI protocol tools
- `useCopilotAction`: Hook that conditionally uses the appropriate format

### Implementation Example

```typescript
// AG-UI configuration example
const aguiConfig: ActionConfigAGUI<SaveDocumentParams> = {
  name: 'saveDocument',
  description: 'Save the current document draft',
  parameters: {
    type: 'object',
    properties: {
      document: {
        type: 'object',
        description: 'The document data to save',
        properties: {
          title: { type: 'string', description: 'Document title' },
          // Additional properties...
        }
      }
    },
    required: ['document']
  },
  handler: async (args: SaveDocumentParams) => {
    // Implementation...
  }
};
```

## Migrated Tools

The following tools have been successfully migrated to the AG-UI format:

### 1. Intake Form Submission

**Purpose**: Submits client intake information collected by the AI.

**Location**: `/lib/actions/intake-actions.ts`

**Parameters**:
- `intakeData` (object): Validated client intake information

**Response**:
- Success response with client ID and intake ID
- Error response with detailed validation errors

### 2. Document Saving

**Purpose**: Saves legal document drafts.

**Location**: `/lib/actions/document-actions.ts`

**Parameters**:
- `document` (object): Document data with title, content, variables, etc.

**Response**:
- Success response with document ID
- Error response with validation details

### 3. Staff Intake Form Completion

**Purpose**: Allows AI to populate the staff intake form with collected information.

**Location**: `/lib/actions/staff-intake-actions.ts`

**Parameters**:
- `clientName` (string): Client's full name
- `practiceArea` (string): Legal practice area
- `caseType` (string): Specific case type
- `details` (string): Case description

**Response**:
- Success response with form population confirmation
- Error response with validation details

## Schema Validation

Each tool now includes robust schema validation using Zod:

```typescript
// Example schema
export const DocumentSchema = z.object({
  id: z.string().optional(),
  title: z.string(),
  content: z.string(),
  variables: z.record(z.string(), z.string().optional()).optional(),
  templateId: z.string().optional(),
});
```

This ensures:
- Type safety across the application
- Consistent error handling
- Better developer experience with TypeScript

## Handling Parameter Changes

The key challenge in migrating tools was adapting to the different parameter format between CopilotKit v1.x and v2.x:

### Legacy Format (v1.x)
```typescript
parameters: [
  { name: 'document', type: 'object', required: true, description: '...' },
]
```

### AG-UI Format (v2.x)
```typescript
parameters: {
  type: 'object',
  properties: {
    document: {
      type: 'object',
      description: '...',
      properties: {
        // Nested properties
      }
    }
  },
  required: ['document']
}
```

## Usage Examples

### Using the Document Saving Tool

```typescript
// Component implementation
import { useSaveDocumentAction } from '@/lib/actions/document-actions';

function DocumentEditor() {
  // Register the action with CopilotKit
  useSaveDocumentAction();
  
  // Manual save handler (triggered by UI)
  const handleSaveDocument = async () => {
    try {
      // Implementation that matches the AI tool behavior
      console.log("Document saved manually");
    } catch (error) {
      console.error("Error saving document:", error);
    }
  };
  
  return (
    <Button onClick={handleSaveDocument}>Save Document</Button>
  );
}
```

### Using the Staff Intake Tool

```typescript
// Component implementation
import { useStaffIntakeAction } from '@/lib/actions/staff-intake-actions';

function IntakeForm() {
  const form = useForm();
  
  // Register the action with a handler function
  useStaffIntakeAction(async ({ clientName, practiceArea, caseType, details }) => {
    // Populate form fields with AI-supplied data
    form.setValue('firstName', clientName.split(' ')[0]);
    form.setValue('lastName', clientName.split(' ').slice(-1)[0]);
    // Additional fields...
  });
  
  return (
    <form>
      {/* Form fields */}
    </form>
  );
}
```

## Testing Migrated Tools

Each migrated tool has been tested to ensure:

1. **Functionality**: The tool performs its intended action
2. **Validation**: Input validation works correctly
3. **Error Handling**: Errors are properly caught and formatted
4. **Compatibility**: Works with both feature flag on and off

## Best Practices

When creating or modifying tools, follow these best practices:

1. **Schema First**: Always define a Zod schema for validation
2. **Clear Types**: Define TypeScript interfaces for parameters and responses
3. **Error Handling**: Provide detailed error messages for validation failures
4. **Documentation**: Document the purpose, parameters, and responses
5. **Testing**: Test with both legacy and AG-UI formats

## Next Steps

The following improvements are planned:

1. Add monitoring for tool usage and performance
2. Implement more sophisticated error handling
3. Create comprehensive testing framework for tools
4. Add more detailed documentation for each tool

---

Document last updated: May 14, 2025
