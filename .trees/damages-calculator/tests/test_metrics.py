"""
Tests for Prometheus metrics functionality.

This module contains tests to verify that the metrics endpoint works correctly
and that the calendar API metrics are properly collected.
"""

import time
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from backend.api.main import create_app
from backend.metrics import (
    REGISTRY,
    calendar_api_requests_total,
    calendar_api_response_time,
)


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    app = create_app()
    return TestClient(app)


@pytest.fixture(autouse=True)
def clear_metrics():
    """Clear metrics before each test to ensure clean state."""
    # Clear the registry
    REGISTRY._collector_to_names.clear()
    REGISTRY._names_to_collectors.clear()
    
    # Re-register our metrics
    REGISTRY.register(calendar_api_requests_total)
    REGISTRY.register(calendar_api_response_time)


def test_metrics_endpoint_exists(client):
    """Test that the /metrics endpoint exists and returns proper content type."""
    response = client.get("/metrics")
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/plain; version=0.0.4"


def test_metrics_endpoint_contains_calendar_metrics(client):
    """Test that the /metrics endpoint contains the expected calendar metrics."""
    response = client.get("/metrics")
    assert response.status_code == 200
    
    content = response.text
    
    # Check that our metrics are present in the output
    assert "calendar_api_requests_total" in content
    assert "calendar_api_response_time" in content
    
    # Check metric help text
    assert "Calendar CRUD requests" in content
    assert "Latency of calendar provider calls" in content


def test_calendar_metrics_collection_success(client):
    """Test that calendar API requests generate success metrics."""
    # Mock the get_current_user dependency to avoid authentication issues
    with patch("backend.api.routes.calendar.get_current_user") as mock_auth:
        mock_auth.return_value = MagicMock(firm_id="test-firm")
        
        # Make a request to a calendar endpoint
        response = client.get("/calendar/connect?provider=google")
        
        # The request might fail due to missing OAuth config, but metrics should still be collected
        # Check metrics endpoint
        metrics_response = client.get("/metrics")
        assert metrics_response.status_code == 200
        
        content = metrics_response.text
        
        # Verify that metrics were recorded
        assert "calendar_api_requests_total" in content
        assert "calendar_api_response_time" in content


def test_calendar_metrics_collection_error(client):
    """Test that calendar API errors generate error metrics."""
    # Make a request to a calendar endpoint that will fail
    response = client.get("/calendar/connect?provider=invalid")
    
    # Check metrics endpoint
    metrics_response = client.get("/metrics")
    assert metrics_response.status_code == 200
    
    content = metrics_response.text
    
    # Verify that metrics were recorded
    assert "calendar_api_requests_total" in content
    assert "calendar_api_response_time" in content


def test_non_calendar_routes_not_tracked(client):
    """Test that non-calendar routes don't generate calendar metrics."""
    # Make a request to a non-calendar endpoint
    response = client.get("/health", allow_redirects=False)
    
    # Check metrics endpoint
    metrics_response = client.get("/metrics")
    assert metrics_response.status_code == 200
    
    content = metrics_response.text
    
    # The metrics should exist but should not have been incremented by the /health request
    # This is harder to test directly, but we can at least verify the metrics exist
    assert "calendar_api_requests_total" in content
    assert "calendar_api_response_time" in content


def test_metrics_performance():
    """Test that metrics collection doesn't significantly impact performance."""
    app = create_app()
    client = TestClient(app)
    
    # Measure time for multiple requests
    start_time = time.time()
    
    # Make multiple requests to ensure we stay under 60s for CI
    for i in range(10):
        response = client.get("/metrics")
        assert response.status_code == 200
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Ensure the test completes quickly (well under 60s)
    assert total_time < 5.0, f"Metrics endpoint too slow: {total_time:.2f}s for 10 requests"


def test_metrics_labels():
    """Test that metrics include the expected labels."""
    app = create_app()
    client = TestClient(app)
    
    # Mock authentication for calendar routes
    with patch("backend.api.routes.calendar.get_current_user") as mock_auth:
        mock_auth.return_value = MagicMock(firm_id="test-firm")
        
        # Make requests with different providers
        client.get("/calendar/connect?provider=google")
        client.get("/calendar/connect?provider=microsoft")
        
        # Check metrics
        response = client.get("/metrics")
        content = response.text
        
        # Verify labels are present in the metrics output
        # The exact format depends on prometheus_client, but labels should be there
        assert "provider=" in content or "provider\"" in content
        assert "endpoint=" in content or "endpoint\"" in content
        assert "status=" in content or "status\"" in content


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
