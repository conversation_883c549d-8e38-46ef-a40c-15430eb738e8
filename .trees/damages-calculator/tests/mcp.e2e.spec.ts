/**
 * MCP Rules Engine E2E Tests
 *
 * Jest tests that stub fetch and assert error handling & retry logic
 * for the MCP Rules Engine client.
 */

// Mock node-fetch before importing the client
const mockFetch = jest.fn();
jest.mock('node-fetch', () => mockFetch);

import { McpClient, McpApiError } from '../packages/mcp-client/src/index';

describe('MCP Rules Engine E2E Tests', () => {
  let mcpClient: McpClient;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    mcpClient = new McpClient({
      baseUrl: 'https://rules.ailexlaw.com',
      apiKey: 'test-api-key',
      timeout: 5000,
      maxRetries: 3,
      retryDelay: 100, // Faster retries for testing
    });
  });

  describe('calculateDeadlines', () => {
    it('should successfully calculate deadlines', async () => {
      const mockResponse = {
        deadlines: [
          {
            id: '1',
            name: 'Statute of Limitations',
            description: 'File lawsuit within 2 years',
            dueDate: '2025-01-15T00:00:00Z',
            priority: 'high' as const,
            category: 'filing',
            isStatutory: true,
          },
        ],
        jurisdiction: 'TX',
        triggerCode: 'ACCIDENT',
        startDate: '2023-01-15T00:00:00Z',
        practiceArea: 'personal-injury',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await mcpClient.calculateDeadlines(
        'TX',
        'ACCIDENT',
        '2023-01-15T00:00:00Z',
        'personal-injury'
      );

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/api/v1/deadlines/calculate',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'x-api-key': 'test-api-key',
          }),
          body: JSON.stringify({
            jurisdiction: 'TX',
            triggerCode: 'ACCIDENT',
            startDate: '2023-01-15T00:00:00Z',
            practiceArea: 'personal-injury',
          }),
        })
      );
    });

    it('should handle 4xx client errors without retry', async () => {
      const errorResponse = {
        message: 'Invalid jurisdiction code',
        code: 'INVALID_JURISDICTION',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue(errorResponse),
      });

      await expect(
        mcpClient.calculateDeadlines('INVALID', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow(McpApiError);

      // Should not retry on 4xx errors
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should retry on 5xx server errors with exponential backoff', async () => {
      // First two calls fail with 500
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          json: jest.fn().mockResolvedValue({ message: 'Server error' }),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          json: jest.fn().mockResolvedValue({ message: 'Server error' }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({
            deadlines: [],
            jurisdiction: 'TX',
            triggerCode: 'ACCIDENT',
            startDate: '2023-01-15T00:00:00Z',
          }),
        });

      const result = await mcpClient.calculateDeadlines(
        'TX',
        'ACCIDENT',
        '2023-01-15T00:00:00Z'
      );

      expect(result).toBeDefined();
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should retry on 429 rate limiting', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 429,
          statusText: 'Too Many Requests',
          json: jest.fn().mockResolvedValue({ message: 'Rate limited' }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({
            deadlines: [],
            jurisdiction: 'TX',
            triggerCode: 'ACCIDENT',
            startDate: '2023-01-15T00:00:00Z',
          }),
        });

      const result = await mcpClient.calculateDeadlines(
        'TX',
        'ACCIDENT',
        '2023-01-15T00:00:00Z'
      );

      expect(result).toBeDefined();
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      // All calls fail with 500
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' }),
      });

      await expect(
        mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow(McpApiError);

      // Should retry 3 times + initial call = 4 total
      expect(mockFetch).toHaveBeenCalledTimes(4);
    });

    it('should handle network errors with retry', async () => {
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({
            deadlines: [],
            jurisdiction: 'TX',
            triggerCode: 'ACCIDENT',
            startDate: '2023-01-15T00:00:00Z',
          }),
        });

      const result = await mcpClient.calculateDeadlines(
        'TX',
        'ACCIDENT',
        '2023-01-15T00:00:00Z'
      );

      expect(result).toBeDefined();
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('healthCheck', () => {
    it('should successfully perform health check', async () => {
      const mockResponse = {
        status: 'healthy' as const,
        timestamp: '2023-01-15T12:00:00Z',
        version: '1.0.0',
        uptime: 3600,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      const result = await mcpClient.healthCheck();

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/health',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'x-api-key': 'test-api-key',
          }),
        })
      );
    });

    it('should handle unhealthy status', async () => {
      const mockResponse = {
        status: 'unhealthy' as const,
        timestamp: '2023-01-15T12:00:00Z',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable',
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      await expect(mcpClient.healthCheck()).rejects.toThrow(McpApiError);
    });
  });

  describe('Error handling', () => {
    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockRejectedValue(new Error('Invalid JSON')),
      });

      await expect(
        mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow(McpApiError);
    });

    it('should create proper McpApiError instances', async () => {
      const errorResponse = {
        message: 'Custom error message',
        code: 'CUSTOM_ERROR',
        details: { field: 'jurisdiction' },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        statusText: 'Unprocessable Entity',
        json: jest.fn().mockResolvedValue(errorResponse),
      });

      try {
        await mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        fail('Expected McpApiError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(McpApiError);
        expect((error as McpApiError).status).toBe(422);
        expect((error as McpApiError).code).toBe('CUSTOM_ERROR');
        expect((error as McpApiError).details).toEqual({ field: 'jurisdiction' });
        expect((error as McpApiError).message).toBe('Custom error message');
      }
    });
  });

  describe('Circuit Breaker', () => {
    it('should open circuit breaker after 3 consecutive failures', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' }),
      });

      // Make 3 failed requests to trigger circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should now be open
      const state = mcpClient.getCircuitBreakerState();
      expect(state.state).toBe('open');
      expect(state.consecutiveFailures).toBe(3);

      // Next request should fail immediately with circuit breaker error
      await expect(
        mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow('Circuit breaker is OPEN');
    });

    it('should reset circuit breaker on successful request', async () => {
      // First, trigger circuit breaker with failures
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' }),
      });

      // Make 2 failed requests
      for (let i = 0; i < 2; i++) {
        try {
          await mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      // Now make a successful request
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          deadlines: [],
          jurisdiction: 'TX',
          triggerCode: 'ACCIDENT',
          startDate: '2023-01-15T00:00:00Z',
        }),
      });

      await mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');

      // Circuit breaker should be reset
      const state = mcpClient.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
    });
  });

  describe('Configuration', () => {
    it('should handle custom timeout and retry settings', async () => {
      const customClient = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key',
        timeout: 1000,
        maxRetries: 1,
        retryDelay: 50,
      });

      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' }),
      });

      await expect(
        customClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow(McpApiError);

      // Should only retry once (1 retry + initial call = 2 total)
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should strip trailing slash from base URL', async () => {
      const clientWithTrailingSlash = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com/',
        apiKey: 'test-api-key',
      });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({ status: 'healthy', timestamp: '2023-01-15T12:00:00Z' }),
      });

      await clientWithTrailingSlash.healthCheck();

      expect(mockFetch).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/health',
        expect.any(Object)
      );
    });
  });
});
