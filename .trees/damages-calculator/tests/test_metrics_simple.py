"""
Simple tests for Prometheus metrics functionality.

This module contains basic tests to verify that the metrics components
are properly structured and can be imported.
"""

import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_metrics_module_imports():
    """Test that the metrics module can be imported and has expected components."""
    try:
        from backend.metrics import (
            REGISTRY,
            calendar_api_requests_total,
            calendar_api_response_time,
            get_metrics_output,
        )

        # Verify the metrics have the expected names (prometheus_client may truncate)
        assert "calendar_api_requests" in calendar_api_requests_total._name
        assert calendar_api_response_time._name == "calendar_api_response_time"

        # Verify the metrics have the expected labels
        assert "provider" in calendar_api_requests_total._labelnames
        assert "endpoint" in calendar_api_requests_total._labelnames
        assert "status" in calendar_api_requests_total._labelnames

        assert "provider" in calendar_api_response_time._labelnames

        # Verify the histogram has the expected buckets
        expected_buckets = [0.1, 0.3, 0.5, 1.0, 2.0, 5.0, float('inf')]
        actual_buckets = getattr(calendar_api_response_time, '_upper_bounds', None)
        if actual_buckets:
            assert list(actual_buckets) == expected_buckets

        print("✓ Metrics module imports and structure test passed")
        return True

    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_middleware_module_imports():
    """Test that the middleware module can be imported."""
    try:
        from backend.middleware.metrics_middleware import CalendarMetricsMiddleware

        # Verify it's a class
        assert isinstance(CalendarMetricsMiddleware, type)

        print("✓ Middleware module imports test passed")
        return True

    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_metrics_output_format():
    """Test that metrics output can be generated."""
    try:
        from backend.metrics import calendar_api_requests_total, get_metrics_output

        # Increment a metric to ensure there's some data
        calendar_api_requests_total.labels(
            provider="test",
            endpoint="test",
            status="success"
        ).inc()

        # Generate metrics output
        output = get_metrics_output()

        # Verify it's bytes
        assert isinstance(output, bytes)

        # Verify it contains our metric
        output_str = output.decode('utf-8')
        assert "calendar_api_requests_total" in output_str

        print("✓ Metrics output format test passed")
        return True

    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def main():
    """Run all tests."""
    print("Running simple metrics tests...\n")

    tests = [
        test_metrics_module_imports,
        test_middleware_module_imports,
        test_metrics_output_format
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print(f"Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
