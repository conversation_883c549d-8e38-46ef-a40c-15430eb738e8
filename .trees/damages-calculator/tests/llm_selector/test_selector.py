"""
Tests for the LLM selector functionality.

These tests verify that the LLM selector correctly resolves the appropriate
LLM client based on tenant, agent, and node configurations.
"""

import os
from unittest.mock import MagicMock, patch

import pytest

from shared.core.llm.factory import create_llm
from shared.core.llm.selector import (
    _fetch_selection,
    parse_model_string,
    resolve_llm,
)


@pytest.fixture
def mock_supabase():
    """Mock Supabase client for testing."""
    mock_client = MagicMock()
    mock_table = MagicMock()
    mock_select = MagicMock()
    mock_eq = MagicMock()
    mock_is_ = MagicMock()
    mock_execute = MagicMock()

    # Set up the mock chain
    mock_client.table.return_value = mock_table
    mock_table.select.return_value = mock_select
    mock_select.eq.return_value = mock_select
    mock_select.is_.return_value = mock_eq
    mock_eq.execute.return_value = mock_execute

    return mock_client


@pytest.mark.parametrize(
    "model_string,expected_provider,expected_model",
    [
        ("openai/gpt-4", "openai", "gpt-4"),
        ("anthropic/claude-3-opus", "anthropic", "claude-3-opus"),
        ("groq/voyage-large-turbo", "groq", "voyage-large-turbo"),
    ],
)
def test_parse_model_string(model_string, expected_provider, expected_model):
    """Test parsing model strings."""
    provider, model = parse_model_string(model_string)
    assert provider == expected_provider
    assert model == expected_model


def test_parse_model_string_invalid():
    """Test parsing invalid model strings."""
    with pytest.raises(ValueError):
        parse_model_string("invalid-model-string")


@pytest.mark.parametrize(
    "tenant,agent,node,expected_result",
    [
        ("tenant1", "agent1", "node1", {"model_name": "openai/gpt-4", "temperature": 0.2}),
        ("tenant1", "agent1", None, {"model_name": "anthropic/claude-3", "temperature": 0.1}),
        ("*", "agent1", "node1", {"model_name": "groq/voyage-large", "temperature": 0.3}),
        ("*", "agent1", None, {"model_name": "openai/gpt-3.5-turbo", "temperature": 0.0}),
    ],
)
@patch("shared.core.llm.selector.get_supabase_client")
def test_fetch_selection(mock_get_supabase, tenant, agent, node, expected_result):
    """Test fetching LLM selection from the database."""
    # Set up the mock
    mock_client = MagicMock()
    mock_get_supabase.return_value = mock_client

    # Set up the mock response
    mock_result = MagicMock()
    mock_result.data = [expected_result]

    # Set up the mock chain
    mock_select = MagicMock()
    mock_select.execute.return_value = mock_result

    if node is None:
        mock_is = MagicMock()
        mock_is.is_.return_value = mock_select
        mock_eq2 = MagicMock()
        mock_eq2.eq.return_value = mock_is
        mock_eq1 = MagicMock()
        mock_eq1.eq.return_value = mock_eq2
        mock_client.table.return_value = MagicMock()
        mock_client.table.return_value.select.return_value = mock_eq1
    else:
        mock_eq3 = MagicMock()
        mock_eq3.eq.return_value = mock_select
        mock_eq2 = MagicMock()
        mock_eq2.eq.return_value = mock_eq3
        mock_eq1 = MagicMock()
        mock_eq1.eq.return_value = mock_eq2
        mock_client.table.return_value = MagicMock()
        mock_client.table.return_value.select.return_value = mock_eq1

    # Clear the cache
    _fetch_selection.cache_clear()

    # Call the function
    result = _fetch_selection(tenant, agent, node)

    # Verify the result
    assert result == expected_result

    # Verify the mock was called correctly
    mock_client.table.assert_called_once_with("admin_llm_selection")
    mock_client.table.return_value.select.assert_called_once_with("model_name,temperature")
    mock_eq1.eq.assert_called_once_with("tenant_id", tenant)
    mock_eq2.eq.assert_called_once_with("agent", agent)

    if node is None:
        mock_is.is_.assert_called_once_with("node", "null")
    else:
        mock_eq3.eq.assert_called_once_with("node", node)


@pytest.mark.parametrize(
    "state,agent,node,expected_model",
    [
        ({"tenant_id": "tenant1"}, "agent1", "node1", "openai/gpt-4"),
        ({"tenant_id": "tenant1"}, "agent1", None, "anthropic/claude-3"),
        ({"tenant_id": "tenant2"}, "agent1", "node1", "groq/voyage-large"),
        ({"tenant_id": "tenant2"}, "agent1", None, "openai/gpt-3.5-turbo"),
        ({}, "agent1", "node1", "groq/voyage-large"),
        ({}, "agent1", None, "openai/gpt-3.5-turbo"),
    ],
)
@patch("shared.core.llm.selector._fetch_selection")
@patch("shared.core.llm.selector.create_llm")
def test_resolve_llm_precedence(mock_create_llm, mock_fetch_selection, state, agent, node, expected_model):
    """Test LLM resolution precedence."""
    # Set up the mock responses
    def fetch_selection_side_effect(tenant, agent_name, node_name):
        if tenant == "tenant1" and agent_name == "agent1" and node_name == "node1":
            return {"model_name": "openai/gpt-4", "temperature": 0.2}
        elif tenant == "tenant1" and agent_name == "agent1" and node_name is None:
            return {"model_name": "anthropic/claude-3", "temperature": 0.1}
        elif tenant == "*" and agent_name == "agent1" and node_name == "node1":
            return {"model_name": "groq/voyage-large", "temperature": 0.3}
        elif tenant == "*" and agent_name == "agent1" and node_name is None:
            return {"model_name": "openai/gpt-3.5-turbo", "temperature": 0.0}
        return None

    mock_fetch_selection.side_effect = fetch_selection_side_effect

    # Call the function
    resolve_llm(state, agent, node)

    # Verify create_llm was called with the expected model
    mock_create_llm.assert_called_once()
    args, kwargs = mock_create_llm.call_args
    assert args[0] == expected_model


@pytest.mark.llm
def test_factory_roundtrip():
    """Test that create_llm returns the correct client type."""
    # Skip if no API keys are available
    if not os.getenv("OPENAI_API_KEY"):
        pytest.skip("OpenAI API key not available")

    # Create an OpenAI client
    client = create_llm("openai/gpt-3.5-turbo")

    # Verify the client type
    assert client.__class__.__name__ == "OpenAIClient"

    # Skip if no Anthropic API key
    if not os.getenv("ANTHROPIC_API_KEY"):
        pytest.skip("Anthropic API key not available")

    # Create an Anthropic client
    client = create_llm("anthropic/claude-3-haiku")

    # Verify the client type
    assert client.__class__.__name__ == "AnthropicClient"


def test_node_override():
    """Test that node-specific model selection overrides agent-level selection."""
    # Set up the mock responses
    with patch("shared.core.llm.selector._fetch_selection") as mock_fetch_selection:
        def fetch_selection_side_effect(tenant, agent_name, node_name):
            if tenant == "*" and agent_name == "researchAgent" and node_name == "rerank":
                return {"model_name": "groq/voyage-rerank-8k", "temperature": 0.0}
            elif tenant == "*" and agent_name == "researchAgent" and node_name is None:
                return {"model_name": "openai/gpt-4o", "temperature": 0.2}
            return None

        mock_fetch_selection.side_effect = fetch_selection_side_effect

        # Set up the mock create_llm function
        with patch("shared.core.llm.selector.create_llm") as mock_create_llm:
            mock_llm = MagicMock()
            mock_create_llm.return_value = mock_llm

            # Create a state with debug mode
            state = {"debug": True}

            # Resolve LLM for the rerank node
            llm = resolve_llm(state, "researchAgent", "rerank")

            # Verify the state contains the correct model
            assert state.get("llm_used") == "groq/voyage-rerank-8k"

            # Verify create_llm was called with the correct model
            mock_create_llm.assert_called_with("groq/voyage-rerank-8k", temperature=0.0)

            # Reset the mock
            mock_create_llm.reset_mock()

            # Resolve LLM for the agent
            llm = resolve_llm(state, "researchAgent", None)

            # Verify the state contains the correct model
            assert state.get("llm_used") == "openai/gpt-4o"

            # Verify create_llm was called with the correct model
            mock_create_llm.assert_called_with("openai/gpt-4o", temperature=0.2)


def test_real_llm_selection():
    """Test that the LLM selection works with real LLM clients."""
    # Set up the mock responses
    with patch("shared.core.llm.selector._fetch_selection") as mock_fetch_selection:
        def fetch_selection_side_effect(tenant, agent_name, node_name):
            if tenant == "*" and agent_name == "calendarCrudAgent" and node_name is None:
                return {"model_name": "gemini/gemini-2.0-flash", "temperature": 0.2}
            return None

        mock_fetch_selection.side_effect = fetch_selection_side_effect

        # Set up the mock create_llm function
        with patch("shared.core.llm.selector.create_llm") as mock_create_llm:
            mock_llm = MagicMock()
            mock_llm.__class__.__name__ = "GeminiClient"
            mock_create_llm.return_value = mock_llm

            # Create a state with debug mode
            state = {"tenant_id": "test_tenant", "debug": True}

            # Resolve LLM for the calendar agent
            llm = resolve_llm(state, "calendarCrudAgent", None)

            # Verify the state contains the correct model
            assert state.get("llm_used") == "gemini/gemini-2.0-flash"

            # Verify create_llm was called with the correct model
            mock_create_llm.assert_called_with("gemini/gemini-2.0-flash", temperature=0.2)

            # Verify the LLM client is of the correct type
            assert llm.__class__.__name__ == "GeminiClient"
