"""
Tests for auth client retry functionality - failure scenarios.

This module tests specific failure scenarios for auth-service retry logic,
including successful recovery after single 429 errors and metrics verification.
"""

import time
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest

from shared.auth_client import (
    _token_cache,
    get_access_token_with_retry,
)


@pytest.fixture(autouse=True)
def clear_token_cache():
    """Clear token cache before each test."""
    _token_cache.clear()
    yield
    _token_cache.clear()


@pytest.fixture
def mock_auth_service_base():
    """Mock AUTH_SERVICE_BASE environment variable."""
    with patch('backend.config.settings.auth_service_base', 'http://auth.local'):
        yield


@pytest.fixture
def mock_metrics_collector():
    """Mock metrics collector."""
    mock_collector = MagicMock()
    with patch('shared.auth_client._get_metrics_collector', return_value=mock_collector):
        yield mock_collector


@pytest.mark.timeout(5)
class TestAuthRetryFailureScenarios:
    """Test specific auth retry failure and recovery scenarios."""

    @pytest.mark.asyncio
    async def test_single_429_retry_success(self, mock_auth_service_base, mock_metrics_collector):
        """Test that auth succeeds after a single 429 error with proper metrics."""
        mock_response = {
            "access_token": "success_after_retry_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        retry_count = 0
        
        # Mock get_with_retry to simulate single 429 then success
        async def mock_get_with_retry_single_429(url, retry_callback=None, **kwargs):
            nonlocal retry_count
            # Simulate exactly one retry (single 429 followed by success)
            if retry_callback:
                retry_callback()  # Single retry
                retry_count += 1
            return mock_response
        
        start_time = time.time()
        with patch('shared.http.SharedHTTPClient.get_with_retry', side_effect=mock_get_with_retry_single_429):
            token = await get_access_token_with_retry("firm123", "google")
            
        end_time = time.time()
        elapsed = end_time - start_time
        
        assert token == "success_after_retry_token"
        # Should complete quickly since we're mocking the retry logic
        assert elapsed < 1.0
        
        # Verify exactly one retry metric was incremented
        assert retry_count == 1
        assert mock_metrics_collector.increment_counter.call_count == 1
        mock_metrics_collector.increment_counter.assert_called_with(
            "auth_retry_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_single_500_retry_success(self, mock_auth_service_base, mock_metrics_collector):
        """Test that auth succeeds after a single 500 error with proper metrics."""
        mock_response = {
            "access_token": "success_after_server_error_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        retry_count = 0
        
        # Mock get_with_retry to simulate single 500 then success
        async def mock_get_with_retry_single_500(url, retry_callback=None, **kwargs):
            nonlocal retry_count
            # Simulate exactly one retry (single 500 followed by success)
            if retry_callback:
                retry_callback()  # Single retry
                retry_count += 1
            return mock_response
        
        with patch('shared.http.SharedHTTPClient.get_with_retry', side_effect=mock_get_with_retry_single_500):
            token = await get_access_token_with_retry("firm123", "google")
            
        assert token == "success_after_server_error_token"
        
        # Verify exactly one retry metric was incremented
        assert retry_count == 1
        assert mock_metrics_collector.increment_counter.call_count == 1
        mock_metrics_collector.increment_counter.assert_called_with(
            "auth_retry_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_network_error_retry_success(self, mock_auth_service_base, mock_metrics_collector):
        """Test that auth succeeds after a network error with proper metrics."""
        mock_response = {
            "access_token": "success_after_network_error_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        retry_count = 0
        
        # Mock get_with_retry to simulate single network error then success
        async def mock_get_with_retry_network_error(url, retry_callback=None, **kwargs):
            nonlocal retry_count
            # Simulate exactly one retry (single network error followed by success)
            if retry_callback:
                retry_callback()  # Single retry
                retry_count += 1
            return mock_response
        
        with patch('shared.http.SharedHTTPClient.get_with_retry', side_effect=mock_get_with_retry_network_error):
            token = await get_access_token_with_retry("firm123", "google")
            
        assert token == "success_after_network_error_token"
        
        # Verify exactly one retry metric was incremented
        assert retry_count == 1
        assert mock_metrics_collector.increment_counter.call_count == 1
        mock_metrics_collector.increment_counter.assert_called_with(
            "auth_retry_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_multiple_providers_separate_retry_metrics(self, mock_auth_service_base, mock_metrics_collector):
        """Test that retry metrics are tracked separately for different providers."""
        mock_response_google = {
            "access_token": "google_retry_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        mock_response_calendly = {
            "access_token": "calendly_retry_token", 
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        google_retry_count = 0
        calendly_retry_count = 0
        
        # Mock get_with_retry to simulate retries for both providers
        async def mock_get_with_retry_multi_provider(url, retry_callback=None, **kwargs):
            nonlocal google_retry_count, calendly_retry_count
            if 'google' in url:
                if retry_callback:
                    retry_callback()  # Google retry
                    google_retry_count += 1
                return mock_response_google
            elif 'calendly' in url:
                if retry_callback:
                    retry_callback()  # Calendly retry
                    calendly_retry_count += 1
                return mock_response_calendly
            else:
                raise ValueError(f"Unexpected URL: {url}")
        
        with patch('shared.http.SharedHTTPClient.get_with_retry', side_effect=mock_get_with_retry_multi_provider):
            google_token = await get_access_token_with_retry("firm123", "google")
            calendly_token = await get_access_token_with_retry("firm123", "calendly")
            
        assert google_token == "google_retry_token"
        assert calendly_token == "calendly_retry_token"
        
        # Verify retry metrics were incremented for both providers
        assert google_retry_count == 1
        assert calendly_retry_count == 1
        assert mock_metrics_collector.increment_counter.call_count == 2
        
        # Check that both providers had their metrics incremented
        expected_calls = [
            (("auth_retry_total",), {"tags": {"provider": "google"}}),
            (("auth_retry_total",), {"tags": {"provider": "calendly"}})
        ]
        actual_calls = mock_metrics_collector.increment_counter.call_args_list
        assert len(actual_calls) == 2
        for call in actual_calls:
            assert call in expected_calls

    @pytest.mark.asyncio
    async def test_no_retries_needed_no_metrics(self, mock_auth_service_base, mock_metrics_collector):
        """Test that no retry metrics are incremented when no retries are needed."""
        mock_response = {
            "access_token": "immediate_success_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        # Mock get_with_retry to succeed immediately (no retries)
        async def mock_get_with_retry_immediate_success(url, retry_callback=None, **kwargs):
            # No retry callback invocation - immediate success
            return mock_response
        
        with patch('shared.http.SharedHTTPClient.get_with_retry', side_effect=mock_get_with_retry_immediate_success):
            token = await get_access_token_with_retry("firm123", "google")
            
        assert token == "immediate_success_token"
        
        # Verify no retry metrics were incremented
        mock_metrics_collector.increment_counter.assert_not_called()

    @pytest.mark.asyncio
    async def test_cached_token_no_auth_service_call(self, mock_auth_service_base, mock_metrics_collector):
        """Test that cached tokens don't trigger auth-service calls or metrics."""
        # Pre-populate cache with valid token
        cache_key = "firm123:google"
        future_time = datetime.now() + timedelta(seconds=300)
        _token_cache[cache_key] = {
            "access_token": "cached_token_no_call",
            "expires_at": future_time,
            "cached_at": datetime.now()
        }
        
        with patch('shared.http.SharedHTTPClient.get_with_retry') as mock_get:
            token = await get_access_token_with_retry("firm123", "google")
            
        assert token == "cached_token_no_call"
        
        # Verify no auth-service call was made
        mock_get.assert_not_called()
        
        # Verify no metrics were incremented
        mock_metrics_collector.increment_counter.assert_not_called()
