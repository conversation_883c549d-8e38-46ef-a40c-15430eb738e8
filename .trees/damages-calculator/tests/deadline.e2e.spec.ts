/**
 * E2E Tests for Deadline Agent Integration
 * 
 * This test suite verifies the complete deadline calculation workflow
 * from user input through MCP Rules Engine to UI display.
 */

import { test, expect, Page } from '@playwright/test';
import { jest } from '@jest/globals';

// Mock MCP client for testing
const mockMcpClient = {
  calculateDeadlines: jest.fn(),
};

// Mock deadline response
const mockDeadlineResponse = {
  deadlines: [
    {
      id: 'deadline_1',
      name: 'Statute of Limitations',
      dueDate: '2027-01-15',
      priority: 'high' as const,
      category: 'statutory',
      description: 'File lawsuit within 2 years of injury',
      legalBasis: 'Texas Civil Practice & Remedies Code § 16.003',
      consequences: 'Case will be time-barred if not filed by this date',
    },
    {
      id: 'deadline_2', 
      name: 'Discovery Deadline',
      dueDate: '2026-06-15',
      priority: 'medium' as const,
      category: 'procedural',
      description: 'Complete all discovery requests',
      legalBasis: 'Texas Rules of Civil Procedure Rule 190',
      consequences: 'May not be able to obtain evidence after this date',
    },
  ],
  jurisdiction: 'TX_STATE',
  triggerCode: 'SERVICE_OF_PROCESS',
  startDate: '2025-01-15',
  practiceArea: 'personal_injury',
  calculatedAt: '2025-06-17T10:00:00Z',
  source: 'mcp_rules_engine',
};

test.describe('Deadline Agent E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the MCP client
    await page.addInitScript(() => {
      // @ts-ignore
      window.mockMcpClient = {
        calculateDeadlines: () => Promise.resolve({
          deadlines: [
            {
              id: 'deadline_1',
              name: 'Statute of Limitations',
              dueDate: '2027-01-15',
              priority: 'high',
              category: 'statutory',
              description: 'File lawsuit within 2 years of injury',
              legalBasis: 'Texas Civil Practice & Remedies Code § 16.003',
            },
            {
              id: 'deadline_2',
              name: 'Discovery Deadline', 
              dueDate: '2026-06-15',
              priority: 'medium',
              category: 'procedural',
              description: 'Complete all discovery requests',
              legalBasis: 'Texas Rules of Civil Procedure Rule 190',
            },
          ],
        }),
      };
    });

    // Navigate to the page with DeadlinesPanel
    await page.goto('/dashboard'); // Adjust URL as needed
  });

  test('should display deadline calculation request interface', async ({ page }) => {
    // Check that the DeadlinesPanel is rendered
    await expect(page.locator('[data-testid="deadlines-panel"]')).toBeVisible();
    
    // Check for the title
    await expect(page.locator('text=Legal Deadlines')).toBeVisible();
    
    // Check for the description
    await expect(page.locator('text=Statutory deadlines and filing requirements')).toBeVisible();
    
    // Check for the empty state message
    await expect(page.locator('text=Ask me to calculate deadlines for your case')).toBeVisible();
  });

  test('should trigger deadline calculation from chat input', async ({ page }) => {
    // Find the chat input (adjust selector based on your CopilotKit setup)
    const chatInput = page.locator('[data-testid="copilot-chat-input"]');
    await expect(chatInput).toBeVisible();

    // Type a deadline request
    await chatInput.fill('Calculate deadlines for Texas service of process on 2025-01-15');
    
    // Submit the request
    await chatInput.press('Enter');
    
    // Wait for loading state
    await expect(page.locator('[data-testid="deadlines-loading"]')).toBeVisible();
    
    // Wait for results to appear
    await expect(page.locator('[data-testid="deadlines-table"]')).toBeVisible({ timeout: 10000 });
  });

  test('should display deadline results in sortable table', async ({ page }) => {
    // Simulate receiving deadline results
    await page.evaluate((mockResponse) => {
      // Simulate the message event that would come from CopilotKit
      window.dispatchEvent(new MessageEvent('message', {
        data: {
          role: 'deadline_results',
          deadlines: mockResponse.deadlines,
          metadata: {
            jurisdiction: mockResponse.jurisdiction,
            triggerCode: mockResponse.triggerCode,
            startDate: mockResponse.startDate,
            practiceArea: mockResponse.practiceArea,
            calculatedAt: mockResponse.calculatedAt,
            source: mockResponse.source,
          },
        },
      }));
    }, mockDeadlineResponse);

    // Wait for the table to appear
    await expect(page.locator('[data-testid="deadlines-table"]')).toBeVisible();
    
    // Check that deadlines are displayed
    await expect(page.locator('text=Statute of Limitations')).toBeVisible();
    await expect(page.locator('text=Discovery Deadline')).toBeVisible();
    
    // Check metadata display
    await expect(page.locator('text=TX_STATE')).toBeVisible();
    await expect(page.locator('text=SERVICE_OF_PROCESS')).toBeVisible();
    
    // Check priority badges
    await expect(page.locator('[data-testid="priority-badge-high"]')).toBeVisible();
    await expect(page.locator('[data-testid="priority-badge-medium"]')).toBeVisible();
    
    // Check legal basis
    await expect(page.locator('text=Texas Civil Practice & Remedies Code § 16.003')).toBeVisible();
  });

  test('should sort deadlines by different columns', async ({ page }) => {
    // First, populate the table with data
    await page.evaluate((mockResponse) => {
      window.dispatchEvent(new MessageEvent('message', {
        data: {
          role: 'deadline_results',
          deadlines: mockResponse.deadlines,
          metadata: mockResponse,
        },
      }));
    }, mockDeadlineResponse);

    await expect(page.locator('[data-testid="deadlines-table"]')).toBeVisible();
    
    // Test sorting by name
    await page.locator('[data-testid="sort-name"]').click();
    
    // Verify sort order changed (Discovery should come before Statute alphabetically)
    const firstRow = page.locator('[data-testid="deadline-row"]').first();
    await expect(firstRow.locator('text=Discovery Deadline')).toBeVisible();
    
    // Test sorting by priority
    await page.locator('[data-testid="sort-priority"]').click();
    
    // High priority should come first
    const firstRowAfterPrioritySort = page.locator('[data-testid="deadline-row"]').first();
    await expect(firstRowAfterPrioritySort.locator('text=Statute of Limitations')).toBeVisible();
    
    // Test sorting by due date
    await page.locator('[data-testid="sort-dueDate"]').click();
    
    // Earlier date should come first
    const firstRowAfterDateSort = page.locator('[data-testid="deadline-row"]').first();
    await expect(firstRowAfterDateSort.locator('text=Discovery Deadline')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Simulate an error response
    await page.evaluate(() => {
      window.dispatchEvent(new MessageEvent('message', {
        data: {
          role: 'deadline_error',
          error: 'Failed to calculate deadlines',
        },
      }));
    });

    // Check that error is displayed
    await expect(page.locator('[data-testid="error-alert"]')).toBeVisible();
    await expect(page.locator('text=Failed to calculate deadlines')).toBeVisible();
  });

  test('should show loading state during calculation', async ({ page }) => {
    // Trigger loading state
    await page.evaluate(() => {
      // Simulate the loading state
      const event = new CustomEvent('deadlines-loading', { detail: true });
      window.dispatchEvent(event);
    });

    // Check for skeleton loading elements
    await expect(page.locator('[data-testid="deadlines-skeleton"]')).toBeVisible();
  });

  test('should format dates correctly', async ({ page }) => {
    await page.evaluate((mockResponse) => {
      window.dispatchEvent(new MessageEvent('message', {
        data: {
          role: 'deadline_results',
          deadlines: mockResponse.deadlines,
          metadata: mockResponse,
        },
      }));
    }, mockDeadlineResponse);

    await expect(page.locator('[data-testid="deadlines-table"]')).toBeVisible();
    
    // Check that dates are formatted properly (e.g., "Jan 15, 2027")
    await expect(page.locator('text=Jan 15, 2027')).toBeVisible();
    await expect(page.locator('text=Jun 15, 2026')).toBeVisible();
  });

  test('should display priority icons correctly', async ({ page }) => {
    await page.evaluate((mockResponse) => {
      window.dispatchEvent(new MessageEvent('message', {
        data: {
          role: 'deadline_results',
          deadlines: mockResponse.deadlines,
          metadata: mockResponse,
        },
      }));
    }, mockDeadlineResponse);

    await expect(page.locator('[data-testid="deadlines-table"]')).toBeVisible();
    
    // Check for priority icons (emojis)
    await expect(page.locator('text=🔴')).toBeVisible(); // High priority
    await expect(page.locator('text=🟡')).toBeVisible(); // Medium priority
  });

  test('should handle empty deadline results', async ({ page }) => {
    // Simulate empty results
    await page.evaluate(() => {
      window.dispatchEvent(new MessageEvent('message', {
        data: {
          role: 'deadline_results',
          deadlines: [],
          metadata: {
            jurisdiction: 'TX_STATE',
            triggerCode: 'SERVICE_OF_PROCESS',
            startDate: '2025-01-15',
            practiceArea: 'personal_injury',
          },
        },
      }));
    });

    // Check for empty state message
    await expect(page.locator('text=No deadlines found for the specified criteria')).toBeVisible();
  });
});

// Unit tests for deadline agent functions
test.describe('Deadline Agent Unit Tests', () => {
  test('should extract deadline parameters from user message', async () => {
    // This would test the extractDeadlineParameters function
    // Implementation depends on how you expose the function for testing
  });

  test('should validate deadline parameters correctly', async () => {
    // This would test the validateDeadlineParameters function
  });

  test('should determine when to trigger deadline agent', async () => {
    // This would test the shouldTriggerDeadlineAgent function
  });
});
