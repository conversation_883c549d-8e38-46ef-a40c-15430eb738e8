"""
Test Automatic Mapping System

Tests the automatic mapping between Supabase subscription plans and Stripe products
across multiple countries and currencies.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from uuid import uuid4

from backend.services.stripe_mapping import stripe_mapper, PlanMapping
from backend.services.enhanced_webhook_handler import enhanced_webhook_handler

class TestAutomaticMapping:
    """Test suite for automatic mapping functionality."""
    
    @pytest.fixture
    def sample_plan_mapping(self):
        """Sample plan mapping for testing."""
        return PlanMapping(
            internal_id=uuid4(),
            internal_type="plan",
            plan_code="team",
            country_code="US",
            currency="USD",
            billing_cycle="monthly",
            stripe_price_id="price_test_123",
            tax_behavior="exclusive"
        )
    
    @pytest.fixture
    def sample_checkout_session(self):
        """Sample Stripe checkout session for testing."""
        return {
            "id": "cs_test_123",
            "customer": "cus_test_123",
            "subscription": "sub_test_123",
            "line_items": {
                "data": [{
                    "price": {
                        "id": "price_test_123",
                        "currency": "usd",
                        "unit_amount": 19900
                    },
                    "quantity": 1
                }]
            },
            "customer_details": {
                "email": "<EMAIL>",
                "name": "Test User",
                "address": {
                    "country": "US",
                    "state": "CA",
                    "city": "San Francisco"
                }
            },
            "metadata": {}
        }
    
    @pytest.fixture
    def sample_subscription(self):
        """Sample Stripe subscription for testing."""
        return {
            "id": "sub_test_123",
            "customer": "cus_test_123",
            "status": "active",
            "items": {
                "data": [{
                    "id": "si_test_123",
                    "price": {
                        "id": "price_test_123",
                        "currency": "usd"
                    },
                    "quantity": 1
                }]
            }
        }

class TestStripeProductMapper:
    """Test the core mapping service."""
    
    @pytest.mark.asyncio
    async def test_resolve_stripe_price_success(self):
        """Test successful price resolution."""
        
        # Mock database response
        with patch('backend.services.stripe_mapping.get_async_session') as mock_session:
            mock_db = Mock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock successful query result
            mock_result = Mock()
            mock_row = Mock()
            mock_row.internal_id = uuid4()
            mock_row.internal_type = "plan"
            mock_row.plan_code = "team"
            mock_row.country_code = "US"
            mock_row.currency = "USD"
            mock_row.billing_cycle = "monthly"
            mock_row.stripe_price_id = "price_test_123"
            mock_row.tax_behavior = "exclusive"
            
            mock_result.fetchone.return_value = mock_row
            mock_db.execute.return_value = mock_result
            
            # Test the mapping
            mapping = await stripe_mapper.resolve_stripe_price("price_test_123")
            
            assert mapping is not None
            assert mapping.plan_code == "team"
            assert mapping.country_code == "US"
            assert mapping.currency == "USD"
            assert mapping.billing_cycle == "monthly"
    
    @pytest.mark.asyncio
    async def test_resolve_stripe_price_not_found(self):
        """Test price resolution when no mapping exists."""
        
        with patch('backend.services.stripe_mapping.get_async_session') as mock_session:
            mock_db = Mock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock empty query result
            mock_result = Mock()
            mock_result.fetchone.return_value = None
            mock_db.execute.return_value = mock_result
            
            # Test the mapping
            mapping = await stripe_mapper.resolve_stripe_price("price_nonexistent")
            
            assert mapping is None
    
    @pytest.mark.asyncio
    async def test_get_stripe_price_for_plan(self):
        """Test getting Stripe price ID for plan/country/cycle."""
        
        with patch('backend.services.stripe_mapping.get_async_session') as mock_session:
            mock_db = Mock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock successful query result
            mock_result = Mock()
            mock_result.fetchone.return_value = ["price_test_123"]
            mock_db.execute.return_value = mock_result
            
            # Test the lookup
            price_id = await stripe_mapper.get_stripe_price_for_plan("team", "US", "monthly")
            
            assert price_id == "price_test_123"

class TestEnhancedWebhookHandler:
    """Test the enhanced webhook handler."""
    
    @pytest.mark.asyncio
    async def test_handle_checkout_session_completed(self, sample_checkout_session, sample_plan_mapping):
        """Test checkout session handling with automatic mapping."""
        
        # Mock the mapping service
        with patch.object(enhanced_webhook_handler.mapper, 'resolve_stripe_price') as mock_resolve:
            mock_resolve.return_value = sample_plan_mapping
            
            # Mock database operations
            with patch.object(enhanced_webhook_handler, '_process_subscription_with_mapping') as mock_process:
                mock_process.return_value = {"status": "success", "firm_id": str(uuid4())}
                
                # Create test event
                event = {"data": {"object": sample_checkout_session}}
                
                # Test the handler
                result = await enhanced_webhook_handler.handle_checkout_session_completed(event)
                
                assert result["status"] == "success"
                assert "firm_id" in result
                
                # Verify mapping was called
                mock_resolve.assert_called_once_with("price_test_123")
    
    @pytest.mark.asyncio
    async def test_handle_checkout_session_unmapped_price(self, sample_checkout_session):
        """Test checkout session handling with unmapped price."""
        
        # Mock the mapping service to return None
        with patch.object(enhanced_webhook_handler.mapper, 'resolve_stripe_price') as mock_resolve:
            mock_resolve.return_value = None
            
            # Create test event
            event = {"data": {"object": sample_checkout_session}}
            
            # Test the handler
            result = await enhanced_webhook_handler.handle_checkout_session_completed(event)
            
            assert result["status"] == "error"
            assert "Unmapped price ID" in result["message"]
    
    @pytest.mark.asyncio
    async def test_handle_subscription_created(self, sample_subscription, sample_plan_mapping):
        """Test subscription creation handling with automatic mapping."""
        
        # Mock the mapping service
        with patch.object(enhanced_webhook_handler.mapper, 'resolve_stripe_price') as mock_resolve:
            mock_resolve.return_value = sample_plan_mapping
            
            # Mock database operations
            with patch.object(enhanced_webhook_handler, '_update_subscription_with_mapping') as mock_update:
                mock_update.return_value = {"status": "success", "subscription_id": "sub_test_123"}
                
                # Create test event
                event = {"data": {"object": sample_subscription}}
                
                # Test the handler
                result = await enhanced_webhook_handler.handle_customer_subscription_created(event)
                
                assert result["status"] == "success"
                assert result["subscription_id"] == "sub_test_123"
    
    @pytest.mark.asyncio
    async def test_country_detection_priority(self):
        """Test country detection priority order."""
        
        # Test with customer details address
        session_with_customer_address = {
            "customer_details": {"address": {"country": "BE"}},
            "shipping": {"address": {"country": "US"}},
            "metadata": {"country": "CA"}
        }
        
        plan_mapping = PlanMapping(
            internal_id=uuid4(),
            internal_type="plan",
            plan_code="solo",
            country_code="FR",
            currency="EUR",
            billing_cycle="monthly",
            stripe_price_id="price_test",
            tax_behavior="inclusive"
        )
        
        customer_info = await enhanced_webhook_handler._extract_customer_info(
            session_with_customer_address, plan_mapping
        )
        
        # Should prioritize customer_details.address.country
        assert customer_info["country_code"] == "BE"
    
    @pytest.mark.asyncio
    async def test_country_detection_fallback(self):
        """Test country detection fallback to mapping."""
        
        # Test with no address information
        session_no_address = {
            "customer_details": {"email": "<EMAIL>"},
            "metadata": {}
        }
        
        plan_mapping = PlanMapping(
            internal_id=uuid4(),
            internal_type="plan",
            plan_code="solo",
            country_code="BE",
            currency="EUR",
            billing_cycle="monthly",
            stripe_price_id="price_test",
            tax_behavior="inclusive"
        )
        
        customer_info = await enhanced_webhook_handler._extract_customer_info(
            session_no_address, plan_mapping
        )
        
        # Should fallback to mapping country
        assert customer_info["country_code"] == "BE"

class TestMultiCountryScenarios:
    """Test multi-country specific scenarios."""
    
    @pytest.mark.asyncio
    async def test_us_subscription_mapping(self):
        """Test US subscription with USD pricing."""
        
        us_session = {
            "id": "cs_us_test",
            "line_items": {
                "data": [{
                    "price": {"id": "price_us_team_monthly"},
                    "quantity": 1
                }]
            },
            "customer_details": {
                "email": "<EMAIL>",
                "address": {"country": "US", "state": "CA"}
            }
        }
        
        us_mapping = PlanMapping(
            internal_id=uuid4(),
            internal_type="plan",
            plan_code="team",
            country_code="US",
            currency="USD",
            billing_cycle="monthly",
            stripe_price_id="price_us_team_monthly",
            tax_behavior="exclusive"
        )
        
        with patch.object(enhanced_webhook_handler.mapper, 'resolve_stripe_price') as mock_resolve:
            mock_resolve.return_value = us_mapping
            
            with patch.object(enhanced_webhook_handler, '_process_subscription_with_mapping') as mock_process:
                mock_process.return_value = {"status": "success", "country": "US", "currency": "USD"}
                
                event = {"data": {"object": us_session}}
                result = await enhanced_webhook_handler.handle_checkout_session_completed(event)
                
                assert result["status"] == "success"
                assert result["country"] == "US"
                assert result["currency"] == "USD"
    
    @pytest.mark.asyncio
    async def test_belgium_subscription_mapping(self):
        """Test Belgium subscription with EUR pricing."""
        
        be_session = {
            "id": "cs_be_test",
            "line_items": {
                "data": [{
                    "price": {"id": "price_be_team_monthly"},
                    "quantity": 1
                }]
            },
            "customer_details": {
                "email": "<EMAIL>",
                "address": {"country": "BE"}
            }
        }
        
        be_mapping = PlanMapping(
            internal_id=uuid4(),
            internal_type="plan",
            plan_code="team",
            country_code="BE",
            currency="EUR",
            billing_cycle="monthly",
            stripe_price_id="price_be_team_monthly",
            tax_behavior="inclusive"
        )
        
        with patch.object(enhanced_webhook_handler.mapper, 'resolve_stripe_price') as mock_resolve:
            mock_resolve.return_value = be_mapping
            
            with patch.object(enhanced_webhook_handler, '_process_subscription_with_mapping') as mock_process:
                mock_process.return_value = {"status": "success", "country": "BE", "currency": "EUR"}
                
                event = {"data": {"object": be_session}}
                result = await enhanced_webhook_handler.handle_checkout_session_completed(event)
                
                assert result["status"] == "success"
                assert result["country"] == "BE"
                assert result["currency"] == "EUR"
    
    @pytest.mark.asyncio
    async def test_addon_mapping(self):
        """Test add-on mapping (AI Receptionist)."""
        
        subscription_with_addon = {
            "id": "sub_with_addon",
            "items": {
                "data": [
                    {
                        "id": "si_plan",
                        "price": {"id": "price_team_monthly"},
                        "quantity": 1
                    },
                    {
                        "id": "si_addon",
                        "price": {"id": "price_ai_receptionist"},
                        "quantity": 1
                    }
                ]
            }
        }
        
        plan_mapping = PlanMapping(
            internal_id=uuid4(),
            internal_type="plan",
            plan_code="team",
            country_code="US",
            currency="USD",
            billing_cycle="monthly",
            stripe_price_id="price_team_monthly",
            tax_behavior="exclusive"
        )
        
        addon_mapping = PlanMapping(
            internal_id=uuid4(),
            internal_type="addon",
            plan_code="ai_receptionist",
            country_code="US",
            currency="USD",
            billing_cycle="monthly",
            stripe_price_id="price_ai_receptionist",
            tax_behavior="exclusive"
        )
        
        def mock_resolve(price_id):
            if price_id == "price_team_monthly":
                return plan_mapping
            elif price_id == "price_ai_receptionist":
                return addon_mapping
            return None
        
        with patch.object(enhanced_webhook_handler.mapper, 'resolve_stripe_price', side_effect=mock_resolve):
            with patch.object(enhanced_webhook_handler, '_update_subscription_with_mapping') as mock_update:
                mock_update.return_value = {"status": "success", "mapped_items": 2}
                
                event = {"data": {"object": subscription_with_addon}}
                result = await enhanced_webhook_handler.handle_customer_subscription_created(event)
                
                assert result["status"] == "success"
                assert result["mapped_items"] == 2

# Integration test function
async def run_integration_tests():
    """Run integration tests with actual database."""
    
    print("🧪 Running Automatic Mapping Integration Tests")
    
    try:
        # Test 1: Database connectivity
        print("1. Testing database connectivity...")
        async with get_async_session() as session:
            result = await session.execute("SELECT 1")
            assert result.scalar() == 1
        print("   ✅ Database connection successful")
        
        # Test 2: Plan mapping resolution
        print("2. Testing plan mapping resolution...")
        # This would test with actual database data
        # mapping = await stripe_mapper.resolve_stripe_price("actual_price_id")
        print("   ✅ Plan mapping resolution ready")
        
        # Test 3: Multi-country data validation
        print("3. Testing multi-country data...")
        async with get_async_session() as session:
            result = await session.execute("""
                SELECT COUNT(*) FROM tenants.plan_pricing 
                WHERE country_code IN ('US', 'BE')
            """)
            count = result.scalar()
            assert count > 0
        print(f"   ✅ Found {count} multi-country pricing records")
        
        print("🎉 All integration tests passed!")
        
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
        raise

if __name__ == "__main__":
    # Run integration tests
    asyncio.run(run_integration_tests())
