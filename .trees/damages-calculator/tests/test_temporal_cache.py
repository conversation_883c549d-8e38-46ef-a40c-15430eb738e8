"""
Unit tests for temporal awareness and semantic caching functionality.

This module tests the temporal query parsing and semantic caching
components of the Research Agent.
"""

import asyncio
import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from backend.agents.interactive.research.temporal import (
    TemporalQueryParser,
    TemporalContext,
    TemporalFilter
)
from backend.agents.interactive.research.cache import (
    SemanticCache,
    CacheConfig,
    CacheResult
)


class TestTemporalQueryParser:
    """Test cases for temporal query parsing."""
    
    @pytest.fixture
    def parser(self):
        """Create a temporal query parser instance."""
        return TemporalQueryParser()
    
    @pytest.fixture
    def sample_context(self):
        """Create a sample temporal context."""
        return TemporalContext(
            query="What are the recent changes to personal injury law in Texas?",
            jurisdiction="texas",
            practice_areas=["personal_injury"],
            legal_domain="personal_injury"
        )
    
    @pytest.mark.asyncio
    async def test_rule_based_temporal_parsing(self, parser, sample_context):
        """Test rule-based temporal parsing when <PERSON> is not available."""
        # Mock Gemini client to be None (fallback to rules)
        parser._gemini_client = None
        
        result = await parser.parse_temporal_context(sample_context)
        
        assert isinstance(result, TemporalFilter)
        assert result.recency_boost is True
        assert "recent" in result.detected_cues
        assert result.temporal_priority == "recent"
        assert result.confidence > 0
    
    @pytest.mark.asyncio
    async def test_historical_query_parsing(self, parser):
        """Test parsing of historical legal queries."""
        parser._gemini_client = None
        
        context = TemporalContext(
            query="What are the foundational cases for negligence law?",
            jurisdiction="texas",
            practice_areas=["personal_injury"]
        )
        
        result = await parser.parse_temporal_context(context)
        
        assert result.temporal_priority == "historical"
        assert "foundational" in result.detected_cues
    
    @pytest.mark.asyncio
    async def test_balanced_query_parsing(self, parser):
        """Test parsing of balanced legal queries."""
        parser._gemini_client = None
        
        context = TemporalContext(
            query="What is the statute of limitations for personal injury in Texas?",
            jurisdiction="texas",
            practice_areas=["personal_injury"]
        )
        
        result = await parser.parse_temporal_context(context)
        
        assert result.temporal_priority == "balanced"
    
    @pytest.mark.asyncio
    async def test_explicit_date_parsing(self, parser):
        """Test parsing of queries with explicit dates."""
        parser._gemini_client = None
        
        context = TemporalContext(
            query="What cases were decided between 2020 and 2023?",
            jurisdiction="texas"
        )
        
        result = await parser.parse_temporal_context(context)
        
        assert result.start_date is not None
        assert result.end_date is not None
        assert result.start_date.year == 2020
        assert result.end_date.year == 2023


class TestSemanticCache:
    """Test cases for semantic caching."""
    
    @pytest.fixture
    def cache_config(self):
        """Create a test cache configuration."""
        return CacheConfig(
            ttl=3600,  # 1 hour for testing
            similarity_threshold=0.8,
            max_entries_per_tenant=100,
            enabled=True,
            redis_url="redis://localhost:6379",
            redis_db=15  # Use separate DB for testing
        )
    
    @pytest.mark.asyncio
    async def test_cache_miss(self, cache_config):
        """Test cache miss scenario."""
        with patch('redis.asyncio.from_url') as mock_redis:
            # Mock Redis client
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.ping.return_value = True
            mock_client.keys.return_value = []
            
            # Mock Voyage client
            with patch('voyageai.AsyncClient') as mock_voyage:
                mock_voyage_instance = AsyncMock()
                mock_voyage.return_value = mock_voyage_instance
                
                async with SemanticCache(cache_config) as cache:
                    result = await cache.get(
                        "test query",
                        "tenant-123",
                        {"jurisdiction": "texas"}
                    )
                    
                    assert result.hit is False
                    assert result.data is None
    
    @pytest.mark.asyncio
    async def test_cache_disabled(self):
        """Test behavior when cache is disabled."""
        config = CacheConfig(enabled=False)
        
        async with SemanticCache(config) as cache:
            result = await cache.get(
                "test query",
                "tenant-123",
                {"jurisdiction": "texas"}
            )
            
            assert result.hit is False
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self, cache_config):
        """Test cache key generation with tenant isolation."""
        cache = SemanticCache(cache_config)
        
        key1 = cache._generate_cache_key(
            "test query",
            "tenant-123",
            {"jurisdiction": "texas"}
        )
        
        key2 = cache._generate_cache_key(
            "test query",
            "tenant-456",
            {"jurisdiction": "texas"}
        )
        
        # Keys should be different for different tenants
        assert key1 != key2
        assert "tenant-123" in key1
        assert "tenant-456" in key2
    
    @pytest.mark.asyncio
    async def test_similarity_calculation(self, cache_config):
        """Test embedding similarity calculation."""
        cache = SemanticCache(cache_config)
        
        # Test identical embeddings
        embedding1 = [1.0, 0.0, 0.0]
        embedding2 = [1.0, 0.0, 0.0]
        similarity = cache._calculate_similarity(embedding1, embedding2)
        assert similarity == 1.0
        
        # Test orthogonal embeddings
        embedding3 = [0.0, 1.0, 0.0]
        similarity = cache._calculate_similarity(embedding1, embedding3)
        assert similarity == 0.0


@pytest.mark.asyncio
async def test_integration_temporal_cache():
    """Integration test for temporal parsing and caching together."""
    parser = TemporalQueryParser()
    parser._gemini_client = None  # Use rule-based parsing
    
    context = TemporalContext(
        query="What are recent developments in data privacy law?",
        jurisdiction="texas",
        practice_areas=["technology"],
        legal_domain="data_privacy"
    )
    
    temporal_filter = await parser.parse_temporal_context(context)
    
    # Should detect recency and boost for fast-changing legal domain
    assert temporal_filter.recency_boost is True
    assert temporal_filter.temporal_priority in ["recent", "balanced"]
    assert "recent" in temporal_filter.detected_cues


class TestTenantIsolation:
    """Test cases for tenant isolation in semantic caching."""

    @pytest.fixture
    def cache_config(self):
        """Create a test cache configuration."""
        return CacheConfig(
            ttl=3600,
            similarity_threshold=0.8,
            max_entries_per_tenant=100,
            enabled=True,
            redis_url="redis://localhost:6379",
            redis_db=15  # Use separate DB for testing
        )

    @pytest.mark.asyncio
    async def test_tenant_cache_key_isolation(self, cache_config):
        """Test that cache keys are properly isolated by tenant."""
        cache = SemanticCache(cache_config)

        # Same query, different tenants should have different cache keys
        query = "What is the statute of limitations for personal injury?"
        context = {"jurisdiction": "texas", "practice_areas": ["personal_injury"]}

        key_tenant_1 = cache._generate_cache_key(query, "tenant-123", context)
        key_tenant_2 = cache._generate_cache_key(query, "tenant-456", context)

        # Keys should be different
        assert key_tenant_1 != key_tenant_2

        # Keys should contain tenant IDs
        assert "tenant-123" in key_tenant_1
        assert "tenant-456" in key_tenant_2

        # Keys should not contain other tenant's ID
        assert "tenant-456" not in key_tenant_1
        assert "tenant-123" not in key_tenant_2

    @pytest.mark.asyncio
    async def test_tenant_data_isolation_logic(self, cache_config):
        """Test the core tenant isolation logic without external dependencies."""
        cache = SemanticCache(cache_config)

        # Test that cache keys are properly isolated by tenant
        query = "What is the statute of limitations?"
        context = {"jurisdiction": "texas"}

        key_tenant_1 = cache._generate_cache_key(query, "tenant-123", context)
        key_tenant_2 = cache._generate_cache_key(query, "tenant-456", context)

        # Keys should be different for different tenants
        assert key_tenant_1 != key_tenant_2
        assert "tenant-123" in key_tenant_1
        assert "tenant-456" in key_tenant_2

        # Keys should not leak tenant information
        assert "tenant-456" not in key_tenant_1
        assert "tenant-123" not in key_tenant_2

    @pytest.mark.asyncio
    async def test_cache_key_tenant_isolation_patterns(self, cache_config):
        """Test that cache key patterns properly isolate tenants."""
        cache = SemanticCache(cache_config)

        # Test various queries and contexts
        test_cases = [
            ("personal injury statute of limitations", {"jurisdiction": "texas"}),
            ("negligence law recent changes", {"jurisdiction": "california"}),
            ("contract law precedents", {"jurisdiction": "texas", "practice_areas": ["contract"]})
        ]

        for query, context in test_cases:
            key_1 = cache._generate_cache_key(query, "tenant-alpha", context)
            key_2 = cache._generate_cache_key(query, "tenant-beta", context)

            # Each tenant should have unique keys
            assert key_1 != key_2

            # Keys should contain tenant identifiers
            assert "tenant-alpha" in key_1
            assert "tenant-beta" in key_2

            # No cross-tenant contamination
            assert "tenant-beta" not in key_1
            assert "tenant-alpha" not in key_2

    @pytest.mark.asyncio
    async def test_monitoring_tenant_isolation(self):
        """Test that monitoring metrics are isolated by tenant."""
        from backend.agents.interactive.research.monitoring import cache_monitor

        # Record queries for different tenants
        cache_monitor.record_query("tenant-123", cache_hit=True, similarity_score=0.9)
        cache_monitor.record_query("tenant-123", cache_hit=False)
        cache_monitor.record_query("tenant-456", cache_hit=True, similarity_score=0.8)

        # Get metrics for each tenant
        metrics_123 = cache_monitor.get_current_metrics("tenant-123")
        metrics_456 = cache_monitor.get_current_metrics("tenant-456")

        # Each tenant should see only their own metrics
        assert metrics_123.tenant_id == "tenant-123"
        assert metrics_123.total_queries == 2
        assert metrics_123.cache_hits == 1

        assert metrics_456.tenant_id == "tenant-456"
        assert metrics_456.total_queries == 1
        assert metrics_456.cache_hits == 1

        # Tenants should not see each other's metrics
        assert metrics_123.tenant_id != metrics_456.tenant_id


@pytest.mark.asyncio
async def test_end_to_end_tenant_isolation():
    """End-to-end test of tenant isolation in the Research Agent workflow."""
    from backend.agents.interactive.research.state import ResearchState, UserContext
    from backend.agents.interactive.research.nodes import temporal_cached_retrieval
    from langchain_core.runnables import RunnableConfig

    # Create states for different tenants
    user_context_1 = UserContext(
        user_id="user-123",
        tenant_id="tenant-123",
        role="attorney"
    )

    user_context_2 = UserContext(
        user_id="user-456",
        tenant_id="tenant-456",
        role="attorney"
    )

    state_1 = ResearchState(
        question="What is the statute of limitations for personal injury in Texas?",
        user_context=user_context_1,
        jurisdiction="texas"
    )

    state_2 = ResearchState(
        question="What is the statute of limitations for personal injury in Texas?",
        user_context=user_context_2,
        jurisdiction="texas"
    )

    config = RunnableConfig()

    # Mock the underlying hybrid_vector_search to avoid actual API calls
    with patch('backend.agents.interactive.research.nodes.hybrid_vector_search') as mock_search:
        mock_search.return_value = {"status": "success", "next": "graph_expand"}

        # Process same query for different tenants
        result_1 = await temporal_cached_retrieval(state_1, config)
        result_2 = await temporal_cached_retrieval(state_2, config)

        # Both should succeed
        assert result_1["status"] == "success"
        assert result_2["status"] == "success"

        # Check that tenant isolation is maintained in search metadata
        # Handle both dict and object cases for user_context
        if hasattr(state_1.user_context, 'tenant_id'):
            assert state_1.user_context.tenant_id == "tenant-123"
            assert state_2.user_context.tenant_id == "tenant-456"
        elif isinstance(state_1.user_context, dict):
            assert state_1.user_context.get('tenant_id') == "tenant-123"
            assert state_2.user_context.get('tenant_id') == "tenant-456"

        # Verify cache operations used correct tenant IDs
        # (This would be verified through monitoring in a real scenario)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
