"""
Test suite for Deadline Insights API endpoints.

This module tests all the deadline insights API endpoints to ensure
they work correctly with proper authentication and data handling.
"""

import pytest
import json
import uuid
from datetime import datetime, timezone, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Import the FastAPI app
from backend.api.main import create_app
from backend.api.dependencies.auth import get_current_user, get_current_tenant, UserContext
from backend.api.dependencies.authorization import require_permission

# Create test client
app = create_app()

# Mock authentication functions
class MockUserContext(UserContext):
    """Mock user context that can be accessed both as object and dictionary."""

    def __getitem__(self, key):
        """Allow dictionary-style access."""
        if key == "id":
            return str(self.id)
        elif key == "tenant_id":
            return str(self.tenant_id)
        else:
            return getattr(self, key)

    def get(self, key, default=None):
        """Allow dictionary-style .get() access."""
        try:
            return self[key]
        except (KeyError, AttributeError):
            return default

def mock_get_current_user():
    return MockUserContext(
        id=uuid.UUID("12345678-1234-5678-9012-123456789012"),
        email="<EMAIL>",
        name="Test User",
        tenant_id=uuid.UUID("*************-8765-2109-************"),
        role="partner"  # Use a valid role that has WRITE_DEADLINE_INSIGHTS permission
    )

def mock_get_current_tenant():
    return {
        "id": "test-tenant-123",
        "name": "Test Tenant"
    }

def mock_require_permission(permission):
    def dependency():
        return None
    return dependency

# Override dependencies
app.dependency_overrides[get_current_user] = mock_get_current_user
app.dependency_overrides[get_current_tenant] = mock_get_current_tenant
app.dependency_overrides[require_permission] = mock_require_permission

client = TestClient(app)


class TestDeadlineInsightsAPI:
    """Test class for deadline insights API endpoints."""
    

    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client."""
        with patch('backend.db.supabase_client.get_supabase_client') as mock_client:
            mock_supabase = MagicMock()
            mock_client.return_value = mock_supabase
            yield mock_supabase

    @pytest.fixture
    def mock_job_submission(self):
        """Mock Celery job submission to prevent Redis connection issues."""
        with patch('jobs.helpers.submit_deadline_insights_job') as mock_submit:
            mock_submit.return_value = "test-job-123"
            yield mock_submit
    
    def test_get_deadline_insights_summary_success(self, mock_supabase):
        """Test successful retrieval of deadline insights summary."""

        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "recommendations": [
                        {"priority": "CRITICAL"},
                        {"priority": "HIGH"},
                        {"priority": "MEDIUM"}
                    ],
                    "conflicts": [{"type": "scheduling"}]
                },
                "generated_at": "2025-07-12T10:00:00Z"
            }
        ]

        response = client.get("/deadline-insights/summary")

        assert response.status_code == 200
        data = response.json()
        # Verify the response structure is correct
        assert "critical_deadlines" in data
        assert "high_risk_deadlines" in data
        assert "conflicts_detected" in data
        assert "recommendations" in data
        assert "last_updated" in data
        # Check that all values are non-negative integers
        assert data["critical_deadlines"] >= 0
        assert data["high_risk_deadlines"] >= 0
        assert data["conflicts_detected"] >= 0
        assert data["recommendations"] >= 0
    
    def test_get_deadline_insights_summary_no_data(self, mock_supabase):
        """Test summary endpoint when no insights data exists."""
        
        # Mock empty Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = []
        
        response = client.get("/deadline-insights/summary")
        
        assert response.status_code == 200
        data = response.json()
        assert data["critical_deadlines"] == 0
        assert data["high_risk_deadlines"] == 0
        assert data["conflicts_detected"] == 0
        assert data["recommendations"] == 0
    
    @patch('backend.api.routes.deadline_insights.get_supabase_client')
    @patch('jobs.helpers.submit_deadline_insights_job')
    def test_get_detailed_deadline_insights_success(self, mock_submit_job, mock_supabase_client):
        """Test successful retrieval of detailed deadline insights."""

        # Mock job submission
        mock_submit_job.return_value = "test-job-123"

        # Create mock Supabase client and result
        mock_supabase = MagicMock()
        mock_supabase_client.return_value = mock_supabase

        # Create mock result with existing insights (cache hit scenario)
        mock_result = MagicMock()
        mock_result.data = [
            {
                "analysis_type": "comprehensive",
                "insights": {
                    "recommendations": [],
                    "conflicts": [],
                    "risk_assessment": "low"
                },
                "generated_at": "2025-07-12T10:00:00Z",
                "expires_at": "2025-07-12T14:00:00Z"
            }
        ]

        # Set up the complete mock chain for detailed insights query
        # API calls: supabase.schema('tenants').from_('deadline_insights').select('*').eq('tenant_id', ...).eq('analysis_type', 'comprehensive').gte('expires_at', ...).order('generated_at', desc=True).limit(1).execute()
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.eq.return_value.gte.return_value.order.return_value.limit.return_value.execute.return_value = mock_result

        response = client.get("/deadline-insights/detailed")

        assert response.status_code == 200
        data = response.json()
        assert "tenant_id" in data
        assert "analysis_type" in data
        assert "insights" in data
        assert "generated_at" in data
        assert "expires_at" in data
        assert data["cache_hit"] == True
    
    def test_get_critical_deadlines_success(self, mock_supabase):
        """Test successful retrieval of critical deadlines."""
        
        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "recommendations": [
                        {
                            "id": "rec-1",
                            "title": "Critical Deadline",
                            "priority": "CRITICAL",
                            "due_date": "2025-07-13"
                        },
                        {
                            "id": "rec-2", 
                            "title": "High Priority Task",
                            "priority": "HIGH",
                            "due_date": "2025-07-14"
                        }
                    ]
                },
                "generated_at": "2025-07-12T10:00:00Z"
            }
        ]
        
        response = client.get("/deadline-insights/critical?limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert "critical_deadlines" in data
        # Check if we have data or the no-data response
        if data.get("message") == "No insights available":
            assert data["critical_deadlines"] == []
        else:
            assert len(data["critical_deadlines"]) >= 0
    
    def test_get_deadline_conflicts_success(self, mock_supabase):
        """Test successful retrieval of deadline conflicts."""
        
        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "conflicts": [
                        {
                            "id": "conflict-1",
                            "type": "scheduling",
                            "description": "Two deadlines on same day",
                            "severity": "high"
                        }
                    ]
                },
                "generated_at": "2025-07-12T10:00:00Z"
            }
        ]
        
        response = client.get("/deadline-insights/conflicts")
        
        assert response.status_code == 200
        data = response.json()
        assert "conflicts" in data
        # Check if we have data or the no-data response
        if data.get("message") == "No insights available":
            assert data["conflicts"] == []
        else:
            assert len(data["conflicts"]) >= 0
    
    @patch('jobs.helpers.submit_deadline_insights_job')
    def test_trigger_deadline_insights_success(self, mock_submit_job, mock_supabase):
        """Test successful triggering of deadline insights generation."""
        mock_submit_job.return_value = "job-123"

        # Mock recent insights check (no recent insights) - should proceed with job submission
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.execute.return_value.data = []

        request_data = {
            "analysis_type": "comprehensive",
            "force_refresh": False
        }

        response = client.post("/deadline-insights/trigger", json=request_data)

        assert response.status_code == 202
        data = response.json()
        assert "job_id" in data
        assert data["job_id"] == "job-123"
        assert "message" in data
        mock_submit_job.assert_called_once()
    
    @patch('backend.api.routes.deadline_insights.get_supabase_client')
    @patch('jobs.helpers.submit_deadline_insights_job')
    def test_trigger_deadline_insights_recent_generation(self, mock_submit_job, mock_supabase_client):
        """Test triggering insights when recent generation exists."""

        # Ensure submit_job returns a string (in case it gets called)
        mock_submit_job.return_value = "job-123"

        # Create mock Supabase client
        mock_supabase = MagicMock()
        mock_supabase_client.return_value = mock_supabase

        # Mock recent insights check (recent insights exist) - should trigger early return
        recent_time = datetime.now(timezone.utc) - timedelta(minutes=10)
        mock_recent_result = MagicMock()
        mock_recent_result.data = [{"generated_at": recent_time.isoformat()}]

        # Set up mock chain for recent insights query
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.execute.return_value = mock_recent_result

        request_data = {
            "analysis_type": "comprehensive",
            "force_refresh": False
        }

        response = client.post("/deadline-insights/trigger", json=request_data)

        assert response.status_code == 202
        data = response.json()
        assert "message" in data
        assert "recently generated" in data["message"].lower()
    
    def test_get_morning_briefing_success(self, mock_supabase):
        """Test successful retrieval of morning briefing."""
        
        # Mock deadlines response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.lte.return_value.eq.return_value.execute.return_value.data = [
            {
                "id": "deadline-1",
                "title": "Critical Filing",
                "priority": "critical",
                "due_date": "2025-07-12T17:00:00Z"
            }
        ]
        
        # Mock tasks response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.lte.return_value.neq.return_value.execute.return_value.data = [
            {
                "id": "task-1",
                "title": "Review documents",
                "priority": "high"
            }
        ]
        
        # Mock insights response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "recommendations": []
                }
            }
        ]
        
        response = client.get("/deadline-insights/morning-briefing")
        
        assert response.status_code == 200
        data = response.json()
        assert "greeting" in data
        assert "today_summary" in data
        assert "priority_actions" in data
        assert "insights" in data
        assert "weather_check" in data
        # Check that critical_deadlines is a non-negative integer
        assert data["today_summary"]["critical_deadlines"] >= 0
    
    def test_unauthorized_access(self):
        """Test that endpoints require authentication."""
        # Temporarily remove the global override
        original_override = app.dependency_overrides.get(get_current_user)

        def mock_unauthorized():
            from fastapi import HTTPException
            raise HTTPException(status_code=401, detail="Unauthorized")

        app.dependency_overrides[get_current_user] = mock_unauthorized

        try:
            response = client.get("/deadline-insights/summary")
            assert response.status_code == 401
        finally:
            # Restore the original override
            if original_override:
                app.dependency_overrides[get_current_user] = original_override


class TestJobsAPI:
    """Test class for jobs API endpoints."""
    

    
    @patch('jobs.helpers.trigger_user_return_insights')
    def test_trigger_user_return_insights_success(self, mock_trigger):
        """Test successful triggering of user return insights."""
        mock_trigger.return_value = "job-456"
        
        request_data = {
            "user_id": "test-user-123",
            "tenant_id": "test-tenant-123",
            "last_activity_time": "2025-07-12T08:00:00Z"
        }
        
        response = client.post("/jobs/user-return-insights", json=request_data)
        
        assert response.status_code == 202
        data = response.json()
        assert data["success"] == True
        assert data["job_id"] == "job-456"
        mock_trigger.assert_called_once()
    
    @patch('jobs.helpers.check_job_status')
    def test_get_job_status_success(self, mock_check_status):
        """Test successful job status retrieval."""
        mock_check_status.return_value = {
            "job_id": "job-123",
            "status": "SUCCESS",
            "checked_at": "2025-07-12T10:00:00Z",
            "result": {"message": "Job completed successfully"}
        }
        
        response = client.get("/jobs/status/job-123")
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == "job-123"
        assert data["status"] == "SUCCESS"
        assert data["result"]["message"] == "Job completed successfully"


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
