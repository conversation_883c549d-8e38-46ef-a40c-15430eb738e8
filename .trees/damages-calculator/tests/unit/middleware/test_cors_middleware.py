"""Tests for CORS middleware configuration."""
import os
from unittest.mock import patch

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.testclient import TestClient


# Create a simple test app
def create_test_app(origins):
    app = FastAPI()
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["Authorization", "Content-Type", "X-CPK-Endpoint-Key", "X-Client-Info"],
        expose_headers=["Content-Length", "Content-Range"],
        max_age=86400,  # 24 hours
    )

    @app.get("/test")
    def test_endpoint():
        return {"message": "Test endpoint"}

    return app

# Tests
def test_cors_allowed_origin():
    """Test that requests from allowed origins are accepted."""
    # Create app with specific allowed origin
    app = create_test_app(["https://example.com"])
    client = TestClient(app)

    # Test with allowed origin
    response = client.get(
        "/test",
        headers={"Origin": "https://example.com"}
    )

    assert response.status_code == 200
    assert response.headers["Access-Control-Allow-Origin"] == "https://example.com"
    assert response.headers["Access-Control-Allow-Credentials"] == "true"

def test_cors_disallowed_origin():
    """Test that requests from disallowed origins don't get CORS headers."""
    # Create app with specific allowed origin
    app = create_test_app(["https://example.com"])
    client = TestClient(app)

    # Test with disallowed origin
    response = client.get(
        "/test",
        headers={"Origin": "https://malicious-site.com"}
    )

    assert response.status_code == 200
    assert "Access-Control-Allow-Origin" not in response.headers

def test_cors_wildcard_origin():
    """Test that wildcard origin allows all origins."""
    # Create app with wildcard origin
    app = create_test_app(["*"])
    client = TestClient(app)

    # Test with any origin
    response = client.get(
        "/test",
        headers={"Origin": "https://any-site.com"}
    )

    assert response.status_code == 200
    assert response.headers["Access-Control-Allow-Origin"] == "*"

def test_cors_preflight_request():
    """Test that preflight requests are handled correctly."""
    # Create app with specific allowed origin
    app = create_test_app(["https://example.com"])
    client = TestClient(app)

    # Test preflight request
    response = client.options(
        "/test",
        headers={
            "Origin": "https://example.com",
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "Authorization,Content-Type"
        }
    )

    assert response.status_code == 200
    assert response.headers["Access-Control-Allow-Origin"] == "https://example.com"
    # Remove spaces for comparison to handle different formatting
    assert response.headers["Access-Control-Allow-Methods"].replace(" ", "") == "GET,POST,PUT,DELETE,OPTIONS,PATCH"
    assert "Authorization" in response.headers["Access-Control-Allow-Headers"]
    assert "Content-Type" in response.headers["Access-Control-Allow-Headers"]
    assert response.headers["Access-Control-Max-Age"] == "86400"

def test_cors_with_env_origins():
    """Test that CORS origins from environment variables work correctly."""
    # Mock environment variable
    with patch.dict(os.environ, {"CORS_ORIGINS": "https://example.com,https://test.com"}):
        # Create app with origins from environment
        origins = os.environ.get("CORS_ORIGINS", "").split(",")
        app = create_test_app(origins)
        client = TestClient(app)

        # Test with first allowed origin
        response1 = client.get(
            "/test",
            headers={"Origin": "https://example.com"}
        )

        assert response1.status_code == 200
        assert response1.headers["Access-Control-Allow-Origin"] == "https://example.com"

        # Test with second allowed origin
        response2 = client.get(
            "/test",
            headers={"Origin": "https://test.com"}
        )

        assert response2.status_code == 200
        assert response2.headers["Access-Control-Allow-Origin"] == "https://test.com"

        # Test with disallowed origin
        response3 = client.get(
            "/test",
            headers={"Origin": "https://malicious-site.com"}
        )

        assert response3.status_code == 200
        assert "Access-Control-Allow-Origin" not in response3.headers
