"""Tests for rate limiting middleware."""
import os
import time
from unittest.mock import patch

from fastapi import FastAPI, Request
from fastapi.testclient import TestClient

from src.pi_lawyer.middleware.rate_limit_middleware import (
    rate_limit_middleware,
    rate_limit_store,
)


# Create a simple test app with the rate limiting middleware
def create_test_app():
    app = FastAPI()

    @app.middleware("http")
    async def test_rate_limit_middleware(request: Request, call_next):
        return await rate_limit_middleware(request, call_next)

    @app.get("/test")
    def test_endpoint():
        return {"message": "Test endpoint"}

    @app.get("/health")
    def health_endpoint():
        return {"status": "ok"}

    return app

# Tests
def test_rate_limit_not_exceeded():
    """Test that requests under the rate limit are allowed."""
    # Clear rate limit store
    rate_limit_store.clear()

    # Mock environment variables
    with patch.dict(os.environ, {
        "ENABLE_RATE_LIMIT": "true",
        "RATE_LIMIT": "5",
        "RATE_LIMIT_WINDOW": "60"
    }):
        app = create_test_app()
        client = TestClient(app)

        # Make requests under the limit
        for _ in range(5):
            response = client.get("/test")
            assert response.status_code == 200
            assert response.json() == {"message": "Test endpoint"}

def test_rate_limit_exceeded():
    """Test that requests exceeding the rate limit are blocked."""
    # Clear rate limit store
    rate_limit_store.clear()

    # Mock environment variables
    with patch.dict(os.environ, {
        "ENABLE_RATE_LIMIT": "true",
        "RATE_LIMIT": "3",
        "RATE_LIMIT_WINDOW": "60"
    }):
        app = create_test_app()
        client = TestClient(app)

        # Make requests up to the limit
        for _ in range(3):
            response = client.get("/test")
            assert response.status_code == 200

        # Make one more request that should be blocked
        response = client.get("/test")
        assert response.status_code == 429
        assert "Rate limit exceeded" in response.json()["detail"]
        assert "Retry-After" in response.headers

def test_rate_limit_disabled():
    """Test that rate limiting can be disabled."""
    # Clear rate limit store
    rate_limit_store.clear()

    # Mock environment variables to disable rate limiting
    with patch.dict(os.environ, {"ENABLE_RATE_LIMIT": "false"}):
        app = create_test_app()
        client = TestClient(app)

        # Make many requests that would exceed the default limit
        for _ in range(10):
            response = client.get("/test")
            assert response.status_code == 200

def test_public_endpoint_not_rate_limited():
    """Test that public endpoints are not rate limited."""
    # Clear rate limit store
    rate_limit_store.clear()

    # Mock environment variables
    with patch.dict(os.environ, {
        "ENABLE_RATE_LIMIT": "true",
        "RATE_LIMIT": "3",
        "RATE_LIMIT_WINDOW": "60"
    }):
        app = create_test_app()
        client = TestClient(app)

        # Make many requests to a public endpoint
        for _ in range(10):
            response = client.get("/health")
            assert response.status_code == 200

def test_rate_limit_window_reset():
    """Test that rate limit counter resets after the window expires."""
    # Clear rate limit store
    rate_limit_store.clear()

    # Mock environment variables with a very short window
    with patch.dict(os.environ, {
        "ENABLE_RATE_LIMIT": "true",
        "RATE_LIMIT": "3",
        "RATE_LIMIT_WINDOW": "1"  # 1 second window
    }):
        app = create_test_app()
        client = TestClient(app)

        # Make requests up to the limit
        for _ in range(3):
            response = client.get("/test")
            assert response.status_code == 200

        # Make one more request that should be blocked
        response = client.get("/test")
        assert response.status_code == 429

        # Wait for the window to expire
        time.sleep(1.1)

        # Make another request that should now be allowed
        response = client.get("/test")
        assert response.status_code == 200

def test_different_clients_separate_limits():
    """Test that different clients have separate rate limits."""
    # This test uses two different clients with different headers to simulate different users

    # Clear rate limit store
    rate_limit_store.clear()

    # Mock environment variables
    with patch.dict(os.environ, {
        "ENABLE_RATE_LIMIT": "true",
        "RATE_LIMIT": "3",
        "RATE_LIMIT_WINDOW": "60"
    }):
        app = create_test_app()
        client = TestClient(app)

        # Make requests for client1 up to the limit
        for _ in range(3):
            response = client.get("/test", headers={"X-User-ID": "user1"})
            assert response.status_code == 200

        # Client1 should now be rate limited
        response = client.get("/test", headers={"X-User-ID": "user1"})
        assert response.status_code == 429

        # But client2 should still be allowed
        for _ in range(3):
            response = client.get("/test", headers={"X-User-ID": "user2"})
            assert response.status_code == 200
