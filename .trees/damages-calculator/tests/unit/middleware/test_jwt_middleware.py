"""Tests for JWT verification middleware."""
import datetime

from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.testclient import <PERSON><PERSON><PERSON>
from jose import jwt

from src.pi_lawyer.middleware.jwt_middleware import verify_jwt_middleware

# Test app
app = FastAPI()
app.middleware("http")(verify_jwt_middleware)

@app.get("/protected")
async def protected_route(request: Request):
    user = request.state.user
    return {"user_id": user.user_id, "role": user.role, "tenant_id": user.tenant_id}

@app.get("/public")
async def public_route():
    return {"status": "public"}

client = TestClient(app)

# Helper to create test tokens
def create_test_token(
    user_id="test-user",
    email="<EMAIL>",
    role="user",
    tenant_id="test-tenant",
    exp_minutes=30,
    secret="test-secret"
):
    payload = {
        "sub": user_id,
        "email": email,
        "role": role,
        "tenant_id": tenant_id,
        "exp": datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(minutes=exp_minutes),
        "iat": datetime.datetime.now(datetime.timezone.utc)
    }
    return jwt.encode(payload, secret, algorithm="HS256")

# Tests
def test_protected_route_with_valid_token(monkeypatch):
    # Mock the JWT secret
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-secret")
    
    token = create_test_token()
    response = client.get(
        "/protected",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    assert response.json()["user_id"] == "test-user"
    assert response.json()["role"] == "user"
    assert response.json()["tenant_id"] == "test-tenant"

def test_protected_route_without_token():
    response = client.get("/protected")
    assert response.status_code == 401
    assert response.json()["detail"] == "Authentication required"

def test_protected_route_with_invalid_token(monkeypatch):
    # Mock the JWT secret
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-secret")
    
    # Create token with wrong secret
    token = create_test_token(secret="wrong-secret")
    response = client.get(
        "/protected",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid authentication token"

def test_public_route_without_token():
    response = client.get("/public")
    assert response.status_code == 200
    assert response.json()["status"] == "public"

def test_development_mode_without_token(monkeypatch):
    # Set development mode
    monkeypatch.setenv("APP_ENV", "development")
    
    response = client.get("/protected")
    assert response.status_code == 200
    assert response.json()["user_id"] == "00000000-0000-0000-0000-000000000000"
    assert response.json()["role"] == "user"
    assert response.json()["tenant_id"] == "00000000-0000-0000-0000-000000000000"
