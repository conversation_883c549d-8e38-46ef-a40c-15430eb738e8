"""Tests for security headers middleware."""
import os
from unittest.mock import patch

from fastapi import FastAP<PERSON>, Request
from fastapi.testclient import Test<PERSON>lient

from src.pi_lawyer.middleware.security_headers_middleware import (
    security_headers_middleware,
)


# Create a simple test app with the security headers middleware
def create_test_app():
    app = FastAPI()
    
    @app.middleware("http")
    async def test_security_headers_middleware(request: Request, call_next):
        return await security_headers_middleware(request, call_next)
    
    @app.get("/test")
    def test_endpoint():
        return {"message": "Test endpoint"}
    
    return app

# Tests
def test_default_security_headers():
    """Test that default security headers are added to responses."""
    app = create_test_app()
    client = TestClient(app)
    
    response = client.get("/test")
    
    assert response.status_code == 200
    assert response.headers["X-Content-Type-Options"] == "nosniff"
    assert response.headers["X-XSS-Protection"] == "1; mode=block"
    assert response.headers["X-Frame-Options"] == "DENY"
    assert response.headers["Referrer-Policy"] == "strict-origin-when-cross-origin"
    assert response.headers["Permissions-Policy"] == "camera=(), microphone=(), geolocation=()"
    assert response.headers["Cache-Control"] == "no-store, max-age=0"
    assert response.headers["Pragma"] == "no-cache"

def test_custom_security_headers():
    """Test that custom security headers from environment variables are used."""
    # Mock environment variables
    with patch.dict(os.environ, {
        "X_CONTENT_TYPE_OPTIONS": "custom-nosniff",
        "X_XSS_PROTECTION": "custom-xss-protection",
        "X_FRAME_OPTIONS": "SAMEORIGIN",
        "REFERRER_POLICY": "no-referrer",
        "PERMISSIONS_POLICY": "custom-permissions-policy",
    }):
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/test")
        
        assert response.status_code == 200
        assert response.headers["X-Content-Type-Options"] == "custom-nosniff"
        assert response.headers["X-XSS-Protection"] == "custom-xss-protection"
        assert response.headers["X-Frame-Options"] == "SAMEORIGIN"
        assert response.headers["Referrer-Policy"] == "no-referrer"
        assert response.headers["Permissions-Policy"] == "custom-permissions-policy"

def test_content_security_policy():
    """Test that Content-Security-Policy header is added when enabled."""
    app = create_test_app()
    client = TestClient(app)
    
    response = client.get("/test")
    
    assert response.status_code == 200
    assert "Content-Security-Policy" in response.headers

def test_disabled_content_security_policy():
    """Test that Content-Security-Policy header is not added when disabled."""
    # Mock environment variable to disable CSP
    with patch.dict(os.environ, {"ENABLE_CSP": "false"}):
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/test")
        
        assert response.status_code == 200
        assert "Content-Security-Policy" not in response.headers

def test_custom_content_security_policy():
    """Test that custom Content-Security-Policy from environment variable is used."""
    # Mock environment variable with custom CSP
    custom_csp = "default-src 'self'; script-src 'self'; object-src 'none'"
    with patch.dict(os.environ, {"CONTENT_SECURITY_POLICY": custom_csp}):
        app = create_test_app()
        client = TestClient(app)
        
        response = client.get("/test")
        
        assert response.status_code == 200
        assert response.headers["Content-Security-Policy"] == custom_csp
