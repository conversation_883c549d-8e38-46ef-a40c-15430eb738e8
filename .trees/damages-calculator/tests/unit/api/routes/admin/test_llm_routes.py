"""
Unit tests for the admin LLM routes.
"""

import unittest
from unittest.mock import MagicMock, patch

from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from backend.api.routes.admin.llm import router
from src.pi_lawyer.middleware.jwt_middleware import User<PERSON>ontext
from src.pi_lawyer.shared.core.llm.admin.models import ModelCapabilities, ModelConfig
from src.pi_lawyer.shared.core.llm.config import LLMProvider


class TestAdminLLMRoutes(unittest.TestCase):
    """Test the admin LLM routes."""

    def setUp(self):
        """Set up the test."""
        # Create a FastAPI app
        self.app = FastAPI()
        
        # Add the router
        self.app.include_router(router)
        
        # Create a test client
        self.client = TestClient(self.app)
        
        # Create test models
        self.test_models = [
            ModelConfig(
                provider=LLMProvider.OPENAI,
                model_id="gpt-4o",
                display_name="GPT-4o",
                description="Test model",
                capabilities=ModelCapabilities()
            ),
            ModelConfig(
                provider=LLMProvider.OPENAI,
                model_id="gpt-3.5-turbo",
                display_name="GPT-3.5 Turbo",
                description="Test model",
                capabilities=ModelCapabilities()
            ),
            ModelConfig(
                provider=LLMProvider.ANTHROPIC,
                model_id="claude-3-opus",
                display_name="Claude 3 Opus",
                description="Test model",
                capabilities=ModelCapabilities()
            )
        ]
        
        # Create test default models
        self.test_defaults = {
            "openai": "gpt-4o",
            "anthropic": "claude-3-opus"
        }
        
        # Create a mock admin user
        self.admin_user = UserContext(
            user_id="admin-user",
            email="<EMAIL>",
            role="admin",
            firm_id="test-firm",
            permissions=[],
            is_authenticated=True
        )
        
        # Create a mock non-admin user
        self.non_admin_user = UserContext(
            user_id="non-admin-user",
            email="<EMAIL>",
            role="user",
            firm_id="test-firm",
            permissions=[],
            is_authenticated=True
        )
        
        # Create a mock unauthenticated user
        self.unauthenticated_user = UserContext(
            user_id="",
            email="",
            role="",
            firm_id="",
            permissions=[],
            is_authenticated=False
        )

    @patch("backend.api.routes.admin.llm.verify_jwt")
    @patch("backend.api.routes.admin.llm.get_available_models")
    @patch("backend.api.routes.admin.llm.get_llm_registry")
    def test_get_llm_models(self, mock_get_registry, mock_get_models, mock_verify_jwt):
        """Test the GET /admin/llm endpoint."""
        # Set up the mocks
        mock_verify_jwt.return_value = self.admin_user
        mock_get_models.return_value = self.test_models
        
        mock_registry = MagicMock()
        mock_registry.get_all_defaults.return_value = self.test_defaults
        mock_get_registry.return_value = mock_registry
        
        # Make the request
        response = self.client.get("/admin/llm")
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        
        # Parse the response
        data = response.json()
        
        # Check the response data
        self.assertIn("models", data)
        self.assertIn("default_models", data)
        
        # Check the models
        self.assertEqual(len(data["models"]), 3)
        
        # Check that the default models are marked
        for model in data["models"]:
            if model["provider"] == "openai" and model["model_id"] == "gpt-4o":
                self.assertTrue(model["is_default"])
            elif model["provider"] == "anthropic" and model["model_id"] == "claude-3-opus":
                self.assertTrue(model["is_default"])
            else:
                self.assertFalse(model["is_default"])
        
        # Check the default models
        self.assertEqual(data["default_models"], self.test_defaults)

    @patch("backend.api.routes.admin.llm.verify_jwt")
    @patch("backend.api.routes.admin.llm.get_llm_registry")
    def test_set_default_model(self, mock_get_registry, mock_verify_jwt):
        """Test the PUT /admin/llm/default endpoint."""
        # Set up the mocks
        mock_verify_jwt.return_value = self.admin_user
        
        mock_registry = MagicMock()
        mock_registry.set_default_model.return_value = True
        mock_get_registry.return_value = mock_registry
        
        # Make the request
        response = self.client.put(
            "/admin/llm/default",
            json={"provider": "openai", "model_id": "gpt-3.5-turbo"}
        )
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        
        # Parse the response
        data = response.json()
        
        # Check the response data
        self.assertTrue(data["success"])
        self.assertEqual(data["provider"], "openai")
        self.assertEqual(data["model_id"], "gpt-3.5-turbo")
        
        # Check that the registry was called
        mock_registry.set_default_model.assert_called_once_with(LLMProvider.OPENAI, "gpt-3.5-turbo")

    @patch("backend.api.routes.admin.llm.verify_jwt")
    @patch("backend.api.routes.admin.llm.get_llm_registry")
    def test_set_default_model_failure(self, mock_get_registry, mock_verify_jwt):
        """Test the PUT /admin/llm/default endpoint with a failure."""
        # Set up the mocks
        mock_verify_jwt.return_value = self.admin_user
        
        mock_registry = MagicMock()
        mock_registry.set_default_model.return_value = False
        mock_get_registry.return_value = mock_registry
        
        # Make the request
        response = self.client.put(
            "/admin/llm/default",
            json={"provider": "openai", "model_id": "nonexistent"}
        )
        
        # Check the response
        self.assertEqual(response.status_code, 400)
        
        # Parse the response
        data = response.json()
        
        # Check the response data
        self.assertEqual(data["detail"], "Model 'nonexistent' not found for provider 'openai'")

    @patch("backend.api.routes.admin.llm.verify_jwt")
    def test_unauthorized_access(self, mock_verify_jwt):
        """Test unauthorized access to the endpoints."""
        # Set up the mock to return a non-admin user
        mock_verify_jwt.return_value = self.non_admin_user
        
        # Make the request to GET /admin/llm
        response = self.client.get("/admin/llm")
        
        # Check the response
        self.assertEqual(response.status_code, 403)
        
        # Make the request to PUT /admin/llm/default
        response = self.client.put(
            "/admin/llm/default",
            json={"provider": "openai", "model_id": "gpt-3.5-turbo"}
        )
        
        # Check the response
        self.assertEqual(response.status_code, 403)

    @patch("backend.api.routes.admin.llm.verify_jwt")
    def test_unauthenticated_access(self, mock_verify_jwt):
        """Test unauthenticated access to the endpoints."""
        # Set up the mock to return an unauthenticated user
        mock_verify_jwt.return_value = self.unauthenticated_user
        
        # Make the request to GET /admin/llm
        response = self.client.get("/admin/llm")
        
        # Check the response
        self.assertEqual(response.status_code, 401)
        
        # Make the request to PUT /admin/llm/default
        response = self.client.put(
            "/admin/llm/default",
            json={"provider": "openai", "model_id": "gpt-3.5-turbo"}
        )
        
        # Check the response
        self.assertEqual(response.status_code, 401)


if __name__ == "__main__":
    unittest.main()
