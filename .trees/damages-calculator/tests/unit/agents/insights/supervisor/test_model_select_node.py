"""
Unit tests for the model select node.
"""

import unittest
from unittest.mock import MagicMock, patch

from src.pi_lawyer.agents.insights.supervisor.nodes import model_select_node
from src.pi_lawyer.shared.core.llm.admin.registry import LLMRegistry
from src.pi_lawyer.shared.core.llm.config import LLMProvider


class TestModelSelectNode(unittest.IsolatedAsyncioTestCase):
    """Test the model select node."""

    async def test_model_select_node_with_default_model(self):
        """Test the model select node with a default model."""
        # Create a mock registry
        mock_registry = MagicMock(spec=LLMRegistry)
        mock_registry.get_default_model.return_value = "gpt-4o"
        
        # Create a mock state
        state = {
            "llm_config": {
                "provider": "openai"
            },
            "memory": {}
        }
        
        # Create a mock config
        config = {
            "timestamp": "2023-01-01T00:00:00Z"
        }
        
        # Patch the get_llm_registry function
        with patch("src.pi_lawyer.agents.insights.supervisor.nodes.get_llm_registry", return_value=mock_registry):
            # Call the node
            result = await model_select_node(state, config)
            
            # Check that the registry was called
            mock_registry.get_default_model.assert_called_once_with("openai")
            
            # Check that the state was updated
            self.assertEqual(result["llm_config"]["model"], "gpt-4o")
            self.assertEqual(result["llm_config"]["provider"], "openai")
            
            # Check that the model selection was stored in memory
            self.assertIn("model_selections", result["memory"])
            self.assertEqual(len(result["memory"]["model_selections"]), 1)
            self.assertEqual(result["memory"]["model_selections"][0]["provider"], "openai")
            self.assertEqual(result["memory"]["model_selections"][0]["model"], "gpt-4o")
            self.assertEqual(result["memory"]["model_selections"][0]["timestamp"], "2023-01-01T00:00:00Z")
            self.assertEqual(result["memory"]["model_selections"][0]["node"], "model_select_node")

    async def test_model_select_node_with_enum_provider(self):
        """Test the model select node with an enum provider."""
        # Create a mock registry
        mock_registry = MagicMock(spec=LLMRegistry)
        mock_registry.get_default_model.return_value = "claude-3-opus"
        
        # Create a mock state
        state = {
            "llm_config": {
                "provider": LLMProvider.ANTHROPIC
            },
            "memory": {}
        }
        
        # Create a mock config
        config = {}
        
        # Patch the get_llm_registry function
        with patch("src.pi_lawyer.agents.insights.supervisor.nodes.get_llm_registry", return_value=mock_registry):
            # Call the node
            result = await model_select_node(state, config)
            
            # Check that the registry was called
            mock_registry.get_default_model.assert_called_once_with("anthropic")
            
            # Check that the state was updated
            self.assertEqual(result["llm_config"]["model"], "claude-3-opus")
            self.assertEqual(result["llm_config"]["provider"], LLMProvider.ANTHROPIC)

    async def test_model_select_node_with_no_default_model(self):
        """Test the model select node with no default model."""
        # Create a mock registry
        mock_registry = MagicMock(spec=LLMRegistry)
        mock_registry.get_default_model.return_value = None
        
        # Create a mock state
        state = {
            "llm_config": {
                "provider": "openai"
            },
            "memory": {}
        }
        
        # Create a mock config
        config = {}
        
        # Patch the get_llm_registry function and os.getenv
        with patch("src.pi_lawyer.agents.insights.supervisor.nodes.get_llm_registry", return_value=mock_registry), \
             patch("src.pi_lawyer.agents.insights.supervisor.nodes.os.getenv") as mock_getenv:
            # Set up the mock getenv
            mock_getenv.side_effect = lambda key, default=None: {
                "FALLBACK_LLM_MODEL": "gpt-3.5-turbo",
                "FALLBACK_LLM_PROVIDER": "openai"
            }.get(key, default)
            
            # Call the node
            result = await model_select_node(state, config)
            
            # Check that the registry was called
            mock_registry.get_default_model.assert_called_once_with("openai")
            
            # Check that the state was updated with fallback values
            self.assertEqual(result["llm_config"]["model"], "gpt-3.5-turbo")
            self.assertEqual(result["llm_config"]["provider"], "openai")
            
            # Check that the model selection was stored in memory
            self.assertIn("model_selections", result["memory"])
            self.assertEqual(len(result["memory"]["model_selections"]), 1)
            self.assertEqual(result["memory"]["model_selections"][0]["provider"], "openai")
            self.assertEqual(result["memory"]["model_selections"][0]["model"], "gpt-3.5-turbo")
            self.assertTrue(result["memory"]["model_selections"][0]["fallback"])

    async def test_model_select_node_with_no_llm_config(self):
        """Test the model select node with no llm_config in state."""
        # Create a mock registry
        mock_registry = MagicMock(spec=LLMRegistry)
        mock_registry.get_default_model.return_value = "gpt-4o"
        
        # Create a mock state with no llm_config
        state = {
            "memory": {}
        }
        
        # Create a mock config
        config = {}
        
        # Patch the get_llm_registry function
        with patch("src.pi_lawyer.agents.insights.supervisor.nodes.get_llm_registry", return_value=mock_registry):
            # Call the node
            result = await model_select_node(state, config)
            
            # Check that the state was updated with a new llm_config
            self.assertIn("llm_config", result)
            
            # The provider should be the default "openai"
            mock_registry.get_default_model.assert_called_once_with("openai")
            
            # Check that the model was set
            self.assertEqual(result["llm_config"]["model"], "gpt-4o")
            self.assertEqual(result["llm_config"]["provider"], "openai")

    async def test_model_select_node_with_no_memory(self):
        """Test the model select node with no memory in state."""
        # Create a mock registry
        mock_registry = MagicMock(spec=LLMRegistry)
        mock_registry.get_default_model.return_value = "gpt-4o"
        
        # Create a mock state with no memory
        state = {
            "llm_config": {
                "provider": "openai"
            }
        }
        
        # Create a mock config
        config = {}
        
        # Patch the get_llm_registry function
        with patch("src.pi_lawyer.agents.insights.supervisor.nodes.get_llm_registry", return_value=mock_registry):
            # Call the node
            result = await model_select_node(state, config)
            
            # Check that the state was updated
            self.assertEqual(result["llm_config"]["model"], "gpt-4o")
            self.assertEqual(result["llm_config"]["provider"], "openai")
            
            # Check that no memory was added
            self.assertNotIn("memory", result)


if __name__ == "__main__":
    unittest.main()
