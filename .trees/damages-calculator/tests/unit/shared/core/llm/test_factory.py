"""
Unit tests for the LLM factory.
"""

from unittest.mock import <PERSON><PERSON>ock, patch

import pytest

from src.pi_lawyer.shared.core.llm.config import LLMConfig, LLMProvider
from src.pi_lawyer.shared.core.llm.factory import LLMFactory, get_llm_client


class TestLLMFactory:
    """Tests for the LLMFactory class."""

    def test_singleton(self):
        """Test that LLMFactory is a singleton."""
        factory1 = LLMFactory()
        factory2 = LLMFactory()

        assert factory1 is factory2

    def test_get_client_different_providers(self):
        """Test getting clients for different providers."""
        # Arrange
        factory = LLMFactory()

        # Clear the cache to ensure clean test
        factory._clients = {}

        # Create configs for different providers
        openai_config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")
        anthropic_config = LLMConfig(provider=LLMProvider.ANTHROPIC, model="claude-3-opus")

        # Act - Get clients with mocked _create_client
        with patch.object(LLMFactory, '_create_client') as mock_create:
            # Set up the mock to return different values for different calls
            mock_openai_client = MagicMock(name="openai_client")
            mock_anthropic_client = MagicMock(name="anthropic_client")

            # Configure the mock to return different clients based on provider
            def side_effect(config):
                if config.provider == LLMProvider.OPENAI:
                    return mock_openai_client
                elif config.provider == LLMProvider.ANTHROPIC:
                    return mock_anthropic_client
                return MagicMock()

            mock_create.side_effect = side_effect

            # Get clients
            openai_client = factory.get_client(openai_config)
            anthropic_client = factory.get_client(anthropic_config)

            # Assert
            assert openai_client is mock_openai_client
            assert anthropic_client is mock_anthropic_client
            assert mock_create.call_count == 2

    def test_get_client_invalid_provider(self):
        """Test getting a client with an invalid provider."""
        # Arrange
        factory = LLMFactory()

        # Act & Assert
        with patch.object(LLMFactory, '_create_client') as mock_create:
            mock_create.side_effect = ValueError("Unsupported provider")
            with pytest.raises(ValueError, match="Unsupported provider"):
                factory.get_client(LLMConfig(provider=LLMProvider.OPENAI))

    def test_get_client_caching(self):
        """Test that clients are cached."""
        # Arrange
        factory = LLMFactory()
        factory._clients = {}  # Clear the cache
        config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")

        # Act
        with patch.object(LLMFactory, '_create_client') as mock_create:
            mock_client = MagicMock(name="cached_client")
            mock_create.return_value = mock_client

            # Get the client twice
            client1 = factory.get_client(config)

            # Verify the client was created
            assert client1 is mock_client
            assert mock_create.call_count == 1

            # Get the client again - should use cache
            client2 = factory.get_client(config)

            # Assert
            assert client2 is client1  # Same client returned
            assert mock_create.call_count == 1  # No additional calls to create

    def test_get_client_force_new(self):
        """Test forcing creation of a new client."""
        # Arrange
        factory = LLMFactory()
        factory._clients = {}  # Clear the cache
        config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")

        # Act
        with patch.object(LLMFactory, '_create_client') as mock_create:
            mock_client1 = MagicMock(name="client1")
            mock_client2 = MagicMock(name="client2")
            mock_create.side_effect = [mock_client1, mock_client2]

            # Get the client
            client1 = factory.get_client(config)

            # Get the client again with force_new=True
            client2 = factory.get_client(config, force_new=True)

            # Assert
            assert client1 is mock_client1
            assert client2 is mock_client2
            assert client1 is not client2
            assert mock_create.call_count == 2

    def test_get_llm_client(self):
        """Test the get_llm_client function."""
        # Arrange
        config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")

        # Act
        with patch.object(LLMFactory, 'get_client') as mock_get_client:
            mock_client = MagicMock(name="client")
            mock_get_client.return_value = mock_client

            # Call the function
            client = get_llm_client(config)

            # Assert
            assert client is mock_client
            mock_get_client.assert_called_once_with(config, False)
