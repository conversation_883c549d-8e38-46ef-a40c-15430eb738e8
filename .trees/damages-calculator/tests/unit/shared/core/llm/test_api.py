"""
Unit tests for the LLM API.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest

from src.pi_lawyer.shared.core.llm.api import generate_chat, generate_text
from src.pi_lawyer.shared.core.llm.base import LLMError
from src.pi_lawyer.shared.core.llm.config import LLMConfig, LLMMessage, LLMProvider


class TestLLMAPI:
    """Tests for the LLM API functions."""
    
    @pytest.mark.asyncio
    @patch('src.pi_lawyer.shared.core.llm.api.get_llm_client')
    async def test_generate_text(self, mock_get_client):
        """Test generating text."""
        # Arrange
        mock_client = MagicMock()
        mock_client.generate = AsyncMock(return_value={
            "text": "This is a test response",
            "model": "test-model",
            "usage": {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15},
            "finish_reason": "stop"
        })
        mock_get_client.return_value = mock_client
        
        config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")
        
        # Act
        response = await generate_text("Test prompt", config)
        
        # Assert
        assert response.content == "This is a test response"
        assert response.model == "test-model"
        assert response.usage == {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        assert response.finish_reason == "stop"
        mock_client.generate.assert_called_once_with(
            prompt="Test prompt",
            model="gpt-4",
            temperature=0.7,
            max_tokens=1000,
            stop_sequences=None
        )
    
    @pytest.mark.asyncio
    @patch('src.pi_lawyer.shared.core.llm.api.get_llm_client')
    async def test_generate_chat(self, mock_get_client):
        """Test generating chat responses."""
        # Arrange
        mock_client = MagicMock()
        mock_client.chat = AsyncMock(return_value={
            "text": "This is a test response",
            "model": "test-model",
            "usage": {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15},
            "finish_reason": "stop"
        })
        mock_get_client.return_value = mock_client
        
        config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")
        messages = [
            LLMMessage(role="system", content="You are a helpful assistant"),
            LLMMessage(role="user", content="Test message")
        ]
        
        # Act
        response = await generate_chat(messages, config)
        
        # Assert
        assert response.content == "This is a test response"
        assert response.model == "test-model"
        assert response.usage == {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        assert response.finish_reason == "stop"
        mock_client.chat.assert_called_once_with(
            messages=[
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Test message"}
            ],
            model="gpt-4",
            temperature=0.7,
            max_tokens=1000,
            stop_sequences=None
        )
    
    @pytest.mark.asyncio
    @patch('src.pi_lawyer.shared.core.llm.api.get_llm_client')
    async def test_generate_text_with_fallback(self, mock_get_client):
        """Test generating text with fallback."""
        # Arrange
        mock_primary_client = MagicMock()
        mock_primary_client.generate = AsyncMock(side_effect=LLMError("Test error"))
        
        mock_fallback_client = MagicMock()
        mock_fallback_client.generate = AsyncMock(return_value={
            "text": "This is a fallback response",
            "model": "fallback-model",
            "usage": {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15},
            "finish_reason": "stop"
        })
        
        mock_get_client.side_effect = [mock_primary_client, mock_fallback_client]
        
        primary_config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")
        fallback_config = LLMConfig(provider=LLMProvider.MOCK, model="mock-model")
        
        # Act
        response = await generate_text(
            "Test prompt",
            primary_config,
            fallback_configs=[fallback_config]
        )
        
        # Assert
        assert response.content == "This is a fallback response"
        assert response.model == "fallback-model"
        mock_primary_client.generate.assert_called_once()
        mock_fallback_client.generate.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.pi_lawyer.shared.core.llm.api.get_llm_client')
    async def test_generate_text_all_fail(self, mock_get_client):
        """Test generating text when all providers fail."""
        # Arrange
        mock_primary_client = MagicMock()
        mock_primary_client.generate = AsyncMock(side_effect=LLMError("Primary error"))
        
        mock_fallback_client = MagicMock()
        mock_fallback_client.generate = AsyncMock(side_effect=LLMError("Fallback error"))
        
        mock_get_client.side_effect = [mock_primary_client, mock_fallback_client]
        
        primary_config = LLMConfig(provider=LLMProvider.OPENAI, model="gpt-4")
        fallback_config = LLMConfig(provider=LLMProvider.MOCK, model="mock-model")
        
        # Act & Assert
        with pytest.raises(LLMError, match="Primary error"):
            await generate_text(
                "Test prompt",
                primary_config,
                fallback_configs=[fallback_config]
            )
        
        mock_primary_client.generate.assert_called_once()
        mock_fallback_client.generate.assert_called_once()
