"""
Tests for the LLM selections functionality.
"""

import pytest


class TestLLMSelectionsLogic:
    """Test the LLM selections logic and data structures."""

    def test_agent_definitions_structure(self):
        """Test that agent definitions are properly structured."""
        # Define the expected agent structure locally for testing
        AGENT_DEFINITIONS = {
            'researchAgent': {
                'displayName': 'Research Agent',
                'icon': '🔍',
                'nodes': {
                    'query_gen': 'Query Generation',
                    'rerank': 'Result Reranking',
                    'exit_guard': 'Exit Guard'
                }
            },
            'caseCrudAgent': {
                'displayName': 'Case & Client CRUD Agent',
                'icon': '⚖️',
                'nodes': {
                    'router': 'Router',
                    'create_case': 'Create Case',
                    'read_case': 'Read Case',
                    'update_case': 'Update Case',
                    'delete_case': 'Delete Case',
                    'create_client': 'Create Client',
                    'read_client': 'Read Client',
                    'update_client': 'Update Client',
                    'delete_client': 'Delete Client'
                }
            }
        }

        # Verify all required agents are defined
        required_agents = ['researchAgent', 'caseCrudAgent']

        for agent in required_agents:
            assert agent in AGENT_DEFINITIONS
            assert 'displayName' in AGENT_DEFINITIONS[agent]
            assert 'icon' in AGENT_DEFINITIONS[agent]
            assert 'nodes' in AGENT_DEFINITIONS[agent]

    def test_case_crud_agent_nodes(self):
        """Test that caseCrudAgent has all required nodes."""
        case_crud_nodes = {
            'router': 'Router',
            'create_case': 'Create Case',
            'read_case': 'Read Case',
            'update_case': 'Update Case',
            'delete_case': 'Delete Case',
            'create_client': 'Create Client',
            'read_client': 'Read Client',
            'update_client': 'Update Client',
            'delete_client': 'Delete Client'
        }

        required_nodes = [
            'router',
            'create_case',
            'read_case',
            'update_case',
            'delete_case',
            'create_client',
            'read_client',
            'update_client',
            'delete_client'
        ]

        for node in required_nodes:
            assert node in case_crud_nodes
            assert isinstance(case_crud_nodes[node], str)  # Display name

    def test_research_agent_nodes(self):
        """Test that researchAgent has all required nodes."""
        research_nodes = {
            'query_gen': 'Query Generation',
            'rerank': 'Result Reranking',
            'exit_guard': 'Exit Guard'
        }

        required_nodes = ['query_gen', 'rerank', 'exit_guard']

        for node in required_nodes:
            assert node in research_nodes
            assert isinstance(research_nodes[node], str)  # Display name

    @pytest.mark.asyncio
    async def test_get_selections_structure(self):
        """Test the structure of the GET /api/admin/llm-selections response."""
        # This would be a more comprehensive test with actual API calls
        # For now, we'll test the data structure

        expected_agent_config_keys = [
            'name', 'displayName', 'icon', 'nodes',
            'currentModel', 'temperature', 'isConfigured', 'inheritsFrom'
        ]

        expected_node_config_keys = [
            'name', 'displayName', 'currentModel', 'temperature',
            'isConfigured', 'inheritsFrom'
        ]

        # These keys should be present in the response structure
        assert len(expected_agent_config_keys) == 8
        assert len(expected_node_config_keys) == 6

    def test_model_string_parsing(self):
        """Test model string parsing functionality."""
        def parseModelString(modelString: str):
            """Parse model string into provider and model parts."""
            provider, *modelParts = modelString.split('/')
            return {
                'provider': provider,
                'model': '/'.join(modelParts)
            }

        test_cases = [
            ('openai/gpt-4o', {'provider': 'openai', 'model': 'gpt-4o'}),
            ('anthropic/claude-3-sonnet', {'provider': 'anthropic', 'model': 'claude-3-sonnet'}),
            ('gemini/gemini-2.0-flash', {'provider': 'gemini', 'model': 'gemini-2.0-flash'}),
            ('groq/voyage-large-turbo', {'provider': 'groq', 'model': 'voyage-large-turbo'})
        ]

        for model_string, expected in test_cases:
            result = parseModelString(model_string)
            assert result == expected

    def test_format_model_display_name(self):
        """Test model display name formatting."""
        def formatModelDisplayName(modelString: str):
            """Format model string for display."""
            provider, *modelParts = modelString.split('/')
            model = '/'.join(modelParts)

            providerNames = {
                'openai': 'OpenAI',
                'anthropic': 'Anthropic',
                'google': 'Google',
                'gemini': 'Gemini',
                'groq': 'Groq',
                'cohere': 'Cohere',
                'mistral': 'Mistral'
            }

            return f"{providerNames.get(provider, provider)} {model}"

        test_cases = [
            ('openai/gpt-4o', 'OpenAI gpt-4o'),
            ('anthropic/claude-3-sonnet', 'Anthropic claude-3-sonnet'),
            ('gemini/gemini-2.0-flash', 'Gemini gemini-2.0-flash'),
            ('unknown/some-model', 'unknown some-model')
        ]

        for model_string, expected in test_cases:
            result = formatModelDisplayName(model_string)
            assert result == expected

    def test_temperature_validation(self):
        """Test temperature value validation."""
        # Temperature should be between 0 and 2
        valid_temperatures = [0.0, 0.2, 0.5, 1.0, 1.5, 2.0]
        invalid_temperatures = [-0.1, 2.1, -1.0, 3.0]

        for temp in valid_temperatures:
            assert 0.0 <= temp <= 2.0

        for temp in invalid_temperatures:
            assert not (0.0 <= temp <= 2.0)

    def test_agent_inheritance_logic(self):
        """Test the inheritance logic for nodes."""
        # Node should inherit from agent if agent is configured but node is not
        # Node should inherit from default if neither agent nor node is configured

        test_scenarios = [
            {
                'agent_configured': True,
                'node_configured': False,
                'expected_inherit_from': 'agent'
            },
            {
                'agent_configured': False,
                'node_configured': False,
                'expected_inherit_from': 'default'
            },
            {
                'agent_configured': True,
                'node_configured': True,
                'expected_inherit_from': None  # Node has its own config
            }
        ]

        for scenario in test_scenarios:
            if scenario['node_configured']:
                assert scenario['expected_inherit_from'] is None
            elif scenario['agent_configured']:
                assert scenario['expected_inherit_from'] == 'agent'
            else:
                assert scenario['expected_inherit_from'] == 'default'


if __name__ == '__main__':
    pytest.main([__file__])
