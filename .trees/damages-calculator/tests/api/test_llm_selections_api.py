"""
Comprehensive tests for the LLM selections API endpoints.
"""

from unittest.mock import MagicMock, patch

import pytest


class TestLLMSelectionsAPI:
    """Test the LLM selections API endpoints with mocked dependencies."""

    @pytest.fixture
    def mock_supabase_client(self):
        """Mock Supabase client for testing."""
        mock_client = MagicMock()
        mock_session = MagicMock()
        mock_session.user = MagicMock()
        mock_session.user.id = "test-user-id"
        mock_session.user.email = "<EMAIL>"

        mock_client.auth.getSession.return_value = ({"data": {"session": mock_session}, "error": None})
        mock_client.schema.return_value = mock_client
        mock_client.from_.return_value = mock_client
        mock_client.select.return_value = mock_client
        mock_client.eq.return_value = mock_client
        mock_client.is_.return_value = mock_client
        mock_client.upsert.return_value = mock_client
        mock_client.single.return_value = mock_client
        mock_client.delete.return_value = mock_client

        return mock_client

    @pytest.fixture
    def mock_auth_functions(self):
        """Mock authentication functions."""
        with patch('frontend.src.app.api.admin.llm_selections.route.isAuthenticated') as mock_is_auth, \
             patch('frontend.src.app.api.admin.llm_selections.route.hasRole') as mock_has_role:
            mock_is_auth.return_value = True
            mock_has_role.return_value = True
            yield mock_is_auth, mock_has_role

    def test_agent_definitions_completeness(self):
        """Test that all required agents are defined with proper structure."""
        # This would normally import from the actual route file
        # For testing purposes, we'll define the expected structure
        expected_agents = {
            'researchAgent': {
                'displayName': 'Research Agent',
                'icon': '🔍',
                'nodes': ['query_gen', 'rerank', 'exit_guard']
            },
            'intakeAgent': {
                'displayName': 'Intake Agent',
                'icon': '📝',
                'nodes': []
            },
            'supervisorAgent': {
                'displayName': 'Supervisor Agent',
                'icon': '👥',
                'nodes': []
            },
            'calendarCrudAgent': {
                'displayName': 'Calendar CRUD Agent',
                'icon': '📅',
                'nodes': []
            },
            'taskCrudAgent': {
                'displayName': 'Task CRUD Agent',
                'icon': '📋',
                'nodes': []
            },
            'caseCrudAgent': {
                'displayName': 'Case & Client CRUD Agent',
                'icon': '⚖️',
                'nodes': [
                    'router', 'create_case', 'read_case', 'update_case', 'delete_case',
                    'create_client', 'read_client', 'update_client', 'delete_client'
                ]
            }
        }

        for agent_name, agent_info in expected_agents.items():
            assert 'displayName' in agent_info
            assert 'icon' in agent_info
            assert 'nodes' in agent_info
            assert isinstance(agent_info['nodes'], list)

    @pytest.mark.asyncio
    async def test_get_selections_success(self, mock_supabase_client, mock_auth_functions):
        """Test successful GET request to /api/admin/llm-selections."""
        # Mock database response
        mock_selections = [
            {
                'id': 'test-id-1',
                'tenant_id': '*',
                'agent': 'researchAgent',
                'node': 'query_gen',
                'model_name': 'groq/voyage-large-turbo',
                'temperature': 0.2
            },
            {
                'id': 'test-id-2',
                'tenant_id': '*',
                'agent': 'caseCrudAgent',
                'node': None,
                'model_name': 'gemini/gemini-2.0-flash',
                'temperature': 0.2
            }
        ]

        mock_supabase_client.data = mock_selections
        mock_supabase_client.error = None

        # The actual test would make an HTTP request to the endpoint
        # For now, we'll test the data structure expectations
        assert len(mock_selections) == 2
        assert mock_selections[0]['agent'] == 'researchAgent'
        assert mock_selections[0]['node'] == 'query_gen'
        assert mock_selections[1]['agent'] == 'caseCrudAgent'
        assert mock_selections[1]['node'] is None

    @pytest.mark.asyncio
    async def test_post_selection_success(self, mock_supabase_client, mock_auth_functions):
        """Test successful POST request to create/update LLM selection."""
        request_data = {
            'tenant_id': '*',
            'agent': 'caseCrudAgent',
            'node': 'router',
            'model_name': 'anthropic/claude-3-sonnet',
            'temperature': 0.3
        }

        mock_response = {
            'id': 'new-id',
            **request_data
        }

        mock_supabase_client.data = mock_response
        mock_supabase_client.error = None

        # Test data validation
        assert request_data['tenant_id'] == '*'
        assert request_data['agent'] == 'caseCrudAgent'
        assert request_data['node'] == 'router'
        assert request_data['model_name'] == 'anthropic/claude-3-sonnet'
        assert 0.0 <= request_data['temperature'] <= 2.0

    @pytest.mark.asyncio
    async def test_post_selection_validation_errors(self):
        """Test POST request validation errors."""
        invalid_requests = [
            # Missing required fields
            {'agent': 'caseCrudAgent'},
            {'tenant_id': '*'},
            {'tenant_id': '*', 'agent': 'caseCrudAgent'},

            # Invalid agent
            {
                'tenant_id': '*',
                'agent': 'invalidAgent',
                'model_name': 'openai/gpt-4'
            },

            # Invalid node for agent
            {
                'tenant_id': '*',
                'agent': 'intakeAgent',
                'node': 'invalid_node',
                'model_name': 'openai/gpt-4'
            },

            # Invalid temperature
            {
                'tenant_id': '*',
                'agent': 'caseCrudAgent',
                'model_name': 'openai/gpt-4',
                'temperature': 3.0  # Too high
            }
        ]

        for invalid_request in invalid_requests:
            # Each of these should fail validation
            if 'agent' not in invalid_request:
                assert False, "Missing agent should fail validation"
            elif 'tenant_id' not in invalid_request:
                assert False, "Missing tenant_id should fail validation"
            elif 'model_name' not in invalid_request:
                assert False, "Missing model_name should fail validation"
            elif invalid_request.get('agent') == 'invalidAgent':
                assert False, "Invalid agent should fail validation"
            elif invalid_request.get('temperature', 0) > 2.0:
                assert False, "Invalid temperature should fail validation"

    @pytest.mark.asyncio
    async def test_delete_selection_success(self, mock_supabase_client, mock_auth_functions):
        """Test successful DELETE request to remove LLM selection."""
        query_params = {
            'tenant_id': '*',
            'agent': 'caseCrudAgent',
            'node': 'router'
        }

        mock_supabase_client.data = [{'id': 'deleted-id'}]
        mock_supabase_client.error = None

        # Test query parameter validation
        assert query_params['tenant_id'] == '*'
        assert query_params['agent'] == 'caseCrudAgent'
        assert query_params['node'] == 'router'

    def test_inheritance_hierarchy_logic(self):
        """Test the inheritance hierarchy logic for model selection."""
        test_cases = [
            {
                'description': 'Node-specific configuration takes precedence',
                'node_config': 'anthropic/claude-3-sonnet',
                'agent_config': 'gemini/gemini-2.0-flash',
                'global_config': 'openai/gpt-3.5-turbo',
                'expected': 'anthropic/claude-3-sonnet'
            },
            {
                'description': 'Agent configuration when no node config',
                'node_config': None,
                'agent_config': 'gemini/gemini-2.0-flash',
                'global_config': 'openai/gpt-3.5-turbo',
                'expected': 'gemini/gemini-2.0-flash'
            },
            {
                'description': 'Global configuration as fallback',
                'node_config': None,
                'agent_config': None,
                'global_config': 'openai/gpt-3.5-turbo',
                'expected': 'openai/gpt-3.5-turbo'
            }
        ]

        for case in test_cases:
            # Simulate the inheritance logic
            result = (case['node_config'] or
                     case['agent_config'] or
                     case['global_config'])

            assert result == case['expected'], f"Failed: {case['description']}"

    def test_model_string_validation(self):
        """Test model string format validation."""
        valid_models = [
            'openai/gpt-4o',
            'anthropic/claude-3-sonnet',
            'gemini/gemini-2.0-flash',
            'groq/voyage-large-turbo',
            'cohere/command-r-plus',
            'mistral/mistral-large'
        ]

        invalid_models = [
            'invalid-format',
            'openai/',
            '/gpt-4',
            '',
            'openai/gpt-4/extra'
        ]

        for model in valid_models:
            parts = model.split('/')
            assert len(parts) >= 2, f"Valid model {model} should have provider/model format"
            assert parts[0], f"Provider should not be empty in {model}"
            assert parts[1], f"Model should not be empty in {model}"

        for model in invalid_models:
            parts = model.split('/')
            if len(parts) < 2 or not parts[0] or not parts[1]:
                # This should be considered invalid
                assert True, f"Invalid model {model} correctly identified"

    def test_temperature_range_validation(self):
        """Test temperature value validation."""
        valid_temperatures = [0.0, 0.1, 0.2, 0.5, 1.0, 1.5, 2.0]
        invalid_temperatures = [-0.1, -1.0, 2.1, 3.0, 10.0]

        for temp in valid_temperatures:
            assert 0.0 <= temp <= 2.0, f"Temperature {temp} should be valid"

        for temp in invalid_temperatures:
            assert not (0.0 <= temp <= 2.0), f"Temperature {temp} should be invalid"

    @pytest.mark.asyncio
    async def test_authentication_required(self):
        """Test that authentication is required for all endpoints."""
        # Mock unauthenticated request
        with patch('frontend.src.app.api.admin.llm_selections.route.isAuthenticated') as mock_auth:
            mock_auth.return_value = False

            # All endpoints should return 401 for unauthenticated requests
            assert not mock_auth.return_value

    @pytest.mark.asyncio
    async def test_superadmin_role_required(self):
        """Test that superadmin role is required for all endpoints."""
        # Mock authenticated but non-superadmin user
        with patch('frontend.src.app.api.admin.llm_selections.route.hasRole') as mock_role:
            mock_role.return_value = False

            # All endpoints should return 403 for non-superadmin users
            assert not mock_role.return_value

    def test_tenant_isolation(self):
        """Test that tenant isolation is properly implemented."""
        tenant_selections = {
            '*': [
                {'agent': 'researchAgent', 'model': 'openai/gpt-4'},
                {'agent': 'caseCrudAgent', 'model': 'gemini/gemini-2.0-flash'}
            ],
            'tenant1': [
                {'agent': 'researchAgent', 'model': 'anthropic/claude-3-sonnet'}
            ],
            'tenant2': [
                {'agent': 'caseCrudAgent', 'model': 'openai/gpt-4o'}
            ]
        }

        # Each tenant should only see their own selections plus global
        for tenant_id, selections in tenant_selections.items():
            for selection in selections:
                # Verify tenant-specific data doesn't leak
                assert 'agent' in selection
                assert 'model' in selection


if __name__ == '__main__':
    pytest.main([__file__])
