"""
Tests for capability-aware operation handling in calendar providers.

This module tests the capability-aware operation handling in calendar providers,
specifically focusing on the Calendly provider which has limited capabilities.
"""

from enum import Enum

import pytest


# Mock the necessary classes and functions
class ProviderCapability(str, Enum):
    """Mock of ProviderCapability enum."""
    CREATE_EVENT = "create_event"
    READ_EVENT = "read_event"
    UPDATE_EVENT = "update_event"
    DELETE_EVENT = "delete_event"
    FREE_BUSY = "free_busy"
    LIST_CALENDARS = "list_calendars"
    WEBHOOK_SUPPORT = "webhook_support"


class UnsupportedOperationError(Exception):
    """Mock of UnsupportedOperationError exception."""
    pass


# Mock Calendly provider class
class MockCalendlyProvider:
    """Mock of CalendlyProvider class."""
    
    @property
    def provider_id(self):
        return "calendly"
    
    @property
    def provider_name(self):
        return "Calendly"
    
    @property
    def capabilities(self):
        return [
            ProviderCapability.READ_EVENT,
            ProviderCapability.LIST_CALENDARS,
            ProviderCapability.FREE_BUSY,
            ProviderCapability.WEBHOOK_SUPPORT
        ]
    
    def has_capability(self, capability):
        """Check if this provider supports a capability."""
        capability_name = capability.value if isinstance(capability, ProviderCapability) else capability
        return any(cap.value == capability_name for cap in self.capabilities)
    
    def require_capability(self, capability):
        """Require that this provider supports a capability."""
        capability_name = capability.value if isinstance(capability, ProviderCapability) else capability
        if not self.has_capability(capability_name):
            raise UnsupportedOperationError(
                f"The operation '{capability_name}' is not supported by the provider '{self.provider_name}'"
            )
    
    async def create_event(self, firm_id, calendar_id, event):
        """Create a new event in the calendar."""
        self.require_capability(ProviderCapability.CREATE_EVENT)
        # This line should never be reached because the capability check will fail
        return {"id": "new-event-id"}
    
    async def update_event(self, firm_id, calendar_id, event_id, event):
        """Update an existing event in the calendar."""
        self.require_capability(ProviderCapability.UPDATE_EVENT)
        # This line should never be reached because the capability check will fail
        return {"id": event_id}
    
    async def delete_event(self, firm_id, calendar_id, event_id):
        """Delete an event from the calendar."""
        self.require_capability(ProviderCapability.DELETE_EVENT)
        # This line should never be reached because the capability check will fail
        return True
    
    async def get_event(self, firm_id, calendar_id, event_id):
        """Get an event from the calendar."""
        self.require_capability(ProviderCapability.READ_EVENT)
        # This should succeed because READ_EVENT is supported
        return {"id": event_id}
    
    async def check_free_busy(self, firm_id, request):
        """Check free/busy times."""
        self.require_capability(ProviderCapability.FREE_BUSY)
        # This should succeed because FREE_BUSY is supported
        return {"busy": []}


class TestCalendlyProviderOperations:
    """Tests for capability-aware operation handling in the Calendly provider."""
    
    @pytest.mark.asyncio
    async def test_unsupported_operations(self):
        """Test that unsupported operations raise UnsupportedOperationError."""
        provider = MockCalendlyProvider()
        
        # Test create_event (not supported)
        with pytest.raises(UnsupportedOperationError) as excinfo:
            await provider.create_event("firm_id", "calendar_id", {})
        assert "create_event" in str(excinfo.value)
        assert "Calendly" in str(excinfo.value)
        
        # Test update_event (not supported)
        with pytest.raises(UnsupportedOperationError) as excinfo:
            await provider.update_event("firm_id", "calendar_id", "event_id", {})
        assert "update_event" in str(excinfo.value)
        assert "Calendly" in str(excinfo.value)
        
        # Test delete_event (not supported)
        with pytest.raises(UnsupportedOperationError) as excinfo:
            await provider.delete_event("firm_id", "calendar_id", "event_id")
        assert "delete_event" in str(excinfo.value)
        assert "Calendly" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_supported_operations(self):
        """Test that supported operations do not raise UnsupportedOperationError."""
        provider = MockCalendlyProvider()
        
        # Test get_event (supported)
        result = await provider.get_event("firm_id", "calendar_id", "event_id")
        assert result["id"] == "event_id"
        
        # Test check_free_busy (supported)
        result = await provider.check_free_busy("firm_id", {})
        assert "busy" in result
