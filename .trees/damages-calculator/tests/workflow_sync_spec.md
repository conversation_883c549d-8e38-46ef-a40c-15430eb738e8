# Terraform Sync Workflow Test Specification

## Overview

This document defines the test cases and validation criteria for the Terraform State Sync workflow (`terraform-sync.yml`). The workflow is designed to maintain infrastructure state consistency and apply monitoring configurations.

## Test Categories

### 1. State Sync Job Tests

#### T1.1: No Changes Detected
**Objective**: Verify workflow handles infrastructure that's already in sync.

**Preconditions**:
- Infrastructure is deployed and in desired state
- No manual changes made to resources
- Terraform state matches actual infrastructure

**Test Steps**:
1. Trigger workflow manually
2. Monitor `state_sync` job execution
3. Verify terraform plan shows no changes

**Success Criteria**:
- [ ] `terraform plan` exits with code 0
- [ ] `changes_detected` output is `false`
- [ ] No `terraform apply` is executed
- [ ] Job completes successfully
- [ ] Workflow summary shows "No changes detected"

**Expected Output**:
```
✅ No changes detected - infrastructure is in sync
```

#### T1.2: Expected Changes Detected (IAM/Monitoring Drift)
**Objective**: Verify workflow correctly handles expected infrastructure drift.

**Preconditions**:
- Simulate IAM policy drift by manual changes
- Or monitoring resource configuration changes
- Changes are in allowed resource types

**Test Steps**:
1. Make manual IAM policy changes in Google Cloud Console
2. Trigger workflow
3. Monitor plan analysis and apply execution

**Success Criteria**:
- [ ] `terraform plan` exits with code 2
- [ ] `changes_detected` output is `true`
- [ ] Plan analysis identifies changes as expected
- [ ] `terraform apply` executes successfully
- [ ] Only IAM/monitoring resources are modified
- [ ] Gateway host output is populated

**Expected Resource Types** (allowed):
- `google_project_iam_*`
- `google_monitoring_*`
- `google_secret_manager_secret`
- `google_api_gateway_*`

#### T1.3: Unexpected Changes Detected
**Objective**: Verify workflow fails safely when unexpected changes are detected.

**Preconditions**:
- Simulate unexpected resource changes
- Changes involve non-IAM/monitoring resources
- `force_apply` is set to `false`

**Test Steps**:
1. Modify terraform configuration to add unexpected resource
2. Trigger workflow without force_apply
3. Monitor failure handling

**Success Criteria**:
- [ ] `terraform plan` exits with code 2
- [ ] Plan analysis identifies unexpected changes
- [ ] Workflow fails with appropriate error message
- [ ] No `terraform apply` is executed
- [ ] Error message suggests using `force_apply=true`

**Expected Output**:
```
❌ Unexpected changes detected in non-IAM/monitoring resources:
  - google_cloud_run_service.example
❌ Unexpected infrastructure changes detected. Use force_apply=true to override.
```

#### T1.4: Force Apply Override
**Objective**: Verify force apply mechanism works correctly.

**Preconditions**:
- Unexpected changes are present
- `force_apply` input is set to `true`

**Test Steps**:
1. Use same setup as T1.3
2. Trigger workflow with `force_apply=true`
3. Monitor override behavior

**Success Criteria**:
- [ ] Plan analysis detects unexpected changes
- [ ] Force apply warning is displayed
- [ ] `terraform apply` executes despite unexpected changes
- [ ] Workflow completes successfully
- [ ] Warning about force apply is logged

#### T1.5: Terraform Plan Failure
**Objective**: Verify workflow handles terraform plan failures gracefully.

**Preconditions**:
- Invalid terraform configuration
- Or authentication issues
- Or state lock conflicts

**Test Steps**:
1. Introduce terraform configuration error
2. Trigger workflow
3. Monitor error handling

**Success Criteria**:
- [ ] `terraform plan` fails with non-zero exit code (not 0 or 2)
- [ ] Workflow fails immediately
- [ ] Error is properly logged
- [ ] No apply attempt is made
- [ ] Subsequent jobs are skipped

### 2. Apply Alerts Job Tests

#### T2.1: Successful Alert Policy Application
**Objective**: Verify alert policies are applied correctly.

**Preconditions**:
- `state_sync` job completed successfully
- Monitoring API is enabled
- Service account has monitoring.admin permissions

**Test Steps**:
1. Ensure `state_sync` completes
2. Monitor `apply_alerts` job execution
3. Verify alert policies in Google Cloud Console

**Success Criteria**:
- [ ] Job depends on successful `state_sync`
- [ ] `apply-alert-policies.sh` script executes
- [ ] Alert policies are created or updated
- [ ] Job completes successfully
- [ ] All 4 expected policies are present

**Expected Alert Policies**:
- [ ] MCP Rules Gateway - High Error Rate
- [ ] MCP Rules Gateway - Circuit Breaker Open
- [ ] MCP Rules Gateway - High Latency
- [ ] MCP Rules Gateway - API Key Quota Exhaustion

#### T2.2: Alert Policy Script Failure
**Objective**: Verify workflow handles alert policy application failures.

**Preconditions**:
- Monitoring API is disabled
- Or insufficient permissions
- Or malformed policy configuration

**Test Steps**:
1. Disable monitoring API or remove permissions
2. Trigger workflow
3. Monitor failure handling

**Success Criteria**:
- [ ] `apply_alerts` job fails
- [ ] Error is properly logged
- [ ] Subsequent `verify_gateway` job is skipped
- [ ] Workflow overall status is failed

#### T2.3: Existing Policies Handling
**Objective**: Verify script handles existing alert policies correctly.

**Preconditions**:
- Alert policies already exist
- Policies may have been manually modified

**Test Steps**:
1. Ensure alert policies exist in project
2. Trigger workflow
3. Monitor policy update behavior

**Success Criteria**:
- [ ] Script detects existing policies
- [ ] Appropriate warnings are logged
- [ ] No duplicate policies are created
- [ ] Job completes successfully
- [ ] Manual update guidance is provided

### 3. Verify Gateway Job Tests

#### T3.1: Successful Gateway Verification
**Objective**: Verify API Gateway health check passes.

**Preconditions**:
- Both `state_sync` and `apply_alerts` completed successfully
- API Gateway is healthy and responding
- Required secrets and API keys are configured

**Test Steps**:
1. Ensure prerequisite jobs complete
2. Monitor `verify_gateway` job execution
3. Check verification script output

**Success Criteria**:
- [ ] Job depends on both prerequisite jobs
- [ ] `verify-api-gateway-prod.sh` executes
- [ ] Health endpoint returns 200
- [ ] MCP endpoint test succeeds
- [ ] Performance test passes
- [ ] Job completes successfully

#### T3.2: Gateway Health Check Failure
**Objective**: Verify workflow detects API Gateway issues.

**Preconditions**:
- API Gateway is down or misconfigured
- Or backend Cloud Run service is unhealthy

**Test Steps**:
1. Simulate gateway failure (stop Cloud Run service)
2. Trigger workflow
3. Monitor verification failure

**Success Criteria**:
- [ ] Health check fails with non-200 response
- [ ] Verification script exits with error
- [ ] `verify_gateway` job fails
- [ ] Workflow overall status is failed
- [ ] Error details are logged

### 4. Integration Tests

#### T4.1: End-to-End Success Flow
**Objective**: Verify complete workflow execution with no issues.

**Test Steps**:
1. Ensure clean infrastructure state
2. Trigger workflow
3. Monitor all three jobs

**Success Criteria**:
- [ ] All three jobs complete successfully
- [ ] Proper job dependencies are respected
- [ ] Workflow summary is generated
- [ ] No errors or warnings (except expected ones)

#### T4.2: Partial Failure Recovery
**Objective**: Verify workflow handles partial failures correctly.

**Test Steps**:
1. Simulate failure in middle job (`apply_alerts`)
2. Trigger workflow
3. Monitor failure propagation

**Success Criteria**:
- [ ] `state_sync` completes successfully
- [ ] `apply_alerts` fails as expected
- [ ] `verify_gateway` is skipped due to dependency failure
- [ ] Workflow status reflects partial failure

### 5. Security and Permissions Tests

#### T5.1: Service Account Permissions
**Objective**: Verify service account has minimal required permissions.

**Test Steps**:
1. Review service account IAM roles
2. Test workflow execution
3. Verify no permission escalation

**Success Criteria**:
- [ ] Service account has only required roles
- [ ] Workflow can access necessary resources
- [ ] No overprivileged access is granted

#### T5.2: Secret Access Validation
**Objective**: Verify secure access to required secrets.

**Test Steps**:
1. Verify GitHub secrets are configured
2. Test secret access during workflow
3. Ensure no secret leakage in logs

**Success Criteria**:
- [ ] `GCP_SA_KEY` is accessible
- [ ] `CLOUD_RUN_URL` is accessible
- [ ] Secrets are not exposed in logs
- [ ] Authentication works correctly

## Test Execution

### Manual Testing
```bash
# Test basic workflow
gh workflow run terraform-sync.yml

# Test force apply
gh workflow run terraform-sync.yml -f force_apply=true

# Monitor workflow
gh run list --workflow=terraform-sync.yml
gh run view RUN_ID
```

### Automated Testing
```bash
# Run terraform validation
cd infra/terraform
terraform init
terraform validate
terraform plan -detailed-exitcode

# Test alert policy script
./scripts/apply-alert-policies.sh

# Test gateway verification
./scripts/verify-api-gateway-prod.sh
```

### Validation Commands
```bash
# Check alert policies
gcloud alpha monitoring policies list --project=texas-laws-personalinjury

# Verify gateway health
curl -s https://GATEWAY_HOST/health

# Check terraform state
terraform state list
terraform show
```

## Test Data and Fixtures

### Required Secrets
- `GCP_SA_KEY`: Valid service account JSON
- `CLOUD_RUN_URL`: Production Cloud Run URL

### Test Environment
- Project: `texas-laws-personalinjury`
- Region: `us-central1`
- Terraform workspace: `default`

### Cleanup Procedures
```bash
# Reset terraform state if needed
terraform refresh

# Remove test alert policies
gcloud alpha monitoring policies delete POLICY_ID

# Restore original configuration
git checkout main
```

## Success Metrics

### Performance Targets
- Workflow completion time: < 10 minutes
- Terraform plan time: < 2 minutes
- Alert policy application: < 3 minutes
- Gateway verification: < 5 minutes

### Reliability Targets
- Success rate: > 95% for normal operations
- False positive rate: < 5% for drift detection
- Recovery time: < 15 minutes for manual intervention
