"""
Unit tests for the error classification service.

This file contains comprehensive tests for error classification, retry decisions,
and delay calculations based on error types.
"""

import logging
from unittest.mock import patch

from pi_lawyer.services.error_classification import ErrorCategory, ErrorClassifier


class TestErrorClassification:
    """Test suite for ErrorClassifier functionality."""

    # ----------------------
    # classify_error tests
    # ----------------------

    def test_classify_openai_rate_limit_error(self):
        """Test OpenAI rate limit error classification."""

        class OpenAIRateLimitError(Exception):
            pass

        error = OpenAIRateLimitError("Rate limit exceeded, please try again later")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.RATE_LIMIT

    def test_classify_openai_validation_error(self):
        """Test OpenAI validation error classification."""

        class OpenAIInvalidRequestError(Exception):
            pass

        error = OpenAIInvalidRequestError("Invalid request parameters")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.VALIDATION

    def test_classify_openai_auth_error(self):
        """Test OpenAI authentication error classification."""

        class OpenAIAuthenticationError(Exception):
            pass

        error = OpenAIAuthenticationError("Authentication failed, check your API key")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.AUTHORIZATION

    def test_classify_openai_not_found_error(self):
        """Test OpenAI resource not found error classification."""

        class OpenAIError(Exception):
            pass

        error = OpenAIError("Resource not found")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.RESOURCE

    def test_classify_openai_server_error(self):
        """Test OpenAI server error classification."""

        class OpenAIAPIError(Exception):
            pass

        error = OpenAIAPIError("Server error 500")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.SERVER

    def test_classify_openai_connection_error(self):
        """Test OpenAI connection error classification."""

        class OpenAITimeoutError(Exception):
            pass

        error = OpenAITimeoutError("Request timed out")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.TRANSIENT

    def test_classify_pinecone_rate_limit_error(self):
        """Test Pinecone rate limit error classification."""

        class PineconeRateLimitError(Exception):
            pass

        error = PineconeRateLimitError("Quota exceeded for queries")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.RATE_LIMIT

    def test_classify_pinecone_timeout_error(self):
        """Test Pinecone timeout error classification."""

        class PineconeTimeoutError(Exception):
            pass

        error = PineconeTimeoutError("Connection timeout")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.TRANSIENT

    def test_classify_database_connection_error(self):
        """Test database connection error classification."""

        class DatabaseConnectionError(Exception):
            pass

        error = DatabaseConnectionError("Database connection failed")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.TRANSIENT

    def test_classify_supabase_validation_error(self):
        """Test Supabase validation error classification."""

        class SupabaseConstraintError(Exception):
            pass

        error = SupabaseConstraintError("Foreign key constraint violation")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.VALIDATION

    def test_classify_network_error(self):
        """Test network error classification."""

        class NetworkTimeoutError(Exception):
            pass

        error = NetworkTimeoutError("Socket timeout")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.TRANSIENT

    def test_classify_unknown_error(self):
        """Test unknown error classification."""
        error = Exception("Some totally unknown error")
        category = ErrorClassifier.classify_error(error)
        assert category == ErrorCategory.UNKNOWN

    # ----------------------
    # should_retry tests
    # ----------------------

    def test_should_retry_transient_error(self):
        """Test retry decision for transient errors."""
        error = TimeoutError("Connection timeout")
        assert ErrorClassifier.should_retry(error) is True

    def test_should_retry_rate_limit_error(self):
        """Test retry decision for rate limit errors."""

        class RateLimitError(Exception):
            pass

        error = RateLimitError("Rate limit exceeded")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.RATE_LIMIT
        ):
            assert ErrorClassifier.should_retry(error) is True

    def test_should_retry_server_error(self):
        """Test retry decision for server errors."""
        error = Exception("Internal server error 500")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.SERVER
        ):
            assert ErrorClassifier.should_retry(error) is True

    def test_should_not_retry_validation_error(self):
        """Test retry decision for validation errors."""
        error = ValueError("Invalid parameter")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.VALIDATION
        ):
            assert ErrorClassifier.should_retry(error) is False

    def test_should_not_retry_auth_error(self):
        """Test retry decision for authorization errors."""
        error = PermissionError("Not authorized")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.AUTHORIZATION
        ):
            assert ErrorClassifier.should_retry(error) is False

    def test_should_not_retry_unknown_error(self):
        """Test retry decision for unknown errors."""
        error = Exception("Unknown error")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.UNKNOWN
        ):
            assert ErrorClassifier.should_retry(error) is False

    # ----------------------
    # get_retry_delay tests
    # ----------------------

    def test_retry_delay_rate_limit(self):
        """Test retry delay calculation for rate limit errors."""
        error = Exception("Rate limit")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.RATE_LIMIT
        ):
            # First retry
            assert (
                ErrorClassifier.get_retry_delay(error, 0) == 5 + 30
            )  # base * (2^0) + 30
            # Second retry
            assert (
                ErrorClassifier.get_retry_delay(error, 1) == 10 + 30
            )  # base * (2^1) + 30
            # Third retry
            assert (
                ErrorClassifier.get_retry_delay(error, 2) == 20 + 30
            )  # base * (2^2) + 30

    def test_retry_delay_transient(self):
        """Test retry delay calculation for transient errors."""
        error = TimeoutError("Timeout")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.TRANSIENT
        ):
            # First retry
            assert ErrorClassifier.get_retry_delay(error, 0) == 5  # base * (2^0)
            # Second retry
            assert ErrorClassifier.get_retry_delay(error, 1) == 10  # base * (2^1)
            # Third retry
            assert ErrorClassifier.get_retry_delay(error, 2) == 20  # base * (2^2)

    def test_retry_delay_server_error(self):
        """Test retry delay calculation for server errors."""
        error = Exception("Server error")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.SERVER
        ):
            # First retry
            assert (
                ErrorClassifier.get_retry_delay(error, 0) == 5 + 10
            )  # base * (2^0) + 10
            # Second retry
            assert (
                ErrorClassifier.get_retry_delay(error, 1) == 10 + 10
            )  # base * (2^1) + 10
            # Third retry
            assert (
                ErrorClassifier.get_retry_delay(error, 2) == 20 + 10
            )  # base * (2^2) + 10

    def test_retry_delay_other_error(self):
        """Test retry delay calculation for other error types."""
        error = Exception("Other error")
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.UNKNOWN
        ):
            # First retry
            assert ErrorClassifier.get_retry_delay(error, 0) == 0  # base * 0
            # Second retry
            assert ErrorClassifier.get_retry_delay(error, 1) == 5  # base * 1
            # Third retry
            assert ErrorClassifier.get_retry_delay(error, 2) == 10  # base * 2

    # ----------------------
    # log_error_with_classification tests
    # ----------------------

    def test_log_error_with_classification(self, caplog):
        """Test error logging with classification."""
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.TRANSIENT
        ), patch.object(ErrorClassifier, "should_retry", return_value=True):
            caplog.set_level(logging.ERROR)
            error = TimeoutError("Connection timeout")
            context = {"service": "pinecone", "operation": "query"}

            ErrorClassifier.log_error_with_classification(error, context)

            assert "Error [transient]: TimeoutError" in caplog.text
            assert "service=pinecone, operation=query" in caplog.text

    def test_log_error_without_context(self, caplog):
        """Test error logging without additional context."""
        with patch.object(
            ErrorClassifier, "classify_error", return_value=ErrorCategory.VALIDATION
        ), patch.object(ErrorClassifier, "should_retry", return_value=False):
            caplog.set_level(logging.ERROR)
            error = ValueError("Invalid parameter")

            ErrorClassifier.log_error_with_classification(error, None)

            assert "Error [validation]: ValueError" in caplog.text
