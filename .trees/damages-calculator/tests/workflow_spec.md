# CI Workflow Specification Tests

## Overview

This document defines the success criteria and test specifications for the production deployment workflow that handles the cut-over from Vercel to Google API Gateway.

## Test Categories

### 1. Pre-deployment Validation Tests

#### T1.1: Terraform Configuration Validation
**Objective**: Ensure Terraform configuration is valid and ready for deployment.

**Success Criteria**:
- [ ] `terraform validate` returns exit code 0
- [ ] No deprecated `template_file` resources present
- [ ] `templatefile()` function used for OpenAPI spec processing
- [ ] All required variables defined in `variables.tf`
- [ ] Provider configuration uses `google-beta` for API Gateway resources

**Test Command**:
```bash
cd infra/terraform
terraform init
terraform validate
```

#### T1.2: Required Secrets Validation
**Objective**: Verify all required GitHub secrets are configured.

**Success Criteria**:
- [ ] `GCP_SA_KEY` contains valid service account JSON
- [ ] `CLOUD_RUN_URL` points to valid Cloud Run service
- [ ] `GCP_PROJECT_ID` matches backend project
- [ ] `MCP_PROJECT` matches MCP Rules Engine project
- [ ] Service account has required permissions in both projects

### 2. Gateway Apply Job Tests

#### T2.1: Terraform Plan Validation
**Objective**: Ensure Terraform plan shows no changes for cut-over.

**Success Criteria**:
- [ ] `terraform plan -detailed-exitcode` returns exit code 0 (no changes)
- [ ] Plan output contains no resource additions, changes, or deletions
- [ ] Only provider lock file updates are acceptable
- [ ] Job fails if exit code is 2 (changes detected)

**Expected Behavior**:
```
Plan: 0 to add, 0 to change, 0 to destroy.
```

#### T2.2: Terraform Apply Execution
**Objective**: Verify Terraform apply updates state without resource changes.

**Success Criteria**:
- [ ] `terraform apply` completes successfully
- [ ] No actual infrastructure changes made
- [ ] State file updated with provider references
- [ ] Gateway host output available: `terraform output -raw prod_gateway_host`

#### T2.3: Gateway Verification
**Objective**: Validate API Gateway is functional and accessible.

**Success Criteria**:
- [ ] Health endpoint returns HTTP 200: `GET /health`
- [ ] MCP endpoint accepts valid requests: `POST /mcp/run`
- [ ] API key authentication works correctly
- [ ] Response contains expected deadline data structure
- [ ] Verification script exits with code 0

**Test Payload**:
```json
{
  "toolName": "calculate_deadlines",
  "params": {
    "jurisdiction": "TX_STATE",
    "triggerCode": "SERVICE_OF_PROCESS",
    "startDate": "2025-08-01"
  }
}
```

**Expected Response Structure**:
```json
{
  "deadlines": [
    {
      "name": "string",
      "date": "YYYY-MM-DD",
      "description": "string",
      "priority": "high|medium|low"
    }
  ]
}
```

### 3. Backend Deployment Job Tests

#### T3.1: Environment Variable Update
**Objective**: Ensure backend service environment is updated with gateway host.

**Success Criteria**:
- [ ] `MCP_RULES_BASE` environment variable updated
- [ ] Variable value matches gateway host from previous job
- [ ] Cloud Run service configuration updated successfully
- [ ] No downtime during environment update

**Validation Command**:
```bash
gcloud run services describe ailex-backend-prod \
  --region=us-central1 \
  --project=new-texas-laws \
  --format="value(spec.template.spec.containers[0].env[?name=='MCP_RULES_BASE'].value)"
```

#### T3.2: Backend Service Deployment
**Objective**: Verify backend service deploys successfully with new configuration.

**Success Criteria**:
- [ ] Docker image builds without errors
- [ ] Image pushed to Container Registry successfully
- [ ] Cloud Run deployment completes without errors
- [ ] Service becomes available within 60 seconds
- [ ] Health check endpoint returns HTTP 200

#### T3.3: Integration Testing
**Objective**: Validate end-to-end functionality through the new gateway.

**Success Criteria**:
- [ ] Backend can reach API Gateway successfully
- [ ] MCP Rules Engine integration works correctly
- [ ] Response times within acceptable limits (<2 seconds)
- [ ] Error handling works for invalid requests
- [ ] API key validation functions properly

### 4. Workflow Orchestration Tests

#### T4.1: Job Dependencies
**Objective**: Ensure proper job sequencing and dependency management.

**Success Criteria**:
- [ ] `deploy-production` job waits for `gateway_apply` completion
- [ ] Gateway host output passed correctly between jobs
- [ ] Job failure in `gateway_apply` prevents `deploy-production` execution
- [ ] Parallel execution not attempted for dependent jobs

#### T4.2: Error Handling
**Objective**: Verify workflow handles failures gracefully.

**Success Criteria**:
- [ ] Terraform plan failure stops workflow execution
- [ ] Gateway verification failure prevents backend deployment
- [ ] Backend deployment failure is properly reported
- [ ] Partial deployments are avoided

#### T4.3: Output and Notifications
**Objective**: Ensure proper reporting and notification of deployment status.

**Success Criteria**:
- [ ] GitHub comment posted on successful deployment
- [ ] Comment contains gateway host, image tag, and timestamp
- [ ] Deployment summary includes all key metrics
- [ ] Failure notifications include actionable error information

### 5. Security and Permissions Tests

#### T5.1: Cross-Project Authentication
**Objective**: Validate service account works across both GCP projects.

**Success Criteria**:
- [ ] Service account can access texas-laws-personalinjury resources
- [ ] Service account can deploy to new-texas-laws project
- [ ] API Gateway operations succeed with provided credentials
- [ ] Secret Manager access works for API key retrieval

#### T5.2: Secret Management
**Objective**: Ensure secrets are handled securely throughout workflow.

**Success Criteria**:
- [ ] API keys retrieved from Secret Manager, not hardcoded
- [ ] Service account key not exposed in logs
- [ ] Secrets not persisted in workflow artifacts
- [ ] Proper secret rotation capabilities maintained

### 6. Performance and Reliability Tests

#### T6.1: Deployment Speed
**Objective**: Ensure deployment completes within acceptable timeframes.

**Success Criteria**:
- [ ] Gateway apply job completes within 5 minutes
- [ ] Backend deployment completes within 10 minutes
- [ ] Total workflow duration under 15 minutes
- [ ] No unnecessary waiting or polling

#### T6.2: Reliability and Idempotency
**Objective**: Verify workflow can be run multiple times safely.

**Success Criteria**:
- [ ] Re-running workflow produces same results
- [ ] No resource conflicts on repeated execution
- [ ] State management handles concurrent runs gracefully
- [ ] Rollback procedures work correctly

### 7. Monitoring and Observability Tests

#### T7.1: Logging and Metrics
**Objective**: Ensure adequate logging and monitoring throughout deployment.

**Success Criteria**:
- [ ] All major steps logged with appropriate detail
- [ ] Error messages include actionable information
- [ ] Deployment metrics captured and reported
- [ ] Performance data available for analysis

#### T7.2: Health Checks and Validation
**Objective**: Verify comprehensive health checking throughout process.

**Success Criteria**:
- [ ] Gateway health validated before backend update
- [ ] Backend health confirmed after deployment
- [ ] End-to-end functionality tested post-deployment
- [ ] Rollback triggers work on health check failures

## Test Execution Strategy

### Automated Testing
- **Unit Tests**: Terraform configuration validation
- **Integration Tests**: Gateway and backend functionality
- **End-to-End Tests**: Complete workflow execution

### Manual Testing
- **Pre-deployment**: Infrastructure readiness validation
- **Post-deployment**: Manual verification of cut-over success
- **Rollback Testing**: Manual rollback procedure validation

### Continuous Monitoring
- **Real-time**: Workflow execution monitoring
- **Post-deployment**: Service health and performance monitoring
- **Long-term**: Trend analysis and optimization opportunities

## Success Metrics

### Primary Success Criteria
1. **Zero Downtime**: No service interruption during cut-over
2. **Functional Parity**: All features work identically post-cut-over
3. **Performance Maintained**: Response times within 10% of baseline
4. **Error Rate**: <0.1% error rate during and after deployment

### Secondary Success Criteria
1. **Deployment Speed**: Complete cut-over within 15 minutes
2. **Rollback Capability**: Ability to rollback within 5 minutes
3. **Monitoring Coverage**: 100% visibility into deployment process
4. **Documentation**: Complete runbooks and troubleshooting guides

## Failure Scenarios and Recovery

### Critical Failures
- **Terraform Apply Failure**: Immediate workflow termination
- **Gateway Verification Failure**: Prevent backend deployment
- **Backend Deployment Failure**: Automatic rollback consideration

### Recovery Procedures
- **Infrastructure Issues**: Manual Terraform state recovery
- **Service Issues**: Cloud Run service rollback
- **Network Issues**: DNS and routing verification

## Compliance and Audit

### Audit Trail
- [ ] All deployment actions logged
- [ ] Change tracking maintained
- [ ] Access patterns recorded
- [ ] Compliance requirements met

### Security Validation
- [ ] No credential exposure
- [ ] Proper access controls maintained
- [ ] Security scanning passed
- [ ] Vulnerability assessment completed

## Conclusion

This specification defines comprehensive testing criteria for the CI workflow that automates the cut-over from Vercel to Google API Gateway. Success requires all primary criteria to be met, with secondary criteria providing additional confidence in the deployment process.

Regular review and updates of these specifications ensure the workflow remains robust and reliable as the system evolves.
