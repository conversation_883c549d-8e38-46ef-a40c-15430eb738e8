"""
Unit tests for the Research Agent state model.

This module contains tests for the ResearchState class and related models.
"""

import os
import sys

import pytest

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.agents.interactive.research.state import (
    Citation,
    ResearchState,
    SearchResult,
    UserContext,
)


# Test fixtures
@pytest.fixture
def user_context():
    """Create a test user context."""
    return UserContext(
        user_id="test-user",
        tenant_id="test-tenant",
        role="attorney",
        assigned_case_ids=["case-1", "case-2"],
        settings={}
    )


@pytest.fixture
def research_state(user_context):
    """Create a test research state."""
    return ResearchState(
        question="What is the statute of limitations for personal injury in Texas?",
        user_context=user_context,
        jurisdiction="texas",
        practice_areas={"personal_injury"},
    )


# Tests for state model
def test_research_state_initialization(user_context):
    """Test that ResearchState initializes correctly with required fields."""
    state = ResearchState(
        question="Test question",
        user_context=user_context
    )

    assert state.question == "Test question"
    assert state.user_context == user_context
    assert state.jurisdiction == "texas"  # Default value
    assert state.practice_areas == set()  # Default empty set
    assert state.queries == []  # Default empty list
    assert state.query_type is None
    assert state.data_source is None
    assert state.legal_documents == []
    assert state.case_documents == []
    assert state.web_search_results == []
    assert state.citations == []
    assert state.citation_map == {}
    assert state.rerank_scores == []
    assert state.search_metadata == {}
    assert state.answer is None


def test_research_state_can_view_results(user_context):
    """Test the can_view_results method of ResearchState."""
    # Public data is always accessible
    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="public"
    )
    assert state.can_view_results() is True

    # Private data is accessible to attorneys
    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="private"
    )
    assert state.can_view_results() is True

    # Private data is not accessible to paralegals
    paralegal_context = UserContext(
        user_id="test-user",
        tenant_id="test-tenant",
        role="paralegal",
        assigned_case_ids=["case-1", "case-2"],
        settings={}
    )
    state = ResearchState(
        question="Test question",
        user_context=paralegal_context,
        data_source="private"
    )
    assert state.can_view_results() is False

    # Case-specific access check
    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="private",
        case_id="case-1"
    )
    assert state.can_view_results() is True

    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="private",
        case_id="case-3"  # Not in assigned_case_ids
    )
    assert state.can_view_results() is False


def test_citation_model():
    """Test the Citation model."""
    citation = Citation(
        id="doc-1",
        title="Texas Civil Practice and Remedies Code",
        url="https://example.com/texas-code",
        source_type="legal",
        jurisdiction="texas",
        citation_text="Section 16.003",
        relevance_score=0.95,
        metadata={"year": 2023}
    )

    assert citation.id == "doc-1"
    assert citation.title == "Texas Civil Practice and Remedies Code"
    assert citation.url == "https://example.com/texas-code"
    assert citation.source_type == "legal"
    assert citation.jurisdiction == "texas"
    assert citation.citation_text == "Section 16.003"
    assert citation.relevance_score == 0.95
    assert citation.metadata == {"year": 2023}


def test_search_result_model():
    """Test the SearchResult model."""
    result = SearchResult(
        query_type="legal_research",
        data_source="public",
        answer="The statute of limitations for personal injury in Texas is two years.",
        citations=[
            Citation(
                id="doc-1",
                title="Texas Civil Practice and Remedies Code",
                source_type="legal",
                jurisdiction="texas"
            )
        ],
        summary="Research on Texas statute of limitations",
        recommendations=["Consider tolling provisions"],
        chunk_ids=["chunk-1", "chunk-2"],
        sensitive_data=False,
        audit_log={"user": "test-user", "timestamp": "2023-05-18T12:00:00Z"},
        search_metadata={"duration_ms": 250}
    )

    assert result.query_type == "legal_research"
    assert result.data_source == "public"
    assert result.answer == "The statute of limitations for personal injury in Texas is two years."
    assert len(result.citations) == 1
    assert result.citations[0].id == "doc-1"
    assert result.summary == "Research on Texas statute of limitations"
    assert result.recommendations == ["Consider tolling provisions"]
    assert result.chunk_ids == ["chunk-1", "chunk-2"]
    assert result.sensitive_data is False
    assert result.audit_log == {"user": "test-user", "timestamp": "2023-05-18T12:00:00Z"}
    assert result.search_metadata == {"duration_ms": 250}
