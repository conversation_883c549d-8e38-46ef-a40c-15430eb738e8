"""Test access control in the research agent."""
import logging
from typing import Any, Dict

import pytest
from postgrest import APIError

from pi_lawyer.agents.research_agent.graph import classify_input, perform_research
from pi_lawyer.agents.research_agent.state import (
    LawResearchState as ResearchState,
)

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Create console handler if no handlers exist
if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    console_handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(console_handler)


@pytest.fixture(autouse=True)
async def cleanup(supabase_client):
    try:
        yield
        # Cleanup all test cases after each test
        logger.info("Cleaning up test data...")
        await supabase_client.delete_test_data()
        logger.info("Test data cleanup completed successfully")
    except Exception as e:
        logger.error(f"Error during test cleanup: {str(e)}", exc_info=True)
        raise


async def verify_supabase_response(result: Dict[str, Any], context: str) -> None:
    """Helper to verify and log Supabase responses."""
    logger.debug(f"Verifying Supabase response for: {context}")
    logger.debug(f"Response: {result}")

    if "error" in result:
        error_msg = f"Error in {context}: {result['error']}"
        logger.error(error_msg)
        raise AssertionError(error_msg)

    if "data" not in result and "status" not in result:
        error_msg = f"Invalid response format in {context}. Missing 'data' or 'status'"
        logger.error(error_msg)
        raise AssertionError(error_msg)


@pytest.mark.asyncio
@pytest.mark.xfail(
    reason="Query classification logic is unfinished or unstable. Remove xfail when feature is complete."
)
async def test_query_classification(supabase_client, test_users):
    """Test that queries are correctly classified based on content."""
    try:
        partner = test_users["partner"]
        logger.info(f"Testing query classification with partner user: {partner['id']}")

        # Test public query
        query = "What is the statute of limitations for personal injury in Texas?"
        logger.info(f"Testing public query: {query}")
        state = ResearchState(question=query, user_context=partner)
        result = await classify_input(state)
        await verify_supabase_response(result, "public query classification")
        assert result["classification"]["type"] == "public"
        assert result["access_granted"] is True
        assert "error" not in result
        logger.info("Public query test passed")

        # Test private query
        query = "Find similar cases to our client John Doe's workplace injury."
        logger.info(f"Testing private query: {query}")
        state = ResearchState(question=query, user_context=partner)
        result = await classify_input(state)
        await verify_supabase_response(result, "private query classification")
        assert result["classification"]["type"] == "private"
        assert result["access_granted"] is True
        assert "error" not in result
        logger.info("Private query test passed")

    except APIError as e:
        logger.error(f"Supabase API error: {str(e)}", exc_info=True)
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in query classification test: {str(e)}", exc_info=True
        )
        raise


@pytest.mark.asyncio
async def test_paralegal_query_access(supabase_client, test_users, test_case):
    """Test paralegal access to different query types."""
    try:
        paralegal = test_users["paralegal"]
        logger.info(f"Testing paralegal access with user: {paralegal['id']}")

        # Public query should work
        query = "What are Texas personal injury laws?"
        logger.info(f"Testing public query access: {query}")
        state = ResearchState(question=query, user_context=paralegal)
        result = await classify_input(state)
        await verify_supabase_response(result, "paralegal public query")
        assert result["access_granted"] is True
        assert "error" not in result
        logger.info("Paralegal public query test passed")

        # Private query without case assignment should fail
        query = f"Show me documents from case {test_case['case_id']}"
        logger.info(f"Testing unauthorized private query: {query}")
        state = ResearchState(question=query, user_context=paralegal)
        result = await classify_input(state)
        assert result["access_granted"] is False
        assert "error" in result
        assert "unauthorized" in result["error"].lower()
        logger.info("Unauthorized access test passed")

        # Assign paralegal to case
        partner = test_users["partner"]
        logger.info(
            f"Assigning paralegal {paralegal['id']} to case {test_case['case_id']}"
        )
        try:
            assignment = await supabase_client.assign_to_case(
                test_case["case_id"], paralegal["id"], assigner_context=partner
            )
            await verify_supabase_response(assignment, "case assignment")
            logger.info("Case assignment successful")
        except Exception as e:
            logger.error(f"Failed to assign paralegal to case: {str(e)}", exc_info=True)
            raise

        # Test access after assignment
        logger.info("Testing access after case assignment")
        state = ResearchState(question=query, user_context=paralegal)
        result = await classify_input(state)
        await verify_supabase_response(result, "post-assignment query")
        assert result["access_granted"] is True
        assert "error" not in result
        logger.info("Post-assignment access test passed")

    except APIError as e:
        logger.error(
            f"Supabase API error in paralegal access test: {str(e)}", exc_info=True
        )
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in paralegal access test: {str(e)}", exc_info=True
        )
        raise


@pytest.mark.asyncio
async def test_search_private_data_access(supabase_client, test_users, test_case):
    """Test access control for private data search."""
    try:
        # Staff should not access private data
        staff = test_users["staff"]
        logger.info(f"Testing staff access with user: {staff['id']}")
        state = ResearchState(
            question="Show me case documents",
            user_context=staff,
            case_id=test_case["case_id"],
        )
        result = await perform_research(state)
        await verify_supabase_response(result, "staff private data access")
        assert "error" in result
        assert "access denied" in result["error"].lower()
        logger.info("Staff access test passed")

        # Partner should access private data
        partner = test_users["partner"]
        logger.info(f"Testing partner access with user: {partner['id']}")
        state = ResearchState(
            question="Show me case documents",
            user_context=partner,
            case_id=test_case["case_id"],
        )
        result = await perform_research(state)
        await verify_supabase_response(result, "partner private data access")
        assert "error" not in result
        assert result["status"] == "success"
        logger.info("Partner access test passed")

    except APIError as e:
        logger.error(
            f"Supabase API error in private data access test: {str(e)}", exc_info=True
        )
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in private data access test: {str(e)}", exc_info=True
        )
        raise


@pytest.mark.asyncio
async def test_attorney_case_access(supabase_client, test_users, test_case):
    """Test attorney access to regular and sensitive cases."""
    try:
        partner = test_users["partner"]
        attorney = test_users["attorney"]
        logger.info(f"Testing attorney access with user: {attorney['id']}")

        # Create regular case
        logger.info("Creating regular case")
        try:
            regular_case = await supabase_client.create_case(
                {
                    "title": "Regular Case",
                    "description": "Standard case",
                    "status": "active",
                    "sensitive": False,
                    "client_id": test_case["client_id"],
                },
                user_context=partner,
            )
            await verify_supabase_response(regular_case, "regular case creation")
            logger.info("Regular case created successfully")
        except Exception as e:
            logger.error(f"Failed to create regular case: {str(e)}", exc_info=True)
            raise

        # Attorney should have access to regular cases by default
        logger.info("Testing attorney access to regular case")
        state = ResearchState(
            question=f"Show documents from case {regular_case}", user_context=attorney
        )
        result = await perform_research(state)
        await verify_supabase_response(result, "attorney regular case access")
        assert "error" not in result
        assert result["status"] == "success"
        logger.info("Attorney regular case access test passed")

        # Create sensitive case
        logger.info("Creating sensitive case")
        try:
            sensitive_case = await supabase_client.create_case(
                {
                    "title": "Sensitive Case",
                    "description": "High-profile client",
                    "sensitive": True,
                    "status": "active",
                    "client_id": test_case["client_id"],
                },
                user_context=partner,
            )
            await verify_supabase_response(sensitive_case, "sensitive case creation")
            logger.info("Sensitive case created successfully")
        except Exception as e:
            logger.error(f"Failed to create sensitive case: {str(e)}", exc_info=True)
            raise

        # Attorney needs explicit assignment for sensitive cases
        logger.info("Testing attorney access to sensitive case without assignment")
        state = ResearchState(
            question=f"Show documents from case {sensitive_case}",
            user_context=attorney,
            case_id=sensitive_case,
        )
        result = await perform_research(state)
        assert result["access_granted"] is False
        assert "error" in result
        logger.info("Attorney sensitive case access without assignment test passed")

        # Assign attorney to sensitive case
        logger.info(
            f"Assigning attorney {attorney['id']} to sensitive case {sensitive_case}"
        )
        try:
            assignment = await supabase_client.assign_to_case(
                sensitive_case, attorney["id"], assigner_context=partner
            )
            await verify_supabase_response(assignment, "sensitive case assignment")
            logger.info("Sensitive case assignment successful")
        except Exception as e:
            logger.error(
                f"Failed to assign attorney to sensitive case: {str(e)}", exc_info=True
            )
            raise

        # Test access after assignment
        logger.info("Testing attorney access to sensitive case after assignment")
        state = ResearchState(
            question=f"Show documents from case {sensitive_case}",
            user_context=attorney,
            case_id=sensitive_case,
        )
        result = await perform_research(state)
        await verify_supabase_response(
            result, "attorney sensitive case access after assignment"
        )
        assert result["access_granted"] is True
        assert "error" not in result
        assert result["status"] == "success"
        logger.info("Attorney sensitive case access after assignment test passed")

    except APIError as e:
        logger.error(
            f"Supabase API error in attorney case access test: {str(e)}", exc_info=True
        )
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in attorney case access test: {str(e)}", exc_info=True
        )
        raise


@pytest.mark.asyncio
async def test_sensitive_case_access(supabase_client, test_users, test_case):
    """Test access control for sensitive cases."""
    try:
        partner = test_users["partner"]
        attorney = test_users["attorney"]
        logger.info(f"Testing sensitive case access with user: {attorney['id']}")

        # Create sensitive case
        logger.info("Creating sensitive case")
        try:
            sensitive_case = await supabase_client.create_case(
                {
                    "title": "Sensitive Case",
                    "description": "High-profile client",
                    "sensitive": True,
                    "status": "active",
                    "client_id": test_case["client_id"],
                },
                user_context=partner,
            )
            await verify_supabase_response(sensitive_case, "sensitive case creation")
            logger.info("Sensitive case created successfully")
        except Exception as e:
            logger.error(f"Failed to create sensitive case: {str(e)}", exc_info=True)
            raise

        # Attorney needs explicit assignment for sensitive cases
        logger.info("Testing attorney access to sensitive case without assignment")
        state = ResearchState(
            question=f"Show documents from case {sensitive_case}",
            user_context=attorney,
            case_id=sensitive_case,
        )
        result = await perform_research(state)
        assert result["access_granted"] is False
        assert "error" in result
        logger.info("Attorney sensitive case access without assignment test passed")

        # Assign attorney to sensitive case
        logger.info(
            f"Assigning attorney {attorney['id']} to sensitive case {sensitive_case}"
        )
        try:
            assignment = await supabase_client.assign_to_case(
                sensitive_case, attorney["id"], assigner_context=partner
            )
            await verify_supabase_response(assignment, "sensitive case assignment")
            logger.info("Sensitive case assignment successful")
        except Exception as e:
            logger.error(
                f"Failed to assign attorney to sensitive case: {str(e)}", exc_info=True
            )
            raise

        # Test access after assignment
        logger.info("Testing attorney access to sensitive case after assignment")
        state = ResearchState(
            question=f"Show documents from case {sensitive_case}",
            user_context=attorney,
            case_id=sensitive_case,
        )
        result = await perform_research(state)
        await verify_supabase_response(
            result, "attorney sensitive case access after assignment"
        )
        assert result["access_granted"] is True
        assert "error" not in result
        logger.info("Attorney sensitive case access after assignment test passed")

        # Cleanup
        logger.info(f"Deleting sensitive case {sensitive_case}")
        try:
            await supabase_client.client.from_("cases").delete().eq(
                "id", sensitive_case
            ).execute()
            logger.info("Sensitive case deleted successfully")
        except Exception as e:
            logger.error(f"Failed to delete sensitive case: {str(e)}", exc_info=True)
            raise

    except APIError as e:
        logger.error(
            f"Supabase API error in sensitive case access test: {str(e)}", exc_info=True
        )
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in sensitive case access test: {str(e)}", exc_info=True
        )
        raise


@pytest.mark.asyncio
async def test_partner_access(supabase_client, test_users, test_case):
    """Test that partners have full access to all cases."""
    partner = test_users["partner"]

    try:
        # Set auth token for partner
        supabase_client.client.auth.set_session(partner["token"])

        # Query cases
        result = await supabase_client.client.from_("cases").select("*").execute()
        assert result.data, "Partner should be able to view cases"

        # Query sensitive cases
        sensitive_result = (
            await supabase_client.client.from_("cases")
            .select("*")
            .eq("sensitive", True)
            .execute()
        )
        assert (
            sensitive_result is not None
        ), "Partner should be able to query sensitive cases"

    except Exception as e:
        logger.error(f"Partner access test failed: {str(e)}")
        raise


@pytest.mark.asyncio
async def test_attorney_access(supabase_client, test_users, test_case):
    """Test that attorneys can access assigned cases."""
    attorney = test_users["attorney"]

    try:
        # Set auth token for attorney
        supabase_client.client.auth.set_session(attorney["token"])

        # Create case assignment
        await supabase_client.client.from_("assignments").insert(
            {
                "user_id": attorney["id"],
                "case_id": test_case["id"],
                "tenant_id": attorney["tenant_id"],
            }
        ).execute()

        # Query assigned case
        result = (
            await supabase_client.client.from_("cases")
            .select("*")
            .eq("id", test_case["id"])
            .execute()
        )
        assert result.data, "Attorney should be able to view assigned case"

        # Query unassigned case (should return empty)
        unassigned_result = (
            await supabase_client.client.from_("cases")
            .select("*")
            .neq("id", test_case["id"])
            .execute()
        )
        assert not unassigned_result.data, "Attorney should not see unassigned cases"

    except Exception as e:
        logger.error(f"Attorney access test failed: {str(e)}")
        raise
    finally:
        # Cleanup assignment
        try:
            await supabase_client.client.from_("assignments").delete().eq(
                "user_id", attorney["id"]
            ).execute()
        except Exception as e:
            logger.error(f"Failed to cleanup assignment: {str(e)}")


@pytest.mark.asyncio
async def test_paralegal_access(supabase_client, test_users, test_case):
    """Test that paralegals can access assigned non-sensitive cases."""
    paralegal = test_users["paralegal"]

    try:
        # Set auth token for paralegal
        supabase_client.client.auth.set_session(paralegal["token"])

        # Create case assignment
        await supabase_client.client.from_("assignments").insert(
            {
                "user_id": paralegal["id"],
                "case_id": test_case["id"],
                "tenant_id": paralegal["tenant_id"],
            }
        ).execute()

        # Query assigned non-sensitive case
        result = (
            await supabase_client.client.from_("cases")
            .select("*")
            .eq("id", test_case["id"])
            .eq("sensitive", False)
            .execute()
        )
        assert (
            result.data
        ), "Paralegal should be able to view assigned non-sensitive case"

        # Query sensitive case (should be denied)
        with pytest.raises(Exception) as exc_info:
            await supabase_client.client.from_("cases").select("*").eq(
                "sensitive", True
            ).execute()
        assert "permission denied" in str(exc_info.value).lower()

    except Exception as e:
        logger.error(f"Paralegal access test failed: {str(e)}")
        raise
    finally:
        # Cleanup assignment
        try:
            await supabase_client.client.from_("assignments").delete().eq(
                "user_id", paralegal["id"]
            ).execute()
        except Exception as e:
            logger.error(f"Failed to cleanup assignment: {str(e)}")


@pytest.mark.asyncio
async def test_staff_access(supabase_client, test_users, test_case):
    """Test that staff have limited read-only access."""
    staff = test_users["staff"]

    try:
        # Set auth token for staff
        supabase_client.client.auth.set_session(staff["token"])

        # Query non-sensitive cases (should be allowed)
        result = (
            await supabase_client.client.from_("cases")
            .select("*")
            .eq("sensitive", False)
            .execute()
        )
        assert (
            result.data is not None
        ), "Staff should be able to view non-sensitive cases"

        # Attempt to create case (should be denied)
        with pytest.raises(Exception) as exc_info:
            await supabase_client.client.from_("cases").insert(
                {
                    "title": "Unauthorized Case",
                    "tenant_id": staff["tenant_id"],
                    "created_by": staff["id"],
                }
            ).execute()
        assert "permission denied" in str(exc_info.value).lower()

        # Query sensitive cases (should be denied)
        with pytest.raises(Exception) as exc_info:
            await supabase_client.client.from_("cases").select("*").eq(
                "sensitive", True
            ).execute()
        assert "permission denied" in str(exc_info.value).lower()

    except Exception as e:
        logger.error(f"Staff access test failed: {str(e)}")
        raise
