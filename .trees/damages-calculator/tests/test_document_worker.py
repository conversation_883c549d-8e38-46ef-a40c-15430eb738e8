from unittest.mock import AsyncMock, MagicMock, patch

import pytest

# Use patch at module level to prevent actual imports from being used
# Patch 'pinecone' module *within* the pinecone_client module
patch("pi_lawyer.db.pinecone_client.pinecone", MagicMock()).start()
patch("pi_lawyer.db.pinecone_client.PineconeClient", MagicMock()).start()
patch("src.pi_lawyer.db.supabase_client.SupabaseClient", MagicMock()).start()

# Bypass circuit breaker decorator to avoid Redis connections
import pi_lawyer.services.circuit_breaker as _cb

_cb.with_circuit_breaker = lambda *args, **kwargs: (lambda f: f)

# Now we can safely import DocumentWorker
from pi_lawyer.services.document_worker import DocumentWorker

# Unwrap decorated methods if still wrapped by circuit breaker
DocumentWorker._classify_document = getattr(
    DocumentWorker._classify_document, "__wrapped__", DocumentWorker._classify_document
)
DocumentWorker._analyze_document = getattr(
    DocumentWorker._analyze_document, "__wrapped__", DocumentWorker._analyze_document
)


@pytest.fixture
def worker():
    # Patch all service dependencies
    with patch(
        "pi_lawyer.services.document_worker.get_redis_queue_service"
    ) as mock_queue_service, patch(
        "pi_lawyer.services.document_worker.get_redis_lock_service"
    ) as mock_lock_service, patch(
        "pi_lawyer.services.document_worker.DocumentClassifierService"
    ) as mock_classifier, patch(
        "pi_lawyer.services.document_worker.DocumentAnalysisService"
    ) as mock_analyzer, patch(
        "pi_lawyer.services.document_worker.TenantDocumentEmbeddingService"
    ) as mock_embedding, patch(
        "pi_lawyer.services.document_worker.DocumentProcessingTransaction"
    ) as mock_transaction:
        mock_queue_service.return_value = MagicMock()
        mock_lock_service.return_value = MagicMock()
        mock_classifier.return_value = MagicMock()
        mock_analyzer.return_value = MagicMock()
        mock_embedding.return_value = MagicMock()
        mock_transaction.return_value = MagicMock()
        yield DocumentWorker(worker_id="test-worker")


def test_init_sets_worker_id(worker):
    assert worker.worker_id == "test-worker"
    assert worker.running is False
    assert worker.processed_count == 0
    assert worker.error_count == 0


def test_extract_text_from_result_dict():
    result = {"text": "sample text"}
    assert DocumentWorker._extract_text_from_result(result) == "sample text"


def test_extract_text_from_result_str():
    result = "plain string"
    assert DocumentWorker._extract_text_from_result(result) == "plain string"


def test_extract_text_from_result_other():
    class Dummy:
        pass

    dummy = Dummy()
    assert isinstance(DocumentWorker._extract_text_from_result(dummy), str)


def test_classify_document_calls_classifier(worker):
    # Mock classifier on worker instance
    worker.classifier.classify_document = AsyncMock(
        return_value={"result": "classified"}
    )
    job = {"file_path": "foo", "file_name": "bar", "file_type": "pdf", "metadata": {}}
    result = worker._classify_document(job)
    assert result == {"result": "classified"}
    worker.classifier.classify_document.assert_awaited_once()


def test_analyze_document_routes_by_classification(worker):
    # Mock analyzer on worker instance
    worker.analyzer.analyze_medical_form = MagicMock(return_value="medical")
    worker.analyzer.extract_tasks_from_document = MagicMock(return_value="tasks")
    worker.analyzer.analyze_legal_document = MagicMock(return_value="legal")

    job = {"file_path": "foo"}
    # Medical
    classification = {"analysis_type": "medical", "document_type": "med"}
    assert worker._analyze_document(job, classification) == "medical"
    # Tasks
    classification = {"analysis_type": "tasks", "document_type": "tsk"}
    assert worker._analyze_document(job, classification) == "tasks"
    # Legal
    classification = {"analysis_type": "other", "document_type": "legal"}
    assert worker._analyze_document(job, classification) == "legal"


def test_process_document_happy_path(worker):
    worker.classifier.classify_document = AsyncMock(
        return_value={"analysis_type": "tasks", "document_type": "tsk"}
    )
    worker.analyzer.extract_tasks_from_document = MagicMock(
        return_value={"text": "foo"}
    )
    worker.embedding_service.process_document = AsyncMock(
        return_value={"chunk_count": 1, "vector_count": 1, "pool_namespace": "ns"}
    )
    worker.transaction_manager.start = AsyncMock(return_value="txid")
    # Stub register_resource as async method returning None
    worker.transaction_manager.register_resource = AsyncMock(return_value=None)
    worker.transaction_manager.commit = AsyncMock()
    worker.transaction_manager.register_error = AsyncMock()
    worker.transaction_manager.rollback = AsyncMock()
    job = {"id": "1", "tenant_id": "t1", "document_id": "doc1", "file_path": "foo"}
    # Should not raise
    worker._process_document(job)
    worker.transaction_manager.commit.assert_awaited_once()
    worker.transaction_manager.rollback.assert_not_awaited()


def test_process_document_raises_and_rolls_back(worker):
    worker.classifier.classify_document = AsyncMock(side_effect=Exception("fail"))
    worker.transaction_manager.start = AsyncMock(return_value="txid")
    worker.transaction_manager.register_error = AsyncMock()
    worker.transaction_manager.rollback = AsyncMock()
    job = {"id": "1", "tenant_id": "t1", "document_id": "doc1", "file_path": "foo"}
    with pytest.raises(Exception):
        worker._process_document(job)
    worker.transaction_manager.rollback.assert_awaited_once()
    worker.transaction_manager.register_error.assert_awaited()
