"""
Standalone tests for calendar provider capabilities.

This module tests the capability matrix and capability-aware operation handling
without depending on the actual implementation.
"""

import json
from enum import Enum
from unittest.mock import mock_open, patch

import pytest


# Mock the necessary classes and functions
class ProviderCapability(str, Enum):
    """Mock of ProviderCapability enum."""
    CREATE_EVENT = "create_event"
    READ_EVENT = "read_event"
    UPDATE_EVENT = "update_event"
    DELETE_EVENT = "delete_event"
    FREE_BUSY = "free_busy"
    LIST_CALENDARS = "list_calendars"
    WEBHOOK_SUPPORT = "webhook_support"


class ConfigurationError(Exception):
    """Mock of ConfigurationError exception."""
    pass


class UnsupportedOperationError(Exception):
    """Mock of UnsupportedOperationError exception."""
    pass


# Mock functions
def load_capability_matrix():
    """Mock of load_capability_matrix function."""
    try:
        with open("capabilities.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        raise ConfigurationError("Capability matrix file not found")
    except json.JSONDecodeError as e:
        raise ConfigurationError(f"Invalid capability matrix file: {str(e)}")


def get_provider_capabilities(provider_id):
    """Mock of get_provider_capabilities function."""
    matrix = load_capability_matrix()

    if provider_id not in matrix:
        raise ConfigurationError(f"Provider not found in capability matrix: {provider_id}")

    return matrix[provider_id]["capabilities"]


def check_capability(provider, capability):
    """Mock of check_capability function."""
    # Convert provider to provider ID if it's a provider instance
    provider_id = provider.provider_id if hasattr(provider, "provider_id") else provider

    # Convert capability to string if it's a ProviderCapability enum
    capability_name = capability.value if isinstance(capability, ProviderCapability) else capability

    # Get provider capabilities from the matrix
    capabilities = get_provider_capabilities(provider_id)

    # Check if the capability is supported
    return capabilities.get(capability_name, False)


def require_capability(provider, capability):
    """Mock of require_capability function."""
    # Get provider ID and name for error message
    if hasattr(provider, "provider_id"):
        provider_id = provider.provider_id
        provider_name = provider.provider_name
    else:
        provider_id = provider
        matrix = load_capability_matrix()
        provider_name = matrix.get(provider_id, {}).get("name", provider_id)

    # Convert capability to string if it's a ProviderCapability enum
    capability_name = capability.value if isinstance(capability, ProviderCapability) else capability

    # Check if the capability is supported
    if not check_capability(provider_id, capability_name):
        raise UnsupportedOperationError(
            f"The operation '{capability_name}' is not supported by the provider '{provider_name}'"
        )


# Mock provider class
class MockProvider:
    """Mock provider for testing."""

    @property
    def provider_id(self):
        return "mock"

    @property
    def provider_name(self):
        return "Mock Provider"

    @property
    def capabilities(self):
        return [
            ProviderCapability.READ_EVENT,
            ProviderCapability.LIST_CALENDARS
        ]

    def has_capability(self, capability):
        """Check if this provider supports a capability."""
        capability_name = capability.value if isinstance(capability, ProviderCapability) else capability
        return any(cap.value == capability_name for cap in self.capabilities)

    def require_capability(self, capability):
        """Require that this provider supports a capability."""
        capability_name = capability.value if isinstance(capability, ProviderCapability) else capability
        if not self.has_capability(capability_name):
            raise UnsupportedOperationError(
                f"The operation '{capability_name}' is not supported by the provider '{self.provider_name}'"
            )


# Test classes
class TestCapabilityMatrix:
    """Tests for capability matrix."""

    def test_load_capability_matrix(self):
        """Test loading the capability matrix."""
        mock_json = {
            "google": {
                "name": "Google Calendar",
                "capabilities": {
                    "create_event": True,
                    "read_event": True
                }
            }
        }

        with patch("builtins.open", mock_open(read_data=json.dumps(mock_json))):
            with patch("os.path.exists", return_value=True):
                matrix = load_capability_matrix()
                assert matrix == mock_json

    def test_get_provider_capabilities(self):
        """Test getting provider capabilities."""
        # Create a simplified test that doesn't rely on mocking
        mock_json = {
            "google": {
                "name": "Google Calendar",
                "capabilities": {
                    "create_event": True,
                    "read_event": True
                }
            }
        }

        # Define a simple function that mimics get_provider_capabilities
        def mock_get_capabilities(provider_id):
            if provider_id not in mock_json:
                raise ConfigurationError(f"Provider not found in capability matrix: {provider_id}")
            return mock_json[provider_id]["capabilities"]

        # Test the function
        capabilities = mock_get_capabilities("google")
        assert capabilities == {"create_event": True, "read_event": True}

        # Test error case
        with pytest.raises(ConfigurationError):
            mock_get_capabilities("unknown")


class TestProviderCapabilities:
    """Tests for provider capability checking."""

    def test_has_capability(self):
        """Test the has_capability method."""
        provider = MockProvider()

        assert provider.has_capability(ProviderCapability.READ_EVENT) is True
        assert provider.has_capability(ProviderCapability.LIST_CALENDARS) is True
        assert provider.has_capability(ProviderCapability.CREATE_EVENT) is False
        assert provider.has_capability(ProviderCapability.UPDATE_EVENT) is False
        assert provider.has_capability(ProviderCapability.DELETE_EVENT) is False

        assert provider.has_capability("read_event") is True
        assert provider.has_capability("list_calendars") is True
        assert provider.has_capability("create_event") is False
        assert provider.has_capability("update_event") is False
        assert provider.has_capability("delete_event") is False

    def test_require_capability_success(self):
        """Test the require_capability method when the capability is supported."""
        provider = MockProvider()

        # Should not raise an exception
        provider.require_capability(ProviderCapability.READ_EVENT)
        provider.require_capability(ProviderCapability.LIST_CALENDARS)
        provider.require_capability("read_event")
        provider.require_capability("list_calendars")

    def test_require_capability_failure(self):
        """Test the require_capability method when the capability is not supported."""
        provider = MockProvider()

        # Should raise an exception
        with pytest.raises(UnsupportedOperationError):
            provider.require_capability(ProviderCapability.CREATE_EVENT)

        with pytest.raises(UnsupportedOperationError):
            provider.require_capability(ProviderCapability.UPDATE_EVENT)

        with pytest.raises(UnsupportedOperationError):
            provider.require_capability(ProviderCapability.DELETE_EVENT)

        with pytest.raises(UnsupportedOperationError):
            provider.require_capability("create_event")

        with pytest.raises(UnsupportedOperationError):
            provider.require_capability("update_event")

        with pytest.raises(UnsupportedOperationError):
            provider.require_capability("delete_event")
