from pi_lawyer.utils import demonstrate_paths


def test_generate_gcs_path_basic():
    # Only required args: tenant_id, case_id, document_category
    path = demonstrate_paths.generate_gcs_path(
        tenant_id="t1", case_id="c1", document_category="cat"
    )
    # Should start with tenant and case path and include category
    assert path.startswith("tenants/t1/cases/c1/cat/")


# TODO: Add more parameter variations and edge cases.
# TODO: Add integration/external API tests.
