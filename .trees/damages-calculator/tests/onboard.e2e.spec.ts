/**
 * End-to-End Tests for Automated Tenant Onboarding
 * 
 * Tests the complete flow of tenant creation and MCP key provisioning:
 * 1. Create tenant with 'pending_key' status
 * 2. Verify Cloud Function triggers and provisions API key
 * 3. Verify Secret Manager secret creation
 * 4. Verify tenant document update with secret path and 'active' status
 * 5. Verify deadlines tool can access the provisioned key
 */

import { test, expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';

// Mock implementations for Google Cloud services
const mockGoogleCloudServices = {
  apiKeys: {
    createKey: jest.fn(),
  },
  secretManager: {
    createSecret: jest.fn(),
    addSecretVersion: jest.fn(),
    setIamPolicy: jest.fn(),
    accessSecretVersion: jest.fn(),
  },
  firestore: {
    collection: jest.fn(),
    doc: jest.fn(),
    update: jest.fn(),
    get: jest.fn(),
  },
};

// Test configuration
const TEST_CONFIG = {
  PROJECT_ID: 'texas-laws-personalinjury',
  TENANT_COLLECTION: 'tenants',
  FUNCTION_TIMEOUT: 30000, // 30 seconds
  VERIFICATION_TIMEOUT: 5000, // 5 seconds
};

describe('Tenant Onboarding E2E Tests', () => {
  let testTenantId: string;
  let mockApiKey: string;
  let mockSecretName: string;

  beforeEach(() => {
    // Generate test data
    testTenantId = `test-tenant-${uuidv4()}`;
    mockApiKey = `test-api-key-${Date.now()}`;
    mockSecretName = `projects/${TEST_CONFIG.PROJECT_ID}/secrets/mcp-key-${testTenantId}`;

    // Reset mocks
    jest.clearAllMocks();

    // Setup mock responses
    mockGoogleCloudServices.apiKeys.createKey.mockResolvedValue([
      {
        promise: () => Promise.resolve([{
          name: `projects/${TEST_CONFIG.PROJECT_ID}/locations/global/keys/mcp-rules-api-${testTenantId}`,
          keyString: mockApiKey,
        }]),
      },
    ]);

    mockGoogleCloudServices.secretManager.createSecret.mockResolvedValue([{
      name: mockSecretName,
    }]);

    mockGoogleCloudServices.secretManager.addSecretVersion.mockResolvedValue([{
      name: `${mockSecretName}/versions/1`,
    }]);

    mockGoogleCloudServices.secretManager.setIamPolicy.mockResolvedValue([{}]);

    mockGoogleCloudServices.firestore.collection.mockReturnValue({
      doc: mockGoogleCloudServices.firestore.doc,
    });

    mockGoogleCloudServices.firestore.doc.mockReturnValue({
      update: mockGoogleCloudServices.firestore.update,
      get: mockGoogleCloudServices.firestore.get,
    });
  });

  afterEach(async () => {
    // Cleanup: Remove test tenant if it exists
    try {
      await mockGoogleCloudServices.firestore.collection(TEST_CONFIG.TENANT_COLLECTION)
        .doc(testTenantId)
        .delete();
    } catch (error) {
      console.warn(`Failed to cleanup test tenant ${testTenantId}:`, error);
    }
  });

  test('should automatically provision MCP key for new tenant', async () => {
    // Step 1: Create tenant document with 'pending_key' status
    const tenantData = {
      id: testTenantId,
      name: `Test Firm ${testTenantId}`,
      status: 'pending_key',
      created_at: new Date(),
    };

    // Mock Firestore document creation
    mockGoogleCloudServices.firestore.get.mockResolvedValue({
      exists: true,
      data: () => tenantData,
    });

    // Step 2: Simulate Cloud Function trigger
    const { onTenantCreate } = await import('../cloud-functions/onTenantCreate');
    
    const mockEvent = {
      params: { tenantId: testTenantId },
      data: {
        data: () => tenantData,
      },
    };

    // Execute the Cloud Function
    await expect(onTenantCreate(mockEvent as any)).resolves.not.toThrow();

    // Step 3: Verify API key creation
    expect(mockGoogleCloudServices.apiKeys.createKey).toHaveBeenCalledWith({
      parent: `projects/${TEST_CONFIG.PROJECT_ID}/locations/global`,
      keyId: `mcp-rules-api-${testTenantId}`,
      key: {
        displayName: `MCP Rules Engine API Key - ${testTenantId}`,
        restrictions: {},
      },
    });

    // Step 4: Verify Secret Manager secret creation
    expect(mockGoogleCloudServices.secretManager.createSecret).toHaveBeenCalledWith({
      parent: `projects/${TEST_CONFIG.PROJECT_ID}`,
      secretId: `mcp-key-${testTenantId}`,
      secret: {
        labels: {
          tenant: testTenantId,
          service: 'mcp-rules-engine',
          environment: expect.any(String),
          managed_by: 'cloud-function',
        },
        replication: { automatic: {} },
      },
    });

    // Step 5: Verify secret version creation
    expect(mockGoogleCloudServices.secretManager.addSecretVersion).toHaveBeenCalledWith({
      parent: mockSecretName,
      payload: {
        data: Buffer.from(mockApiKey, 'utf8'),
      },
    });

    // Step 6: Verify IAM policy setting
    expect(mockGoogleCloudServices.secretManager.setIamPolicy).toHaveBeenCalledWith({
      resource: mockSecretName,
      policy: {
        bindings: [{
          role: 'roles/secretmanager.secretAccessor',
          members: [
            `serviceAccount:main-jp@${TEST_CONFIG.PROJECT_ID}.iam.gserviceaccount.com`,
            `serviceAccount:mcp-client@${TEST_CONFIG.PROJECT_ID}.iam.gserviceaccount.com`,
          ],
        }],
      },
    });

    // Step 7: Verify tenant document update
    expect(mockGoogleCloudServices.firestore.update).toHaveBeenCalledWith({
      mcpSecretPath: mockSecretName,
      status: 'active',
      updated_at: expect.any(Date),
    });
  });

  test('should handle API key creation failure gracefully', async () => {
    // Setup: Mock API key creation failure
    mockGoogleCloudServices.apiKeys.createKey.mockRejectedValue(
      new Error('API key creation failed')
    );

    const tenantData = {
      id: testTenantId,
      name: `Test Firm ${testTenantId}`,
      status: 'pending_key',
      created_at: new Date(),
    };

    mockGoogleCloudServices.firestore.get.mockResolvedValue({
      exists: true,
      data: () => tenantData,
    });

    const { onTenantCreate } = await import('../cloud-functions/onTenantCreate');
    
    const mockEvent = {
      params: { tenantId: testTenantId },
      data: {
        data: () => tenantData,
      },
    };

    // Execute the Cloud Function and expect it to handle the error
    await expect(onTenantCreate(mockEvent as any)).rejects.toThrow('API key creation failed');

    // Verify error status update
    expect(mockGoogleCloudServices.firestore.update).toHaveBeenCalledWith({
      status: 'key_provisioning_failed',
      error: 'API key creation failed',
      updated_at: expect.any(Date),
    });
  });

  test('should skip processing for tenants without pending_key status', async () => {
    const tenantData = {
      id: testTenantId,
      name: `Test Firm ${testTenantId}`,
      status: 'active', // Not 'pending_key'
      created_at: new Date(),
    };

    const { onTenantCreate } = await import('../cloud-functions/onTenantCreate');
    
    const mockEvent = {
      params: { tenantId: testTenantId },
      data: {
        data: () => tenantData,
      },
    };

    // Execute the Cloud Function
    await expect(onTenantCreate(mockEvent as any)).resolves.not.toThrow();

    // Verify no API key creation was attempted
    expect(mockGoogleCloudServices.apiKeys.createKey).not.toHaveBeenCalled();
    expect(mockGoogleCloudServices.secretManager.createSecret).not.toHaveBeenCalled();
  });

  test('should verify deadlines tool can access provisioned key', async () => {
    // Setup: Mock successful key provisioning
    mockGoogleCloudServices.secretManager.accessSecretVersion.mockResolvedValue([{
      payload: {
        data: Buffer.from(mockApiKey, 'utf8'),
      },
    }]);

    // Import and test the deadlines tool
    const { getMcpApiKey } = await import('../backend/agents/interactive/deadline/tools/deadlinesTool');
    
    // Mock the tenant context to include the secret path
    process.env.TENANT_MCP_SECRET_PATH = mockSecretName;

    // Test API key retrieval
    const retrievedKey = await getMcpApiKey();
    expect(retrievedKey).toBe(mockApiKey);

    // Verify secret access
    expect(mockGoogleCloudServices.secretManager.accessSecretVersion).toHaveBeenCalledWith({
      name: `${mockSecretName}/versions/latest`,
    });
  });

  test('should handle concurrent tenant creation', async () => {
    // Create multiple tenants simultaneously
    const tenantIds = Array.from({ length: 3 }, () => `test-tenant-${uuidv4()}`);
    
    const createTenantPromises = tenantIds.map(async (tenantId) => {
      const tenantData = {
        id: tenantId,
        name: `Test Firm ${tenantId}`,
        status: 'pending_key',
        created_at: new Date(),
      };

      const { onTenantCreate } = await import('../cloud-functions/onTenantCreate');
      
      const mockEvent = {
        params: { tenantId },
        data: {
          data: () => tenantData,
        },
      };

      return onTenantCreate(mockEvent as any);
    });

    // Execute all tenant creations concurrently
    await expect(Promise.all(createTenantPromises)).resolves.not.toThrow();

    // Verify each tenant got its own API key and secret
    expect(mockGoogleCloudServices.apiKeys.createKey).toHaveBeenCalledTimes(tenantIds.length);
    expect(mockGoogleCloudServices.secretManager.createSecret).toHaveBeenCalledTimes(tenantIds.length);
  });
});
