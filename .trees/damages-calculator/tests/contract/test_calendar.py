"""
Contract tests for the Calendar CRUD Agent.

This module contains contract tests for the Calendar CRUD Agent, verifying that
it correctly implements the expected behavior for calendar operations including
conflict detection.
"""

from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from langchain_core.messages import HumanMessage

from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
from backend.agents.interactive.calendar_crud.providers.models import (
    CalendarEvent,
    FreeBusyResponse,
    TimeSlot,
)


@pytest.fixture
def mock_providers():
    """Create mock calendar providers."""
    # Create a mock Google Calendar provider
    google_provider = MagicMock()
    google_provider.provider_id = "google"
    google_provider.provider_name = "Google Calendar"
    google_provider.has_capability.return_value = True
    google_provider.supports = AsyncMock(return_value=True)
    google_provider.events = []
    
    # Mock create_event
    async def mock_create_event(calendar_id, event):
        event_id = f"google-{len(google_provider.events) + 1}"
        new_event = CalendarEvent(
            id=event_id,
            provider_id="google-123",
            summary=event.summary,
            description=event.description,
            start_time=event.start_time,
            end_time=event.end_time,
            location=event.location,
            attendees=event.attendees,
            status="confirmed"
        )
        google_provider.events.append(new_event.model_dump())
        return new_event
    
    google_provider.create_event = AsyncMock(side_effect=mock_create_event)
    
    # Mock update_event
    async def mock_update_event(calendar_id, event_id, event):
        for i, existing_event in enumerate(google_provider.events):
            if existing_event["id"] == event_id:
                # Update the event
                updated_event = {**existing_event}
                if hasattr(event, "summary") and event.summary is not None:
                    updated_event["summary"] = event.summary
                if hasattr(event, "description") and event.description is not None:
                    updated_event["description"] = event.description
                if hasattr(event, "start_time") and event.start_time is not None:
                    updated_event["start_time"] = event.start_time
                if hasattr(event, "end_time") and event.end_time is not None:
                    updated_event["end_time"] = event.end_time
                if hasattr(event, "location") and event.location is not None:
                    updated_event["location"] = event.location
                if hasattr(event, "attendees") and event.attendees is not None:
                    updated_event["attendees"] = [a.model_dump() for a in event.attendees]
                
                google_provider.events[i] = updated_event
                
                return CalendarEvent(**updated_event)
        
        # If event not found, raise an exception
        raise Exception(f"Event {event_id} not found")
    
    google_provider.update_event = AsyncMock(side_effect=mock_update_event)
    
    # Mock delete_event
    async def mock_delete_event(calendar_id, event_id):
        for i, existing_event in enumerate(google_provider.events):
            if existing_event["id"] == event_id:
                del google_provider.events[i]
                return True
        return False
    
    google_provider.delete_event = AsyncMock(side_effect=mock_delete_event)
    
    # Mock get_event
    async def mock_get_event(calendar_id, event_id):
        for existing_event in google_provider.events:
            if existing_event["id"] == event_id:
                return CalendarEvent(**existing_event)
        raise Exception(f"Event {event_id} not found")
    
    google_provider.get_event = AsyncMock(side_effect=mock_get_event)
    
    # Mock check_free_busy
    async def mock_check_free_busy(firm_id, request):
        # Return busy slots for existing events that overlap with the request time range
        busy_slots = []
        for event in google_provider.events:
            event_start = event["start_time"]
            event_end = event["end_time"]
            
            # Check if the event overlaps with the request time range
            if event_start < request.end_time and event_end > request.start_time:
                busy_slots.append(TimeSlot(
                    start=event_start,
                    end=event_end,
                    event_id=event["id"]
                ))
        
        return FreeBusyResponse(
            calendars={request.calendar_ids[0]: busy_slots},
            time_min=request.start_time,
            time_max=request.end_time
        )
    
    google_provider.check_free_busy = AsyncMock(side_effect=mock_check_free_busy)
    
    # Create a mock Calendly provider
    calendly_provider = MagicMock()
    calendly_provider.provider_id = "calendly"
    calendly_provider.provider_name = "Calendly"
    
    # Calendly has limited capabilities
    def has_capability(capability):
        return capability in ["create_event", "free_busy"]
    
    calendly_provider.has_capability = MagicMock(side_effect=has_capability)
    calendly_provider.supports = AsyncMock(side_effect=lambda cap: has_capability(cap))
    
    # Mock get_scheduling_link
    async def mock_get_scheduling_link():
        return "https://calendly.com/test-user/30min"
    
    calendly_provider.get_scheduling_link = AsyncMock(side_effect=mock_get_scheduling_link)
    
    # Return the mock providers
    return {
        "google": google_provider,
        "calendly": calendly_provider
    }


@pytest.fixture
def mock_llm():
    """Create mock LLM clients for different nodes."""
    # Create mock LLM clients
    router_client = AsyncMock()
    create_client = AsyncMock()
    read_client = AsyncMock()
    update_client = AsyncMock()
    delete_client = AsyncMock()
    free_busy_client = AsyncMock()
    date_parser_client = AsyncMock()
    
    # Patch the VoyageClient
    with patch("backend.agents.interactive.calendar_crud.router.VoyageClient", return_value=router_client), \
         patch("backend.agents.interactive.calendar_crud.nodes.create.VoyageClient", return_value=create_client), \
         patch("backend.agents.interactive.calendar_crud.nodes.read.VoyageClient", return_value=read_client), \
         patch("backend.agents.interactive.calendar_crud.nodes.update.VoyageClient", return_value=update_client), \
         patch("backend.agents.interactive.calendar_crud.nodes.delete.VoyageClient", return_value=delete_client), \
         patch("backend.agents.interactive.calendar_crud.nodes.free_busy.VoyageClient", return_value=free_busy_client), \
         patch("backend.agents.interactive.calendar_crud.nodes.parse_date.VoyageClient", return_value=date_parser_client):
        
        yield {
            "router": router_client,
            "create": create_client,
            "read": read_client,
            "update": update_client,
            "delete": delete_client,
            "free_busy": free_busy_client,
            "date_parser": date_parser_client
        }


@pytest.fixture
def mock_provider_factory(mock_providers):
    """Create a mock provider factory."""
    # Patch the get_provider_instance function
    with patch("backend.agents.interactive.calendar_crud.providers.factory.get_provider_instance") as mock_factory:
        def get_provider(provider_id, tenant_id=None):
            return mock_providers.get(provider_id, mock_providers["google"])
        
        mock_factory.side_effect = get_provider
        yield mock_factory


@pytest.mark.asyncio
async def test_create_event_no_conflicts(mock_provider_factory, mock_llm):
    """Test creating an event with no conflicts."""
    # Configure mocks
    mock_llm["router"].invoke.return_value = "create_event"
    mock_llm["create"].invoke.return_value = """```json
{
  "summary": "Meeting with John",
  "description": "Regular meeting",
  "start_time": "2023-06-01T14:00:00",
  "end_time": "2023-06-01T15:00:00",
  "location": "Office",
  "attendees": ["<EMAIL>"],
  "calendar_id": "primary",
  "provider": "google"
}
```"""
    
    # Create agent
    agent = CalendarCrudAgent()
    
    # Invoke agent
    result = await agent.invoke({
        "messages": [HumanMessage(content="Create a meeting with John tomorrow at 2pm")],
        "configurable": {
            "thread_id": "test-thread",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
    })
    
    # Assert
    assert result is not None
    assert "messages" in result
    assert len(result["messages"]) == 2
    assert "Event created successfully!" in result["messages"][1].content
    assert "event" in result
    
    assert result["event"]["summary"] == "Meeting with John"
    assert result["event"]["provider_id"] == "google-123"
    
    # Verify no conflict warning
    assert "Warning" not in result["messages"][1].content


@pytest.mark.asyncio
async def test_create_event_with_conflicts(mock_provider_factory, mock_llm, mock_providers):
    """Test creating an event with conflicts."""
    # Add an existing event to create a conflict
    existing_event = CalendarEvent(
        id="google-existing",
        provider_id="google-123",
        summary="Existing Meeting",
        description="This is an existing meeting",
        start_time=datetime(2023, 6, 1, 13, 30, tzinfo=timezone.utc),
        end_time=datetime(2023, 6, 1, 14, 30, tzinfo=timezone.utc),
        location="Office",
        attendees=[],
        status="confirmed"
    )
    mock_providers["google"].events.append(existing_event.model_dump())
    
    # Configure mocks
    mock_llm["router"].invoke.return_value = "create_event"
    mock_llm["create"].invoke.return_value = """```json
{
  "summary": "Meeting with John",
  "description": "Regular meeting",
  "start_time": "2023-06-01T14:00:00",
  "end_time": "2023-06-01T15:00:00",
  "location": "Office",
  "attendees": ["<EMAIL>"],
  "calendar_id": "primary",
  "provider": "google"
}
```"""
    
    # Create agent
    agent = CalendarCrudAgent()
    
    # Invoke agent
    result = await agent.invoke({
        "messages": [HumanMessage(content="Create a meeting with John tomorrow at 2pm")],
        "configurable": {
            "thread_id": "test-thread",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
    })
    
    # Assert
    assert result is not None
    assert "messages" in result
    assert len(result["messages"]) == 2
    assert "Event created successfully!" in result["messages"][1].content
    assert "event" in result
    
    # Verify conflict warning
    assert "Warning" in result["messages"][1].content
    assert "conflicts with existing events" in result["messages"][1].content


@pytest.mark.asyncio
async def test_update_event(mock_provider_factory, mock_llm, mock_providers):
    """Test updating an event."""
    # Add an existing event to update
    existing_event = CalendarEvent(
        id="google-update-test",
        provider_id="google-123",
        summary="Original Meeting",
        description="This is the original meeting",
        start_time=datetime(2023, 6, 1, 10, 0, tzinfo=timezone.utc),
        end_time=datetime(2023, 6, 1, 11, 0, tzinfo=timezone.utc),
        location="Office",
        attendees=[],
        status="confirmed"
    )
    mock_providers["google"].events.append(existing_event.model_dump())
    
    # Configure mocks
    mock_llm["router"].invoke.return_value = "update_event"
    mock_llm["update"].invoke.return_value = """```json
{
  "event_id": "google-update-test",
  "calendar_id": "primary",
  "provider": "google",
  "updates": {
    "summary": "Updated Meeting",
    "description": "This is the updated meeting",
    "start_time": "2023-06-01T11:00:00",
    "end_time": "2023-06-01T12:00:00"
  }
}
```"""
    
    # Create agent
    agent = CalendarCrudAgent()
    
    # Invoke agent
    result = await agent.invoke({
        "messages": [HumanMessage(content="Update my meeting to start at 11am instead of 10am")],
        "configurable": {
            "thread_id": "test-thread",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
    })
    
    # Assert
    assert result is not None
    assert "messages" in result
    assert len(result["messages"]) == 2
    assert "Event updated successfully!" in result["messages"][1].content
    assert "event" in result
    
    # Verify the event was updated
    assert result["event"]["summary"] == "Updated Meeting"
    assert "2023-06-01T11:00:00" in str(result["event"]["start_time"])


@pytest.mark.asyncio
async def test_delete_event(mock_provider_factory, mock_llm, mock_providers):
    """Test deleting an event."""
    # Add an existing event to delete
    existing_event = CalendarEvent(
        id="google-delete-test",
        provider_id="google-123",
        summary="Meeting to Delete",
        description="This meeting will be deleted",
        start_time=datetime(2023, 6, 1, 15, 0, tzinfo=timezone.utc),
        end_time=datetime(2023, 6, 1, 16, 0, tzinfo=timezone.utc),
        location="Office",
        attendees=[],
        status="confirmed"
    )
    mock_providers["google"].events.append(existing_event.model_dump())
    
    # Configure mocks
    mock_llm["router"].invoke.return_value = "delete_event"
    mock_llm["delete"].invoke.return_value = """```json
{
  "event_id": "google-delete-test",
  "calendar_id": "primary",
  "provider": "google"
}
```"""
    
    # Create agent
    agent = CalendarCrudAgent()
    
    # Invoke agent
    result = await agent.invoke({
        "messages": [HumanMessage(content="Delete my 3pm meeting")],
        "configurable": {
            "thread_id": "test-thread",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
    })
    
    # Assert
    assert result is not None
    assert "messages" in result
    assert len(result["messages"]) == 2
    assert "Event deleted successfully" in result["messages"][1].content
    
    # Verify the event was deleted
    assert len([e for e in mock_providers["google"].events if e["id"] == "google-delete-test"]) == 0
