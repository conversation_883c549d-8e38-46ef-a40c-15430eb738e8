# PI Lawyer AI Test Suite

This directory contains comprehensive tests for the PI Lawyer AI application, with a focus on the client intake process and the LangGraph migration.

## LangGraph Migration Tests

The LangGraph migration tests follow a structured approach as outlined in the [LangGraph-Testing-Strategy.md](../docs/migration/LangGraph-Testing-Strategy.md) document. These tests are organized into three categories:

- **Unit Tests** (`tests/unit/`): Tests for individual components in isolation
- **Integration Tests** (`tests/integration/`): Tests for interactions between components
- **End-to-End Tests** (`tests/e2e/`): Tests for complete user workflows

### Running LangGraph Migration Tests

```bash
# Run all LangGraph migration tests
pytest tests/unit/ tests/integration/ tests/e2e/

# Run specific test categories
pytest tests/unit/middleware/  # Run JWT middleware tests
```

### Implemented LangGraph Tests

- **JWT Middleware**:
  - Unit tests: `tests/unit/middleware/test_jwt_middleware.py`
  - Integration tests: `tests/integration/api/test_jwt_integration.py`

## Testing Approach

Our testing approach follows a multi-layered strategy:

1. **Unit Tests**: Testing individual components and functions
2. **Integration Tests**: Testing the interaction between components
3. **API Tests**: Testing the API endpoints
4. **AI Integration Tests**: Testing AI-assisted features
5. **End-to-End Tests**: Testing the complete user flow

## Test Components

### Supabase Function Tests

- **File**: `client-intake-test.js`
- **Purpose**: Tests the `create_client_intake` Supabase function
- **Run Command**: `node client-intake-test.js`

These tests validate the core function responsible for creating client and case records in the database.

### Python API Tests

- **File**: `python-api-test.py`
- **Purpose**: Provides a comprehensive test suite for the client intake API
- **Run Command**: `python python-api-test.py --run-all` or `python python-api-test.py --test basic_client_intake`

This tool can test both direct database access and REST API endpoints with the same test cases.

### PyTest Tests

- **File**: `tests/test_client_intake.py`
- **Purpose**: Tests the client intake functionality using pytest
- **Run Command**: `pytest tests/test_client_intake.py -v`

These tests provide comprehensive validation with detailed failure output.

### AI Integration Tests

- **File**: `tests/test_ai_intake.py`
- **Purpose**: Tests the AI capabilities for extracting client information and assisting with form completion
- **Run Command**: `pytest tests/test_ai_intake.py -v`

These tests validate that the AI components correctly analyze conversations and assist with intake.

### Cypress End-to-End Tests

- **Directory**: `cypress/e2e/`
- **Purpose**: Tests the complete user flow through the browser
- **Run Command**: `cd frontend && npx cypress run`

These tests validate the complete client intake process from the user's perspective.

## Continuous Integration

The tests are integrated into our CI/CD pipeline using GitHub Actions:

- **Workflow File**: `.github/workflows/test-client-intake.yml`
- **Trigger**: Push to main/develop branches or relevant file changes
- **Jobs**:
  - Supabase Function Tests
  - Python API Tests
  - PyTest Tests
  - AI Integration Tests
  - Cypress E2E Tests

## Setting Up for Local Testing

1. Create a `.env` file in the project root with the following variables:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_key
   OPENAI_API_KEY=your_openai_key
   ```

2. Install dependencies:
   ```bash
   # For Node.js tests
   npm install

   # For Python tests
   pip install -r requirements.txt
   pip install pytest pytest-cov requests

   # For Cypress tests
   cd frontend
   npm install
   npm install cypress --save-dev
   ```

## Best Practices

1. **Run tests before committing**: Always run tests locally before pushing changes
2. **Add tests for new features**: Every new feature should include tests
3. **Add regression tests**: When fixing bugs, add tests to prevent recurrence
4. **Keep tests isolated**: Tests should not depend on each other
5. **Use unique test data**: Generate unique data for each test run

## Test Data Management

- All test helpers include functions to generate unique test data
- Test data is isolated by using timestamps in names and emails
- Data cleanup should be performed manually or by admin scripts

## Troubleshooting

If tests are failing, check:

1. Environment variables are correctly set
2. Supabase is accessible from your environment
3. The required permissions are in place
4. The database schema matches what the tests expect

For more detailed logs, add the `-v` flag to pytest commands or set environment variables to increase logging.
