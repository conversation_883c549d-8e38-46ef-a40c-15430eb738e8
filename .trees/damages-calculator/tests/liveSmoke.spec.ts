/**
 * Live Smoke Tests for MCP Rules Engine Integration
 * 
 * This test file contains live integration tests that hit the real MCP Gateway
 * to verify the production integration is working correctly.
 * 
 * These tests are designed to run only in CI staging environment and use
 * real secrets from Secret Manager to test the complete integration flow.
 */

import { describe, it, expect, beforeAll } from '@jest/globals';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { McpClient } from '@ailex/mcp-client';

// Skip all tests in this module if not in CI staging environment
const isCI = process.env.CI === 'true';
const isStaging = process.env.NODE_ENV === 'staging' || process.env.APP_ENV === 'staging';
const skipTests = !isCI || !isStaging;

describe('MCP Rules Engine Live Smoke Tests', () => {
  let mcpClient: McpClient;
  let secretManager: SecretManagerServiceClient;

  beforeAll(async () => {
    if (skipTests) {
      console.log('Skipping live smoke tests - not in CI staging environment');
      return;
    }

    // Initialize Secret Manager client
    secretManager = new SecretManagerServiceClient();

    // Get API key from Secret Manager
    const secretName = process.env.MCP_API_KEY || 'projects/PROJECT_ID/secrets/mcp-key-test-tenant/versions/latest';
    
    try {
      const [version] = await secretManager.accessSecretVersion({
        name: secretName,
      });

      const apiKey = version.payload?.data?.toString();
      if (!apiKey) {
        throw new Error('Secret Manager returned empty API key');
      }

      // Initialize MCP client with production Gateway URL
      mcpClient = new McpClient({
        baseUrl: process.env.MCP_RULES_BASE || 'https://rules.ailexlaw.com',
        apiKey,
        timeout: 30000,
        maxRetries: 3,
      });

      console.log('Live smoke test setup complete - using real Gateway and secrets');
    } catch (error) {
      console.error('Failed to setup live smoke test:', error);
      throw error;
    }
  });

  it('should successfully call the production MCP Gateway and return deadlines', async () => {
    if (skipTests) {
      console.log('Skipping test - not in CI staging environment');
      return;
    }

    // Test parameters
    const testParams = {
      jurisdiction: 'TX_STATE',
      triggerCode: 'SERVICE_OF_PROCESS',
      startDate: '2025-01-15',
      practiceArea: 'personal_injury',
    };

    console.log('Testing MCP Gateway with parameters:', testParams);

    // Call the real MCP Gateway
    const response = await mcpClient.calculateDeadlines(
      testParams.jurisdiction,
      testParams.triggerCode,
      testParams.startDate,
      testParams.practiceArea
    );

    // Assertions
    expect(response).toBeDefined();
    expect(response.deadlines).toBeDefined();
    expect(Array.isArray(response.deadlines)).toBe(true);
    expect(response.deadlines.length).toBeGreaterThanOrEqual(1);

    // Validate deadline structure
    if (response.deadlines.length > 0) {
      const firstDeadline = response.deadlines[0];
      expect(firstDeadline).toHaveProperty('id');
      expect(firstDeadline).toHaveProperty('name');
      expect(firstDeadline).toHaveProperty('dueDate');
      expect(firstDeadline).toHaveProperty('priority');
      expect(firstDeadline).toHaveProperty('category');
    }

    console.log(`✅ Live smoke test passed! Retrieved ${response.deadlines.length} deadline(s) from production Gateway`);
  }, 60000); // 60 second timeout for network calls

  it('should handle authentication correctly with Secret Manager API key', async () => {
    if (skipTests) {
      console.log('Skipping test - not in CI staging environment');
      return;
    }

    // This test verifies that the Secret Manager integration works
    // by making a simple call that would fail with authentication errors
    // if the API key retrieval or usage was incorrect

    const testParams = {
      jurisdiction: 'CA_STATE',
      triggerCode: 'COMPLAINT_FILED',
      startDate: '2025-02-01',
      practiceArea: 'personal_injury',
    };

    // Should not throw authentication errors
    const response = await mcpClient.calculateDeadlines(
      testParams.jurisdiction,
      testParams.triggerCode,
      testParams.startDate,
      testParams.practiceArea
    );

    // Basic response validation
    expect(response).toBeDefined();
    expect(response.deadlines).toBeDefined();

    console.log('✅ Authentication test passed - Secret Manager API key working correctly');
  }, 30000);

  it('should return proper error handling for invalid requests', async () => {
    if (skipTests) {
      console.log('Skipping test - not in CI staging environment');
      return;
    }

    // Test with invalid jurisdiction to verify error handling
    const invalidParams = {
      jurisdiction: 'INVALID_STATE',
      triggerCode: 'SERVICE_OF_PROCESS',
      startDate: '2025-01-15',
      practiceArea: 'personal_injury',
    };

    try {
      await mcpClient.calculateDeadlines(
        invalidParams.jurisdiction,
        invalidParams.triggerCode,
        invalidParams.startDate,
        invalidParams.practiceArea
      );
      
      // If we get here without an error, that's also acceptable
      // as the Gateway might handle invalid jurisdictions gracefully
      console.log('✅ Gateway handled invalid jurisdiction gracefully');
    } catch (error) {
      // Expect a proper error response, not a network/auth error
      expect(error).toBeDefined();
      expect(error instanceof Error).toBe(true);
      
      // Should not be authentication or network errors
      const errorMessage = error.message.toLowerCase();
      expect(errorMessage).not.toContain('unauthorized');
      expect(errorMessage).not.toContain('forbidden');
      expect(errorMessage).not.toContain('network');
      
      console.log('✅ Gateway returned proper error for invalid request:', error.message);
    }
  }, 30000);
});

// Export test configuration for CI
export const testConfig = {
  requiresCI: true,
  requiresStaging: true,
  requiresSecrets: true,
  timeout: 60000,
};
