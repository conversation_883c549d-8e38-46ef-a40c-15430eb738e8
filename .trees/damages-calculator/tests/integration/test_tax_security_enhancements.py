"""
Comprehensive Security Tests for Tax Calculation System

Tests role-based access control, tenant isolation, rate limiting,
audit logging, and cross-tenant data leakage prevention.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, AsyncMock
from uuid import uuid4

pytestmark = pytest.mark.integration

from fastapi import HTTPException, status
from fastapi.testclient import TestClient

from backend.api.routes.tax_calculation import (
    check_rate_limit,
    log_tax_audit,
    calculate_tax,
    validate_vat_number,
)
from backend.services.tax_calculation_service import (
    TaxCalculationService,
    TaxCalculationResult,
    VATValidationResult,
)


class TestRoleBasedAccessControl:
    """Test suite for role-based access control on tax endpoints."""

    @pytest.mark.asyncio
    async def test_calculate_tax_requires_admin_role(self):
        """Test that calculate_tax endpoint requires admin or partner role."""
        # Mock user with insufficient role
        mock_user = {
            "user_id": str(uuid4()),
            "tenant_id": str(uuid4()),
            "email": "<EMAIL>",
            "role": "user",  # Insufficient role
        }

        with patch(
            "backend.api.routes.tax_calculation.require_role"
        ) as mock_require_role:
            mock_require_role.side_effect = HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
            )

            with pytest.raises(HTTPException) as exc_info:
                # This would be called by the endpoint
                mock_require_role([])

            assert exc_info.value.status_code == 403

    @pytest.mark.asyncio
    async def test_vat_validation_requires_admin_role(self):
        """Test that VAT validation endpoint requires admin or partner role."""
        # Mock user with insufficient role
        mock_user = {
            "user_id": str(uuid4()),
            "tenant_id": str(uuid4()),
            "email": "<EMAIL>",
            "role": "user",  # Insufficient role
        }

        with patch(
            "backend.api.routes.tax_calculation.require_role"
        ) as mock_require_role:
            mock_require_role.side_effect = HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
            )

            with pytest.raises(HTTPException) as exc_info:
                mock_require_role([])

            assert exc_info.value.status_code == 403

    @pytest.mark.asyncio
    async def test_admin_role_access_granted(self):
        """Test that admin role grants access to tax endpoints."""
        mock_user = {
            "user_id": str(uuid4()),
            "tenant_id": str(uuid4()),
            "email": "<EMAIL>",
            "role": "admin",
        }

        with patch(
            "backend.api.routes.tax_calculation.require_role"
        ) as mock_require_role:
            mock_require_role.return_value = mock_user

            # Should not raise exception
            result = mock_require_role([])
            assert result == mock_user


class TestTenantIsolation:
    """Test suite for tenant isolation in tax calculations."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = TaxCalculationService()
        self.tenant_a = str(uuid4())
        self.tenant_b = str(uuid4())

    @pytest.mark.asyncio
    @patch("backend.services.tax_calculation_service.get_db_session")
    async def test_vat_cache_tenant_isolation(self, mock_db_session):
        """Test that VAT validation cache enforces tenant isolation."""
        # Mock database session
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        # Mock cache query result - should only return records for specific tenant
        mock_result = Mock()
        mock_result.fetchone.return_value = None  # Cache miss
        mock_db.execute.return_value = mock_result

        # Test VAT validation with tenant context
        with patch.object(self.service, "_validate_with_stripe") as mock_stripe:
            mock_stripe.return_value = VATValidationResult(
                vat_number="BE0123456789", country_code="BE", is_valid=True
            )

            # Validate for tenant A
            result_a = await self.service.validate_vat_number(
                "BE0123456789", "BE", self.tenant_a
            )

            # Verify database query includes tenant isolation
            mock_db.execute.assert_called()
            # The query should include tenant_id filter

    @pytest.mark.asyncio
    @patch("backend.services.tax_calculation_service.get_db_session")
    async def test_cross_tenant_vat_cache_isolation(self, mock_db_session):
        """Test that tenants cannot access each other's VAT cache."""
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        # Mock cache with data for tenant A
        mock_cached_record = Mock()
        mock_cached_record.vat_number = "BE0123456789"
        mock_cached_record.country_code = "BE"
        mock_cached_record.validation_status = "valid"
        mock_cached_record.tenant_id = self.tenant_a

        # When tenant B queries, should not find tenant A's cache
        mock_result = Mock()
        mock_result.fetchone.return_value = None  # No cache hit for different tenant
        mock_db.execute.return_value = mock_result

        # Test that tenant B doesn't get tenant A's cached result
        result = await self.service.validate_vat_number(
            "BE0123456789", "BE", self.tenant_b
        )

        # Should not find cached result from different tenant
        assert mock_result.fetchone.return_value is None

    @pytest.mark.asyncio
    async def test_tax_calculation_audit_includes_tenant_context(self):
        """Test that tax calculations include requesting tenant context."""
        tenant_id = str(uuid4())

        with patch.object(self.service, "save_tax_calculation") as mock_save:
            # Mock successful calculation
            result = TaxCalculationResult(
                subtotal_amount=Decimal("100.00"),
                tax_amount=Decimal("21.00"),
                total_amount=Decimal("121.00"),
                tax_rate=Decimal("0.21"),
                tax_type="vat",
                currency="EUR",
                customer_country="BE",
                customer_type="B2C",
            )

            # Verify tenant context is passed to audit
            subscription_id = uuid4()
            await self.service.save_tax_calculation(subscription_id, result)

            # Should be called with tenant context
            mock_save.assert_called_once()


class TestRateLimiting:
    """Test suite for per-tenant rate limiting."""

    @pytest.mark.asyncio
    @patch("backend.api.routes.tax_calculation.get_db_session")
    async def test_rate_limit_enforcement(self, mock_db_session):
        """Test that rate limiting is enforced per tenant."""
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        tenant_id = str(uuid4())
        user_id = str(uuid4())

        # Mock existing rate limit record at threshold
        mock_rate_record = Mock()
        mock_rate_record.request_count = 99  # Near limit
        mock_rate_record.is_blocked = False
        mock_rate_record.blocked_until = None
        mock_rate_record.id = uuid4()

        mock_result = Mock()
        mock_result.fetchone.return_value = mock_rate_record
        mock_db.execute.return_value = mock_result

        # First request should succeed
        remaining = await check_rate_limit(
            tenant_id=tenant_id,
            endpoint="/api/tax/calculate",
            action="calculate_tax",
            user_id=user_id,
            db=mock_db,
            limit=100,
        )

        assert remaining == 0  # Should be at limit

        # Mock rate limit exceeded
        mock_rate_record.request_count = 100
        mock_rate_record.is_blocked = True
        mock_rate_record.blocked_until = datetime.utcnow() + timedelta(hours=1)

        # Next request should raise exception
        with pytest.raises(HTTPException) as exc_info:
            await check_rate_limit(
                tenant_id=tenant_id,
                endpoint="/api/tax/calculate",
                action="calculate_tax",
                user_id=user_id,
                db=mock_db,
                limit=100,
            )

        assert exc_info.value.status_code == 429
        assert "Rate limit exceeded" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("backend.api.routes.tax_calculation.get_db_session")
    async def test_rate_limit_per_tenant_isolation(self, mock_db_session):
        """Test that rate limits are isolated per tenant."""
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        tenant_a = str(uuid4())
        tenant_b = str(uuid4())
        user_id = str(uuid4())

        # Mock no existing rate limit record (new tenant)
        mock_result = Mock()
        mock_result.fetchone.return_value = None
        mock_db.execute.return_value = mock_result

        # Both tenants should start with full quota
        remaining_a = await check_rate_limit(
            tenant_id=tenant_a,
            endpoint="/api/tax/calculate",
            action="calculate_tax",
            user_id=user_id,
            db=mock_db,
            limit=100,
        )

        remaining_b = await check_rate_limit(
            tenant_id=tenant_b,
            endpoint="/api/tax/calculate",
            action="calculate_tax",
            user_id=user_id,
            db=mock_db,
            limit=100,
        )

        # Both should have same remaining quota (isolated)
        assert remaining_a == 99
        assert remaining_b == 99


class TestAuditLogging:
    """Test suite for comprehensive audit logging."""

    @pytest.mark.asyncio
    @patch("backend.api.routes.tax_calculation.get_db_session")
    async def test_audit_log_captures_request_details(self, mock_db_session):
        """Test that audit logging captures all required request details."""
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        # Mock request object
        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.client.host = "***********"
        mock_request.headers = {"user-agent": "test-client"}

        user_context = {
            "user_id": str(uuid4()),
            "tenant_id": str(uuid4()),
            "email": "<EMAIL>",
            "role": "admin",
        }

        request_data = {"amount": 100.0, "currency": "EUR", "customer_country": "BE"}

        response_data = {"tax_amount": 21.0, "stripe_calculation_id": "calc_123"}

        # Test audit logging
        await log_tax_audit(
            user_context=user_context,
            request=mock_request,
            action="calculate_tax",
            endpoint="/api/tax/calculate",
            request_data=request_data,
            response_status=200,
            response_data=response_data,
            request_duration_ms=150,
            rate_limit_remaining=99,
            db=mock_db,
        )

        # Verify audit record was inserted
        mock_db.execute.assert_called()
        mock_db.commit.assert_called()

    @pytest.mark.asyncio
    @patch("backend.api.routes.tax_calculation.get_db_session")
    async def test_audit_log_captures_errors(self, mock_db_session):
        """Test that audit logging captures error details."""
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.client.host = "***********"
        mock_request.headers = {"user-agent": "test-client"}

        user_context = {
            "user_id": str(uuid4()),
            "tenant_id": str(uuid4()),
            "email": "<EMAIL>",
            "role": "admin",
        }

        # Test error logging
        await log_tax_audit(
            user_context=user_context,
            request=mock_request,
            action="calculate_tax",
            endpoint="/api/tax/calculate",
            request_data={},
            response_status=500,
            error_message="Internal server error",
            error_code="INTERNAL_ERROR",
            db=mock_db,
        )

        # Verify error was logged
        mock_db.execute.assert_called()
        mock_db.commit.assert_called()


class TestFallbackReconciliation:
    """Test suite for fallback reconciliation functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = TaxCalculationService()

    @pytest.mark.asyncio
    @patch("backend.services.tax_calculation_service.get_db_session")
    async def test_reconcile_fallback_calculations(self, mock_db_session):
        """Test fallback reconciliation process."""
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        # Mock pending reconciliation records
        mock_reconciliation = Mock()
        mock_reconciliation.id = uuid4()
        mock_reconciliation.tenant_id = str(uuid4())
        mock_reconciliation.reconciliation_attempts = 0
        mock_reconciliation.max_attempts = 3
        mock_reconciliation.fallback_amount = Decimal("100.00")
        mock_reconciliation.fallback_tax_amount = Decimal("21.00")
        mock_reconciliation.original_calculation_id = uuid4()

        mock_result = Mock()
        mock_result.fetchall.return_value = [mock_reconciliation]
        mock_db.execute.return_value = mock_result

        # Test reconciliation
        stats = await self.service.reconcile_fallback_calculations()

        # Verify reconciliation was attempted
        assert "total_pending" in stats
        assert "reconciled" in stats
        assert "failed" in stats
        assert "skipped" in stats

    @pytest.mark.asyncio
    async def test_reconciliation_max_attempts(self):
        """Test that reconciliation respects maximum attempts."""
        # This would test the max attempts logic
        # Implementation depends on actual reconciliation logic
        pass


class TestSecurityIntegration:
    """Integration tests for complete security flow."""

    @pytest.mark.asyncio
    async def test_complete_tax_calculation_security_flow(self):
        """Test complete security flow for tax calculation."""
        # This would test the entire flow:
        # 1. Authentication check
        # 2. Role-based authorization
        # 3. Rate limiting
        # 4. Tenant isolation
        # 5. Audit logging
        # 6. Response
        pass

    @pytest.mark.asyncio
    async def test_cross_tenant_data_leakage_prevention(self):
        """Test that cross-tenant data leakage is prevented."""
        # This would test various scenarios where tenant A
        # might try to access tenant B's data
        pass


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
