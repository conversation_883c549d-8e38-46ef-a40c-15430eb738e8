"""
Multi-Factor Authentication (MFA) Integration Tests

Comprehensive test suite for MFA functionality including TOTP setup,
verification, session management, and security scenarios.
"""

import pytest
import asyncio
import pyotp
import hashlib
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from backend.api.main import create_app
from backend.services.mfa_service import mfa_service

pytestmark = pytest.mark.integration
from backend.models.mfa import (
    MFAMethod,
    SuperadminMFAConfig,
    SuperadminMFASession,
    BackupMFAToken,
)


@pytest.fixture
def app():
    """Create test FastAPI application."""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_supabase():
    """Mock Supabase client."""
    with patch("backend.services.mfa_service.get_supabase_client") as mock:
        mock_client = Mock()
        mock.return_value = mock_client
        yield mock_client


@pytest.fixture
def test_user_id():
    """Test user ID."""
    return "550e8400-e29b-41d4-a716-446655440000"


@pytest.fixture
def test_user_email():
    """Test user email."""
    return "<EMAIL>"


@pytest.fixture
def auth_headers():
    """Mock authentication headers."""
    return {"Authorization": "Bearer test_token"}


class TestMFAService:
    """Test MFA service functionality."""

    @pytest.mark.asyncio
    async def test_get_mfa_config(self, mock_supabase, test_user_id):
        """Test getting MFA configuration."""
        # Mock database response
        mock_supabase.table.return_value.select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
            "id": "config-id",
            "user_id": test_user_id,
            "email": "<EMAIL>",
            "mfa_required": True,
            "recovery_codes": ["CODE1", "CODE2"],
            "recovery_codes_used": 0,
            "session_timeout_hours": 8,
            "max_failed_attempts": 5,
            "lockout_duration_minutes": 30,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
        }

        config = await mfa_service.get_mfa_config(test_user_id)

        assert config is not None
        assert config.user_id == test_user_id
        assert config.mfa_required is True
        assert len(config.recovery_codes) == 2

    @pytest.mark.asyncio
    async def test_create_mfa_config(
        self, mock_supabase, test_user_id, test_user_email
    ):
        """Test creating MFA configuration."""
        # Mock database response
        mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
            {
                "id": "config-id",
                "user_id": test_user_id,
                "email": test_user_email,
                "mfa_required": True,
                "mfa_enforced_at": datetime.utcnow().isoformat(),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
            }
        ]

        config = await mfa_service.create_mfa_config(test_user_id, test_user_email)

        assert config.user_id == test_user_id
        assert config.email == test_user_email
        assert config.mfa_required is True

    @pytest.mark.asyncio
    async def test_setup_totp(self, mock_supabase, test_user_id, test_user_email):
        """Test TOTP setup."""
        # Mock database responses
        mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
            {
                "id": "factor-id",
                "user_id": test_user_id,
                "factor_type": "totp",
                "status": "unverified",
            }
        ]

        setup_response = await mfa_service.setup_totp(test_user_id, test_user_email)

        assert setup_response.secret is not None
        assert len(setup_response.secret) == 32  # Base32 secret length
        assert setup_response.qr_code_uri.startswith("otpauth://totp/")
        assert len(setup_response.backup_codes) == 10
        assert "Google Authenticator" in setup_response.setup_instructions

    @pytest.mark.asyncio
    async def test_verify_totp_setup_success(self, mock_supabase, test_user_id):
        """Test successful TOTP setup verification."""
        # Generate a valid TOTP token
        secret = pyotp.random_base32()
        totp = pyotp.TOTP(secret)
        valid_token = totp.now()

        # Mock database responses
        mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value.execute.return_value.data = {
            "id": "factor-id",
            "user_id": test_user_id,
            "secret": mfa_service._encrypt_secret(secret),
            "status": "unverified",
        }

        result = await mfa_service.verify_totp_setup(
            test_user_id, valid_token, "factor-id"
        )

        assert result is True

    @pytest.mark.asyncio
    async def test_verify_totp_setup_failure(self, mock_supabase, test_user_id):
        """Test failed TOTP setup verification."""
        secret = pyotp.random_base32()
        invalid_token = "123456"  # Invalid token

        # Mock database responses
        mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value.execute.return_value.data = {
            "id": "factor-id",
            "user_id": test_user_id,
            "secret": mfa_service._encrypt_secret(secret),
            "status": "unverified",
        }

        result = await mfa_service.verify_totp_setup(
            test_user_id, invalid_token, "factor-id"
        )

        assert result is False

    @pytest.mark.asyncio
    async def test_verify_totp_authentication(self, mock_supabase, test_user_id):
        """Test TOTP authentication verification."""
        secret = pyotp.random_base32()
        totp = pyotp.TOTP(secret)
        valid_token = totp.now()

        # Mock database responses
        mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value.data = [
            {
                "id": "factor-id",
                "user_id": test_user_id,
                "secret": mfa_service._encrypt_secret(secret),
                "status": "verified",
            }
        ]

        result = await mfa_service.verify_totp(test_user_id, valid_token)

        assert result is True

    @pytest.mark.asyncio
    async def test_create_mfa_session(self, mock_supabase, test_user_id):
        """Test MFA session creation."""
        # Mock database responses
        mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
            {
                "id": "session-id",
                "user_id": test_user_id,
                "session_token": "test-token",
                "verified_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(hours=8)).isoformat(),
                "ip_address": "127.0.0.1",
                "mfa_method": "totp",
                "is_active": True,
            }
        ]

        session = await mfa_service.create_mfa_session(
            user_id=test_user_id,
            mfa_method=MFAMethod.TOTP,
            ip_address="127.0.0.1",
            user_agent="test-agent",
        )

        assert session.user_id == test_user_id
        assert session.mfa_method == MFAMethod.TOTP
        assert session.is_active is True

    @pytest.mark.asyncio
    async def test_validate_mfa_session(self, mock_supabase, test_user_id):
        """Test MFA session validation."""
        # Mock database response
        mock_supabase.rpc.return_value.execute.return_value.data = True

        result = await mfa_service.validate_mfa_session(test_user_id, "test-token")

        assert result is True

    @pytest.mark.asyncio
    async def test_get_mfa_status(self, mock_supabase, test_user_id):
        """Test getting MFA status."""
        # Mock MFA config
        mock_supabase.table.return_value.select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
            "user_id": test_user_id,
            "mfa_required": True,
            "recovery_codes": ["CODE1", "CODE2"],
            "recovery_codes_used": 0,
        }

        # Mock verified factors
        mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [
            {"factor_type": "totp", "status": "verified"}
        ]

        # Mock active session
        mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.gte.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {"expires_at": (datetime.utcnow() + timedelta(hours=4)).isoformat()}
        ]

        status = await mfa_service.get_mfa_status(test_user_id)

        assert status.mfa_required is True
        assert status.mfa_configured is True
        assert MFAMethod.TOTP in status.available_methods
        assert status.recovery_codes_available == 2
        assert status.session_valid is True


class TestMFAAPI:
    """Test MFA API endpoints."""

    def test_get_mfa_status_endpoint(self, client, auth_headers):
        """Test MFA status endpoint."""
        with patch("backend.api.dependencies.auth.require_super_admin") as mock_auth:
            mock_auth.return_value = Mock(
                user_id="test-user", email="<EMAIL>"
            )

            with patch(
                "backend.services.mfa_service.mfa_service.get_mfa_status"
            ) as mock_status:
                mock_status.return_value = Mock(
                    mfa_required=True,
                    mfa_configured=False,
                    available_methods=[],
                    backup_methods_configured={"sms": False, "email": False},
                    recovery_codes_available=0,
                    session_valid=False,
                )

                response = client.get("/api/auth/mfa/status", headers=auth_headers)

                assert response.status_code == 200
                data = response.json()
                assert data["mfa_required"] is True
                assert data["mfa_configured"] is False

    def test_setup_totp_endpoint(self, client, auth_headers):
        """Test TOTP setup endpoint."""
        with patch("backend.api.dependencies.auth.require_super_admin") as mock_auth:
            mock_auth.return_value = Mock(
                user_id="test-user", email="<EMAIL>"
            )

            with patch(
                "backend.services.mfa_service.mfa_service.get_mfa_config"
            ) as mock_config:
                mock_config.return_value = None

                with patch(
                    "backend.services.mfa_service.mfa_service.create_mfa_config"
                ) as mock_create:
                    mock_create.return_value = Mock()

                    with patch(
                        "backend.services.mfa_service.mfa_service.setup_totp"
                    ) as mock_setup:
                        mock_setup.return_value = Mock(
                            secret="TESTSECRET123456",
                            qr_code_uri="otpauth://totp/test",
                            backup_codes=["CODE1", "CODE2"],
                            setup_instructions="Test instructions",
                        )

                        response = client.post(
                            "/api/auth/mfa/setup/totp", headers=auth_headers
                        )

                        assert response.status_code == 200
                        data = response.json()
                        assert "secret" in data
                        assert "qr_code_uri" in data
                        assert "backup_codes" in data


class TestMFAMiddleware:
    """Test MFA middleware functionality."""

    def test_mfa_exempt_paths(self, client):
        """Test that exempt paths don't require MFA."""
        # Test health endpoint (should be exempt)
        response = client.get("/health")
        assert response.status_code == 200

    def test_mfa_required_for_superadmin_paths(self, client, auth_headers):
        """Test that superadmin paths require MFA."""
        with patch(
            "backend.middleware.mfa_middleware.mfa_service.get_mfa_status"
        ) as mock_status:
            mock_status.return_value = Mock(mfa_required=True, mfa_configured=False)

            # This would require proper authentication setup
            # For now, just test that the middleware is loaded
            assert True  # Placeholder for actual middleware test


class TestMFASecurity:
    """Test MFA security scenarios."""

    @pytest.mark.asyncio
    async def test_totp_window_tolerance(self, mock_supabase, test_user_id):
        """Test TOTP time window tolerance."""
        secret = pyotp.random_base32()
        totp = pyotp.TOTP(secret)

        # Generate token for previous time window
        previous_token = totp.at(datetime.now().timestamp() - 30)

        # Mock database responses
        mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value.data = [
            {
                "id": "factor-id",
                "user_id": test_user_id,
                "secret": mfa_service._encrypt_secret(secret),
                "status": "verified",
            }
        ]

        # Should accept token from previous window (within tolerance)
        result = await mfa_service.verify_totp(test_user_id, previous_token)
        assert result is True

    @pytest.mark.asyncio
    async def test_recovery_code_usage(self, mock_supabase, test_user_id):
        """Test recovery code verification and one-time use."""
        recovery_code = "TESTCODE"
        encrypted_code = mfa_service._encrypt_secret(recovery_code)

        # Mock MFA config with recovery codes
        mock_supabase.table.return_value.select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
            "user_id": test_user_id,
            "recovery_codes": [encrypted_code, "OTHER_CODE"],
            "recovery_codes_used": 0,
        }

        result = await mfa_service.verify_recovery_code(test_user_id, recovery_code)

        assert result is True
        # Verify that the code was removed from the list
        mock_supabase.table.return_value.update.assert_called()

    def test_session_token_generation(self):
        """Test session token generation uniqueness."""
        token1 = mfa_service._generate_session_token()
        token2 = mfa_service._generate_session_token()

        assert token1 != token2
        assert len(token1) > 20  # Ensure sufficient length
        assert len(token2) > 20

    def test_recovery_code_generation(self):
        """Test recovery code generation."""
        codes = mfa_service._generate_recovery_codes(10)

        assert len(codes) == 10
        assert len(set(codes)) == 10  # All codes should be unique

        for code in codes:
            assert len(code) == 8
            assert code.isalnum()
            assert code.isupper()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
