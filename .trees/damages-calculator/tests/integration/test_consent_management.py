"""
Test suite for consent management functionality.

Tests GDPR and CCPA compliance features including:
- Recording and withdrawing consent
- Managing privacy preferences
- Data export functionality
- Database functions and constraints
"""

import pytest
import json
from datetime import datetime, timedelta
from uuid import uuid4
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

pytestmark = pytest.mark.integration

from backend.api.main import create_app
from backend.db.supabase_client import get_supabase_client
from backend.api.dependencies.auth import UserContext


@pytest.fixture
def app():
    """Create test FastAPI app"""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_supabase():
    """Mock Supabase client"""
    return Mock()


@pytest.fixture
def mock_auth_user():
    """Mock authenticated user"""
    return UserContext(
        id=uuid4(),
        email="<EMAIL>",
        name="Test User",
        tenant_id=uuid4(),
        role="user",
    )


@pytest.fixture
def sample_consent_request():
    """Sample consent request data"""
    return {
        "consent_type": "marketing",
        "consent_given": True,
        "consent_method": "preferences",
        "legal_basis": "consent",
        "purpose_description": "Marketing communications and promotional content",
        "data_categories": ["email", "preferences"],
        "retention_period_days": 2555,  # 7 years
        "third_party_sharing": False,
        "automated_decision_making": False,
    }


def setup_dependency_overrides(app, mock_supabase, mock_auth_user):
    """Helper function to setup dependency overrides for tests"""
    from backend.api.dependencies.auth import get_current_user
    from backend.db.supabase_client import get_supabase_client

    def mock_get_current_user():
        return mock_auth_user

    def mock_get_supabase_client():
        return mock_supabase

    app.dependency_overrides[get_current_user] = mock_get_current_user
    app.dependency_overrides[get_supabase_client] = mock_get_supabase_client


class TestConsentRecording:
    """Test consent recording functionality"""

    def test_record_consent_success(
        self, app, client, mock_supabase, mock_auth_user, sample_consent_request
    ):
        """Test successful consent recording"""

        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            consent_id = str(uuid4())

            # Setup mock for the RPC call
            mock_rpc_result = Mock()
            mock_rpc_result.data = consent_id
            mock_supabase.rpc.return_value.execute.return_value = mock_rpc_result

            consent_record = {
                "id": consent_id,
                "consent_type": "marketing",
                "consent_given": True,
                "consent_date": datetime.utcnow().isoformat(),
                "legal_basis": "consent",
                "purpose_description": "Marketing communications",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "withdrawn_date": None,
            }

            # Setup mock for the table query
            mock_table_result = Mock()
            mock_table_result.data = [consent_record]
            mock_supabase.table.return_value.select.return_value.eq.return_value.execute.return_value = (
                mock_table_result
            )

            # Make request
            response = client.post("/consent/record", json=sample_consent_request)

            # Debug: Print response details if test fails
            if response.status_code != 200:
                print(f"Response status: {response.status_code}")
                print(f"Response body: {response.text}")
                print(f"Request data: {sample_consent_request}")

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == consent_id
            assert data["consent_type"] == "marketing"
            assert data["consent_given"] is True

            # Verify database function was called correctly
            mock_supabase.rpc.assert_called_once_with(
                "record_user_consent",
                {
                    "p_auth_user_id": None,  # UserContext doesn't have auth_user_id
                    "p_tenant_user_id": mock_auth_user.id,  # UUID object, not string
                    "p_tenant_id": mock_auth_user.tenant_id,  # UUID object, not string
                    "p_consent_type": "marketing",
                    "p_consent_given": True,
                    "p_consent_method": "preferences",
                    "p_legal_basis": "consent",
                    "p_purpose_description": "Marketing communications and promotional content",
                    "p_ip_address": "testclient",  # Test client provides this value
                    "p_user_agent": "testclient",  # Test client provides this value
                },
            )
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_record_consent_invalid_type(self, client, sample_consent_request):
        """Test consent recording with invalid consent type"""
        sample_consent_request["consent_type"] = "invalid_type"

        response = client.post("/consent/record", json=sample_consent_request)

        assert response.status_code == 422  # Validation error

    def test_record_consent_invalid_legal_basis(self, client, sample_consent_request):
        """Test consent recording with invalid legal basis"""
        sample_consent_request["legal_basis"] = "invalid_basis"

        response = client.post("/consent/record", json=sample_consent_request)

        assert response.status_code == 422  # Validation error


class TestConsentRetrieval:
    """Test consent retrieval functionality"""

    def test_get_consent_summary(self, app, client, mock_supabase, mock_auth_user):
        """Test getting consent summary"""

        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            summary_data = [
                {
                    "consent_type": "marketing",
                    "consent_given": True,
                    "consent_date": datetime.utcnow().isoformat(),
                    "withdrawn_date": None,
                    "legal_basis": "consent",
                    "purpose_description": "Marketing communications",
                },
                {
                    "consent_type": "analytics",
                    "consent_given": False,
                    "consent_date": (datetime.utcnow() - timedelta(days=1)).isoformat(),
                    "withdrawn_date": datetime.utcnow().isoformat(),
                    "legal_basis": "consent",
                    "purpose_description": "Analytics tracking",
                },
            ]

            mock_supabase.rpc.return_value.execute.return_value.data = summary_data

            # Make request
            response = client.get("/consent/summary")

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["consent_type"] == "marketing"
            assert data[0]["consent_given"] is True
            assert data[1]["consent_type"] == "analytics"
            assert data[1]["consent_given"] is False
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_check_consent(self, app, client, mock_supabase, mock_auth_user):
        """Test checking specific consent type"""
        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            mock_supabase.rpc.return_value.execute.return_value.data = True

            # Make request
            response = client.get("/consent/check/marketing")

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["consent_type"] == "marketing"
            assert data["has_consent"] is True
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()


class TestConsentWithdrawal:
    """Test consent withdrawal functionality"""

    def test_withdraw_consent(self, app, client, mock_supabase, mock_auth_user):
        """Test withdrawing consent"""
        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            consent_id = str(uuid4())

            # Setup mock for the RPC call
            mock_rpc_result = Mock()
            mock_rpc_result.data = consent_id
            mock_supabase.rpc.return_value.execute.return_value = mock_rpc_result

            withdrawal_record = {
                "id": consent_id,
                "consent_type": "marketing",
                "consent_given": False,
                "consent_date": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                "withdrawn_date": datetime.utcnow().isoformat(),
                "legal_basis": "consent",
                "purpose_description": "Withdrawal of marketing consent",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
            }

            # Setup mock for the table query
            mock_table_result = Mock()
            mock_table_result.data = [withdrawal_record]
            mock_supabase.table.return_value.select.return_value.eq.return_value.execute.return_value = (
                mock_table_result
            )

            # Make request
            response = client.post("/consent/withdraw/marketing")

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["consent_given"] is False
            assert data["withdrawn_date"] is not None
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()


class TestConsentPreferences:
    """Test consent preferences functionality"""

    def test_get_preferences(self, app, client, mock_supabase, mock_auth_user):
        """Test getting consent preferences"""
        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            preferences_data = {
                "id": str(uuid4()),
                "privacy_policy_version": "1.0",
                "terms_of_service_version": "1.0",
                "cookie_policy_version": "1.0",
                "email_marketing": False,
                "email_product_updates": True,
                "email_security_alerts": True,
                "email_legal_notices": True,
                "analytics_tracking": False,
                "personalization": False,
                "third_party_sharing": False,
                "automated_decisions": False,
                "data_residency_preference": "AUTO",
                "language_preference": "en",
                "timezone": None,
                "ccpa_opt_out_sale": False,
                "ccpa_opt_out_targeted_ads": False,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
            }

            mock_supabase.table.return_value.select.return_value.eq.return_value.execute.return_value.data = [
                preferences_data
            ]

            # Make request
            response = client.get("/consent/preferences")

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["email_marketing"] is False
            assert data["email_security_alerts"] is True
            assert data["analytics_tracking"] is False
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_update_preferences(self, app, client, mock_supabase, mock_auth_user):
        """Test updating consent preferences"""
        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            updated_preferences = {
                "id": str(uuid4()),
                "privacy_policy_version": "1.0",
                "terms_of_service_version": "1.0",
                "cookie_policy_version": "1.0",
                "email_marketing": True,
                "email_product_updates": True,
                "email_security_alerts": True,
                "email_legal_notices": True,
                "analytics_tracking": True,
                "personalization": False,
                "third_party_sharing": False,
                "automated_decisions": False,
                "data_residency_preference": "AUTO",
                "language_preference": "en",
                "timezone": None,
                "ccpa_opt_out_sale": True,
                "ccpa_opt_out_targeted_ads": False,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
            }

            mock_supabase.table.return_value.update.return_value.eq.return_value.execute.return_value.data = [
                updated_preferences
            ]

            update_data = {
                "email_marketing": True,
                "analytics_tracking": True,
                "ccpa_opt_out_sale": True,
            }

            # Make request
            response = client.put("/consent/preferences", json=update_data)

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["email_marketing"] is True
            assert data["analytics_tracking"] is True
            assert data["ccpa_opt_out_sale"] is True
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()


class TestDataExport:
    """Test data export functionality"""

    def test_export_consent_data(self, app, client, mock_supabase, mock_auth_user):
        """Test exporting consent data"""
        # Setup dependency overrides
        setup_dependency_overrides(app, mock_supabase, mock_auth_user)

        try:
            consent_records = [
                {
                    "id": str(uuid4()),
                    "consent_type": "marketing",
                    "consent_given": True,
                    "consent_date": datetime.utcnow().isoformat(),
                    "legal_basis": "consent",
                    "purpose_description": "Marketing communications",
                }
            ]

            preferences_data = {
                "id": str(uuid4()),
                "email_marketing": True,
                "analytics_tracking": False,
            }

            # Mock multiple table queries
            mock_supabase.table.return_value.select.return_value.eq.return_value.execute.side_effect = [
                Mock(data=consent_records),  # First call for consent records
                Mock(data=[preferences_data]),  # Second call for preferences
            ]

            # Make request
            response = client.get("/consent/export")

            # Assertions
            assert response.status_code == 200
            data = response.json()

            assert "export_metadata" in data
            assert "consent_records" in data
            assert "consent_preferences" in data
            assert "data_categories" in data
            assert "legal_bases" in data
            assert "retention_information" in data
            assert "your_rights" in data

            assert data["export_metadata"]["user_id"] == str(mock_auth_user.id)
            assert data["export_metadata"]["export_format"] == "JSON"
            assert len(data["consent_records"]) == 1
            assert data["consent_records"][0]["consent_type"] == "marketing"
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()


class TestDatabaseFunctions:
    """Test database functions directly"""

    def test_consent_type_validation(self):
        """Test that consent type validation works correctly"""
        valid_types = [
            "marketing",
            "analytics",
            "cookies",
            "essential",
            "personalization",
            "communication",
            "profiling",
            "third_party_sharing",
            "automated_decisions",
        ]

        # This would be tested with actual database connection
        # For now, we verify the types are defined correctly
        from backend.api.routes.consent_management import ConsentRequest

        for consent_type in valid_types:
            request = ConsentRequest(
                consent_type=consent_type,
                consent_given=True,
                consent_method="api",
                legal_basis="consent",
                purpose_description="Test purpose",
            )
            assert request.consent_type == consent_type

    def test_legal_basis_validation(self):
        """Test that legal basis validation works correctly"""
        valid_bases = [
            "consent",
            "contract",
            "legitimate_interest",
            "legal_obligation",
            "vital_interests",
            "public_task",
        ]

        from backend.api.routes.consent_management import ConsentRequest

        for legal_basis in valid_bases:
            request = ConsentRequest(
                consent_type="marketing",
                consent_given=True,
                consent_method="api",
                legal_basis=legal_basis,
                purpose_description="Test purpose",
            )
            assert request.legal_basis == legal_basis


if __name__ == "__main__":
    pytest.main([__file__])
