"""
Test Suite for Regional Legal Disclaimers

This module tests the regional disclaimer functionality including service logic,
API endpoints, and integration with data residency controls.
"""

import pytest
import os
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from uuid import uuid4
from datetime import datetime, timezone

pytestmark = pytest.mark.integration

# Set testing environment variables before importing modules
os.environ.update(
    {
        "TESTING": "true",
        "ENABLE_GEOLOCATION": "false",
        "ENABLE_STRICT_DATA_RESIDENCY": "false",
        "NEXT_PUBLIC_SUPABASE_URL": "https://test.supabase.co",
        "SUPABASE_SERVICE_KEY": "test_service_key",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY": "test_anon_key",
        "SUPABASE_EU_URL": "https://test-eu.supabase.co",
        "SUPABASE_EU_SERVICE_KEY": "test_eu_service_key",
        "SUPABASE_EU_ANON_KEY": "test_eu_anon_key",
    }
)

from backend.models.legal_disclaimer import (
    LegalDisclaimer,
    LegalDisclaimerCreate,
    DisclaimerType,
    DisclaimerRegion,
    DisclaimerPlacement,
    DisclaimerDisplayContext,
    DisclaimerResponse,
)
from backend.services.regional_disclaimer_service import RegionalDisclaimerService
from backend.services.data_residency import DataRegion
from backend.api.main import create_app


class TestLegalDisclaimerModels:
    """Test legal disclaimer model functionality."""

    def test_disclaimer_type_enum(self):
        """Test DisclaimerType enum values."""
        assert (
            DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP
            == "attorney_client_relationship"
        )
        assert DisclaimerType.ATTORNEY_ADVERTISING == "attorney_advertising"
        assert DisclaimerType.DATA_PROCESSING == "data_processing"
        assert len(DisclaimerType) == 10

    def test_disclaimer_region_enum(self):
        """Test DisclaimerRegion enum values."""
        assert DisclaimerRegion.US == "US"
        assert DisclaimerRegion.EU == "EU"
        assert DisclaimerRegion.GLOBAL == "GLOBAL"
        assert len(DisclaimerRegion) == 3

    def test_disclaimer_placement_enum(self):
        """Test DisclaimerPlacement enum values."""
        assert DisclaimerPlacement.FOOTER == "footer"
        assert DisclaimerPlacement.HEADER == "header"
        assert DisclaimerPlacement.MODAL == "modal"
        assert len(DisclaimerPlacement) == 8

    def test_legal_disclaimer_creation(self):
        """Test LegalDisclaimer model creation."""
        disclaimer = LegalDisclaimer(
            title="Test Disclaimer",
            content="This is a test disclaimer content.",
            disclaimer_type=DisclaimerType.NO_LEGAL_ADVICE,
            region=DisclaimerRegion.US,
            placement=[DisclaimerPlacement.FOOTER],
            priority=1,
        )

        assert disclaimer.title == "Test Disclaimer"
        assert disclaimer.disclaimer_type == DisclaimerType.NO_LEGAL_ADVICE
        assert disclaimer.region == DisclaimerRegion.US
        assert DisclaimerPlacement.FOOTER in disclaimer.placement
        assert disclaimer.priority == 1
        assert disclaimer.is_required is True  # Default value
        assert disclaimer.id is not None
        assert disclaimer.version == 1

    def test_disclaimer_display_context(self):
        """Test DisclaimerDisplayContext model."""
        context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.EU,
            placement=DisclaimerPlacement.FOOTER,
            practice_area="personal_injury",
            jurisdiction="belgium",
        )

        assert context.user_region == DisclaimerRegion.EU
        assert context.placement == DisclaimerPlacement.FOOTER
        assert context.practice_area == "personal_injury"
        assert context.jurisdiction == "belgium"
        assert context.language == "en"  # Default value


class TestRegionalDisclaimerService:
    """Test regional disclaimer service functionality."""

    @pytest.fixture
    def service(self):
        """Create a disclaimer service for testing."""
        return RegionalDisclaimerService()

    def test_data_region_mapping(self, service):
        """Test mapping from data region to disclaimer region."""
        assert (
            service.map_data_region_to_disclaimer_region(DataRegion.US)
            == DisclaimerRegion.US
        )
        assert (
            service.map_data_region_to_disclaimer_region(DataRegion.EU)
            == DisclaimerRegion.EU
        )

    @pytest.mark.asyncio
    async def test_fetch_default_disclaimers_us(self, service):
        """Test fetching default US disclaimers."""
        context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.US, placement=DisclaimerPlacement.FOOTER
        )

        disclaimers = await service._fetch_disclaimers(context)

        # Should have US disclaimers
        us_disclaimers = [d for d in disclaimers if d.region == DisclaimerRegion.US]
        assert len(us_disclaimers) > 0

        # Check for required US disclaimer types
        disclaimer_types = [d.disclaimer_type for d in us_disclaimers]
        assert DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP in disclaimer_types
        assert DisclaimerType.ATTORNEY_ADVERTISING in disclaimer_types

    @pytest.mark.asyncio
    async def test_fetch_default_disclaimers_eu(self, service):
        """Test fetching default EU disclaimers."""
        context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.EU, placement=DisclaimerPlacement.FOOTER
        )

        disclaimers = await service._fetch_disclaimers(context)

        # Should have EU disclaimers
        eu_disclaimers = [d for d in disclaimers if d.region == DisclaimerRegion.EU]
        assert len(eu_disclaimers) > 0

        # Check for required EU disclaimer types
        disclaimer_types = [d.disclaimer_type for d in eu_disclaimers]
        assert DisclaimerType.NO_LEGAL_ADVICE in disclaimer_types
        assert DisclaimerType.DATA_PROCESSING in disclaimer_types

    def test_filter_disclaimers_by_placement(self, service):
        """Test filtering disclaimers by placement."""
        disclaimers = [
            LegalDisclaimer(
                title="Footer Disclaimer",
                content="Footer content",
                disclaimer_type=DisclaimerType.NO_LEGAL_ADVICE,
                region=DisclaimerRegion.US,
                placement=[DisclaimerPlacement.FOOTER],
            ),
            LegalDisclaimer(
                title="Header Disclaimer",
                content="Header content",
                disclaimer_type=DisclaimerType.ATTORNEY_ADVERTISING,
                region=DisclaimerRegion.US,
                placement=[DisclaimerPlacement.HEADER],
            ),
        ]

        context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.US, placement=DisclaimerPlacement.FOOTER
        )

        filtered = service._filter_disclaimers(disclaimers, context)

        assert len(filtered) == 1
        assert filtered[0].title == "Footer Disclaimer"

    def test_filter_disclaimers_by_region(self, service):
        """Test filtering disclaimers by region."""
        disclaimers = [
            LegalDisclaimer(
                title="US Disclaimer",
                content="US content",
                disclaimer_type=DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP,
                region=DisclaimerRegion.US,
                placement=[DisclaimerPlacement.FOOTER],
            ),
            LegalDisclaimer(
                title="EU Disclaimer",
                content="EU content",
                disclaimer_type=DisclaimerType.DATA_PROCESSING,
                region=DisclaimerRegion.EU,
                placement=[DisclaimerPlacement.FOOTER],
            ),
            LegalDisclaimer(
                title="Global Disclaimer",
                content="Global content",
                disclaimer_type=DisclaimerType.NO_LEGAL_ADVICE,
                region=DisclaimerRegion.GLOBAL,
                placement=[DisclaimerPlacement.FOOTER],
            ),
        ]

        # Test US region filtering
        us_context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.US, placement=DisclaimerPlacement.FOOTER
        )

        us_filtered = service._filter_disclaimers(disclaimers, us_context)
        us_titles = [d.title for d in us_filtered]

        assert "US Disclaimer" in us_titles
        assert "Global Disclaimer" in us_titles
        assert "EU Disclaimer" not in us_titles

        # Test EU region filtering
        eu_context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.EU, placement=DisclaimerPlacement.FOOTER
        )

        eu_filtered = service._filter_disclaimers(disclaimers, eu_context)
        eu_titles = [d.title for d in eu_filtered]

        assert "EU Disclaimer" in eu_titles
        assert "Global Disclaimer" in eu_titles
        assert "US Disclaimer" not in eu_titles

    def test_sort_disclaimers(self, service):
        """Test disclaimer sorting by priority and type."""
        disclaimers = [
            LegalDisclaimer(
                title="Low Priority",
                content="Content",
                disclaimer_type=DisclaimerType.THIRD_PARTY_LINKS,
                region=DisclaimerRegion.US,
                placement=[DisclaimerPlacement.FOOTER],
                priority=3,
            ),
            LegalDisclaimer(
                title="High Priority",
                content="Content",
                disclaimer_type=DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP,
                region=DisclaimerRegion.US,
                placement=[DisclaimerPlacement.FOOTER],
                priority=1,
            ),
            LegalDisclaimer(
                title="Medium Priority",
                content="Content",
                disclaimer_type=DisclaimerType.ATTORNEY_ADVERTISING,
                region=DisclaimerRegion.US,
                placement=[DisclaimerPlacement.FOOTER],
                priority=2,
            ),
        ]

        sorted_disclaimers = service._sort_disclaimers(disclaimers)

        assert sorted_disclaimers[0].title == "High Priority"
        assert sorted_disclaimers[1].title == "Medium Priority"
        assert sorted_disclaimers[2].title == "Low Priority"

    def test_cache_key_generation(self, service):
        """Test cache key generation."""
        context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.US,
            placement=DisclaimerPlacement.FOOTER,
            practice_area="personal_injury",
        )

        cache_key = service._generate_cache_key(context)
        expected = "disclaimers:US:footer:personal_injury"

        assert cache_key == expected

    @pytest.mark.asyncio
    async def test_get_disclaimers_for_region(self, service):
        """Test getting disclaimers for a region."""
        context = DisclaimerDisplayContext(
            user_region=DisclaimerRegion.US, placement=DisclaimerPlacement.FOOTER
        )

        with patch.object(service, "_log_disclaimer_access", new_callable=AsyncMock):
            response = await service.get_disclaimers_for_region(context)

        assert isinstance(response, DisclaimerResponse)
        assert len(response.disclaimers) > 0
        assert response.context.user_region == DisclaimerRegion.US
        assert response.total_count == len(response.disclaimers)

    @pytest.mark.asyncio
    async def test_create_disclaimer(self, service):
        """Test creating a new disclaimer."""
        disclaimer_data = LegalDisclaimerCreate(
            title="Test Disclaimer",
            content="Test content",
            disclaimer_type=DisclaimerType.NO_LEGAL_ADVICE,
            region=DisclaimerRegion.US,
            placement=[DisclaimerPlacement.FOOTER],
        )

        created_by = uuid4()

        with patch.object(service, "_clear_cache_for_region"):
            disclaimer = await service.create_disclaimer(disclaimer_data, created_by)

        assert disclaimer.title == "Test Disclaimer"
        assert disclaimer.created_by == created_by
        assert disclaimer.updated_by == created_by
        assert disclaimer.version == 1


class TestRegionalDisclaimerAPI:
    """Test regional disclaimer API endpoints."""

    @pytest.fixture
    def client(self):
        """Create a test client."""
        app = create_app()
        return TestClient(app)

    @pytest.fixture
    def mock_user(self):
        """Create a mock user for testing."""
        return Mock(id=uuid4(), tenant_id=uuid4(), role="attorney")

    def test_get_regional_disclaimers_endpoint(self, client, mock_user):
        """Test the get regional disclaimers endpoint."""
        with patch(
            "backend.api.routes.regional_disclaimers.get_current_user",
            return_value=None,
        ):
            with patch(
                "backend.api.routes.regional_disclaimers.get_user_region_from_request"
            ) as mock_region:
                with patch(
                    "backend.api.routes.regional_disclaimers.regional_disclaimer_service.get_disclaimers_for_region"
                ) as mock_service:

                    mock_region.return_value = DataRegion.US
                    mock_service.return_value = DisclaimerResponse(
                        disclaimers=[],
                        total_count=0,
                        context=DisclaimerDisplayContext(
                            user_region=DisclaimerRegion.US,
                            placement=DisclaimerPlacement.FOOTER,
                        ),
                        last_updated=datetime.now(timezone.utc),
                    )

                    response = client.get("/regional-disclaimers/")

                    assert response.status_code == 200
                    data = response.json()
                    assert "disclaimers" in data
                    assert "total_count" in data
                    assert "context" in data

    def test_get_disclaimers_by_region_endpoint(self, client, mock_user):
        """Test the get disclaimers by region endpoint."""
        with patch(
            "backend.api.routes.regional_disclaimers.get_current_user",
            return_value=mock_user,
        ):
            with patch(
                "backend.api.routes.regional_disclaimers.regional_disclaimer_service.get_regional_disclaimer_set"
            ) as mock_service:

                from backend.models.legal_disclaimer import RegionalDisclaimerSet

                mock_service.return_value = RegionalDisclaimerSet(
                    region=DisclaimerRegion.US,
                    disclaimers=[],
                    version="2024.1",
                    compliance_frameworks=["ABA Model Rules"],
                    last_updated=datetime.now(timezone.utc),
                )

                response = client.get("/regional-disclaimers/region/US")

                assert response.status_code == 200
                data = response.json()
                assert data["region"] == "US"
                assert "disclaimers" in data
                assert "compliance_frameworks" in data

    def test_get_disclaimer_types_endpoint(self, client, mock_user):
        """Test the get disclaimer types endpoint."""
        with patch(
            "backend.api.routes.regional_disclaimers.get_current_user",
            return_value=None,
        ):
            response = client.get("/regional-disclaimers/types")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == len(DisclaimerType)

            # Check structure
            for item in data:
                assert "type" in item
                assert "description" in item
                assert "required_regions" in item

    def test_get_disclaimer_placements_endpoint(self, client, mock_user):
        """Test the get disclaimer placements endpoint."""
        with patch(
            "backend.api.routes.regional_disclaimers.get_current_user",
            return_value=None,
        ):
            response = client.get("/regional-disclaimers/placements")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == len(DisclaimerPlacement)

            # Check structure
            for item in data:
                assert "placement" in item
                assert "description" in item
                assert "recommended_types" in item


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
