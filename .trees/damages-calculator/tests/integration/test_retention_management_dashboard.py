"""
Tests for Retention Management Dashboard

This module contains comprehensive tests for the retention management dashboard
including API endpoints, service integration, and UI component functionality.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

pytestmark = pytest.mark.integration

from backend.api.routes.data_retention_dashboard import (
    RetentionPolicyDashboard,
    LegalHoldDashboard,
    CleanupJobDashboard,
    ComplianceMetricsDashboard,
    CreateRetentionPolicyRequest,
    CreateLegalHoldRequest,
    ScheduleCleanupRequest,
)
from backend.models.data_retention import DataRegion, RetentionBasis, DataSensitivity


class TestRetentionDashboardAPI:
    """Test the retention dashboard API endpoints."""

    @pytest.fixture
    def mock_supabase_client(self):
        """Mock Supabase client for testing."""
        mock_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        mock_result = Mock()

        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        mock_query.order.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.execute.return_value = mock_result

        return mock_client, mock_result

    @pytest.fixture
    def sample_retention_policy_data(self):
        """Sample retention policy data for testing."""
        return {
            "id": "policy-123",
            "data_type": "user_profiles",
            "region": "US",
            "retention_days": 1095,
            "legal_basis": "LEGITIMATE_INTEREST",
            "sensitivity": "MEDIUM",
            "is_active": True,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z",
            "metadata": {"test": True},
        }

    @pytest.fixture
    def sample_legal_hold_data(self):
        """Sample legal hold data for testing."""
        return {
            "id": "hold-123",
            "hold_name": "Litigation Hold 2024-001",
            "hold_type": "LITIGATION",
            "reason": "Pending lawsuit",
            "data_types": ["user_profiles", "client_data"],
            "region": "US",
            "status": "active",
            "created_at": "2024-01-15T10:30:00Z",
            "expires_at": None,
            "created_by": "admin-123",
            "metadata": {"case_number": "2024-001"},
        }

    @pytest.fixture
    def sample_cleanup_job_data(self):
        """Sample cleanup job data for testing."""
        return {
            "id": "job-123",
            "data_type": "user_profiles",
            "region": "US",
            "status": "completed",
            "scheduled_at": "2024-01-15T10:30:00Z",
            "started_at": "2024-01-15T11:00:00Z",
            "completed_at": "2024-01-15T11:30:00Z",
            "records_processed": 1000,
            "records_deleted": 150,
            "error_message": None,
            "dry_run": False,
            "batch_size": 100,
        }

    @patch("backend.api.routes.data_retention_dashboard.get_supabase_client")
    def test_get_retention_policies_success(
        self, mock_get_client, mock_supabase_client, sample_retention_policy_data
    ):
        """Test successful retrieval of retention policies."""
        mock_client, mock_result = mock_supabase_client
        mock_get_client.return_value = mock_client
        mock_result.data = [sample_retention_policy_data]

        from backend.api.routes.data_retention_dashboard import get_retention_policies

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Test the function
        result = get_retention_policies(
            region="US",
            data_type="user_profiles",
            active_only=False,
            current_user=mock_user,
        )

        # Verify the result
        assert len(result) == 1
        policy = result[0]
        assert isinstance(policy, RetentionPolicyDashboard)
        assert policy.id == "policy-123"
        assert policy.data_type == "user_profiles"
        assert policy.region == "US"
        assert policy.retention_days == 1095
        assert policy.is_active is True

    @patch("backend.api.routes.data_retention_dashboard.get_supabase_client")
    def test_get_legal_holds_success(
        self, mock_get_client, mock_supabase_client, sample_legal_hold_data
    ):
        """Test successful retrieval of legal holds."""
        mock_client, mock_result = mock_supabase_client
        mock_get_client.return_value = mock_client
        mock_result.data = [sample_legal_hold_data]

        from backend.api.routes.data_retention_dashboard import get_legal_holds

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Test the function
        result = get_legal_holds(region="US", status="active", current_user=mock_user)

        # Verify the result
        assert len(result) == 1
        hold = result[0]
        assert isinstance(hold, LegalHoldDashboard)
        assert hold.id == "hold-123"
        assert hold.hold_name == "Litigation Hold 2024-001"
        assert hold.status == "active"
        assert "user_profiles" in hold.data_types

    @patch("backend.api.routes.data_retention_dashboard.get_supabase_client")
    def test_get_cleanup_jobs_success(
        self, mock_get_client, mock_supabase_client, sample_cleanup_job_data
    ):
        """Test successful retrieval of cleanup jobs."""
        mock_client, mock_result = mock_supabase_client
        mock_get_client.return_value = mock_client
        mock_result.data = [sample_cleanup_job_data]

        from backend.api.routes.data_retention_dashboard import get_cleanup_jobs

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Test the function
        result = get_cleanup_jobs(
            region="US",
            data_type="user_profiles",
            status="completed",
            limit=50,
            current_user=mock_user,
        )

        # Verify the result
        assert len(result) == 1
        job = result[0]
        assert isinstance(job, CleanupJobDashboard)
        assert job.id == "job-123"
        assert job.data_type == "user_profiles"
        assert job.status == "completed"
        assert job.records_processed == 1000
        assert job.records_deleted == 150

    @patch("backend.api.routes.data_retention_dashboard.get_supabase_client")
    def test_get_compliance_metrics_success(
        self, mock_get_client, mock_supabase_client
    ):
        """Test successful retrieval of compliance metrics."""
        mock_client, mock_result = mock_supabase_client
        mock_get_client.return_value = mock_client

        # Mock different results for different queries
        def mock_execute_side_effect():
            if "retention_policies" in str(mock_client.table.call_args):
                mock_result.data = [
                    {"region": "US", "is_active": True},
                    {"region": "US", "is_active": True},
                    {"region": "EU", "is_active": False},
                ]
            elif "legal_holds" in str(mock_client.table.call_args):
                mock_result.data = [
                    {"status": "active"},
                    {"status": "active"},
                    {"status": "released"},
                ]
            elif "cleanup_jobs" in str(mock_client.table.call_args):
                mock_result.data = [
                    {"status": "completed", "completed_at": "2024-01-15T10:30:00Z"},
                    {"status": "pending", "completed_at": None},
                    {"status": "failed", "completed_at": "2024-01-14T10:30:00Z"},
                ]
            return mock_result

        mock_query = Mock()
        mock_query.execute.side_effect = mock_execute_side_effect
        mock_client.table.return_value.select.return_value = mock_query
        mock_query.eq.return_value = mock_query

        from backend.api.routes.data_retention_dashboard import get_compliance_metrics

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Test the function
        result = get_compliance_metrics(region=None, current_user=mock_user)

        # Verify the result
        assert isinstance(result, ComplianceMetricsDashboard)
        assert result.total_policies == 3
        assert result.active_policies == 2
        assert result.active_legal_holds == 2
        assert result.pending_cleanup_jobs == 1
        assert result.compliance_rate == 66.7
        assert "US" in result.policies_by_region
        assert "EU" in result.policies_by_region

    @patch("backend.api.routes.data_retention_dashboard.DataRetentionService")
    def test_create_retention_policy_success(self, mock_service_class):
        """Test successful creation of retention policy."""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_retention_policy = AsyncMock(return_value="policy-123")

        from backend.api.routes.data_retention_dashboard import create_retention_policy

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Create request
        request = CreateRetentionPolicyRequest(
            data_type="user_profiles",
            region="US",
            retention_days=1095,
            legal_basis="LEGITIMATE_INTEREST",
            sensitivity="MEDIUM",
        )

        # Test the function
        result = create_retention_policy(policy_request=request, current_user=mock_user)

        # Verify the result
        assert result["id"] == "policy-123"
        assert result["status"] == "created"
        mock_service.create_retention_policy.assert_called_once()

    @patch("backend.api.routes.data_retention_dashboard.DataRetentionService")
    def test_create_legal_hold_success(self, mock_service_class):
        """Test successful creation of legal hold."""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_legal_hold = AsyncMock(return_value="hold-123")

        from backend.api.routes.data_retention_dashboard import create_legal_hold

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Create request
        request = CreateLegalHoldRequest(
            hold_name="Test Hold",
            hold_type="LITIGATION",
            reason="Test litigation",
            data_types=["user_profiles"],
            region="US",
        )

        # Test the function
        result = create_legal_hold(hold_request=request, current_user=mock_user)

        # Verify the result
        assert result["id"] == "hold-123"
        assert result["status"] == "created"
        mock_service.create_legal_hold.assert_called_once()

    @patch("backend.api.routes.data_retention_dashboard.DataRetentionService")
    def test_schedule_cleanup_success(self, mock_service_class):
        """Test successful scheduling of cleanup job."""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.schedule_cleanup = AsyncMock(return_value="job-123")

        from backend.api.routes.data_retention_dashboard import schedule_cleanup

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Create request
        request = ScheduleCleanupRequest(
            data_type="user_profiles", region="US", dry_run=True, batch_size=100
        )

        # Test the function
        result = schedule_cleanup(cleanup_request=request, current_user=mock_user)

        # Verify the result
        assert result["job_id"] == "job-123"
        assert result["status"] == "scheduled"
        mock_service.schedule_cleanup.assert_called_once()

    def test_get_data_type_registry_success(self):
        """Test successful retrieval of data type registry."""
        from backend.api.routes.data_retention_dashboard import get_data_type_registry

        # Mock current user
        mock_user = Mock()
        mock_user.id = "admin-123"

        # Test the function
        result = get_data_type_registry(current_user=mock_user)

        # Verify the result
        assert isinstance(result, dict)
        # Should contain data type definitions
        assert len(result) > 0

    @patch("backend.api.routes.data_retention_dashboard.get_supabase_client")
    def test_health_check_success(self, mock_get_client):
        """Test successful health check."""
        mock_client = Mock()
        mock_result = Mock()
        mock_result.count = 5

        mock_query = Mock()
        mock_query.execute.return_value = mock_result
        mock_client.table.return_value.select.return_value.limit.return_value = (
            mock_query
        )

        mock_get_client.return_value = mock_client

        from backend.api.routes.data_retention_dashboard import health_check

        # Test the function
        result = health_check()

        # Verify the result
        assert result["status"] == "healthy"
        assert result["database_us"] == "connected"
        assert result["database_eu"] == "connected"
        assert "timestamp" in result


class TestRetentionDashboardModels:
    """Test the dashboard data models."""

    def test_retention_policy_dashboard_model(self):
        """Test RetentionPolicyDashboard model."""
        policy = RetentionPolicyDashboard(
            id="policy-123",
            data_type="user_profiles",
            region="US",
            retention_days=1095,
            legal_basis="LEGITIMATE_INTEREST",
            sensitivity="MEDIUM",
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert policy.id == "policy-123"
        assert policy.data_type == "user_profiles"
        assert policy.retention_days == 1095
        assert policy.is_active is True

    def test_legal_hold_dashboard_model(self):
        """Test LegalHoldDashboard model."""
        hold = LegalHoldDashboard(
            id="hold-123",
            hold_name="Test Hold",
            hold_type="LITIGATION",
            reason="Test reason",
            data_types=["user_profiles"],
            region="US",
            status="active",
            created_at=datetime.now(),
            created_by="admin-123",
        )

        assert hold.id == "hold-123"
        assert hold.hold_name == "Test Hold"
        assert hold.status == "active"
        assert "user_profiles" in hold.data_types

    def test_cleanup_job_dashboard_model(self):
        """Test CleanupJobDashboard model."""
        job = CleanupJobDashboard(
            id="job-123",
            data_type="user_profiles",
            region="US",
            status="completed",
            scheduled_at=datetime.now(),
            records_processed=1000,
            records_deleted=150,
        )

        assert job.id == "job-123"
        assert job.data_type == "user_profiles"
        assert job.status == "completed"
        assert job.records_processed == 1000
        assert job.records_deleted == 150

    def test_compliance_metrics_dashboard_model(self):
        """Test ComplianceMetricsDashboard model."""
        metrics = ComplianceMetricsDashboard(
            total_policies=10,
            active_policies=8,
            active_legal_holds=2,
            pending_cleanup_jobs=1,
            compliance_rate=95.5,
            last_cleanup="2024-01-15T10:30:00Z",
            policies_by_region={"US": 6, "EU": 4},
            cleanup_success_rate=98.2,
        )

        assert metrics.total_policies == 10
        assert metrics.active_policies == 8
        assert metrics.compliance_rate == 95.5
        assert metrics.policies_by_region["US"] == 6


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
