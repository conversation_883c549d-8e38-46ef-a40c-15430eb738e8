"""
Tests for Case API backward compatibility.

This module tests that the deprecated Case API endpoints continue to work
correctly while delegating to the new Matter implementation.
"""

import warnings
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4
from datetime import datetime

import pytest
from fastapi.testclient import TestClient
from fastapi import FastAPI

pytestmark = pytest.mark.integration

# Import the deprecated case API module to test backward compatibility
try:
    from pi_lawyer.api.cases import router as case_router
    from pi_lawyer.models.case import Case, CaseCreate, CaseUpdate, CaseStatus
    from pi_lawyer.models.matter import Matter, MatterStatus, PracticeArea
    from pi_lawyer.data.case_repository import CaseRepository
    from pi_lawyer.data.matter_repository import MatterRepository
except ImportError:
    # Handle case where modules don't exist yet
    case_router = None
    Case = None
    CaseCreate = None
    CaseUpdate = None
    CaseStatus = None
    Matter = None
    MatterStatus = None
    PracticeArea = None
    CaseRepository = None
    MatterRepository = None


class TestCaseAPIBackwardCompatibility:
    """Test backward compatibility of Case API endpoints."""

    @pytest.fixture
    def app(self):
        """Create FastAPI app with case router for testing."""
        if case_router is None:
            pytest.skip("Case API module not available")

        app = FastAPI()
        app.include_router(case_router)
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_case_repository(self):
        """Mock case repository."""
        return AsyncMock(spec=CaseRepository)

    @pytest.fixture
    def mock_matter_repository(self):
        """Mock matter repository."""
        return AsyncMock(spec=MatterRepository)

    def test_case_api_deprecation_warning(self):
        """Test that importing case API module emits deprecation warning."""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            # Import should trigger deprecation warning
            try:
                import pi_lawyer.api.cases

                # Check that a deprecation warning was issued
                assert len(w) > 0
                assert any(
                    issubclass(warning.category, DeprecationWarning) for warning in w
                )
                assert any(
                    "deprecated" in str(warning.message).lower() for warning in w
                )
                assert any("v2.0.0" in str(warning.message) for warning in w)
            except ImportError:
                pytest.skip("Case API module not available")

    def test_case_model_deprecation_warning(self):
        """Test that importing case models emits deprecation warning."""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            try:
                import pi_lawyer.models.case

                # Check that a deprecation warning was issued
                assert len(w) > 0
                assert any(
                    issubclass(warning.category, DeprecationWarning) for warning in w
                )
                assert any(
                    "deprecated" in str(warning.message).lower() for warning in w
                )
            except ImportError:
                pytest.skip("Case model module not available")

    def test_case_repository_deprecation_warning(self):
        """Test that importing case repository emits deprecation warning."""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            try:
                import pi_lawyer.data.case_repository

                # Check that a deprecation warning was issued
                assert len(w) > 0
                assert any(
                    issubclass(warning.category, DeprecationWarning) for warning in w
                )
                assert any(
                    "deprecated" in str(warning.message).lower() for warning in w
                )
            except ImportError:
                pytest.skip("Case repository module not available")

    def test_case_repository_is_matter_repository_alias(self):
        """Test that CaseRepository is an alias to MatterRepository."""
        if CaseRepository is None or MatterRepository is None:
            pytest.skip("Repository classes not available")

        # CaseRepository should be the same class as MatterRepository
        assert CaseRepository is MatterRepository

    def test_case_model_is_matter_alias(self):
        """Test that Case model is an alias to Matter model."""
        if Case is None or Matter is None:
            pytest.skip("Model classes not available")

        # Case should be the same class as Matter
        assert Case is Matter

    @pytest.mark.asyncio
    async def test_case_api_endpoints_exist(self, client):
        """Test that Case API endpoints exist and are accessible."""
        if case_router is None:
            pytest.skip("Case API module not available")

        # Test that case endpoints are registered
        routes = [route.path for route in client.app.routes]

        # Should have case-related endpoints
        case_endpoints = [
            route for route in routes if "/cases" in route or "/case" in route
        ]
        assert len(case_endpoints) > 0

    @pytest.mark.asyncio
    async def test_case_crud_operations_delegate_to_matter(
        self, mock_matter_repository
    ):
        """Test that Case CRUD operations delegate to Matter repository."""
        if CaseRepository is None:
            pytest.skip("CaseRepository not available")

        # Create case repository (which should be MatterRepository)
        case_repo = CaseRepository()

        # Mock the underlying matter repository methods
        mock_matter = Matter(
            id=uuid4(),
            tenant_id=uuid4(),
            title="Test Matter",
            status=MatterStatus.ACTIVE,
            practice_area=PracticeArea.LITIGATION,
            created_by=uuid4(),
            created_at=datetime.now(),
        )

        with patch.object(case_repo, "create", return_value=mock_matter) as mock_create:
            # Create a "case" (which is actually a matter)
            case_data = {
                "title": "Test Case",
                "status": "active",
                "practice_area": "litigation",
                "tenant_id": str(uuid4()),
                "created_by": str(uuid4()),
            }

            result = await case_repo.create(case_data)

            # Should have called the create method
            mock_create.assert_called_once()
            assert result == mock_matter

    def test_case_status_enum_compatibility(self):
        """Test that CaseStatus enum values are compatible with MatterStatus."""
        if CaseStatus is None or MatterStatus is None:
            pytest.skip("Status enums not available")

        # Test that case status values map to matter status values
        status_mappings = {
            "PENDING": "PENDING",
            "ACTIVE": "ACTIVE",
            "CLOSED": "CLOSED",
            "REJECTED": "REJECTED",
        }

        for case_status, matter_status in status_mappings.items():
            if hasattr(CaseStatus, case_status) and hasattr(
                MatterStatus, matter_status
            ):
                assert getattr(CaseStatus, case_status) == getattr(
                    MatterStatus, matter_status
                )

    @pytest.mark.asyncio
    async def test_case_api_response_format_compatibility(self, client):
        """Test that Case API responses maintain expected format."""
        if case_router is None:
            pytest.skip("Case API module not available")

        with patch("pi_lawyer.data.case_repository.CaseRepository") as mock_repo_class:
            mock_repo = AsyncMock()
            mock_repo_class.return_value = mock_repo

            # Mock a matter that will be returned as a case
            mock_matter = {
                "id": str(uuid4()),
                "tenant_id": str(uuid4()),
                "title": "Test Case",
                "description": "Test description",
                "status": "active",
                "practice_area": "litigation",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }

            mock_repo.list.return_value = [mock_matter]

            # Make request to case endpoint
            response = client.get("/api/cases")

            # Should return successful response
            assert response.status_code in [
                200,
                404,
            ]  # 404 if endpoint not implemented yet

    def test_practice_area_enum_compatibility(self):
        """Test that practice area enums are compatible between Case and Matter."""
        if PracticeArea is None:
            pytest.skip("PracticeArea enum not available")

        # Test that all expected practice areas exist
        expected_areas = ["LITIGATION", "TRANSACTION", "ADVISORY", "IP"]

        for area in expected_areas:
            assert hasattr(PracticeArea, area)
            assert getattr(PracticeArea, area) is not None

    @pytest.mark.asyncio
    async def test_case_api_error_handling_compatibility(self, client):
        """Test that Case API error handling is compatible with Matter API."""
        if case_router is None:
            pytest.skip("Case API module not available")

        # Test error responses for non-existent case
        response = client.get("/api/cases/non-existent-id")

        # Should return appropriate error status
        assert response.status_code in [404, 422, 500]  # Various possible error codes

    def test_deprecation_timeline_documentation(self):
        """Test that deprecation timeline is properly documented."""
        if case_router is None:
            pytest.skip("Case API module not available")

        # Check module docstring contains deprecation information
        import pi_lawyer.api.cases as cases_module

        docstring = cases_module.__doc__ or ""
        assert "DEPRECATED" in docstring.upper()
        assert "v2.0.0" in docstring or "2.0.0" in docstring

    @pytest.mark.asyncio
    async def test_case_service_layer_compatibility(self):
        """Test that case service layer maintains compatibility."""
        # This would test the service layer if it exists
        # For now, we test that the repository layer works

        if CaseRepository is None:
            pytest.skip("CaseRepository not available")

        # Should be able to instantiate CaseRepository
        case_repo = CaseRepository()
        assert case_repo is not None

        # Should have the same interface as MatterRepository
        expected_methods = ["create", "get_by_id", "list", "update", "delete"]

        for method in expected_methods:
            assert hasattr(case_repo, method)
            assert callable(getattr(case_repo, method))

    def test_import_compatibility(self):
        """Test that all expected imports work for backward compatibility."""
        try:
            # Should be able to import Case-related items
            from pi_lawyer.models.case import Case, CaseCreate, CaseUpdate, CaseStatus
            from pi_lawyer.data.case_repository import CaseRepository

            # All imports should succeed
            assert Case is not None
            assert CaseCreate is not None
            assert CaseUpdate is not None
            assert CaseStatus is not None
            assert CaseRepository is not None

        except ImportError as e:
            pytest.skip(f"Case compatibility imports not available: {e}")

    def test_backward_compatibility_documentation_exists(self):
        """Test that backward compatibility is documented."""
        # This test would check for documentation files
        # For now, we just verify the deprecation warnings exist

        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            try:
                import pi_lawyer.api.cases
                import pi_lawyer.models.case
                import pi_lawyer.data.case_repository

                # Should have multiple deprecation warnings
                deprecation_warnings = [
                    warning
                    for warning in w
                    if issubclass(warning.category, DeprecationWarning)
                ]
                assert len(deprecation_warnings) >= 3  # One for each module

            except ImportError:
                pytest.skip("Case modules not available for testing")
