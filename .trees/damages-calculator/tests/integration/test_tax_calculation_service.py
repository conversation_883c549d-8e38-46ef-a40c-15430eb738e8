"""
Tests for Tax Calculation Service

Comprehensive tests for tax calculation, VAT validation,
and multi-country tax compliance functionality.
"""

import pytest
from decimal import Decimal
from unittest.mock import Mock, patch, AsyncMock

pytestmark = pytest.mark.integration

from backend.services.tax_calculation_service import (
    TaxCalculationService,
    TaxCalculationResult,
    VATValidationResult,
)


class TestTaxCalculationService:
    """Test suite for TaxCalculationService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = TaxCalculationService()

    @pytest.mark.asyncio
    async def test_determine_tax_behavior_eu_b2c(self):
        """Test tax behavior determination for EU B2C customers."""
        behavior = await self.service.determine_tax_behavior("BE", "B2C")
        assert behavior == "inclusive"

    @pytest.mark.asyncio
    async def test_determine_tax_behavior_eu_b2b_valid_vat(self):
        """Test tax behavior for EU B2B customers with valid VAT."""
        vat_validation = VATValidationResult(
            vat_number="BE0123456789", country_code="BE", is_valid=True
        )
        behavior = await self.service.determine_tax_behavior(
            "BE", "B2B", vat_validation
        )
        assert behavior == "exclusive"

    @pytest.mark.asyncio
    async def test_determine_tax_behavior_us(self):
        """Test tax behavior for US customers."""
        behavior = await self.service.determine_tax_behavior("US", "B2C")
        assert behavior == "exclusive"

    @pytest.mark.asyncio
    @patch("backend.services.tax_calculation_service.get_db_session")
    async def test_get_tax_rates(self, mock_db_session):
        """Test tax rates retrieval."""
        # Mock database response
        mock_db = AsyncMock()
        mock_db_session.return_value.__aenter__.return_value = mock_db

        mock_result = Mock()
        mock_config = Mock()
        mock_config.country_code = "BE"
        mock_config.region_code = None
        mock_config.tax_type = "vat"
        mock_config.tax_rate = Decimal("0.21")
        mock_config.applies_to = "b2c"
        mock_config.requires_vat_validation = True
        mock_config.reverse_charge_eligible = True
        mock_config.description = "Belgium VAT"

        mock_result.fetchall.return_value = [mock_config]
        mock_db.execute.return_value = mock_result

        rates = await self.service.get_tax_rates("BE")

        assert len(rates) == 1
        assert rates[0]["country_code"] == "BE"
        assert rates[0]["tax_type"] == "vat"
        assert rates[0]["tax_rate"] == 0.21

    def test_clean_vat_number(self):
        """Test VAT number cleaning."""
        # Test with spaces and special characters
        clean = self.service._clean_vat_number("BE 0123.456.789", "BE")
        assert clean == "BE0123456789"

        # Test without country prefix
        clean = self.service._clean_vat_number("0123456789", "BE")
        assert clean == "BE0123456789"

        # Test already clean
        clean = self.service._clean_vat_number("BE0123456789", "BE")
        assert clean == "BE0123456789"

    def test_get_tax_id_type(self):
        """Test tax ID type determination."""
        assert self.service._get_tax_id_type("BE") == "eu_vat"
        assert self.service._get_tax_id_type("GB") == "gb_vat"
        assert self.service._get_tax_id_type("US") == "us_ein"
        assert self.service._get_tax_id_type("CA") == "ca_bn"

    def test_determine_tax_type(self):
        """Test tax type determination."""
        assert self.service._determine_tax_type("BE") == "vat"
        assert self.service._determine_tax_type("GB") == "vat"
        assert self.service._determine_tax_type("US") == "sales_tax"
        assert self.service._determine_tax_type("CA") == "gst"

    @pytest.mark.asyncio
    @patch("stripe.tax.calculations.create")
    async def test_calculate_tax_success(self, mock_stripe_create):
        """Test successful tax calculation with Stripe."""
        # Mock Stripe response
        mock_calculation = Mock()
        mock_calculation.id = "taxcalc_123"
        mock_calculation.amount_total = 12100  # $121.00
        mock_calculation.tax_amount = 2100  # $21.00 tax

        mock_line_item = Mock()
        mock_line_item.tax_breakdown = [Mock()]
        mock_line_item.tax_breakdown[0].tax_rate_details.percentage_decimal = 21.0
        mock_line_item.tax_breakdown[0].jurisdiction.display_name = "Belgium"

        mock_calculation.line_items.data = [mock_line_item]
        mock_stripe_create.return_value = mock_calculation

        # Test calculation
        result = await self.service.calculate_tax(
            amount=Decimal("100.00"),
            currency="EUR",
            customer_country="BE",
            customer_address={
                "line1": "123 Main St",
                "city": "Brussels",
                "country": "BE",
            },
            customer_type="B2C",
        )

        assert isinstance(result, TaxCalculationResult)
        assert result.subtotal_amount == Decimal("100.00")
        assert result.tax_amount == Decimal("21.00")
        assert result.total_amount == Decimal("121.00")
        assert result.tax_rate == Decimal("21.0")
        assert result.currency == "EUR"
        assert result.customer_country == "BE"
        assert result.stripe_calculation_id == "taxcalc_123"

    @pytest.mark.asyncio
    @patch("stripe.tax.calculations.create")
    async def test_calculate_tax_stripe_error_fallback(self, mock_stripe_create):
        """Test fallback calculation when Stripe fails."""
        # Mock Stripe error
        import stripe

        mock_stripe_create.side_effect = stripe.error.StripeError("API Error")

        # Mock fallback tax rates
        with patch.object(self.service, "get_tax_rates") as mock_get_rates:
            mock_get_rates.return_value = [
                {
                    "country_code": "BE",
                    "tax_type": "vat",
                    "tax_rate": 0.21,
                    "applies_to": "b2c",
                    "reverse_charge_eligible": False,
                }
            ]

            result = await self.service.calculate_tax(
                amount=Decimal("100.00"),
                currency="EUR",
                customer_country="BE",
                customer_address={
                    "line1": "123 Main St",
                    "city": "Brussels",
                    "country": "BE",
                },
                customer_type="B2C",
            )

            assert isinstance(result, TaxCalculationResult)
            assert result.subtotal_amount == Decimal("100.00")
            assert result.tax_amount == Decimal("21.00")
            assert result.total_amount == Decimal("121.00")
            assert result.stripe_calculation_id is None  # Fallback calculation

    @pytest.mark.asyncio
    @patch("stripe.tax.calculations.create")
    async def test_validate_vat_number_success(self, mock_stripe_create):
        """Test successful VAT validation."""
        # Mock Stripe response
        mock_calculation = Mock()
        mock_calculation.customer_details.tax_ids = [Mock()]
        mock_calculation.customer_details.tax_ids[0].verification.status = "verified"
        mock_calculation.customer_details.tax_ids[0].verification.verified_name = (
            "Test Company"
        )
        mock_calculation.customer_details.tax_ids[0].verification.verified_address = (
            "123 Test St"
        )

        mock_stripe_create.return_value = mock_calculation

        # Mock cache miss
        with patch.object(self.service, "_cache_vat_validation") as mock_cache:
            with patch(
                "backend.services.tax_calculation_service.get_db_session"
            ) as mock_db_session:
                mock_db = AsyncMock()
                mock_db_session.return_value.__aenter__.return_value = mock_db
                mock_result = Mock()
                mock_result.fetchone.return_value = None  # Cache miss
                mock_db.execute.return_value = mock_result

                result = await self.service.validate_vat_number("BE0123456789", "BE")

                assert isinstance(result, VATValidationResult)
                assert result.vat_number == "BE0123456789"
                assert result.country_code == "BE"
                assert result.is_valid is True
                assert result.company_name == "Test Company"
                assert mock_cache.called

    @pytest.mark.asyncio
    async def test_fallback_tax_calculation_no_rates(self):
        """Test fallback calculation with no tax rates configured."""
        with patch.object(self.service, "get_tax_rates") as mock_get_rates:
            mock_get_rates.return_value = []

            result = await self.service._fallback_tax_calculation(
                amount=Decimal("100.00"),
                currency="EUR",
                customer_country="XX",  # Unknown country
                customer_type="B2C",
                vat_validation=None,
            )

            assert result.subtotal_amount == Decimal("100.00")
            assert result.tax_amount == Decimal("0.00")
            assert result.total_amount == Decimal("100.00")
            assert result.tax_type == "none"

    @pytest.mark.asyncio
    async def test_fallback_tax_calculation_reverse_charge(self):
        """Test fallback calculation with reverse charge for B2B."""
        vat_validation = VATValidationResult(
            vat_number="BE0123456789", country_code="BE", is_valid=True
        )

        with patch.object(self.service, "get_tax_rates") as mock_get_rates:
            mock_get_rates.return_value = [
                {
                    "country_code": "BE",
                    "tax_type": "vat",
                    "tax_rate": 0.21,
                    "applies_to": "b2b",
                    "reverse_charge_eligible": True,
                }
            ]

            result = await self.service._fallback_tax_calculation(
                amount=Decimal("100.00"),
                currency="EUR",
                customer_country="BE",
                customer_type="B2B",
                vat_validation=vat_validation,
            )

            assert result.subtotal_amount == Decimal("100.00")
            assert result.tax_amount == Decimal("0.00")  # Reverse charge
            assert result.total_amount == Decimal("100.00")
            assert result.reverse_charge_applied is True


class TestTaxCalculationResult:
    """Test suite for TaxCalculationResult."""

    def test_to_dict(self):
        """Test conversion to dictionary."""
        result = TaxCalculationResult(
            subtotal_amount=Decimal("100.00"),
            tax_amount=Decimal("21.00"),
            total_amount=Decimal("121.00"),
            tax_rate=Decimal("0.21"),
            tax_type="vat",
            currency="EUR",
            customer_country="BE",
            customer_type="B2C",
        )

        data = result.to_dict()

        assert data["subtotal_amount"] == 100.0
        assert data["tax_amount"] == 21.0
        assert data["total_amount"] == 121.0
        assert data["tax_rate"] == 0.21
        assert data["tax_type"] == "vat"
        assert data["currency"] == "EUR"
        assert data["customer_country"] == "BE"
        assert data["customer_type"] == "B2C"


class TestVATValidationResult:
    """Test suite for VATValidationResult."""

    def test_to_dict(self):
        """Test conversion to dictionary."""
        result = VATValidationResult(
            vat_number="BE0123456789",
            country_code="BE",
            is_valid=True,
            company_name="Test Company",
            validation_source="stripe",
        )

        data = result.to_dict()

        assert data["vat_number"] == "BE0123456789"
        assert data["country_code"] == "BE"
        assert data["is_valid"] is True
        assert data["company_name"] == "Test Company"
        assert data["validation_source"] == "stripe"
