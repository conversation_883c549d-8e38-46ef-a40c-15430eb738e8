"""
Tests for the avricons intake webhook endpoint.

This module contains comprehensive tests for the intake webhook functionality,
including happy path, error cases, and security verification.
"""

import json
import os
from unittest.mock import patch
from uuid import uuid4

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from backend.api.main import create_app
from backend.models.intake_event import IntakeEvent
from backend.utils.hmac_utils import generate_hmac_signature

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def sample_intake_payload():
    """Sample intake webhook payload."""
    return {
        "tenant_id": str(uuid4()),
        "call_id": "call_123456",
        "call_status": "completed",
        "call_duration": 300,
        "transcript": "Hello, I was in a car accident last week...",
        "summary": "Client reports car accident, seeking legal representation",
        "client_info": {
            "name": "<PERSON>",
            "phone": "+1234567890",
            "email": "<EMAIL>",
        },
        "case_info": {
            "type": "personal_injury",
            "incident_date": "2025-01-01",
            "description": "Rear-end collision at intersection",
        },
        "metadata": {
            "call_timestamp": "2025-01-03T10:00:00Z",
            "agent_version": "v1.2.3",
        },
    }


@pytest.fixture
def mock_secret():
    """Mock secret for HMAC verification."""
    return "test_secret_key_123"


class TestIntakeWebhook:
    """Test cases for the intake webhook endpoint."""

    @patch.dict(os.environ, {"CORE_INTAKE_SECRET": "test_secret_key_123"})
    async def test_intake_webhook_success(
        self, client, sample_intake_payload, mock_secret
    ):
        """Test successful intake webhook processing."""
        # Generate valid HMAC signature
        payload_json = json.dumps(sample_intake_payload)
        signature = generate_hmac_signature(payload_json.encode(), mock_secret)

        # Make request
        response = client.post(
            "/api/v1/avricons/intake",
            json=sample_intake_payload,
            headers={"X-Avr-Signature": signature},
        )

        # Verify response
        assert response.status_code == 202
        data = response.json()
        assert data["status"] == "queued"
        assert "event_id" in data
        assert data["message"] == "Event queued for processing"

    @patch.dict(os.environ, {"CORE_INTAKE_SECRET": "test_secret_key_123"})
    async def test_intake_webhook_invalid_signature(
        self, client, sample_intake_payload
    ):
        """Test webhook with invalid HMAC signature."""
        response = client.post(
            "/api/v1/avricons/intake",
            json=sample_intake_payload,
            headers={"X-Avr-Signature": "sha256=invalid_signature"},
        )

        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid signature"

    @patch.dict(os.environ, {"CORE_INTAKE_SECRET": "test_secret_key_123"})
    async def test_intake_webhook_missing_signature(
        self, client, sample_intake_payload
    ):
        """Test webhook with missing HMAC signature."""
        response = client.post("/api/v1/avricons/intake", json=sample_intake_payload)

        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid signature"

    @patch.dict(os.environ, {})  # Remove CORE_INTAKE_SECRET
    async def test_intake_webhook_no_secret_configured(
        self, client, sample_intake_payload
    ):
        """Test webhook when no secret is configured."""
        response = client.post(
            "/api/v1/avricons/intake",
            json=sample_intake_payload,
            headers={"X-Avr-Signature": "sha256=any_signature"},
        )

        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid signature"

    async def test_intake_webhook_invalid_payload(self, client, mock_secret):
        """Test webhook with invalid payload structure."""
        invalid_payload = {"invalid_field": "value"}

        with patch.dict(os.environ, {"CORE_INTAKE_SECRET": mock_secret}):
            payload_json = json.dumps(invalid_payload)
            signature = generate_hmac_signature(payload_json.encode(), mock_secret)

            response = client.post(
                "/api/v1/avricons/intake",
                json=invalid_payload,
                headers={"X-Avr-Signature": signature},
            )

        assert response.status_code == 422  # Validation error

    @patch.dict(os.environ, {"CORE_INTAKE_SECRET": "test_secret_key_123"})
    async def test_intake_webhook_database_storage(
        self, client, sample_intake_payload, mock_secret
    ):
        """Test that webhook events are stored in the database."""
        # Generate valid HMAC signature
        payload_json = json.dumps(sample_intake_payload)
        signature = generate_hmac_signature(payload_json.encode(), mock_secret)

        # Make request
        response = client.post(
            "/api/v1/avricons/intake",
            json=sample_intake_payload,
            headers={"X-Avr-Signature": signature},
        )

        assert response.status_code == 202

        # TODO: Add database verification once we have proper test database setup
        # This would involve:
        # 1. Setting up test database session
        # 2. Querying for the created IntakeEvent record
        # 3. Verifying the stored data matches the payload

    @patch.dict(
        os.environ,
        {"CORE_INTAKE_SECRET": "test_secret_key_123", "USE_REDIS_QUEUE": "true"},
    )
    async def test_intake_webhook_redis_queue_fallback(
        self, client, sample_intake_payload, mock_secret
    ):
        """Test Redis queue integration (currently falls back to database)."""
        # Generate valid HMAC signature
        payload_json = json.dumps(sample_intake_payload)
        signature = generate_hmac_signature(payload_json.encode(), mock_secret)

        # Make request
        response = client.post(
            "/api/v1/avricons/intake",
            json=sample_intake_payload,
            headers={"X-Avr-Signature": signature},
        )

        # Should still succeed even though Redis is not implemented
        assert response.status_code == 202
        data = response.json()
        assert data["status"] == "queued"


class TestHMACUtils:
    """Test cases for HMAC utility functions."""

    def test_generate_and_verify_signature(self):
        """Test HMAC signature generation and verification."""
        from backend.utils.hmac_utils import (
            generate_hmac_signature,
            verify_hmac_signature,
        )

        payload = b"test payload"
        secret = "test_secret"

        # Generate signature
        signature = generate_hmac_signature(payload, secret)
        assert signature.startswith("sha256=")

        # Verify signature
        assert verify_hmac_signature(payload, signature, secret)

        # Verify with wrong secret
        assert not verify_hmac_signature(payload, signature, "wrong_secret")

        # Verify with wrong payload
        assert not verify_hmac_signature(b"wrong payload", signature, secret)

    def test_verify_signature_with_prefix(self):
        """Test signature verification with sha256= prefix."""
        from backend.utils.hmac_utils import verify_hmac_signature

        payload = b"test payload"
        secret = "test_secret"

        # Generate signature manually
        import hashlib
        import hmac

        expected_signature = hmac.new(
            key=secret.encode("utf-8"), msg=payload, digestmod=hashlib.sha256
        ).hexdigest()

        # Test with prefix
        assert verify_hmac_signature(payload, f"sha256={expected_signature}", secret)

        # Test without prefix
        assert verify_hmac_signature(payload, expected_signature, secret)

    def test_verify_signature_edge_cases(self):
        """Test HMAC verification edge cases."""
        from backend.utils.hmac_utils import verify_hmac_signature

        payload = b"test payload"
        secret = "test_secret"

        # Empty signature
        assert not verify_hmac_signature(payload, "", secret)

        # None signature
        assert not verify_hmac_signature(payload, None, secret)

        # Empty secret
        assert not verify_hmac_signature(payload, "signature", "")

        # None secret
        assert not verify_hmac_signature(payload, "signature", None)
