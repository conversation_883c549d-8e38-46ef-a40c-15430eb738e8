"""
Test Suite for Data Residency Controls

This module tests the data residency functionality including regional routing,
location detection, compliance audit logging, and API endpoints.
"""

import pytest
import os
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from uuid import uuid4

pytestmark = pytest.mark.integration

# Set testing environment variables before importing modules
os.environ.update(
    {
        "TESTING": "true",
        "SUPABASE_EU_URL": "https://eu-test.supabase.co",
        "SUPABASE_EU_SERVICE_KEY": "eu-test-key",
        "SUPABASE_EU_ANON_KEY": "eu-anon-key",
        "SUPABASE_US_URL": "https://us-test.supabase.co",
        "SUPABASE_US_SERVICE_KEY": "us-test-key",
        "SUPABASE_US_ANON_KEY": "us-anon-key",
        "ENABLE_GEOLOCATION": "false",
        "ENABLE_STRICT_DATA_RESIDENCY": "false",
    }
)

from backend.services.data_residency import (
    DataRegion,
    RegionalSupabaseClientFactory,
    get_regional_supabase_client,
    validate_regional_configuration,
)
from backend.services.location_detection import (
    LocationDetectionService,
    DataResidencyPreferenceManager,
    detect_user_region,
)
from backend.services.compliance_audit import (
    ComplianceAuditLogger,
    DataResidencyComplianceTracker,
    AuditEventType,
)
from backend.api.main import create_app


@pytest.mark.unit
class TestDataRegion:
    """Test DataRegion enum functionality."""

    def test_data_region_values(self):
        """Test DataRegion enum values."""
        assert DataRegion.US == "US"
        assert DataRegion.EU == "EU"
        assert len(DataRegion) == 2

    def test_data_region_validation(self):
        """Test DataRegion validation."""
        assert DataRegion("US") == DataRegion.US
        assert DataRegion("EU") == DataRegion.EU

        with pytest.raises(ValueError):
            DataRegion("INVALID")


@pytest.mark.integration
class TestRegionalSupabaseClientFactory:
    """Test regional Supabase client factory."""

    @pytest.fixture
    def factory(self):
        """Create a factory instance for testing."""
        return RegionalSupabaseClientFactory()

    def test_regional_configuration(self, factory):
        """Test regional configuration loading."""
        eu_config = factory.config.get_config(DataRegion.EU)
        us_config = factory.config.get_config(DataRegion.US)

        # Test that configuration is loaded (values set in setUp)
        assert eu_config["url"] == "https://eu-test.supabase.co"
        assert eu_config["key"] == "eu-test-key"
        assert us_config["url"] == "https://us-test.supabase.co"
        assert us_config["key"] == "us-test-key"

    @patch("backend.services.data_residency.create_client")
    def test_client_creation(self, mock_create_client, factory):
        """Test Supabase client creation."""
        mock_client = Mock()
        mock_create_client.return_value = mock_client

        # Clear the cache to ensure fresh creation
        factory._clients.clear()

        client = factory.get_client(DataRegion.US)

        assert client == mock_client
        mock_create_client.assert_called_once_with(
            "https://us-test.supabase.co", "us-test-key"
        )

    def test_client_caching(self, factory):
        """Test that clients are cached properly."""
        with patch("backend.services.data_residency.create_client") as mock_create:
            mock_create.return_value = Mock()

            # Clear cache first
            factory._clients.clear()

            client1 = factory.get_client(DataRegion.US)
            client2 = factory.get_client(DataRegion.US)

            assert client1 == client2
            assert mock_create.call_count == 1


class TestLocationDetectionService:
    """Test location detection and region determination."""

    @pytest.fixture
    def service(self):
        """Create a location detection service for testing."""
        return LocationDetectionService()

    def test_private_ip_detection(self, service):
        """Test private IP address detection."""
        assert service._is_private_ip("127.0.0.1") is True
        assert service._is_private_ip("***********") is True
        assert service._is_private_ip("********") is True
        assert service._is_private_ip("**********") is True
        assert service._is_private_ip("*******") is False
        assert service._is_private_ip("*******") is False

    @pytest.mark.asyncio
    async def test_region_detection_private_ip(self, service):
        """Test region detection for private IPs."""
        # Enable geolocation for this test
        service.enable_geolocation = True

        region, metadata = await service.detect_user_region_from_ip("127.0.0.1")

        assert region == DataRegion.US
        assert metadata["method"] == "private_ip"

    @pytest.mark.asyncio
    @patch("httpx.AsyncClient.get")
    async def test_region_detection_eu_country(self, mock_get, service):
        """Test region detection for EU country."""
        # Enable geolocation for this test
        service.enable_geolocation = True

        mock_response = Mock()
        mock_response.json.return_value = {"country": "DE", "city": "Berlin"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        region, metadata = await service.detect_user_region_from_ip("*******")

        assert region == DataRegion.EU
        assert metadata["country"] == "DE"
        assert metadata["detected_region"] == "EU"

    @pytest.mark.asyncio
    @patch("httpx.AsyncClient.get")
    async def test_region_detection_us_country(self, mock_get, service):
        """Test region detection for US country."""
        # Enable geolocation for this test
        service.enable_geolocation = True

        mock_response = Mock()
        mock_response.json.return_value = {"country": "US", "city": "New York"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        region, metadata = await service.detect_user_region_from_ip("*******")

        assert region == DataRegion.US
        assert metadata["country"] == "US"
        assert metadata["detected_region"] == "US"


class TestDataResidencyPreferenceManager:
    """Test data residency preference management."""

    def test_validate_region_preference(self):
        """Test region preference validation."""
        manager = DataResidencyPreferenceManager()

        assert manager.validate_region_preference("US") == DataRegion.US
        assert manager.validate_region_preference("EU") == DataRegion.EU
        assert manager.validate_region_preference("us") == DataRegion.US
        assert manager.validate_region_preference("eu") == DataRegion.EU
        assert manager.validate_region_preference("INVALID") is None
        assert manager.validate_region_preference(None) is None

    def test_get_region_display_name(self):
        """Test region display name retrieval."""
        manager = DataResidencyPreferenceManager()

        assert manager.get_region_display_name(DataRegion.US) == "United States"
        assert manager.get_region_display_name(DataRegion.EU) == "European Union"

    def test_get_available_regions(self):
        """Test available regions retrieval."""
        manager = DataResidencyPreferenceManager()
        regions = manager.get_available_regions()

        assert "US" in regions
        assert "EU" in regions
        assert regions["US"] == "United States"
        assert regions["EU"] == "European Union"


class TestComplianceAuditLogger:
    """Test compliance audit logging functionality."""

    @pytest.fixture
    def audit_logger(self):
        """Create an audit logger for testing."""
        return ComplianceAuditLogger()

    @pytest.mark.asyncio
    async def test_log_event(self, audit_logger):
        """Test audit event logging."""
        with patch.object(
            audit_logger, "_store_audit_record", new_callable=AsyncMock
        ) as mock_store:
            event_id = await audit_logger.log_event(
                event_type=AuditEventType.REGION_ROUTING,
                user_id="test-user-123",
                region="US",
                metadata={"test": "data"},
            )

            assert event_id is not None
            mock_store.assert_called_once()

            # Check the audit record structure
            call_args = mock_store.call_args[0][0]
            assert call_args["event_type"] == "region_routing"
            assert call_args["user_id"] == "test-user-123"
            assert call_args["region"] == "US"
            assert call_args["metadata"]["test"] == "data"


@pytest.mark.integration
class TestDataResidencyAPI:
    """Test data residency API endpoints."""

    @pytest.fixture
    def client(self):
        """Create a test client."""
        app = create_app()
        return TestClient(app)

    @pytest.fixture
    def mock_user(self):
        """Create a mock user for testing."""
        return Mock(id=uuid4(), tenant_id=uuid4(), data_residency_preference="US")

    def test_detect_region_endpoint(self, client, mock_user):
        """Test the region detection endpoint."""
        with patch(
            "backend.api.routes.data_residency.get_current_user", return_value=mock_user
        ):
            with patch(
                "backend.api.routes.data_residency.detect_user_region"
            ) as mock_detect:
                with patch(
                    "backend.api.routes.data_residency.log_data_residency_event"
                ) as mock_log:
                    mock_detect.return_value = (DataRegion.US, {"method": "test"})
                    mock_log.return_value = "test-event-id"

                    response = client.get("/data-residency/detect")

                    assert response.status_code == 200
                    data = response.json()
                    assert data["detected_region"] == "US"
                    assert "available_regions" in data

    def test_get_regions_endpoint(self, client):
        """Test the get regions endpoint."""
        response = client.get("/data-residency/regions")

        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2

        regions = {region["region"]: region for region in data}
        assert "US" in regions
        assert "EU" in regions
        assert regions["US"]["display_name"] == "United States"
        assert regions["EU"]["display_name"] == "European Union"

    def test_country_region_endpoint(self, client):
        """Test the country to region mapping endpoint."""
        # Test EU country
        response = client.get("/data-residency/country/DE")
        assert response.status_code == 200
        data = response.json()
        assert data["country_code"] == "DE"
        assert data["region"] == "EU"

        # Test US country
        response = client.get("/data-residency/country/US")
        assert response.status_code == 200
        data = response.json()
        assert data["country_code"] == "US"
        assert data["region"] == "US"

        # Test invalid country code
        response = client.get("/data-residency/country/INVALID")
        assert response.status_code == 400


class TestDataResidencyMiddleware:
    """Test data residency middleware functionality."""

    @pytest.fixture
    def app_with_middleware(self):
        """Create an app with data residency middleware."""
        app = create_app()
        return TestClient(app)

    def test_middleware_skips_health_checks(self, app_with_middleware):
        """Test that middleware skips health check endpoints."""
        response = app_with_middleware.get("/health")
        # Should not fail due to missing regional configuration
        assert response.status_code in [200, 404]  # 404 if endpoint doesn't exist

    @patch.dict(os.environ, {"TESTING": "true"})
    def test_middleware_in_testing_mode(self, app_with_middleware):
        """Test middleware behavior in testing mode."""
        # In testing mode, should use mock clients
        response = app_with_middleware.get("/data-residency/regions")
        assert response.status_code == 200


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
