# Integration Tests

This directory contains integration tests for the PI Lawyer AI application.

## Auth-Service Flow Tests

The `test_auth_service_flow.py` file contains comprehensive integration tests for the auth-service token → Google Calendar → booking flow.

### Test Coverage

The tests verify the following scenarios:

1. **Happy Path Flow** (`test_happy_path_flow`)
   - Auth-service token retrieval
   - Google Calendar freeBusy API call
   - Proper authorization headers
   - Response parsing
   - TTL token caching

2. **Booking Flow** (`test_booking_flow`)
   - Auth-service token retrieval
   - Google Calendar events.insert API call
   - Event creation response handling
   - Request payload validation

3. **Database Booking Simulation** (`test_database_booking_simulation`)
   - Booking data structure validation
   - Database insertion simulation
   - Field type validation

4. **Error Handling** (`test_auth_service_error_handling`)
   - Auth-service failure scenarios
   - Proper error propagation
   - No downstream calls on auth failure

5. **Network Isolation** (`test_no_network_access_outside_mocks`)
   - Ensures no real network calls are made
   - Verifies test isolation

6. **Performance Benchmark** (`test_performance_benchmark`)
   - Latency requirements (< 2 seconds)
   - Performance monitoring
   - Multiple iteration testing

### Running the Tests

#### Option 1: Using the Test Runner (Recommended)

```bash
python tests/integration/run_auth_service_tests.py
```

This is the recommended approach as it:
- Doesn't require the full test infrastructure
- Works in CI/CD environments
- Provides detailed output and timing
- Handles missing dependencies gracefully

#### Option 2: Using pytest (if dependencies are available)

```bash
pytest tests/integration/test_auth_service_flow.py -v
```

Note: This requires all application dependencies to be installed and may fail if some modules are not available.

### Test Architecture

These are **contract tests** that verify the HTTP interactions and data flow without requiring the full application stack. They use:

- **respx** for HTTP mocking
- **httpx** for async HTTP client simulation
- **pytest** for test framework
- **AsyncMock** for database simulation

### Key Features

- **No Real Network Calls**: All HTTP requests are mocked using respx
- **Latency Guards**: Tests fail if they take longer than 2 seconds
- **Authorization Verification**: Ensures proper Bearer token usage
- **Data Structure Validation**: Verifies request/response formats
- **Error Scenario Testing**: Tests failure modes and error handling

### Requirements

The tests require the following packages:
- `pytest>=7.4.3`
- `pytest-asyncio>=0.21.1`
- `pytest-timeout>=2.1.0`
- `httpx>=0.25.2`
- `respx>=0.20.2`

### CI/CD Integration

These tests are designed to run in CI/CD environments where:
- The `AUTH_SERVICE_BASE` environment variable may not be set
- Some application dependencies may not be available
- Network access should be restricted

The tests use mocked endpoints and don't require real services to be running.

### Expected Output

When all tests pass, you should see:

```
🧪 Running Auth-Service Integration Tests
==================================================

🔍 Running: test_happy_path_flow
   Auth-service → Google freeBusy happy path
   ✅ PASSED (0.054s)

🔍 Running: test_booking_flow
   Auth-service → Google events.insert flow
   ✅ PASSED (0.047s)

🔍 Running: test_database_booking_simulation
   Database booking insertion simulation
   ✅ PASSED (0.000s)

🔍 Running: test_auth_service_error_handling
   Auth-service error handling
   ✅ PASSED (0.047s)

🔍 Running: test_no_network_access_outside_mocks
   Network isolation verification
   ✅ PASSED (0.047s)

🔍 Running: test_performance_benchmark
   Performance benchmark (< 2s latency)
Performance metrics: avg=0.046s, total=0.231s, iterations=5
   ✅ PASSED (0.232s)

==================================================
📊 Test Results Summary
   Total tests: 6
   Passed: 6
   Failed: 0
   Success rate: 100.0%
   Total time: 0.428s

🎉 All tests passed! The auth-service integration is working correctly.
```

### Troubleshooting

If tests fail:

1. **Import Errors**: Use the test runner instead of pytest
2. **Network Errors**: Ensure respx is properly mocking all endpoints
3. **Timeout Errors**: Check if the 2-second latency guard is too strict
4. **Mock Errors**: Verify that all fixtures are properly configured

### Future Enhancements

Potential improvements for these tests:

1. **Real Integration Tests**: Tests against actual staging environments
2. **Load Testing**: Higher volume testing for performance validation
3. **Chaos Testing**: Network failure and retry scenarios
4. **Security Testing**: Token validation and authorization edge cases
