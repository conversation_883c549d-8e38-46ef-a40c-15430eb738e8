from pi_lawyer.utils import test_gcs_structure


def test_main_runs(monkeypatch):
    monkeypatch.setenv("GCS_SERVICE_ACCOUNT_FILE", "/tmp/fake.json")
    monkeypatch.setenv("GCS_BUCKET_NAME", "fake-bucket")
    # Only check that main runs without error; output is not asserted
    try:
        test_gcs_structure.main()
    except Exception as e:
        assert False, f"main() raised: {e}"


# TODO: Add integration tests if needed.
