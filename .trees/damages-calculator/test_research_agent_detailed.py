#!/usr/bin/env python3
"""
Detailed test of the Research Agent to identify specific issues.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set required environment variables
os.environ.setdefault('MCP_RULES_BASE', 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

def test_research_agent_imports():
    """Test Research Agent import chain to identify issues."""
    print("🔍 Testing Research Agent Import Chain...")
    print("=" * 50)
    
    # Test 1: Direct module import
    print("\n1. Testing direct module import:")
    try:
        import backend.agents.interactive.research.agent as research_module
        print("✅ Research module imported successfully")
        print(f"   Module path: {research_module.__file__}")
    except Exception as e:
        print(f"❌ Research module import failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 2: Class import
    print("\n2. Testing ResearchAgent class import:")
    try:
        from backend.agents.interactive.research.agent import ResearchAgent
        print("✅ ResearchAgent class imported successfully")
    except Exception as e:
        print(f"❌ ResearchAgent class import failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 3: Agent instantiation
    print("\n3. Testing ResearchAgent instantiation:")
    try:
        agent = ResearchAgent()
        print("✅ ResearchAgent instantiated successfully")
        print(f"   Agent type: {type(agent)}")
    except Exception as e:
        print(f"❌ ResearchAgent instantiation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 4: Graph creation
    print("\n4. Testing ResearchAgent graph creation:")
    try:
        graph = agent.create_graph()
        print("✅ ResearchAgent graph created successfully")
        print(f"   Graph type: {type(graph)}")
    except Exception as e:
        print(f"❌ ResearchAgent graph creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def analyze_research_agent_dependencies():
    """Analyze what the Research Agent is trying to import."""
    print("\n" + "=" * 50)
    print("🔍 Analyzing Research Agent Dependencies...")
    print("=" * 50)
    
    # Check the research agent file
    research_agent_path = Path("backend/agents/interactive/research/agent.py")
    if research_agent_path.exists():
        print(f"\n📄 Research Agent File: {research_agent_path}")
        
        # Read the file and look for imports
        with open(research_agent_path, 'r') as f:
            content = f.read()
        
        # Find import lines
        import_lines = [line.strip() for line in content.split('\n') if 'import' in line and not line.strip().startswith('#')]
        
        print("\n📦 Import statements found:")
        for i, line in enumerate(import_lines[:10], 1):  # Show first 10 imports
            print(f"   {i:2d}. {line}")
        
        # Look for problematic imports
        problematic_imports = [line for line in import_lines if 'pi_lawyer.agents' in line]
        if problematic_imports:
            print(f"\n⚠️ Problematic imports found:")
            for line in problematic_imports:
                print(f"   - {line}")
        
    else:
        print(f"❌ Research agent file not found: {research_agent_path}")

def check_other_agents():
    """Check status of other agents we haven't tested yet."""
    print("\n" + "=" * 50)
    print("🔍 Checking Other Agents...")
    print("=" * 50)
    
    other_agents = [
        ("CalendarCrudAgent", "backend.agents.interactive.calendar_crud.agent", "CalendarCrudAgent"),
        ("TaskCrudAgent", "backend.agents.interactive.task_crud.agent", "TaskCrudAgent"),
        ("MatterClientAgent", "backend.agents.matter_client.agent", "MatterClientAgent"),
        ("SupervisorAgent", "backend.agents.insights.supervisor.agent", "SupervisorAgent")
    ]
    
    for agent_name, module_path, class_name in other_agents:
        print(f"\n🧪 Testing {agent_name}:")
        try:
            # Try to import the module
            module = __import__(module_path, fromlist=[class_name])
            agent_class = getattr(module, class_name)
            
            # Try to instantiate
            agent = agent_class()
            print(f"   ✅ {agent_name}: Import and instantiation successful")
            
            # Try to create graph
            try:
                graph = agent.create_graph()
                print(f"   ✅ {agent_name}: Graph creation successful")
            except Exception as e:
                print(f"   ⚠️ {agent_name}: Graph creation failed - {e}")
                
        except Exception as e:
            print(f"   ❌ {agent_name}: Failed - {e}")

def main():
    """Run comprehensive agent analysis."""
    print("🚀 Comprehensive Agent Analysis")
    print("=" * 60)
    
    # Test Research Agent specifically
    research_success = test_research_agent_imports()
    
    # Analyze dependencies
    analyze_research_agent_dependencies()
    
    # Check other agents
    check_other_agents()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Analysis Summary:")
    
    if research_success:
        print("✅ ResearchAgent: Working")
    else:
        print("❌ ResearchAgent: Needs fixes")
    
    print("\n💡 Next Steps:")
    print("   1. Fix import paths in problematic agents")
    print("   2. Update agents to use backend.agents.shared.core.base_agent")
    print("   3. Test all agents for AG-UI compliance")
    print("   4. Register working agents with CopilotKit")

if __name__ == "__main__":
    main()
