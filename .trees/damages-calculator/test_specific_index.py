"""
Direct test of an existing Pinecone index.
"""
import os
import sys
from dotenv import load_dotenv
import pinecone
from pinecone import Pinecone # Now using modern client >= v3.0.0

# Load environment variables
load_dotenv()

# Get credentials from environment
api_key = os.getenv("PINECONE_API_KEY")
environment = os.getenv("PINECONE_ENVIRONMENT")  # Should be us-east-1-aws now
index_name = os.getenv("PINECONE_INDEX_NAME")    # Should be texas-laws-voyage3large

print(f"🔄 Testing direct connection to index: {index_name}")
print(f"🔑 Using environment: {environment}")

# --- Modern Pinecone Initialization (pinecone-client >= v3.0.0) ---
print("\n[INFO] Using modern Pinecone Client Initialization.")
try:
    # 1. Initialize the Pinecone client
    # For pod-based indexes (which often use an 'environment' like 'us-east-1-aws'):
    pc = Pinecone(api_key=api_key, environment=environment)

    # For serverless indexes, you usually don't pass 'environment' during client init:
    # pc = Pinecone(api_key=api_key)

    # 2. Get a handle to your specific index
    # This is the equivalent of the old pinecone.Index(index_name)
    index = pc.Index(index_name)
    print(f"  Successfully got handle for index '{index_name}'.")

    # Optional: List indexes to confirm visibility (can be uncommented if needed)
    # print(f"  Available indexes: {pc.list_indexes().names}")

except Exception as e:
    print(f"\n❌ Failed to initialize modern Pinecone client or get index handle: {e}")
    sys.exit(1)
# --- End of Modern Pinecone Initialization ---

try:
    # Get index stats using the handle obtained from the modern client
    stats = index.describe_index_stats()
    print(f"\n✅ Successfully connected to index: {index_name}")
    print(f"📊 Total vectors: {stats.get('total_vector_count', 0)}")
    
    # Check for namespaces
    namespaces = stats.get('namespaces', {})
    if namespaces:
        print(f"\n🗂️ Namespaces found in index:")
        for ns_name, ns_data in namespaces.items():
            print(f"  - {ns_name}: {ns_data.get('vector_count', 0)} vectors")
    else:
        print("\n⚠️ No namespaces found in the index yet")
    
    # Query for a sample vector (simple test query)
    try:
        print("\n🔍 Testing query functionality...")
        # Create a simple random vector of the right dimension
        dimension = stats.get('dimension', 1024)
        import numpy as np
        query_vector = np.random.randn(dimension).tolist()
        
        # Try both default namespace and "public" namespace if it exists
        for namespace in [None] + ([ns for ns in namespaces.keys() if ns == "public"] if "public" in namespaces else []):
            ns_label = namespace if namespace else "default"
            print(f"  - Querying namespace: {ns_label}")
            results = index.query(
                vector=query_vector,
                top_k=1,
                namespace=namespace,
                include_metadata=True
            )
            if results.get('matches'):
                print(f"    ✅ Got {len(results['matches'])} results")
                for match in results['matches'][:1]:  # Show details for first match only
                    print(f"    - ID: {match['id']}")
                    print(f"    - Score: {match['score']}")
                    if 'metadata' in match:
                        print(f"    - Metadata: {match['metadata']}")
            else:
                print(f"    ⚠️ No results found (this may be normal for empty namespaces)")
    except Exception as e:
        print(f"❌ Query test failed: {str(e)}")
    
except Exception as e:
    print(f"❌ Failed to connect to index: {str(e)}")
    sys.exit(1)
