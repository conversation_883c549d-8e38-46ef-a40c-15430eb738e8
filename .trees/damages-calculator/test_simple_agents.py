#!/usr/bin/env python3
"""
Simple test to verify individual agent functionality without import conflicts.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set required environment variables
os.environ.setdefault('MCP_RULES_BASE', 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

def test_intake_agent_direct():
    """Test intake agent directly without other imports."""
    try:
        # Clear any cached modules that might cause conflicts
        modules_to_clear = [k for k in sys.modules.keys() if 'pi_lawyer.agents' in k]
        for module in modules_to_clear:
            del sys.modules[module]
        
        from backend.agents.interactive.intake.agent import IntakeAgent
        agent = IntakeAgent()
        print("✅ IntakeAgent: Initialization successful")
        
        # Test graph creation
        graph = agent.create_graph()
        print("✅ IntakeAgent: Graph creation successful")
        print(f"   Graph type: {type(graph)}")
        
        return True, agent, graph
    except Exception as e:
        print(f"❌ IntakeAgent: Failed - {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_document_agent_direct():
    """Test document agent directly."""
    try:
        from backend.agents.insights.document.agent import DocumentAgent
        agent = DocumentAgent()
        print("✅ DocumentAgent: Initialization successful")
        
        # Test graph creation
        graph = agent.create_graph()
        print("✅ DocumentAgent: Graph creation successful")
        print(f"   Graph type: {type(graph)}")
        
        return True, agent, graph
    except Exception as e:
        print(f"❌ DocumentAgent: Failed - {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_deadline_agent_direct():
    """Test deadline agent directly."""
    try:
        from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
        agent = DeadlineInsightsAgent()
        print("✅ DeadlineInsightsAgent: Initialization successful")
        
        # Test graph creation
        graph = agent.create_graph()
        print("✅ DeadlineInsightsAgent: Graph creation successful")
        print(f"   Graph type: {type(graph)}")
        
        return True, agent, graph
    except Exception as e:
        print(f"❌ DeadlineInsightsAgent: Failed - {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_research_agent_direct():
    """Test research agent directly."""
    try:
        from backend.agents.interactive.research.agent import ResearchAgent
        agent = ResearchAgent()
        print("✅ ResearchAgent: Initialization successful")

        # Test graph creation
        graph = agent.create_graph()
        print("✅ ResearchAgent: Graph creation successful")
        print(f"   Graph type: {type(graph)}")

        return True, agent, graph
    except Exception as e:
        print(f"❌ ResearchAgent: Failed - {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_supervisor_agent_direct():
    """Test supervisor agent directly."""
    try:
        from backend.agents.insights.supervisor.agent import SupervisorAgent
        agent = SupervisorAgent()
        print("✅ SupervisorAgent: Initialization successful")

        # Test graph creation
        graph = agent.create_graph()
        print("✅ SupervisorAgent: Graph creation successful")
        print(f"   Graph type: {type(graph)}")

        return True, agent, graph
    except Exception as e:
        print(f"❌ SupervisorAgent: Failed - {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def main():
    """Run simple agent tests."""
    print("🧪 Testing Individual Agents (Simple)...")
    print("=" * 50)

    # Test each agent individually
    print("\n1. Testing IntakeAgent:")
    intake_result = test_intake_agent_direct()

    print("\n2. Testing DocumentAgent:")
    document_result = test_document_agent_direct()

    print("\n3. Testing DeadlineInsightsAgent:")
    deadline_result = test_deadline_agent_direct()

    print("\n4. Testing ResearchAgent:")
    research_result = test_research_agent_direct()

    print("\n5. Testing SupervisorAgent:")
    supervisor_result = test_supervisor_agent_direct()

    # Summary
    print("\n" + "=" * 50)
    print("📊 Simple Test Summary:")

    results = [
        ("IntakeAgent", intake_result[0]),
        ("DocumentAgent", document_result[0]),
        ("DeadlineInsightsAgent", deadline_result[0]),
        ("ResearchAgent", research_result[0]),
        ("SupervisorAgent", supervisor_result[0])
    ]

    successful = [name for name, success in results if success]
    failed = [name for name, success in results if not success]

    print(f"✅ Successful: {len(successful)} agents")
    if successful:
        print(f"   - {', '.join(successful)}")

    print(f"❌ Failed: {len(failed)} agents")
    if failed:
        print(f"   - {', '.join(failed)}")

if __name__ == "__main__":
    main()
