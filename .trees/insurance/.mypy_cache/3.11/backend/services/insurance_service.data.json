{".class": "MypyFile", "_fullname": "backend.services.insurance_service", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "CarrierStyleProfileCreate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.CarrierStyleProfileCreate", "kind": "Gdef"}, "CarrierStyleProfileORM": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.CarrierStyleProfileORM", "kind": "Gdef"}, "CarrierStyleProfileResponse": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.CarrierStyleProfileResponse", "kind": "Gdef"}, "CoverageLimitORM": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.CoverageLimitORM", "kind": "Gdef"}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "DemandLetterCreate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.DemandLetterCreate", "kind": "Gdef"}, "DemandLetterORM": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.DemandLetterORM", "kind": "Gdef"}, "DemandLetterResponse": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.DemandLetterResponse", "kind": "Gdef"}, "DemandLetterUpdate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.DemandLetterUpdate", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "InsurancePolicyCreate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurancePolicyCreate", "kind": "Gdef"}, "InsurancePolicyORM": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurancePolicyORM", "kind": "Gdef"}, "InsurancePolicyResponse": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurancePolicyResponse", "kind": "Gdef"}, "InsurancePolicyUpdate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurancePolicyUpdate", "kind": "Gdef"}, "InsuranceService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "backend.services.insurance_service.InsuranceService", "name": "InsuranceService", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "backend.services.insurance_service.InsuranceService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "backend.services.insurance_service", "mro": ["backend.services.insurance_service.InsuranceService", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "db", "tenant_id", "user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "db", "tenant_id", "user_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "sqlalchemy.ext.asyncio.session.AsyncSession", {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of InsuranceService", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_offer_analytics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id", "offer_amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService._calculate_offer_analytics", "name": "_calculate_offer_analytics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id", "offer_amount"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID", "decimal.Decimal"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_offer_analytics of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_historical_offers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tenant_id", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService._get_historical_offers", "name": "_get_historical_offers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tenant_id", "limit"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_historical_offers of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hash_phi_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService._hash_phi_data", "name": "_hash_phi_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_hash_phi_data of InsuranceService", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sanitize_for_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService._sanitize_for_logging", "name": "_sanitize_for_logging", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["backend.services.insurance_service.InsuranceService", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_sanitize_for_logging of InsuranceService", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_tenant_access": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record_tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService._validate_tenant_access", "name": "_validate_tenant_access", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record_tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_tenant_access of InsuranceService", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_carrier_profile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "profile_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.create_carrier_profile", "name": "create_carrier_profile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "profile_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "backend.models.insurance.CarrierStyleProfileCreate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_carrier_profile of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "backend.models.insurance.CarrierStyleProfileResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_demand_letter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "demand_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.create_demand_letter", "name": "create_demand_letter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "demand_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "backend.models.insurance.DemandLetterCreate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_demand_letter of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "backend.models.insurance.DemandLetterResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_insurance_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.create_insurance_policy", "name": "create_insurance_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "backend.models.insurance.InsurancePolicyCreate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_insurance_policy of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "backend.models.insurance.InsurancePolicyResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_insurer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "insurer_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.create_insurer", "name": "create_insurer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "insurer_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "backend.models.insurance.InsurerCreate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_insurer of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "backend.models.insurance.InsurerResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_negotiation_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.create_negotiation_event", "name": "create_negotiation_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "backend.models.insurance.NegotiationEventCreate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_negotiation_event of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "backend.models.insurance.NegotiationEventResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "db": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "backend.services.insurance_service.InsuranceService.db", "name": "db", "setter_type": null, "type": "sqlalchemy.ext.asyncio.session.AsyncSession"}}, "delete_negotiation_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_id", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.delete_negotiation_event", "name": "delete_negotiation_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_id", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_negotiation_event of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_carrier_profile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "insurer_id", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_carrier_profile", "name": "get_carrier_profile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "insurer_id", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_carrier_profile of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.CarrierStyleProfileResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_demands_by_matter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_demands_by_matter", "name": "get_demands_by_matter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_demands_by_matter of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["backend.models.insurance.DemandLetterResponse"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_insurance_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_id", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_insurance_policy", "name": "get_insurance_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_id", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_insurance_policy of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.InsurancePolicyResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_insurer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "insurer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_insurer", "name": "get_insurer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "insurer_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_insurer of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.InsurerResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_insurer_by_naic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "naic_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_insurer_by_naic", "name": "get_insurer_by_naic", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "naic_code"], "arg_types": ["backend.services.insurance_service.InsuranceService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_insurer_by_naic of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.InsurerResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_insurers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "skip", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_insurers", "name": "get_insurers", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "skip", "limit"], "arg_types": ["backend.services.insurance_service.InsuranceService", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_insurers of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["backend.models.insurance.InsurerResponse"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_matters_with_outlier_offers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_matters_with_outlier_offers", "name": "get_matters_with_outlier_offers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_matters_with_outlier_offers of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_negotiation_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "matter_id", "tenant_id", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_negotiation_events", "name": "get_negotiation_events", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "matter_id", "tenant_id", "limit"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_negotiation_events of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["backend.models.insurance.NegotiationEventResponse"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_negotiation_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_negotiation_summary", "name": "get_negotiation_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_negotiation_summary of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "backend.models.insurance.NegotiationSummary"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_policies_by_matter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_policies_by_matter", "name": "get_policies_by_matter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "matter_id", "tenant_id"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_policies_by_matter of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["backend.models.insurance.InsurancePolicyResponse"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_policies_needing_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tenant_id", "confidence_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.get_policies_needing_analysis", "name": "get_policies_needing_analysis", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tenant_id", "confidence_threshold"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_policies_needing_analysis of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["backend.models.insurance.InsurancePolicyResponse"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "backend.services.insurance_service.InsuranceService.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "search_insurers_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.search_insurers_by_name", "name": "search_insurers_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "limit"], "arg_types": ["backend.services.insurance_service.InsuranceService", "builtins.str", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "search_insurers_by_name of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["backend.models.insurance.InsurerResponse"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tenant_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "backend.services.insurance_service.InsuranceService.tenant_id", "name": "tenant_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "update_demand_letter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "demand_id", "tenant_id", "update_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.update_demand_letter", "name": "update_demand_letter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "demand_id", "tenant_id", "update_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID", "backend.models.insurance.DemandLetterUpdate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_demand_letter of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.DemandLetterResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_insurance_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "policy_id", "tenant_id", "update_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.update_insurance_policy", "name": "update_insurance_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "policy_id", "tenant_id", "update_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "uuid.UUID", "backend.models.insurance.InsurancePolicyUpdate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_insurance_policy of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.InsurancePolicyResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_insurer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "insurer_id", "update_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "backend.services.insurance_service.InsuranceService.update_insurer", "name": "update_insurer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "insurer_id", "update_data"], "arg_types": ["backend.services.insurance_service.InsuranceService", "uuid.UUID", "backend.models.insurance.InsurerUpdate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_insurer of InsuranceService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["backend.models.insurance.InsurerResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "backend.services.insurance_service.InsuranceService.user_id", "name": "user_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "backend.services.insurance_service.InsuranceService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "backend.services.insurance_service.InsuranceService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InsurerCreate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurerCreate", "kind": "Gdef"}, "InsurerORM": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurerORM", "kind": "Gdef"}, "InsurerResponse": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurerResponse", "kind": "Gdef"}, "InsurerUpdate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.InsurerUpdate", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NegotiationEventCreate": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.NegotiationEventCreate", "kind": "Gdef"}, "NegotiationEventORM": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.NegotiationEventORM", "kind": "Gdef"}, "NegotiationEventResponse": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.NegotiationEventResponse", "kind": "Gdef"}, "NegotiationSummary": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.NegotiationSummary", "kind": "Gdef"}, "OfferAnalytics": {".class": "SymbolTableNode", "cross_ref": "backend.models.insurance.OfferAnalytics", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "backend.services.insurance_service.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "backend.services.insurance_service.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "backend.services.insurance_service.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "backend.services.insurance_service.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "backend.services.insurance_service.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "backend.services.insurance_service.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "desc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.desc", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "joinedload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.joinedload", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "selectinload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.selectinload", "kind": "Gdef"}, "structlog": {".class": "SymbolTableNode", "cross_ref": "structlog", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "backend/services/insurance_service.py"}