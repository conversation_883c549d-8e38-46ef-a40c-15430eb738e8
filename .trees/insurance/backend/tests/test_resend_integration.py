#!/usr/bin/env python3
"""
Resend Email Integration Test for MFA

Test script to verify Resend email service integration for MFA backup authentication.
"""

import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch
import pytest

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


@pytest.mark.integration
def test_resend_integration():
    """Test Resend email integration for MFA."""
    print("📧 Testing Resend Email Integration for MFA")
    print("=" * 60)

    try:
        # Load environment
        from dotenv import load_dotenv

        load_dotenv()

        # Test environment variables
        print("1. Checking Environment Variables...")
        resend_api_key = os.getenv("RESEND_API_KEY")

        if not resend_api_key:
            print("⚠️ RESEND_API_KEY not set - using mock for testing")
            use_mock = True
        else:
            print(f"✅ RESEND_API_KEY: {'*' * 20}...{resend_api_key[-4:]}")
            use_mock = False

        # Test Resend import
        print("\n2. Testing Resend Import...")
        try:
            import resend

            print("✅ Resend package imported successfully")
        except ImportError:
            print("❌ Resend package not available")
            return False

        # Test MFA service with Resend
        print("\n3. Testing MFA Service with Resend...")

        if use_mock:
            # Mock Resend for testing
            with patch("resend.emails.send") as mock_send:
                mock_send.return_value = {"id": "test-email-id"}

                # Test MFA service initialization
                from backend.services.mfa_service import MFAService

                mfa_service = MFAService()

                if mfa_service.resend_client:
                    print("✅ MFA service initialized with Resend client")
                else:
                    print(
                        "⚠️ MFA service initialized without Resend client (expected without API key)"
                    )

                print("✅ Mock Resend integration test passed")
        else:
            # Test with real API key (but don't actually send)
            from backend.services.mfa_service import MFAService

            mfa_service = MFAService()

            if mfa_service.resend_client:
                print("✅ MFA service initialized with Resend client")

                # Test email parameters structure
                print("\n4. Testing Email Parameters Structure...")
                test_email_params = {
                    "from": "<EMAIL>",
                    "to": ["<EMAIL>"],
                    "subject": "PI Lawyer AI - Verification Code",
                    "html": """
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #333;">Verification Code</h2>
                        <p>Your PI Lawyer AI verification code is:</p>
                        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0;">
                            <h1 style="color: #007bff; font-size: 32px; margin: 0; letter-spacing: 4px;">123456</h1>
                        </div>
                        <p>This code is valid for <strong>15 minutes</strong>.</p>
                        <p style="color: #666; font-size: 14px;">If you didn't request this code, please ignore this email.</p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                        <p style="color: #999; font-size: 12px;">PI Lawyer AI Security Team</p>
                    </div>
                    """,
                }

                # Validate email parameters
                required_fields = ["from", "to", "subject", "html"]
                for field in required_fields:
                    if field not in test_email_params:
                        print(f"❌ Missing required field: {field}")
                        return False

                print("✅ Email parameters structure is valid")
                print("✅ Real Resend integration test passed")
            else:
                print("❌ MFA service failed to initialize Resend client")
                assert False

        # Test email template rendering
        print("\n5. Testing Email Template...")
        test_token = "123456"
        template_html = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Verification Code</h2>
            <p>Your PI Lawyer AI verification code is:</p>
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0;">
                <h1 style="color: #007bff; font-size: 32px; margin: 0; letter-spacing: 4px;">{test_token}</h1>
            </div>
            <p>This code is valid for <strong>15 minutes</strong>.</p>
            <p style="color: #666; font-size: 14px;">If you didn't request this code, please ignore this email.</p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
            <p style="color: #999; font-size: 12px;">PI Lawyer AI Security Team</p>
        </div>
        """

        if test_token in template_html:
            print("✅ Email template renders token correctly")
        else:
            print("❌ Email template token rendering failed")
            return False

        print("\n" + "=" * 60)
        print("🎉 ALL RESEND INTEGRATION TESTS PASSED!")
        print("✅ Resend email service ready for MFA backup authentication")
        assert True

    except Exception as e:
        print(f"\n❌ RESEND INTEGRATION TEST FAILED: {e}")
        assert False


@pytest.mark.integration
def test_email_service_availability():
    """Test email service availability check."""
    print("\n6. Testing Email Service Availability...")
    try:
        from backend.services.mfa_service import MFAService

        mfa_service = MFAService()

        if mfa_service.resend_client:
            print("✅ Email service available")
        else:
            print("⚠️ Email service not available (API key not configured)")

        assert True
    except Exception as e:
        print(f"❌ Email service availability test failed: {e}")
        assert False


if __name__ == "__main__":
    # Run Resend integration test
    success = test_resend_integration()

    # Run availability test
    if success:
        availability_success = test_email_service_availability()
        success = success and availability_success

    if success:
        print("\n📧 Resend email integration verified for MFA system!")
        sys.exit(0)
    else:
        print("\n⚠️ Resend email integration issues detected!")
        sys.exit(1)
