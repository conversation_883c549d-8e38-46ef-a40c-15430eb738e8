"""
MFA Implementation Validation Test

Simple validation test to verify MFA components are properly implemented
and can be imported without errors.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_mfa_models_import():
    """Test that MFA models can be imported."""
    try:
        from backend.models.mfa import (
            MFAMethod,
            MFAFactorType,
            MFAFactorStatus,
            SecurityEventCategory,
            SecurityEventSeverity,
            SuperadminMFAConfig,
            SuperadminMFASession,
            SuperadminMFAAttempt,
            BackupMFAToken,
            SecurityEvent,
            MFASetupRequest,
            MFAVerificationRequest,
            MFAStatusResponse,
            TOTPSetupResponse,
            RecoveryCodesResponse,
            MFAChallengeRequest,
            MFAChallengeResponse,
            MFAValidationRequest,
            MFAValidationResponse,
        )

        print("✅ MFA models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import MFA models: {e}")
        return False


def test_mfa_service_import():
    """Test that MFA service can be imported."""
    try:
        # Mock the Supabase dependency
        import unittest.mock

        with unittest.mock.patch("backend.api.dependencies.auth.get_supabase_client"):
            from backend.services.mfa_service import MFAService, mfa_service

            print("✅ MFA service imported successfully")
            return True
    except Exception as e:
        print(f"❌ Failed to import MFA service: {e}")
        return False


def test_mfa_middleware_import():
    """Test that MFA middleware can be imported."""
    try:
        import unittest.mock

        with unittest.mock.patch("backend.services.mfa_service.mfa_service"):
            from backend.middleware.mfa_middleware import MFAMiddleware, mfa_middleware

            print("✅ MFA middleware imported successfully")
            return True
    except Exception as e:
        print(f"❌ Failed to import MFA middleware: {e}")
        return False


def test_mfa_api_routes_import():
    """Test that MFA API routes can be imported."""
    try:
        import unittest.mock

        with unittest.mock.patch("backend.services.mfa_service.mfa_service"):
            with unittest.mock.patch(
                "backend.api.dependencies.auth.require_super_admin"
            ):
                from backend.api.routes.mfa import router

                print("✅ MFA API routes imported successfully")
                return True
    except Exception as e:
        print(f"❌ Failed to import MFA API routes: {e}")
        return False


def test_pyotp_functionality():
    """Test TOTP functionality with pyotp."""
    try:
        import pyotp

        # Generate secret
        secret = pyotp.random_base32()
        assert len(secret) == 32

        # Create TOTP instance
        totp = pyotp.TOTP(secret)

        # Generate token
        token = totp.now()
        assert len(token) == 6
        assert token.isdigit()

        # Verify token
        is_valid = totp.verify(token)
        assert is_valid is True

        # Generate QR URI
        uri = totp.provisioning_uri(name="<EMAIL>", issuer_name="PI Lawyer AI")
        assert uri.startswith("otpauth://totp/")

        print("✅ TOTP functionality validated")
        return True
    except Exception as e:
        print(f"❌ TOTP functionality failed: {e}")
        return False


def test_recovery_code_generation():
    """Test recovery code generation."""
    try:
        import secrets

        # Generate recovery codes
        codes = []
        for _ in range(10):
            code = "".join(
                secrets.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789") for _ in range(8)
            )
            codes.append(code)

        # Validate codes
        assert len(codes) == 10
        assert len(set(codes)) == 10  # All unique

        for code in codes:
            assert len(code) == 8
            assert code.isalnum()
            assert code.isupper()

        print("✅ Recovery code generation validated")
        return True
    except Exception as e:
        print(f"❌ Recovery code generation failed: {e}")
        return False


def test_session_token_generation():
    """Test session token generation."""
    try:
        import secrets

        # Generate session tokens
        tokens = []
        for _ in range(100):
            token = secrets.token_urlsafe(32)
            tokens.append(token)

        # Validate tokens
        assert len(tokens) == 100
        assert len(set(tokens)) == 100  # All unique

        for token in tokens:
            assert len(token) > 20  # Sufficient length

        print("✅ Session token generation validated")
        return True
    except Exception as e:
        print(f"❌ Session token generation failed: {e}")
        return False


def test_backup_token_hashing():
    """Test backup token hashing."""
    try:
        import hashlib

        # Test token hashing
        token = "123456"
        token_hash = hashlib.sha256(token.encode()).hexdigest()

        assert len(token_hash) == 64  # SHA-256 hex length
        assert token_hash != token  # Should be different

        # Test consistency
        token_hash2 = hashlib.sha256(token.encode()).hexdigest()
        assert token_hash == token_hash2  # Should be same

        print("✅ Backup token hashing validated")
        return True
    except Exception as e:
        print(f"❌ Backup token hashing failed: {e}")
        return False


def test_database_schema_validation():
    """Test database schema structure."""
    try:
        # Read the migration file
        migration_file = (
            project_root
            / "backend"
            / "migrations"
            / "add_superadmin_mfa_enhancements.sql"
        )

        if migration_file.exists():
            content = migration_file.read_text()

            # Check for required tables
            required_tables = [
                "superadmin_mfa_config",
                "superadmin_mfa_sessions",
                "superadmin_mfa_attempts",
                "backup_mfa_tokens",
                "security_events",
            ]

            for table in required_tables:
                assert table in content, f"Table {table} not found in migration"

            # Check for required functions
            required_functions = [
                "is_superadmin_mfa_required",
                "validate_mfa_session",
                "log_security_event",
            ]

            for function in required_functions:
                assert (
                    function in content
                ), f"Function {function} not found in migration"

            print("✅ Database schema validation passed")
            return True
        else:
            print("❌ Migration file not found")
            return False
    except Exception as e:
        print(f"❌ Database schema validation failed: {e}")
        return False


def run_all_tests():
    """Run all validation tests."""
    print("🔐 MFA Implementation Validation")
    print("=" * 50)

    tests = [
        ("MFA Models Import", test_mfa_models_import),
        ("MFA Service Import", test_mfa_service_import),
        ("MFA Middleware Import", test_mfa_middleware_import),
        ("MFA API Routes Import", test_mfa_api_routes_import),
        ("TOTP Functionality", test_pyotp_functionality),
        ("Recovery Code Generation", test_recovery_code_generation),
        ("Session Token Generation", test_session_token_generation),
        ("Backup Token Hashing", test_backup_token_hashing),
        ("Database Schema", test_database_schema_validation),
    ]

    results = {}
    for test_name, test_func in tests:
        print(f"\nTesting: {test_name}")
        print("-" * 30)
        results[test_name] = test_func()

    # Summary
    print("\n" + "=" * 50)
    print("VALIDATION SUMMARY")
    print("=" * 50)

    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")

    print(f"\nTotal Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

    if failed_tests == 0:
        print("\n🎉 ALL VALIDATION TESTS PASSED!")
        print("✅ MFA implementation is structurally sound")
        return True
    else:
        print(f"\n⚠️  {failed_tests} TEST(S) FAILED")
        print("❌ Please fix issues before deployment")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
