"""
Integration tests for laws-API client.

These tests verify that the laws-API client works correctly with mocked responses.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from typing import List, Dict, Any

pytestmark = pytest.mark.integration

from backend.services.laws_api_client import (
    LawsApiClient,
    SearchRequest,
    SearchResult,
    RecommendRequest,
    RecommendationResult,
    GraphRequest,
    GraphResult,
    DocumentType,
    PracticeArea,
    EntityType,
    RelationshipType,
    LawsApiException,
)


class TestLawsApiClient:
    """Test the laws-API client functionality."""

    @pytest.fixture
    def mock_response_data(self):
        """Mock response data for laws-API."""
        return {
            "success": True,
            "data": [
                {
                    "id": "tx-statute-12345",
                    "title": "Texas Civil Practice and Remedies Code § 16.003",
                    "content": "A person must bring suit for personal injury not later than two years after the day the cause of action accrues.",
                    "document_type": "statute",
                    "jurisdiction": "texas",
                    "relevance_score": 0.95,
                    "highlights": ["personal injury", "statute of limitations"],
                    "citation": "Tex. Civ. Prac. & Rem. Code § 16.003",
                    "url": "https://statutes.capitol.texas.gov/Docs/CP/htm/CP.16.htm",
                }
            ],
        }

    @pytest.fixture
    def mock_recommendation_data(self):
        """Mock recommendation response data."""
        return {
            "success": True,
            "data": [
                {
                    "id": "tx-case-67890",
                    "title": "Smith v. Jones",
                    "content": "Court held that the statute of limitations for personal injury claims begins to run from the date of injury.",
                    "document_type": "case_law",
                    "jurisdiction": "texas",
                    "similarity_score": 0.88,
                    "relationship_type": "cites",
                    "explanation": "This case interprets the statute of limitations provision",
                }
            ],
        }

    @pytest.fixture
    def mock_graph_data(self):
        """Mock graph response data."""
        return {
            "success": True,
            "data": {
                "nodes": [
                    {
                        "id": "tx-statute-12345",
                        "label": "Tex. Civ. Prac. & Rem. Code § 16.003",
                        "type": "statute",
                        "properties": {
                            "title": "Statute of Limitations",
                            "jurisdiction": "texas",
                        },
                    }
                ],
                "edges": [
                    {
                        "source": "tx-case-67890",
                        "target": "tx-statute-12345",
                        "relationship": "cites",
                        "weight": 0.9,
                    }
                ],
            },
        }

    @pytest.mark.asyncio
    async def test_search_success(self, mock_response_data):
        """Test successful search request."""
        with patch("aiohttp.ClientSession.request") as mock_request:
            # Mock the response
            mock_response = AsyncMock()
            mock_response.ok = True
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_request.return_value.__aenter__.return_value = mock_response

            async with LawsApiClient() as client:
                search_request = SearchRequest(
                    query="personal injury statute of limitations",
                    jurisdiction=["texas"],
                    limit=10,
                )

                results = await client.search(search_request)

                assert len(results) == 1
                assert results[0].id == "tx-statute-12345"
                assert (
                    results[0].title
                    == "Texas Civil Practice and Remedies Code § 16.003"
                )
                assert results[0].document_type == DocumentType.STATUTE
                assert results[0].jurisdiction == "texas"
                assert results[0].relevance_score == 0.95

    @pytest.mark.asyncio
    async def test_recommend_success(self, mock_recommendation_data):
        """Test successful recommendation request."""
        with patch("aiohttp.ClientSession.request") as mock_request:
            # Mock the response
            mock_response = AsyncMock()
            mock_response.ok = True
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_recommendation_data)
            mock_request.return_value.__aenter__.return_value = mock_response

            async with LawsApiClient() as client:
                recommend_request = RecommendRequest(
                    content="statute of limitations for personal injury",
                    jurisdiction=["texas"],
                    limit=5,
                )

                results = await client.recommend(recommend_request)

                assert len(results) == 1
                assert results[0].id == "tx-case-67890"
                assert results[0].title == "Smith v. Jones"
                assert results[0].document_type == DocumentType.CASE_LAW
                assert results[0].similarity_score == 0.88
                assert results[0].relationship_type == RelationshipType.CITES

    @pytest.mark.asyncio
    async def test_graph_query_success(self, mock_graph_data):
        """Test successful graph query."""
        with patch("aiohttp.ClientSession.request") as mock_request:
            # Mock the response
            mock_response = AsyncMock()
            mock_response.ok = True
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_graph_data)
            mock_request.return_value.__aenter__.return_value = mock_response

            async with LawsApiClient() as client:
                graph_request = GraphRequest(
                    entity_id="tx-statute-12345",
                    entity_type=EntityType.STATUTE,
                    depth=2,
                )

                result = await client.graph_query(graph_request)

                assert len(result.nodes) == 1
                assert len(result.edges) == 1
                assert result.nodes[0].id == "tx-statute-12345"
                assert result.nodes[0].type == EntityType.STATUTE
                assert result.edges[0].relationship == RelationshipType.CITES

    @pytest.mark.asyncio
    async def test_rate_limiting_error(self):
        """Test rate limiting error handling."""
        with patch("aiohttp.ClientSession.request") as mock_request:
            # Mock rate limit response
            mock_response = AsyncMock()
            mock_response.ok = False
            mock_response.status = 429
            mock_response.headers = {"Retry-After": "60"}
            mock_response.text = AsyncMock(return_value="Rate limit exceeded")
            mock_request.return_value.__aenter__.return_value = mock_response

            async with LawsApiClient(max_retries=0) as client:
                search_request = SearchRequest(query="test query")

                with pytest.raises(LawsApiException) as exc_info:
                    await client.search(search_request)

                assert exc_info.value.status_code == 429
                assert exc_info.value.retry_after == 60

    @pytest.mark.asyncio
    async def test_authentication_error(self):
        """Test authentication error handling."""
        with patch("aiohttp.ClientSession.request") as mock_request:
            # Mock auth error response
            mock_response = AsyncMock()
            mock_response.ok = False
            mock_response.status = 401
            mock_response.text = AsyncMock(return_value="Unauthorized")
            mock_request.return_value.__aenter__.return_value = mock_response

            async with LawsApiClient(max_retries=0) as client:
                search_request = SearchRequest(query="test query")

                with pytest.raises(LawsApiException) as exc_info:
                    await client.search(search_request)

                assert exc_info.value.status_code == 401


class TestLawsApiClientIntegration:
    """Test the laws-API client integration scenarios."""

    @pytest.mark.asyncio
    async def test_client_context_manager(self):
        """Test that the client works as a context manager."""
        async with LawsApiClient() as client:
            assert client._session is not None

        # Session should be closed after context exit
        # Note: We can't easily test this without accessing private attributes

    @pytest.mark.asyncio
    async def test_multiple_requests_same_client(self, mock_response_data):
        """Test multiple requests using the same client instance."""
        with patch("aiohttp.ClientSession.request") as mock_request:
            # Mock the response
            mock_response = AsyncMock()
            mock_response.ok = True
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_request.return_value.__aenter__.return_value = mock_response

            async with LawsApiClient() as client:
                # First request
                search_request1 = SearchRequest(
                    query="personal injury", jurisdiction=["texas"]
                )
                results1 = await client.search(search_request1)

                # Second request
                search_request2 = SearchRequest(
                    query="statute of limitations", jurisdiction=["texas"]
                )
                results2 = await client.search(search_request2)

                assert len(results1) == 1
                assert len(results2) == 1
                assert mock_request.call_count == 2


@pytest.mark.slow
class TestLawsApiLiveIntegration:
    """
    Live integration tests that actually call the laws-API service.
    These tests are marked as 'slow' and should only be run when the
    laws-API service is available and configured.
    """

    @pytest.mark.asyncio
    async def test_live_search(self):
        """Test live search against laws-API service."""
        # Skip if laws-API is not available
        pytest.skip("Live integration test - requires laws-API service")

        async with LawsApiClient() as client:
            search_request = SearchRequest(
                query="personal injury statute of limitations",
                jurisdiction=["texas"],
                limit=5,
            )

            results = await client.search(search_request)

            # Basic validation
            assert isinstance(results, list)
            if results:
                assert hasattr(results[0], "id")
                assert hasattr(results[0], "title")
                assert hasattr(results[0], "content")

    @pytest.mark.asyncio
    async def test_live_recommendation(self):
        """Test live recommendation against laws-API service."""
        # Skip if laws-API is not available
        pytest.skip("Live integration test - requires laws-API service")

        async with LawsApiClient() as client:
            recommend_request = RecommendRequest(
                content="statute of limitations for personal injury claims",
                jurisdiction=["texas"],
                limit=3,
            )

            results = await client.recommend(recommend_request)

            # Basic validation
            assert isinstance(results, list)
            if results:
                assert hasattr(results[0], "id")
                assert hasattr(results[0], "similarity_score")
                assert hasattr(results[0], "relationship_type")
