#!/usr/bin/env python3
"""
MFA Test Runner

Comprehensive test runner for all MFA functionality including
integration tests, security scenarios, and performance tests.
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def run_command(command, description):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")

    start_time = time.time()
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    end_time = time.time()

    print(f"Duration: {end_time - start_time:.2f} seconds")

    if result.returncode == 0:
        print(f"✅ SUCCESS: {description}")
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
    else:
        print(f"❌ FAILED: {description}")
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)

    return result.returncode == 0


def main():
    """Run all MFA tests."""
    print("🔐 PI Lawyer AI - MFA Test Suite")
    print("=" * 60)

    # Change to project root directory
    os.chdir(project_root)

    # Test categories
    test_results = {}

    # 1. MFA Integration Tests
    test_results["integration"] = run_command(
        "python -m pytest backend/tests/test_mfa_integration.py -v --tb=short",
        "MFA Integration Tests",
    )

    # 2. MFA Security Scenarios
    test_results["security"] = run_command(
        "python -m pytest backend/tests/test_mfa_security_scenarios.py -v --tb=short",
        "MFA Security Scenarios",
    )

    # 3. Code Quality Checks
    test_results["linting"] = run_command(
        "python -m flake8 backend/services/mfa_service.py backend/api/routes/mfa.py backend/middleware/mfa_middleware.py --max-line-length=120 --ignore=E501,W503",
        "Code Quality - Linting",
    )

    # 4. Type Checking (if mypy is available)
    test_results["typing"] = run_command(
        "python -m mypy backend/services/mfa_service.py --ignore-missing-imports --no-strict-optional",
        "Type Checking",
    )

    # 5. Import Tests
    test_results["imports"] = run_command(
        "python -c 'from backend.services.mfa_service import mfa_service; from backend.api.routes.mfa import router; from backend.middleware.mfa_middleware import mfa_middleware; print(\"All imports successful\")'",
        "Import Validation",
    )

    # 6. Database Schema Validation (if available)
    test_results["schema"] = run_command(
        "python -c 'from backend.models.mfa import *; print(\"All MFA models imported successfully\")'",
        "Database Schema Models",
    )

    # Print summary
    print("\n" + "=" * 60)
    print("🔐 MFA TEST SUITE SUMMARY")
    print("=" * 60)

    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    failed_tests = total_tests - passed_tests

    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.upper():20} {status}")

    print(f"\nTotal Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

    if failed_tests == 0:
        print("\n🎉 ALL MFA TESTS PASSED!")
        print("✅ MFA system is ready for production deployment")
        return 0
    else:
        print(f"\n⚠️  {failed_tests} TEST(S) FAILED")
        print("❌ Please fix failing tests before deployment")
        return 1


def run_specific_test(test_name):
    """Run a specific test category."""
    test_commands = {
        "integration": "python -m pytest backend/tests/test_mfa_integration.py -v",
        "security": "python -m pytest backend/tests/test_mfa_security_scenarios.py -v",
        "all": "python backend/tests/run_mfa_tests.py",
    }

    if test_name in test_commands:
        return run_command(test_commands[test_name], f"MFA {test_name.title()} Tests")
    else:
        print(f"Unknown test category: {test_name}")
        print(f"Available categories: {', '.join(test_commands.keys())}")
        return False


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Run specific test
        test_category = sys.argv[1]
        success = run_specific_test(test_category)
        sys.exit(0 if success else 1)
    else:
        # Run all tests
        sys.exit(main())
