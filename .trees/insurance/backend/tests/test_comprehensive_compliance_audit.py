"""
Tests for Comprehensive Compliance Audit Trail System

This module contains comprehensive tests for the compliance audit trail system
including service functionality, API endpoints, middleware, and integrations.
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import Mock, patch, AsyncMock

from backend.services.comprehensive_compliance_audit import (
    ComprehensiveComplianceAuditService,
    ComplianceEventType,
    ComplianceFramework,
    ComplianceSeverity,
    ComplianceAuditEvent,
    comprehensive_audit_service,
    log_data_retention_event,
    log_consent_management_event,
    log_data_residency_event,
    log_regional_disclaimer_event,
    log_professional_responsibility_event,
    log_security_event,
    audit_session,
)
from backend.middleware.compliance_audit_middleware import ComplianceAuditMiddleware
from backend.models.data_retention import DataRegion


class TestComprehensiveComplianceAuditService:
    """Test the comprehensive compliance audit service."""

    @pytest.fixture
    def audit_service(self):
        """Create a test audit service instance."""
        return ComprehensiveComplianceAuditService()

    @pytest.mark.asyncio
    async def test_log_compliance_event_basic(self, audit_service):
        """Test basic compliance event logging."""
        with patch.object(
            audit_service, "_flush_events", new_callable=AsyncMock
        ) as mock_flush:
            event_id = await audit_service.log_compliance_event(
                event_type=ComplianceEventType.CONSENT_GRANTED,
                framework=ComplianceFramework.CONSENT_MANAGEMENT,
                severity=ComplianceSeverity.INFO,
                user_id="test-user-123",
                metadata={"consent_type": "marketing"},
            )

            assert event_id is not None
            assert len(audit_service.event_buffer) == 1

            event = audit_service.event_buffer[0]
            assert event.event_type == ComplianceEventType.CONSENT_GRANTED
            assert event.framework == ComplianceFramework.CONSENT_MANAGEMENT
            assert event.user_id == "test-user-123"
            assert event.metadata["consent_type"] == "marketing"

    @pytest.mark.asyncio
    async def test_log_compliance_event_critical_immediate_flush(self, audit_service):
        """Test that critical events are flushed immediately."""
        with patch.object(
            audit_service, "_log_immediate_event", new_callable=AsyncMock
        ) as mock_immediate:
            await audit_service.log_compliance_event(
                event_type=ComplianceEventType.SECURITY_VIOLATION,
                framework=ComplianceFramework.SECURITY,
                severity=ComplianceSeverity.CRITICAL,
                user_id="test-user-123",
            )

            mock_immediate.assert_called_once()

    @pytest.mark.asyncio
    async def test_buffer_flush_on_size_limit(self, audit_service):
        """Test that events are flushed when buffer reaches size limit."""
        audit_service.buffer_size = 2

        with patch.object(
            audit_service, "_flush_events", new_callable=AsyncMock
        ) as mock_flush:
            # Add first event - should not flush
            await audit_service.log_compliance_event(
                event_type=ComplianceEventType.CONSENT_GRANTED,
                framework=ComplianceFramework.CONSENT_MANAGEMENT,
                user_id="user-1",
            )
            mock_flush.assert_not_called()

            # Add second event - should trigger flush
            await audit_service.log_compliance_event(
                event_type=ComplianceEventType.CONSENT_WITHDRAWN,
                framework=ComplianceFramework.CONSENT_MANAGEMENT,
                user_id="user-2",
            )
            mock_flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_flush_events_database_integration(self, audit_service):
        """Test flushing events to database."""
        # Mock Supabase client
        mock_supabase = Mock()
        mock_table = Mock()
        mock_insert = Mock()
        mock_execute = Mock()

        mock_supabase.table.return_value = mock_table
        mock_table.insert.return_value = mock_insert
        mock_insert.execute.return_value = Mock(data=[{"id": "test-id"}])

        with patch(
            "backend.services.comprehensive_compliance_audit.get_supabase_client",
            return_value=mock_supabase,
        ):
            # Add events to buffer
            audit_service.event_buffer = [
                ComplianceAuditEvent(
                    event_id=str(uuid4()),
                    event_type=ComplianceEventType.CONSENT_GRANTED,
                    framework=ComplianceFramework.CONSENT_MANAGEMENT,
                    severity=ComplianceSeverity.INFO,
                    timestamp=datetime.now(),
                    user_id="test-user",
                )
            ]

            await audit_service._flush_events()

            # Verify database calls
            mock_supabase.table.assert_called_with("compliance_audit_log")
            mock_table.insert.assert_called_once()
            mock_insert.execute.assert_called_once()

            # Verify buffer is cleared
            assert len(audit_service.event_buffer) == 0

    @pytest.mark.asyncio
    async def test_background_task_management(self, audit_service):
        """Test background task lifecycle management."""
        # Start background tasks
        await audit_service.start_background_tasks()
        assert audit_service._flush_task is not None

        # Stop background tasks
        await audit_service.stop_background_tasks()
        assert audit_service._flush_task is None


class TestComplianceEventLoggingFunctions:
    """Test the convenience logging functions."""

    @pytest.mark.asyncio
    async def test_log_data_retention_event(self):
        """Test data retention event logging."""
        with patch.object(
            comprehensive_audit_service, "log_compliance_event", new_callable=AsyncMock
        ) as mock_log:
            await log_data_retention_event(
                event_type=ComplianceEventType.RETENTION_POLICY_APPLIED,
                user_id="test-user",
                region="US",
                resource_type="user_data",
                metadata={"policy_name": "GDPR_Policy"},
            )

            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert (
                call_args["event_type"] == ComplianceEventType.RETENTION_POLICY_APPLIED
            )
            assert call_args["framework"] == ComplianceFramework.DATA_RETENTION
            assert call_args["user_id"] == "test-user"

    @pytest.mark.asyncio
    async def test_log_consent_management_event(self):
        """Test consent management event logging."""
        with patch.object(
            comprehensive_audit_service, "log_compliance_event", new_callable=AsyncMock
        ) as mock_log:
            await log_consent_management_event(
                event_type=ComplianceEventType.CONSENT_GRANTED,
                user_id="test-user",
                consent_type="marketing",
                consent_given=True,
            )

            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["framework"] == ComplianceFramework.CONSENT_MANAGEMENT
            assert call_args["metadata"]["consent_type"] == "marketing"
            assert call_args["metadata"]["consent_given"] is True

    @pytest.mark.asyncio
    async def test_log_security_event(self):
        """Test security event logging."""
        with patch.object(
            comprehensive_audit_service, "log_compliance_event", new_callable=AsyncMock
        ) as mock_log:
            await log_security_event(
                event_type=ComplianceEventType.AUTHENTICATION_EVENT,
                user_id="test-user",
                security_level="HIGH",
                threat_type="brute_force",
            )

            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["framework"] == ComplianceFramework.SECURITY
            assert call_args["metadata"]["security_level"] == "HIGH"
            assert call_args["metadata"]["threat_type"] == "brute_force"

    @pytest.mark.asyncio
    async def test_professional_responsibility_event_high_severity(self):
        """Test that professional responsibility events are always high severity."""
        with patch.object(
            comprehensive_audit_service, "log_compliance_event", new_callable=AsyncMock
        ) as mock_log:
            await log_professional_responsibility_event(
                event_type=ComplianceEventType.ATTORNEY_CLIENT_PRIVILEGE_ACCESS,
                user_id="attorney-123",
                professional_rule="ABA_1.6",
                client_id="client-456",
            )

            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["severity"] == ComplianceSeverity.HIGH
            assert (
                call_args["framework"]
                == ComplianceFramework.PROFESSIONAL_RESPONSIBILITY
            )


class TestAuditSession:
    """Test the audit session context manager."""

    @pytest.mark.asyncio
    async def test_audit_session_success(self):
        """Test successful audit session."""
        with patch.object(
            comprehensive_audit_service, "log_compliance_event", new_callable=AsyncMock
        ) as mock_log:
            async with audit_session(
                user_id="test-user", session_type="api_access"
            ) as session_id:
                assert session_id is not None

            # Should log session start and end
            assert mock_log.call_count == 2

            # Check session start call
            start_call = mock_log.call_args_list[0][1]
            assert start_call["event_type"] == ComplianceEventType.AUTHENTICATION_EVENT
            assert start_call["action"] == "session_start"

            # Check session end call
            end_call = mock_log.call_args_list[1][1]
            assert end_call["action"] == "session_end"
            assert end_call["outcome"] == "SUCCESS"

    @pytest.mark.asyncio
    async def test_audit_session_error(self):
        """Test audit session with error."""
        with patch.object(
            comprehensive_audit_service, "log_compliance_event", new_callable=AsyncMock
        ) as mock_log:
            with pytest.raises(ValueError):
                async with audit_session(user_id="test-user") as session_id:
                    raise ValueError("Test error")

            # Should log session start, error, and end
            assert mock_log.call_count == 3

            # Check error call
            error_call = mock_log.call_args_list[1][1]
            assert error_call["event_type"] == ComplianceEventType.SECURITY_VIOLATION
            assert error_call["action"] == "session_error"
            assert error_call["outcome"] == "FAILED"


class TestComplianceAuditMiddleware:
    """Test the compliance audit middleware."""

    @pytest.fixture
    def middleware(self):
        """Create middleware instance."""
        app = Mock()
        return ComplianceAuditMiddleware(app)

    def test_get_compliance_framework(self, middleware):
        """Test compliance framework detection from paths."""
        assert (
            middleware._get_compliance_framework("/api/consent/record")
            == ComplianceFramework.CONSENT_MANAGEMENT
        )
        assert (
            middleware._get_compliance_framework("/api/data-retention/policies")
            == ComplianceFramework.DATA_RETENTION
        )
        assert (
            middleware._get_compliance_framework("/api/auth/login")
            == ComplianceFramework.SECURITY
        )
        assert (
            middleware._get_compliance_framework("/api/regional-disclaimers/display")
            == ComplianceFramework.REGIONAL_DISCLAIMERS
        )
        assert middleware._get_compliance_framework("/api/unknown/path") is None

    def test_get_event_type_data_retention(self, middleware):
        """Test event type detection for data retention framework."""
        event_type = middleware._get_event_type(
            ComplianceFramework.DATA_RETENTION, "DELETE", "/api/data-retention/cleanup"
        )
        assert event_type == ComplianceEventType.DATA_CLEANUP_EXECUTED

        event_type = middleware._get_event_type(
            ComplianceFramework.DATA_RETENTION, "POST", "/api/data-retention/policy"
        )
        assert event_type == ComplianceEventType.RETENTION_POLICY_APPLIED

    def test_get_event_type_consent_management(self, middleware):
        """Test event type detection for consent management framework."""
        event_type = middleware._get_event_type(
            ComplianceFramework.CONSENT_MANAGEMENT, "DELETE", "/api/consent/withdraw"
        )
        assert event_type == ComplianceEventType.CONSENT_WITHDRAWN

        event_type = middleware._get_event_type(
            ComplianceFramework.CONSENT_MANAGEMENT, "POST", "/api/consent/grant"
        )
        assert event_type == ComplianceEventType.CONSENT_GRANTED

    def test_get_severity(self, middleware):
        """Test severity determination based on method and status code."""
        assert middleware._get_severity("GET", 500) == ComplianceSeverity.CRITICAL
        assert middleware._get_severity("POST", 401) == ComplianceSeverity.HIGH
        assert middleware._get_severity("PUT", 403) == ComplianceSeverity.HIGH
        assert middleware._get_severity("DELETE", 400) == ComplianceSeverity.MEDIUM
        assert middleware._get_severity("GET", 200) == ComplianceSeverity.INFO
        assert middleware._get_severity("DELETE", 200) == ComplianceSeverity.HIGH

    def test_hash_value(self, middleware):
        """Test value hashing for privacy protection."""
        value = "***********"
        hashed = middleware._hash_value(value)

        assert hashed != value
        assert len(hashed) == 64  # SHA256 hex length
        assert middleware._hash_value(value) == hashed  # Consistent hashing

    @pytest.mark.asyncio
    async def test_middleware_dispatch_compliance_path(self, middleware):
        """Test middleware dispatch for compliance-relevant paths."""
        # Mock request and response
        request = Mock()
        request.url.path = "/api/consent/record"
        request.method = "POST"
        request.headers = {"user-agent": "test-agent"}
        request.client.host = "***********"
        request.state = Mock()
        request.state.user = Mock()
        request.state.user.id = "test-user"

        response = Mock()
        response.status_code = 200

        # Mock call_next
        async def mock_call_next(req):
            return response

        with patch.object(
            middleware, "_log_request_event", new_callable=AsyncMock
        ) as mock_log:
            result = await middleware.dispatch(request, mock_call_next)

            assert result == response
            mock_log.assert_called_once()

    @pytest.mark.asyncio
    async def test_middleware_dispatch_security_event(self, middleware):
        """Test middleware dispatch for security events (failed requests)."""
        # Mock request and response
        request = Mock()
        request.url.path = "/api/auth/login"
        request.method = "POST"
        request.headers = {"user-agent": "test-agent"}
        request.client.host = "***********"
        request.state = Mock()

        response = Mock()
        response.status_code = 401

        # Mock call_next
        async def mock_call_next(req):
            return response

        with patch.object(
            middleware, "_log_security_event", new_callable=AsyncMock
        ) as mock_security_log:
            with patch.object(middleware, "_log_request_event", new_callable=AsyncMock):
                result = await middleware.dispatch(request, mock_call_next)

                assert result == response
                mock_security_log.assert_called_once()


class TestComplianceAuditEvent:
    """Test the ComplianceAuditEvent dataclass."""

    def test_compliance_audit_event_creation(self):
        """Test creating a compliance audit event."""
        event = ComplianceAuditEvent(
            event_id="test-id",
            event_type=ComplianceEventType.CONSENT_GRANTED,
            framework=ComplianceFramework.CONSENT_MANAGEMENT,
            severity=ComplianceSeverity.INFO,
            timestamp=datetime.now(),
            user_id="test-user",
            metadata={"test": "data"},
        )

        assert event.event_id == "test-id"
        assert event.event_type == ComplianceEventType.CONSENT_GRANTED
        assert event.framework == ComplianceFramework.CONSENT_MANAGEMENT
        assert event.user_id == "test-user"
        assert event.metadata == {"test": "data"}
        assert event.compliance_status == "COMPLIANT"
        assert event.requires_review is False

    def test_compliance_audit_event_post_init(self):
        """Test post-init processing of ComplianceAuditEvent."""
        # Test with None metadata
        event = ComplianceAuditEvent(
            event_id="test-id",
            event_type=ComplianceEventType.CONSENT_GRANTED,
            framework=ComplianceFramework.CONSENT_MANAGEMENT,
            severity=ComplianceSeverity.INFO,
            timestamp=datetime.now(),
            metadata=None,
        )

        assert event.metadata == {}

        # Test with string timestamp
        event = ComplianceAuditEvent(
            event_id="test-id",
            event_type=ComplianceEventType.CONSENT_GRANTED,
            framework=ComplianceFramework.CONSENT_MANAGEMENT,
            severity=ComplianceSeverity.INFO,
            timestamp="2024-01-15T10:30:00Z",
        )

        assert isinstance(event.timestamp, datetime)


@pytest.mark.integration
class TestComplianceAuditIntegration:
    """Integration tests for the compliance audit system."""

    @pytest.mark.asyncio
    async def test_end_to_end_audit_flow(self):
        """Test complete audit flow from event creation to database storage."""
        # This would require actual database connection in integration environment
        pass

    @pytest.mark.asyncio
    async def test_middleware_integration_with_api(self):
        """Test middleware integration with actual API endpoints."""
        # This would require actual FastAPI application in integration environment
        pass

    @pytest.mark.asyncio
    async def test_compliance_reporting_integration(self):
        """Test integration with compliance reporting systems."""
        # This would test actual reporting functionality
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
