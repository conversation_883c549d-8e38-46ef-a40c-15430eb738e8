"""
Tests for the deprecation middleware.

This module tests the deprecation middleware functionality including
header addition, logging, and metrics collection.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi import <PERSON><PERSON><PERSON>, Response
from fastapi.testclient import TestClient
from starlette.requests import Request

from backend.middleware.deprecation_middleware import (
    DeprecationMiddleware,
    DeprecationMetrics,
)


@pytest.fixture
def app():
    """Create a test FastAPI app with deprecation middleware."""
    app = FastAPI()

    # Add test routes
    @app.get("/api/cases")
    async def get_cases():
        return {"message": "deprecated cases endpoint"}

    @app.get("/api/matters")
    async def get_matters():
        return {"message": "new matters endpoint"}

    # Add deprecation middleware
    app.add_middleware(DeprecationMiddleware)

    return app


@pytest.fixture
def client(app):
    """Create a test client."""
    return TestClient(app)


class TestDeprecationMiddleware:
    """Test cases for the DeprecationMiddleware class."""

    def test_deprecated_endpoint_headers(self, client):
        """Test that deprecated endpoints get proper headers."""
        response = client.get("/api/cases")

        # Check response is successful
        assert response.status_code == 200

        # Check deprecation headers are present
        assert response.headers.get("X-Deprecated") == "true"
        assert "Case API is deprecated" in response.headers.get(
            "X-Deprecation-Warning", ""
        )
        assert response.headers.get("X-Replacement-Endpoint") == "/api/matters"
        assert response.headers.get("X-Sunset") == "2025-12-31"
        assert 'rel="successor-version"' in response.headers.get("Link", "")

    def test_non_deprecated_endpoint_no_headers(self, client):
        """Test that non-deprecated endpoints don't get deprecation headers."""
        response = client.get("/api/matters")

        # Check response is successful
        assert response.status_code == 200

        # Check no deprecation headers are present
        assert "X-Deprecated" not in response.headers
        assert "X-Deprecation-Warning" not in response.headers
        assert "X-Replacement-Endpoint" not in response.headers
        assert "X-Sunset" not in response.headers

    @patch("backend.middleware.deprecation_middleware.logger")
    def test_deprecated_endpoint_logging(self, mock_logger, client):
        """Test that deprecated endpoint usage is logged."""
        response = client.get("/api/cases")

        # Check response is successful
        assert response.status_code == 200

        # Check that warning was logged
        mock_logger.warning.assert_called_once()
        call_args = mock_logger.warning.call_args

        # Check log message
        assert "Deprecated API endpoint accessed" in call_args[0][0]

        # Check log extra data
        extra_data = call_args[1]["extra"]
        assert extra_data["event_type"] == "deprecated_api_usage"
        assert extra_data["endpoint"] == "/api/cases"
        assert extra_data["method"] == "GET"
        assert extra_data["replacement"] == "/api/matters"
        assert extra_data["sunset_date"] == "2025-12-31"

    def test_custom_deprecated_endpoints(self):
        """Test middleware with custom deprecated endpoints configuration."""
        custom_endpoints = {
            "/api/legacy": {
                "replacement": "/api/v2/new",
                "sunset_date": "2025-06-30",
                "message": "Legacy API is deprecated",
            }
        }

        app = FastAPI()

        @app.get("/api/legacy")
        async def legacy_endpoint():
            return {"message": "legacy"}

        app.add_middleware(DeprecationMiddleware, deprecated_endpoints=custom_endpoints)

        client = TestClient(app)
        response = client.get("/api/legacy")

        # Check custom deprecation headers
        assert response.headers.get("X-Deprecated") == "true"
        assert (
            response.headers.get("X-Deprecation-Warning") == "Legacy API is deprecated"
        )
        assert response.headers.get("X-Replacement-Endpoint") == "/api/v2/new"
        assert response.headers.get("X-Sunset") == "2025-06-30"


class TestDeprecationMetrics:
    """Test cases for the DeprecationMetrics class."""

    def test_record_usage(self):
        """Test recording usage of deprecated endpoints."""
        metrics = DeprecationMetrics()

        # Record usage
        metrics.record_usage("/api/cases")
        metrics.record_usage("/api/cases")
        metrics.record_usage("/api/service/cases")

        # Check usage counts
        assert metrics.usage_counts["/api/cases"] == 2
        assert metrics.usage_counts["/api/service/cases"] == 1

        # Check last accessed times are recorded
        assert "/api/cases" in metrics.last_accessed
        assert "/api/service/cases" in metrics.last_accessed

    def test_get_usage_stats(self):
        """Test getting usage statistics."""
        metrics = DeprecationMetrics()

        # Record some usage
        metrics.record_usage("/api/cases")
        metrics.record_usage("/api/service/cases")

        # Get stats
        stats = metrics.get_usage_stats()

        # Check stats structure
        assert "/api/cases" in stats
        assert "/api/service/cases" in stats

        # Check stats content
        cases_stats = stats["/api/cases"]
        assert cases_stats["usage_count"] == 1
        assert cases_stats["last_accessed"] is not None

        service_stats = stats["/api/service/cases"]
        assert service_stats["usage_count"] == 1
        assert service_stats["last_accessed"] is not None

    def test_get_endpoints_by_usage(self):
        """Test filtering endpoints by usage count."""
        metrics = DeprecationMetrics()

        # Record different usage amounts
        for _ in range(5):
            metrics.record_usage("/api/cases")

        for _ in range(2):
            metrics.record_usage("/api/service/cases")

        metrics.record_usage("/api/other")

        # Test different thresholds
        assert len(metrics.get_endpoints_by_usage(min_usage=1)) == 3
        assert len(metrics.get_endpoints_by_usage(min_usage=2)) == 2
        assert len(metrics.get_endpoints_by_usage(min_usage=5)) == 1
        assert len(metrics.get_endpoints_by_usage(min_usage=10)) == 0

        # Check specific endpoints
        high_usage = metrics.get_endpoints_by_usage(min_usage=5)
        assert "/api/cases" in high_usage

    def test_empty_metrics(self):
        """Test behavior with no recorded usage."""
        metrics = DeprecationMetrics()

        # Check empty stats
        stats = metrics.get_usage_stats()
        assert stats == {}

        # Check empty endpoint lists
        endpoints = metrics.get_endpoints_by_usage()
        assert endpoints == []


@pytest.mark.asyncio
class TestDeprecationMiddlewareIntegration:
    """Integration tests for deprecation middleware."""

    async def test_middleware_with_request_response_cycle(self):
        """Test complete request/response cycle with middleware."""
        # Create middleware instance
        middleware = DeprecationMiddleware(app=None)

        # Mock request and response
        request = MagicMock(spec=Request)
        request.url.path = "/api/cases"
        request.method = "GET"
        request.client.host = "127.0.0.1"
        request.headers = {"user-agent": "test-client"}
        request.query_params = {}

        response = Response(content="test", status_code=200)

        # Mock call_next
        async def mock_call_next(req):
            return response

        # Process request through middleware
        with patch("backend.middleware.deprecation_middleware.logger") as mock_logger:
            result = await middleware.dispatch(request, mock_call_next)

        # Check response has deprecation headers
        assert result.headers.get("X-Deprecated") == "true"
        assert "Case API is deprecated" in result.headers.get(
            "X-Deprecation-Warning", ""
        )

        # Check logging was called
        mock_logger.warning.assert_called_once()

    async def test_middleware_with_non_deprecated_endpoint(self):
        """Test middleware with non-deprecated endpoint."""
        middleware = DeprecationMiddleware(app=None)

        # Mock request for non-deprecated endpoint
        request = MagicMock(spec=Request)
        request.url.path = "/api/matters"

        response = Response(content="test", status_code=200)

        async def mock_call_next(req):
            return response

        # Process request through middleware
        with patch("backend.middleware.deprecation_middleware.logger") as mock_logger:
            result = await middleware.dispatch(request, mock_call_next)

        # Check no deprecation headers
        assert "X-Deprecated" not in result.headers

        # Check no logging
        mock_logger.warning.assert_not_called()
