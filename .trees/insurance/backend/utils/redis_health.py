"""
Standalone Redis Health Check Utility

This module provides Redis health checking functionality without any dependencies
on agent modules, ensuring it can be imported quickly for health checks.
"""

import logging
import os
import ssl
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class RedisHealthChecker:
    """Standalone Redis health checker for application monitoring."""

    def __init__(self):
        """Initialize the Redis health checker."""
        self._redis_client: Optional[any] = None
        self._initialized = False

    async def initialize(self):
        """Initialize Redis client for health checks."""
        if self._initialized:
            return

        try:
            # Import Redis client
            import redis.asyncio as redis

            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")

            if not redis_url:
                logger.warning("No REDIS_URL configured")
                self._initialized = True
                return

            # Parse Redis URL and configure SSL if needed
            if redis_url.startswith("rediss://"):
                # Redis Cloud SSL connection with proper TLS configuration
                ssl_context = ssl.create_default_context()
                ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
                ssl_context.check_hostname = True
                ssl_context.verify_mode = ssl.CERT_REQUIRED
                ssl_context.set_ciphers("HIGH:!aNULL:!MD5")

                self._redis_client = redis.from_url(
                    redis_url,
                    ssl_context=ssl_context,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                )
                logger.info("Redis SSL client initialized for health checks")
            else:
                # Local Redis connection
                self._redis_client = redis.from_url(
                    redis_url,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                )
                logger.info("Redis client initialized for health checks")

            self._initialized = True

        except Exception as e:
            logger.error(f"Failed to initialize Redis client: {e}")
            self._redis_client = None
            self._initialized = True

    async def ping(self) -> bool:
        """Test Redis connection with ping."""
        await self.initialize()

        if not self._redis_client:
            return False

        try:
            result = await self._redis_client.ping()
            return result is True
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
            return False

    async def get_info(self) -> Dict[str, Any]:
        """Get Redis server information."""
        await self.initialize()

        if not self._redis_client:
            return {}

        try:
            info = await self._redis_client.info()
            return {
                "version": info.get("redis_version", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
            }
        except Exception as e:
            logger.error(f"Failed to get Redis info: {e}")
            return {}

    async def get_status(self) -> Dict[str, Any]:
        """Get comprehensive Redis status for health checks."""
        await self.initialize()

        if not self._redis_client:
            return {
                "status": "not_initialized",
                "error": "Redis client not initialized",
                "ping": False,
                "info": {},
            }

        try:
            # Test ping
            ping_success = await self.ping()

            # Get server info
            info = await self.get_info() if ping_success else {}

            return {
                "status": "connected" if ping_success else "ping_failed",
                "ping": ping_success,
                "info": info,
                "redis_url_configured": bool(os.getenv("REDIS_URL")),
            }

        except Exception as e:
            logger.error(f"Redis status check failed: {e}")
            return {"status": "error", "error": str(e), "ping": False, "info": {}}

    async def close(self):
        """Close Redis connection."""
        if self._redis_client:
            try:
                await self._redis_client.close()
                logger.info("Redis connection closed")
            except Exception as e:
                logger.error(f"Error closing Redis connection: {e}")
            finally:
                self._redis_client = None
                self._initialized = False


# Global instance for reuse
_redis_health_checker: Optional[RedisHealthChecker] = None


async def get_redis_status() -> Dict[str, Any]:
    """Get Redis status using global health checker instance."""
    global _redis_health_checker

    if _redis_health_checker is None:
        _redis_health_checker = RedisHealthChecker()

    return await _redis_health_checker.get_status()


async def test_redis_ping() -> bool:
    """Simple Redis ping test."""
    global _redis_health_checker

    if _redis_health_checker is None:
        _redis_health_checker = RedisHealthChecker()

    return await _redis_health_checker.ping()
