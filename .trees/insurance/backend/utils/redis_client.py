"""
Redis Client Utility

Provides a shared Redis client instance with proper SSL configuration
for use across the backend application.
"""

import os
import ssl
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# Global Redis client instance
_redis_client: Optional[any] = None


async def get_redis_client():
    """
    Get or create a Redis client instance with proper SSL configuration.

    Returns:
        Redis client instance
    """
    global _redis_client

    if _redis_client is not None:
        return _redis_client

    try:
        # Import Redis client
        import redis.asyncio as redis

        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")

        if not redis_url:
            raise ValueError("No REDIS_URL configured")

        # Parse Redis URL and configure SSL if needed
        if redis_url.startswith("rediss://"):
            # Redis Cloud SSL connection with proper TLS configuration
            ssl_context = ssl.create_default_context()
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
            ssl_context.check_hostname = True
            ssl_context.verify_mode = ssl.CERT_REQUIRED
            ssl_context.set_ciphers("HIGH:!aNULL:!MD5")

            _redis_client = redis.from_url(
                redis_url,
                ssl_context=ssl_context,
                socket_connect_timeout=10,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30,
                retry_on_timeout=True,
                decode_responses=True,
            )

            logger.info("Redis client initialized with SSL")
        else:
            # Non-SSL Redis connection
            _redis_client = redis.from_url(
                redis_url,
                socket_connect_timeout=10,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30,
                retry_on_timeout=True,
                decode_responses=True,
            )

            logger.info("Redis client initialized without SSL")

        # Test the connection
        await _redis_client.ping()
        logger.info("Redis connection test successful")

        return _redis_client

    except Exception as e:
        logger.error(f"Failed to initialize Redis client: {e}")
        _redis_client = None
        raise


async def close_redis_client():
    """Close the Redis client connection."""
    global _redis_client

    if _redis_client is not None:
        try:
            await _redis_client.close()
            logger.info("Redis client connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis client: {e}")
        finally:
            _redis_client = None


async def test_redis_connection() -> bool:
    """
    Test Redis connection.

    Returns:
        True if connection is successful, False otherwise
    """
    try:
        redis_client = await get_redis_client()
        result = await redis_client.ping()
        return result == True
    except Exception as e:
        logger.error(f"Redis connection test failed: {e}")
        return False
