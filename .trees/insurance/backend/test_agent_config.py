#!/usr/bin/env python3
"""
Test script for the centralized agent configuration system.

This script tests the core functionality of the agent configuration system
including registry, loading, validation, and environment management.
"""

import sys
import os
import traceback
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))


def test_imports():
    """Test that all modules can be imported successfully."""
    print("🧪 Testing imports...")

    try:
        from agents.config.models import (
            AgentConfigModel,
            NodeConfigModel,
            PromptConfigModel,
            ModelConfigModel,
            AgentType,
            NodeType,
        )

        print("✅ Models imported successfully")

        from agents.config.loader import ConfigLoader, load_agent_configs

        print("✅ Loader imported successfully")

        from agents.config.validator import ConfigValidator, validate_agent_config

        print("✅ Validator imported successfully")

        from agents.config.environments import EnvironmentConfig, get_environment_config

        print("✅ Environment config imported successfully")

        from agents.config.testing import AgentTester, TestSuite, EvaluationFramework

        print("✅ Testing framework imported successfully")

        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False


def test_model_creation():
    """Test creating configuration models."""
    print("\n🧪 Testing model creation...")

    try:
        from agents.config.models import (
            AgentConfigModel,
            NodeConfigModel,
            PromptConfigModel,
            ModelConfigModel,
            AgentType,
            NodeType,
        )

        # Test model config
        model_config = ModelConfigModel(
            provider="openai", model_name="openai/gpt-4", temperature=0.3
        )
        print("✅ ModelConfigModel created successfully")

        # Test prompt config
        prompt_config = PromptConfigModel(
            key="test_prompt",
            content="You are a test assistant.",
            description="Test prompt",
            version=1,
        )
        print("✅ PromptConfigModel created successfully")

        # Test node config
        node_config = NodeConfigModel(
            name="test_node",
            display_name="Test Node",
            description="A test node",
            node_type=NodeType.PROCESSOR,
            llm_config=model_config,
            prompt_config=prompt_config,
        )
        print("✅ NodeConfigModel created successfully")

        # Test agent config
        agent_config = AgentConfigModel(
            name="test_agent",
            display_name="Test Agent",
            description="A test agent for validation",
            agent_type=AgentType.INTERACTIVE,
            llm_config=model_config,
            prompt_config=prompt_config,
            nodes={"test_node": node_config},
            capabilities=["testing", "validation"],
        )
        print("✅ AgentConfigModel created successfully")

        return True
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        traceback.print_exc()
        return False


def test_config_loader():
    """Test configuration loading functionality."""
    print("\n🧪 Testing configuration loader...")

    try:
        from agents.config.loader import ConfigLoader

        loader = ConfigLoader()
        print("✅ ConfigLoader initialized successfully")

        # Test loading all configs
        configs = loader.load_all_configs()
        print(f"✅ Loaded {len(configs)} agent configurations")

        # Print loaded configs
        for name, config in configs.items():
            print(f"   - {name}: {config.display_name} ({config.agent_type})")

        return True
    except Exception as e:
        print(f"❌ Config loader error: {e}")
        traceback.print_exc()
        return False


def test_config_validator():
    """Test configuration validation."""
    print("\n🧪 Testing configuration validator...")

    try:
        from agents.config.validator import ConfigValidator
        from agents.config.models import AgentConfigModel, ModelConfigModel, AgentType

        validator = ConfigValidator()
        print("✅ ConfigValidator initialized successfully")

        # Test valid configuration
        valid_config = AgentConfigModel(
            name="valid_test_agent",
            display_name="Valid Test Agent",
            description="A valid test agent configuration",
            agent_type=AgentType.INTERACTIVE,
            llm_config=ModelConfigModel(
                provider="openai", model_name="openai/gpt-4", temperature=0.3
            ),
            capabilities=["testing"],
        )

        result = validator.validate_config(valid_config)
        if result.is_valid:
            print("✅ Valid configuration passed validation")
        else:
            print(f"❌ Valid configuration failed: {result.errors}")

        # Test invalid configuration (this should fail at Pydantic level)
        try:
            invalid_config = AgentConfigModel(
                name="invalid-name-with-dashes",  # Invalid name format
                display_name="Invalid Test Agent",
                description="",  # Empty description
                agent_type=AgentType.INTERACTIVE,
                llm_config=ModelConfigModel(
                    provider="invalid_provider",  # Invalid provider
                    model_name="invalid_format",  # Invalid model name format
                    temperature=3.0,  # Invalid temperature
                ),
            )
            print("❌ Invalid configuration should have failed at Pydantic level")
        except Exception as e:
            print("✅ Invalid configuration correctly rejected by Pydantic validation")
            print(f"   Validation errors detected: {type(e).__name__}")

        # Test configuration with valid Pydantic but invalid business logic
        semi_invalid_config = AgentConfigModel(
            name="invalid-name-with-dashes",  # Invalid name format (business logic)
            display_name="Semi Invalid Test Agent",
            description="",  # Empty description (business logic)
            agent_type=AgentType.INTERACTIVE,
            llm_config=ModelConfigModel(
                provider="openai",  # Valid provider
                model_name="openai/gpt-4",  # Valid model name format
                temperature=1.8,  # High but valid temperature
            ),
        )

        result = validator.validate_config(semi_invalid_config)
        if not result.is_valid:
            print("✅ Semi-invalid configuration correctly failed business validation")
            print(f"   Errors: {len(result.errors)}, Warnings: {len(result.warnings)}")
        else:
            print("❌ Semi-invalid configuration incorrectly passed validation")

        return True
    except Exception as e:
        print(f"❌ Config validator error: {e}")
        traceback.print_exc()
        return False


def test_environment_config():
    """Test environment configuration."""
    print("\n🧪 Testing environment configuration...")

    try:
        from agents.config.environments import EnvironmentConfig, get_environment_config

        env_config = EnvironmentConfig()
        print("✅ EnvironmentConfig initialized successfully")

        current_env = env_config.get_current_environment()
        print(f"✅ Current environment: {current_env}")

        env_settings = env_config.get_environment_config()
        print(f"✅ Environment settings loaded: {env_settings.name}")

        # Test feature flags
        debug_mode = env_config.get_feature_flag("enable_debug_mode")
        print(f"✅ Debug mode feature flag: {debug_mode}")

        return True
    except Exception as e:
        print(f"❌ Environment config error: {e}")
        traceback.print_exc()
        return False


def test_testing_framework():
    """Test the testing framework."""
    print("\n🧪 Testing testing framework...")

    try:
        from agents.config.testing import AgentTester, TestSuite, EvaluationFramework
        from agents.config.models import TestCaseModel

        # Test AgentTester
        tester = AgentTester()
        print("✅ AgentTester initialized successfully")

        # Test TestSuite
        suite = TestSuite("test_suite", "A test suite for validation")
        print("✅ TestSuite created successfully")

        # Test EvaluationFramework
        framework = EvaluationFramework()
        print("✅ EvaluationFramework initialized successfully")

        # Create a test case
        test_case = TestCaseModel(
            id="test_case_1",
            name="Basic Test",
            description="A basic test case",
            agent_name="test_agent",
            input_data={"message": "Hello"},
            success_criteria=["Agent responds"],
            timeout_seconds=30,
        )

        suite.add_test_case(test_case)
        print("✅ Test case added to suite successfully")

        return True
    except Exception as e:
        print(f"❌ Testing framework error: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Agent Configuration System Tests\n")

    tests = [
        test_imports,
        test_model_creation,
        test_config_loader,
        test_config_validator,
        test_environment_config,
        test_testing_framework,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1

    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed / (passed + failed)) * 100:.1f}%")

    if failed == 0:
        print(
            "\n🎉 All tests passed! The agent configuration system is working correctly."
        )
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
