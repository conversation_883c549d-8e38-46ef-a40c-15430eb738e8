"""
Regional Legal Disclaimer Service

This service manages regional legal disclaimers that integrate with the data residency
system to ensure appropriate disclaimers are shown based on user location and jurisdiction.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from backend.models.legal_disclaimer import (
    LegalDisclaimer,
    LegalDisclaimerCreate,
    DisclaimerType,
    DisclaimerRegion,
    DisclaimerPlacement,
    DisclaimerDisplayContext,
    DisclaimerResponse,
    RegionalDisclaimerSet,
    DEFAULT_US_DISCLAIMERS,
    DEFAULT_EU_DISCLAIMERS,
)
from backend.services.data_residency import DataRegion
from backend.services.compliance_audit import log_data_residency_event

logger = logging.getLogger(__name__)


class RegionalDisclaimerService:
    """Service for managing regional legal disclaimers."""

    def __init__(self):
        self.disclaimer_cache: Dict[str, List[LegalDisclaimer]] = {}
        self.cache_ttl = 3600  # 1 hour cache TTL
        self.last_cache_update: Dict[str, datetime] = {}

    async def get_disclaimers_for_region(
        self, context: DisclaimerDisplayContext, user_id: Optional[UUID] = None
    ) -> DisclaimerResponse:
        """
        Get appropriate disclaimers for a user's region and context.

        Args:
            context: Display context including region, placement, etc.
            user_id: User ID for audit logging

        Returns:
            DisclaimerResponse with applicable disclaimers
        """
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(context)

            # Check cache first
            cached_disclaimers = self._get_from_cache(cache_key)
            if cached_disclaimers:
                logger.info(
                    f"Retrieved {len(cached_disclaimers)} disclaimers from cache",
                    extra={"cache_key": cache_key, "region": context.user_region},
                )
                return DisclaimerResponse(
                    disclaimers=cached_disclaimers,
                    total_count=len(cached_disclaimers),
                    context=context,
                    cache_key=cache_key,
                    last_updated=self.last_cache_update.get(
                        cache_key, datetime.utcnow()
                    ),
                )

            # Get disclaimers from database/defaults
            disclaimers = await self._fetch_disclaimers(context)

            # Filter and sort disclaimers
            filtered_disclaimers = self._filter_disclaimers(disclaimers, context)
            sorted_disclaimers = self._sort_disclaimers(filtered_disclaimers)

            # Cache the results
            self._update_cache(cache_key, sorted_disclaimers)

            # Log disclaimer access for compliance
            await self._log_disclaimer_access(context, sorted_disclaimers, user_id)

            logger.info(
                f"Retrieved {len(sorted_disclaimers)} disclaimers for region {context.user_region}",
                extra={
                    "region": context.user_region,
                    "placement": context.placement,
                    "disclaimer_count": len(sorted_disclaimers),
                },
            )

            return DisclaimerResponse(
                disclaimers=sorted_disclaimers,
                total_count=len(sorted_disclaimers),
                context=context,
                cache_key=cache_key,
                last_updated=datetime.utcnow(),
            )

        except Exception as e:
            logger.error(
                f"Failed to get disclaimers for region {context.user_region}",
                extra={"error": str(e), "context": context.dict()},
                exc_info=True,
            )
            # Return default disclaimers as fallback
            return await self._get_fallback_disclaimers(context)

    async def get_regional_disclaimer_set(
        self, region: DisclaimerRegion
    ) -> RegionalDisclaimerSet:
        """
        Get complete disclaimer set for a region.

        Args:
            region: Target region

        Returns:
            Complete set of disclaimers for the region
        """
        try:
            # Get all disclaimers for the region
            context = DisclaimerDisplayContext(
                user_region=region,
                placement=DisclaimerPlacement.TERMS_PAGE,  # Get all disclaimers
            )

            disclaimers = await self._fetch_disclaimers(context)
            region_disclaimers = [
                d for d in disclaimers if d.region in [region, DisclaimerRegion.GLOBAL]
            ]

            # Determine compliance frameworks
            compliance_frameworks = self._get_compliance_frameworks(region)

            return RegionalDisclaimerSet(
                region=region,
                disclaimers=region_disclaimers,
                version=f"{datetime.utcnow().year}.{datetime.utcnow().month}",
                compliance_frameworks=compliance_frameworks,
                last_updated=datetime.utcnow(),
            )

        except Exception as e:
            logger.error(
                f"Failed to get disclaimer set for region {region}", exc_info=True
            )
            raise

    async def create_disclaimer(
        self, disclaimer_data: LegalDisclaimerCreate, created_by: UUID
    ) -> LegalDisclaimer:
        """
        Create a new legal disclaimer.

        Args:
            disclaimer_data: Disclaimer data
            created_by: User creating the disclaimer

        Returns:
            Created disclaimer
        """
        try:
            # Create disclaimer object
            disclaimer = LegalDisclaimer(
                **disclaimer_data.model_dump(),
                created_by=created_by,
                updated_by=created_by,
            )

            # In a real implementation, this would save to database
            # For now, we'll just log the creation
            logger.info(
                f"Created disclaimer: {disclaimer.title}",
                extra={
                    "disclaimer_id": str(disclaimer.id),
                    "region": disclaimer.region,
                    "type": disclaimer.disclaimer_type,
                    "created_by": str(created_by),
                },
            )

            # Clear cache for affected regions
            self._clear_cache_for_region(disclaimer.region)

            return disclaimer

        except Exception as e:
            logger.error(f"Failed to create disclaimer", exc_info=True)
            raise

    def map_data_region_to_disclaimer_region(
        self, data_region: DataRegion
    ) -> DisclaimerRegion:
        """
        Map data residency region to disclaimer region.

        Args:
            data_region: Data residency region

        Returns:
            Corresponding disclaimer region
        """
        mapping = {
            DataRegion.US: DisclaimerRegion.US,
            DataRegion.EU: DisclaimerRegion.EU,
        }
        return mapping.get(data_region, DisclaimerRegion.US)  # Default to US

    async def _fetch_disclaimers(
        self, context: DisclaimerDisplayContext
    ) -> List[LegalDisclaimer]:
        """Fetch disclaimers from database or use defaults."""
        # In a real implementation, this would query the database
        # For now, use default disclaimers

        default_disclaimers = []

        if context.user_region in [DisclaimerRegion.US, DisclaimerRegion.GLOBAL]:
            default_disclaimers.extend(
                [
                    LegalDisclaimer(**disclaimer_data)
                    for disclaimer_data in DEFAULT_US_DISCLAIMERS
                ]
            )

        if context.user_region in [DisclaimerRegion.EU, DisclaimerRegion.GLOBAL]:
            default_disclaimers.extend(
                [
                    LegalDisclaimer(**disclaimer_data)
                    for disclaimer_data in DEFAULT_EU_DISCLAIMERS
                ]
            )

        return default_disclaimers

    def _filter_disclaimers(
        self, disclaimers: List[LegalDisclaimer], context: DisclaimerDisplayContext
    ) -> List[LegalDisclaimer]:
        """Filter disclaimers based on context."""
        filtered = []

        for disclaimer in disclaimers:
            # Check region match
            if disclaimer.region not in [context.user_region, DisclaimerRegion.GLOBAL]:
                continue

            # Check placement match
            if context.placement not in disclaimer.placement:
                continue

            # Check if disclaimer is currently effective
            now = datetime.utcnow()
            if disclaimer.effective_date > now:
                continue

            if disclaimer.expiry_date and disclaimer.expiry_date < now:
                continue

            # Check practice area if specified
            if context.practice_area:
                practice_areas = disclaimer.metadata.get("practice_areas", [])
                if practice_areas and context.practice_area not in practice_areas:
                    continue

            filtered.append(disclaimer)

        return filtered

    def _sort_disclaimers(
        self, disclaimers: List[LegalDisclaimer]
    ) -> List[LegalDisclaimer]:
        """Sort disclaimers by priority and type."""
        return sorted(disclaimers, key=lambda d: (d.priority, d.disclaimer_type.value))

    def _generate_cache_key(self, context: DisclaimerDisplayContext) -> str:
        """Generate cache key for context."""
        return f"disclaimers:{context.user_region.value}:{context.placement.value}:{context.practice_area or 'all'}"

    def _get_from_cache(self, cache_key: str) -> Optional[List[LegalDisclaimer]]:
        """Get disclaimers from cache if not expired."""
        if cache_key not in self.disclaimer_cache:
            return None

        last_update = self.last_cache_update.get(cache_key)
        if not last_update:
            return None

        # Check if cache is expired
        if (datetime.utcnow() - last_update).total_seconds() > self.cache_ttl:
            del self.disclaimer_cache[cache_key]
            del self.last_cache_update[cache_key]
            return None

        return self.disclaimer_cache[cache_key]

    def _update_cache(self, cache_key: str, disclaimers: List[LegalDisclaimer]) -> None:
        """Update cache with disclaimers."""
        self.disclaimer_cache[cache_key] = disclaimers
        self.last_cache_update[cache_key] = datetime.utcnow()

    def _clear_cache_for_region(self, region: DisclaimerRegion) -> None:
        """Clear cache for a specific region."""
        keys_to_remove = [
            key
            for key in self.disclaimer_cache.keys()
            if key.startswith(f"disclaimers:{region}:")
        ]

        for key in keys_to_remove:
            del self.disclaimer_cache[key]
            if key in self.last_cache_update:
                del self.last_cache_update[key]

    def _get_compliance_frameworks(self, region: DisclaimerRegion) -> List[str]:
        """Get applicable compliance frameworks for region."""
        frameworks = {
            DisclaimerRegion.US: [
                "ABA Model Rules of Professional Conduct",
                "State Bar Disciplinary Rules",
                "Attorney Advertising Regulations",
            ],
            DisclaimerRegion.EU: [
                "EU Legal Services Directive",
                "GDPR Data Protection Requirements",
                "National Bar Association Rules",
            ],
        }
        return frameworks.get(region, [])

    async def _log_disclaimer_access(
        self,
        context: DisclaimerDisplayContext,
        disclaimers: List[LegalDisclaimer],
        user_id: Optional[UUID],
    ) -> None:
        """Log disclaimer access for compliance audit."""
        try:
            await log_data_residency_event(
                event_type="disclaimer_access",
                user_id=str(user_id) if user_id else None,
                region=context.user_region.value,
                metadata={
                    "placement": context.placement.value,
                    "disclaimer_count": len(disclaimers),
                    "disclaimer_types": [d.disclaimer_type.value for d in disclaimers],
                    "practice_area": context.practice_area,
                    "jurisdiction": context.jurisdiction,
                },
            )
        except Exception as e:
            logger.warning(f"Failed to log disclaimer access: {str(e)}")

    async def _get_fallback_disclaimers(
        self, context: DisclaimerDisplayContext
    ) -> DisclaimerResponse:
        """Get fallback disclaimers when main service fails."""
        # Return minimal required disclaimers
        fallback_disclaimer = LegalDisclaimer(
            title="Legal Notice",
            content="This website provides general information only and does not constitute legal advice. Consult with a qualified attorney for legal advice specific to your situation.",
            disclaimer_type=DisclaimerType.NO_LEGAL_ADVICE,
            region=DisclaimerRegion.GLOBAL,
            placement=[context.placement],
            priority=1,
        )

        return DisclaimerResponse(
            disclaimers=[fallback_disclaimer],
            total_count=1,
            context=context,
            last_updated=datetime.utcnow(),
        )


# Global service instance
regional_disclaimer_service = RegionalDisclaimerService()
