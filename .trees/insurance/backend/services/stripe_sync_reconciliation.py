"""
Stripe Sync Reconciliation Service

Automated reconciliation service for fixing data consistency issues
between local database and Stripe based on validation results.
"""

import asyncio
import logging
import stripe
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import os
from backend.db.session import get_db_session
from backend.models.subscription import SubscriptionPlan, SubscriptionAddon
from backend.models.firm import Firm
from backend.models.subscription import TenantSubscription
from backend.services.stripe_sync_validator import (
    SyncDiscrepancy,
    DiscrepancyType,
    SeverityLevel,
)
from backend.utils.logging import get_logger

# Configure Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

logger = get_logger(__name__)


class ReconciliationAction(Enum):
    """Types of reconciliation actions."""

    UPDATE_LOCAL = "update_local"
    UPDATE_STRIPE = "update_stripe"
    CREATE_LOCAL = "create_local"
    CREATE_STRIPE = "create_stripe"
    DELETE_LOCAL = "delete_local"
    DELETE_STRIPE = "delete_stripe"
    MANUAL_REVIEW = "manual_review"


class ReconciliationStrategy(Enum):
    """Simplified reconciliation strategies - reduced from 4 to 2 essential strategies."""

    STRIPE_AUTHORITATIVE = "stripe_authoritative"  # Stripe is source of truth (default)
    MANUAL_REVIEW = "manual_review"  # Manual review for critical discrepancies


@dataclass
class ReconciliationResult:
    """Result of a reconciliation operation."""

    discrepancy_id: str
    action_taken: ReconciliationAction
    success: bool
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class ReconciliationSummary:
    """Summary of reconciliation operations."""

    total_discrepancies: int
    successful_reconciliations: int
    failed_reconciliations: int
    manual_review_required: int
    results: List[ReconciliationResult]
    duration_seconds: float
    timestamp: datetime


class StripeSyncReconciliation:
    """
    Automated reconciliation service for Stripe sync discrepancies.

    Provides intelligent reconciliation strategies to automatically fix
    data consistency issues between local database and Stripe.
    """

    def __init__(
        self,
        strategy: ReconciliationStrategy = ReconciliationStrategy.STRIPE_AUTHORITATIVE,
    ):
        self.logger = logger
        self.strategy = strategy
        self.results: List[ReconciliationResult] = []

    async def reconcile_discrepancies(
        self,
        discrepancies: List[SyncDiscrepancy],
        strategy: Optional[ReconciliationStrategy] = None,
    ) -> ReconciliationSummary:
        """
        Reconcile a list of sync discrepancies.

        Args:
            discrepancies: List of discrepancies to reconcile
            strategy: Optional strategy override

        Returns:
            ReconciliationSummary with results
        """
        start_time = datetime.utcnow()
        self.results = []

        if strategy:
            self.strategy = strategy

        self.logger.info(
            f"Starting reconciliation of {len(discrepancies)} discrepancies using {self.strategy.value} strategy"
        )

        try:
            for discrepancy in discrepancies:
                result = await self._reconcile_single_discrepancy(discrepancy)
                self.results.append(result)

                # Add small delay to avoid rate limiting
                await asyncio.sleep(0.1)

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Generate summary
            successful = len([r for r in self.results if r.success])
            failed = len([r for r in self.results if not r.success])
            manual_review = len(
                [
                    r
                    for r in self.results
                    if r.action_taken == ReconciliationAction.MANUAL_REVIEW
                ]
            )

            summary = ReconciliationSummary(
                total_discrepancies=len(discrepancies),
                successful_reconciliations=successful,
                failed_reconciliations=failed,
                manual_review_required=manual_review,
                results=self.results,
                duration_seconds=duration,
                timestamp=end_time,
            )

            self.logger.info(
                f"Reconciliation completed: {successful} successful, {failed} failed, {manual_review} require manual review"
            )

            return summary

        except Exception as e:
            self.logger.error(f"Reconciliation failed: {e}", exc_info=True)
            raise

    async def _reconcile_single_discrepancy(
        self, discrepancy: SyncDiscrepancy
    ) -> ReconciliationResult:
        """
        Reconcile a single discrepancy.

        Args:
            discrepancy: The discrepancy to reconcile

        Returns:
            ReconciliationResult
        """
        try:
            # Determine reconciliation action based on strategy and discrepancy
            action = self._determine_reconciliation_action(discrepancy)

            if action == ReconciliationAction.MANUAL_REVIEW:
                return ReconciliationResult(
                    discrepancy_id=discrepancy.entity_id,
                    action_taken=action,
                    success=True,
                    details={
                        "reason": "Requires manual review due to complexity or risk"
                    },
                )

            # Execute the reconciliation action
            success, error_message, details = await self._execute_reconciliation_action(
                discrepancy, action
            )

            return ReconciliationResult(
                discrepancy_id=discrepancy.entity_id,
                action_taken=action,
                success=success,
                error_message=error_message,
                details=details,
            )

        except Exception as e:
            self.logger.error(
                f"Failed to reconcile discrepancy {discrepancy.entity_id}: {e}"
            )
            return ReconciliationResult(
                discrepancy_id=discrepancy.entity_id,
                action_taken=ReconciliationAction.MANUAL_REVIEW,
                success=False,
                error_message=str(e),
            )

    def _determine_reconciliation_action(
        self, discrepancy: SyncDiscrepancy
    ) -> ReconciliationAction:
        """
        Simplified reconciliation action determination.

        Strategy: Stripe is the authoritative source for billing data.
        Critical discrepancies always require manual review.

        Args:
            discrepancy: The discrepancy to analyze

        Returns:
            ReconciliationAction to take
        """
        # Critical discrepancies always require manual review
        if discrepancy.severity == SeverityLevel.CRITICAL:
            return ReconciliationAction.MANUAL_REVIEW

        # Price mismatches are always critical - require manual review
        if discrepancy.type == DiscrepancyType.PRICE_MISMATCH:
            return ReconciliationAction.MANUAL_REVIEW

        # Manual review strategy - no automatic reconciliation
        if self.strategy == ReconciliationStrategy.MANUAL_REVIEW:
            return ReconciliationAction.MANUAL_REVIEW

        # Stripe Authoritative strategy - Stripe data takes precedence
        if self.strategy == ReconciliationStrategy.STRIPE_AUTHORITATIVE:
            if discrepancy.type == DiscrepancyType.MISSING_LOCAL:
                return ReconciliationAction.CREATE_LOCAL
            elif discrepancy.type == DiscrepancyType.MISSING_STRIPE:
                # If missing in Stripe but exists locally, likely a sync issue - create in Stripe
                return ReconciliationAction.CREATE_STRIPE
            elif discrepancy.type in [
                DiscrepancyType.DATA_MISMATCH,
                DiscrepancyType.METADATA_MISMATCH,
                DiscrepancyType.STATUS_MISMATCH,
            ]:
                # Update local data to match Stripe (Stripe is authoritative)
                return ReconciliationAction.UPDATE_LOCAL

        # Default to manual review for safety
        return ReconciliationAction.MANUAL_REVIEW

    # Removed complex smart merge logic - simplified to Stripe authoritative approach

    async def _execute_reconciliation_action(
        self, discrepancy: SyncDiscrepancy, action: ReconciliationAction
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        Execute a reconciliation action.

        Args:
            discrepancy: The discrepancy to reconcile
            action: The action to execute

        Returns:
            Tuple of (success, error_message, details)
        """
        try:
            if action == ReconciliationAction.UPDATE_LOCAL:
                return await self._update_local_data(discrepancy)
            elif action == ReconciliationAction.UPDATE_STRIPE:
                return await self._update_stripe_data(discrepancy)
            elif action == ReconciliationAction.CREATE_LOCAL:
                return await self._create_local_record(discrepancy)
            elif action == ReconciliationAction.CREATE_STRIPE:
                return await self._create_stripe_record(discrepancy)
            elif action == ReconciliationAction.DELETE_LOCAL:
                return await self._delete_local_record(discrepancy)
            elif action == ReconciliationAction.DELETE_STRIPE:
                return await self._delete_stripe_record(discrepancy)
            else:
                return True, None, {"action": "manual_review_required"}

        except Exception as e:
            return False, str(e), None

    async def _update_local_data(
        self, discrepancy: SyncDiscrepancy
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Update local database with Stripe data."""
        # TODO: Implement local data updates based on entity type
        self.logger.info(
            f"Would update local {discrepancy.entity_type} {discrepancy.entity_id}"
        )
        return True, None, {"action": "local_update_simulated"}

    async def _update_stripe_data(
        self, discrepancy: SyncDiscrepancy
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Update Stripe with local database data."""
        # TODO: Implement Stripe data updates based on entity type
        self.logger.info(
            f"Would update Stripe {discrepancy.entity_type} {discrepancy.entity_id}"
        )
        return True, None, {"action": "stripe_update_simulated"}

    async def _create_local_record(
        self, discrepancy: SyncDiscrepancy
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Create local database record from Stripe data."""
        # TODO: Implement local record creation based on entity type
        self.logger.info(
            f"Would create local {discrepancy.entity_type} from Stripe {discrepancy.entity_id}"
        )
        return True, None, {"action": "local_creation_simulated"}

    async def _create_stripe_record(
        self, discrepancy: SyncDiscrepancy
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Create Stripe record from local database data."""
        # TODO: Implement Stripe record creation based on entity type
        self.logger.info(
            f"Would create Stripe {discrepancy.entity_type} from local data"
        )
        return True, None, {"action": "stripe_creation_simulated"}

    async def _delete_local_record(
        self, discrepancy: SyncDiscrepancy
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Delete local database record."""
        # TODO: Implement local record deletion (with safety checks)
        self.logger.info(
            f"Would delete local {discrepancy.entity_type} {discrepancy.entity_id}"
        )
        return True, None, {"action": "local_deletion_simulated"}

    async def _delete_stripe_record(
        self, discrepancy: SyncDiscrepancy
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Delete Stripe record."""
        # TODO: Implement Stripe record deletion (with safety checks)
        self.logger.info(
            f"Would delete Stripe {discrepancy.entity_type} {discrepancy.entity_id}"
        )
        return True, None, {"action": "stripe_deletion_simulated"}


# Global instance
stripe_sync_reconciliation = StripeSyncReconciliation()
