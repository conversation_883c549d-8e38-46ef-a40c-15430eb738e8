"""
Voice Receptionist webhook service.

This module provides functionality for sending webhooks to the Voice Receptionist
system when calendar events are created, updated, or deleted with source=ivr.
"""

import hashlib
import hmac
import json
import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

import httpx
from pydantic import ValidationError

from backend.config import settings
from backend.db.supabase_client import get_supabase_client_async
import inspect

from backend.models.voice_receptionist import (
    BookingCanceledPayload,
    BookingConfirmedPayload,
)

# Configure logging
logger = logging.getLogger(__name__)

# Constants
MAX_RETRY_ATTEMPTS = 10
INITIAL_RETRY_DELAY = 30  # seconds
MAX_RETRY_DELAY = 3600  # 1 hour
JITTER_FACTOR = 0.2  # 20% jitter

# Voice Receptionist API configuration
VOICE_RECEPTIONIST_API_URL = settings.voice_receptionist_api_url
VOICE_RECEPTIONIST_API_KEY = settings.voice_receptionist_api_key
VOICE_RECEPTIONIST_HMAC_SECRET = settings.voice_receptionist_hmac_secret


class WebhookDeliveryError(Exception):
    """Exception raised when webhook delivery fails."""

    pass


class WebhookValidationError(Exception):
    """Exception raised when webhook payload validation fails."""

    pass


class WebhookPermanentError(Exception):
    """Exception raised when webhook delivery fails permanently."""

    pass


async def send_webhook_to_voice_receptionist(
    booking_id: str, event_type: str, payload: Dict[str, Any]
) -> bool:
    """
    Send webhook to Voice Receptionist.

    Args:
        booking_id: The booking ID
        event_type: The event type (booking.confirmed, booking.canceled)
        payload: The webhook payload

    Returns:
        bool: True if webhook was delivered successfully, False otherwise

    Raises:
        WebhookValidationError: If payload validation fails
        WebhookDeliveryError: If webhook delivery fails temporarily
        WebhookPermanentError: If webhook delivery fails permanently
    """
    try:
        # Validate payload
        if event_type == "booking.confirmed":
            webhook_payload = BookingConfirmedPayload(**payload)
        elif event_type == "booking.canceled":
            webhook_payload = BookingCanceledPayload(**payload)
        else:
            raise WebhookValidationError(f"Invalid event type: {event_type}")

        # Convert to dict for sending
        payload_dict = webhook_payload.dict()

        # Add timestamp
        payload_dict["timestamp"] = (
            datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")
        )
        payload_dict["event_type"] = event_type

        # Serialize payload to JSON
        payload_json = json.dumps(payload_dict, sort_keys=True)

        # Generate HMAC signature
        signature = generate_hmac_signature(
            payload_json, VOICE_RECEPTIONIST_HMAC_SECRET
        )

        # Prepare headers with API key, signature, and idempotency key
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": VOICE_RECEPTIONIST_API_KEY,
            "X-Signature": signature,
            "X-Idempotency-Key": booking_id,  # Use booking_id as idempotency key
        }

        # Send webhook
        async with httpx.AsyncClient() as client:
            response = await client.post(
                VOICE_RECEPTIONIST_API_URL,
                headers=headers,
                json=payload_dict,
                timeout=10.0,
            )

            # Handle response
            if response.status_code == 200:
                logger.info(f"Webhook delivered successfully for booking {booking_id}")
                await update_webhook_status(booking_id, "delivered")
                return True
            elif response.status_code == 429:
                # Handle rate limiting with Retry-After header
                retry_after = int(response.headers.get("Retry-After", "60"))
                logger.warning(
                    f"Rate limited by Voice Receptionist API. Retry after {retry_after} seconds"
                )
                await update_webhook_status(
                    booking_id, "failed", next_attempt_delay=retry_after
                )
                raise WebhookDeliveryError(
                    f"Rate limited. Retry after {retry_after} seconds"
                )
            elif 400 <= response.status_code < 500:
                if response.status_code == 400:
                    # Bad request - likely a payload issue
                    logger.error(
                        f"Bad request when delivering webhook for booking {booking_id}: {response.text}"
                    )
                    await update_webhook_status(booking_id, "permanent_failure")
                    raise WebhookPermanentError(f"Bad request: {response.text}")
                elif response.status_code == 401 or response.status_code == 403:
                    # Authentication or authorization issue
                    logger.error(
                        f"Authentication error when delivering webhook for booking {booking_id}: {response.text}"
                    )
                    await update_webhook_status(booking_id, "permanent_failure")
                    raise WebhookPermanentError(
                        f"Authentication error: {response.text}"
                    )
                else:
                    # Other 4xx errors - may be temporary
                    logger.error(
                        f"Client error when delivering webhook for booking {booking_id}: {response.text}"
                    )
                    await update_webhook_status(booking_id, "failed")
                    raise WebhookDeliveryError(f"Client error: {response.text}")
            else:
                # 5xx errors - likely temporary
                logger.error(
                    f"Server error when delivering webhook for booking {booking_id}: {response.text}"
                )
                await update_webhook_status(booking_id, "failed")
                raise WebhookDeliveryError(f"Server error: {response.text}")
    except ValidationError as e:
        logger.error(f"Validation error for booking {booking_id}: {str(e)}")
        await update_webhook_status(booking_id, "permanent_failure")
        raise WebhookValidationError(f"Validation error: {str(e)}")
    except httpx.RequestError as e:
        logger.error(
            f"Network error when delivering webhook for booking {booking_id}: {str(e)}"
        )
        await update_webhook_status(booking_id, "failed")
        raise WebhookDeliveryError(f"Network error: {str(e)}")
    except WebhookDeliveryError:
        # Re-raise WebhookDeliveryError without updating status again
        # Status is already updated by the specific error handler
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error when delivering webhook for booking {booking_id}: {str(e)}"
        )
        await update_webhook_status(booking_id, "failed")
        raise WebhookDeliveryError(f"Unexpected error: {str(e)}")


def generate_hmac_signature(payload: str, secret: str) -> str:
    """
    Generate HMAC-SHA256 signature for payload.

    Args:
        payload: The payload to sign
        secret: The secret key

    Returns:
        str: The HMAC-SHA256 signature
    """
    return hmac.new(
        secret.encode("utf-8"), payload.encode("utf-8"), hashlib.sha256
    ).hexdigest()


# Backwards-compatible accessor so tests can patch backend.services.voice_receptionist.get_supabase_client
# It returns a Supabase client; if the underlying function is async, it awaits it.
async def get_supabase_client():
    client = get_supabase_client_async()
    if inspect.isawaitable(client):
        return await client  # type: ignore
    return client  # type: ignore


async def update_webhook_status(
    booking_id: str, status: str, next_attempt_delay: Optional[int] = None
) -> None:
    """
    Update webhook status in database.

    Args:
        booking_id: The booking ID
        status: The webhook status (pending, delivered, failed, permanent_failure)
        next_attempt_delay: Optional delay in seconds for next attempt
    """
    supabase = await get_supabase_client()

    # Get current booking record
    response = (
        await supabase.table("tenants.bookings")
        .select("*")
        .eq("id", booking_id)
        .execute()
    )

    if not response.data:
        logger.error(f"Booking {booking_id} not found")
        return

    booking = response.data[0]

    # Calculate next attempt time if needed
    next_attempt = None
    if status == "failed":
        attempts = booking.get("recep_webhook_attempts", 0) + 1

        if attempts >= MAX_RETRY_ATTEMPTS:
            status = "permanent_failure"
        else:
            # Use provided delay or calculate with exponential backoff
            if next_attempt_delay is None:
                delay = min(
                    INITIAL_RETRY_DELAY * (2 ** (attempts - 1)), MAX_RETRY_DELAY
                )
                # Add jitter to prevent thundering herd
                jitter = random.uniform(-JITTER_FACTOR, JITTER_FACTOR)
                delay = delay * (1 + jitter)
                next_attempt_delay = int(delay)

            next_attempt = datetime.now(timezone.utc) + timedelta(
                seconds=next_attempt_delay
            )

    # Update booking record
    update_data = {
        "recep_webhook_status": status,
        "recep_webhook_last_attempt": datetime.now(timezone.utc).isoformat(),
    }

    if status == "failed":
        update_data["recep_webhook_attempts"] = (
            booking.get("recep_webhook_attempts", 0) + 1
        )

    if next_attempt:
        update_data["recep_webhook_next_attempt"] = next_attempt.isoformat()

    await (
        supabase.table("tenants.bookings")
        .update(update_data)
        .eq("id", booking_id)
        .execute()
    )


async def retry_failed_webhooks() -> None:
    """
    Retry failed webhooks.

    This function is intended to be run as a background task to retry
    failed webhook deliveries.
    """
    supabase = await get_supabase_client()

    # Get failed webhooks that are due for retry
    now = datetime.now(timezone.utc).isoformat()
    response = (
        await supabase.table("tenants.bookings")
        .select("*")
        .eq("recep_webhook_status", "failed")
        .lte("recep_webhook_next_attempt", now)
        .execute()
    )

    if not response.data:
        logger.info("No failed webhooks to retry")
        return

    logger.info(f"Retrying {len(response.data)} failed webhooks")

    for booking in response.data:
        booking_id = booking["id"]
        event_type = "booking.confirmed"  # Default

        if booking.get("status") == "canceled":
            event_type = "booking.canceled"

        # Construct payload from booking data
        payload = construct_webhook_payload_from_booking(booking)

        try:
            await send_webhook_to_voice_receptionist(booking_id, event_type, payload)
            logger.info(f"Successfully retried webhook for booking {booking_id}")
        except (
            WebhookDeliveryError,
            WebhookValidationError,
            WebhookPermanentError,
        ) as e:
            logger.error(f"Failed to retry webhook for booking {booking_id}: {str(e)}")
            # Status is already updated by send_webhook_to_voice_receptionist


def construct_webhook_payload_from_booking(booking: Dict[str, Any]) -> Dict[str, Any]:
    """
    Construct webhook payload from booking data.

    Args:
        booking: The booking data

    Returns:
        Dict[str, Any]: The webhook payload
    """
    # Extract customer data
    customer = {
        "name": booking.get("customer_name", ""),
        "email": booking.get("customer_email", ""),
        "phone": booking.get("customer_phone", ""),
    }

    # Extract appointment data
    appointment = {
        "title": booking.get("title", "Consultation"),
        "start_time": booking.get("start_time", ""),
        "end_time": booking.get("end_time", ""),
        "timezone": booking.get("timezone", "UTC"),
        "location": booking.get("location", ""),
        "provider_name": booking.get("provider_name", ""),
    }

    # Extract metadata
    metadata = {
        "calendly_uri": booking.get("calendly_uri", ""),
        "provider_event_link": booking.get("provider_event_link", ""),
    }

    # Construct payload
    payload = {
        "booking_id": booking["id"],
        "firm_id": booking["firm_id"],
        "call_id": booking.get("call_id", ""),
        "status": booking.get("status", "confirmed"),
        "customer": customer,
        "appointment": appointment,
        "metadata": metadata,
    }

    return payload
