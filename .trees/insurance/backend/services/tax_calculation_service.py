"""
Tax Calculation Service for PI Lawyer AI

Implements comprehensive tax calculation with Stripe Tax integration,
VAT validation, and multi-country compliance support.
"""

import logging
import re
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any, Optional, List, Tuple
from uuid import UUID

import stripe
from sqlalchemy import select, insert, update, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

# Import configuration and database utilities
import os
import sys

# Add the project root to the path for imports
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from backend.models.subscription import TenantSubscription
    from backend.core.config import settings
    from backend.core.database import get_db_session
except ImportError:
    # Fallback imports
    try:
        from models.subscription import TenantSubscription
        from core.config import settings
        from core.database import get_db_session
    except ImportError:
        # Mock for testing
        TenantSubscription = None
        settings = type("Settings", (), {"stripe_secret_key": "sk_test_mock"})()
        get_db_session = lambda: None

logger = logging.getLogger(__name__)

# Configure Stripe
if hasattr(settings, "stripe_secret_key") and settings.stripe_secret_key:
    stripe.api_key = settings.stripe_secret_key
else:
    # Use test key for development/testing
    stripe.api_key = "sk_test_mock_key_for_testing"


class TaxCalculationResult:
    """Result of tax calculation with detailed breakdown."""

    def __init__(
        self,
        subtotal_amount: Decimal,
        tax_amount: Decimal,
        total_amount: Decimal,
        tax_rate: Decimal,
        tax_type: str,
        currency: str,
        customer_country: str,
        customer_type: str = "B2C",
        vat_number: Optional[str] = None,
        vat_validation_status: Optional[str] = None,
        reverse_charge_applied: bool = False,
        tax_jurisdiction: Optional[str] = None,
        stripe_calculation_id: Optional[str] = None,
    ):
        self.subtotal_amount = subtotal_amount
        self.tax_amount = tax_amount
        self.total_amount = total_amount
        self.tax_rate = tax_rate
        self.tax_type = tax_type
        self.currency = currency
        self.customer_country = customer_country
        self.customer_type = customer_type
        self.vat_number = vat_number
        self.vat_validation_status = vat_validation_status
        self.reverse_charge_applied = reverse_charge_applied
        self.tax_jurisdiction = tax_jurisdiction
        self.stripe_calculation_id = stripe_calculation_id

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "subtotal_amount": float(self.subtotal_amount),
            "tax_amount": float(self.tax_amount),
            "total_amount": float(self.total_amount),
            "tax_rate": float(self.tax_rate),
            "tax_type": self.tax_type,
            "currency": self.currency,
            "customer_country": self.customer_country,
            "customer_type": self.customer_type,
            "vat_number": self.vat_number,
            "vat_validation_status": self.vat_validation_status,
            "reverse_charge_applied": self.reverse_charge_applied,
            "tax_jurisdiction": self.tax_jurisdiction,
            "stripe_calculation_id": self.stripe_calculation_id,
        }


class VATValidationResult:
    """Result of VAT number validation."""

    def __init__(
        self,
        vat_number: str,
        country_code: str,
        is_valid: bool,
        company_name: Optional[str] = None,
        company_address: Optional[str] = None,
        validation_source: str = "stripe",
    ):
        self.vat_number = vat_number
        self.country_code = country_code
        self.is_valid = is_valid
        self.company_name = company_name
        self.company_address = company_address
        self.validation_source = validation_source

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "vat_number": self.vat_number,
            "country_code": self.country_code,
            "is_valid": self.is_valid,
            "company_name": self.company_name,
            "company_address": self.company_address,
            "validation_source": self.validation_source,
        }


class TaxCalculationService:
    """
    Comprehensive tax calculation service with Stripe Tax integration.

    Provides real-time tax calculation, VAT validation, and compliance
    tracking for multi-country subscription billing.
    """

    def __init__(self):
        self.stripe_client = stripe
        self.logger = logger

    async def calculate_tax(
        self,
        amount: Decimal,
        currency: str,
        customer_country: str,
        customer_address: Dict[str, str],
        customer_type: str = "B2C",
        vat_number: Optional[str] = None,
        line_items: Optional[List[Dict[str, Any]]] = None,
        tenant_id: Optional[str] = None,
    ) -> TaxCalculationResult:
        """
        Calculate tax for a subscription using Stripe Tax API.

        Args:
            amount: Subscription amount in smallest currency unit
            currency: Currency code (USD, EUR, etc.)
            customer_country: Customer country code
            customer_address: Customer billing address
            customer_type: B2C or B2B customer type
            vat_number: VAT number for B2B customers
            line_items: Optional line items for detailed calculation
            tenant_id: Tenant ID for audit and isolation purposes

        Returns:
            TaxCalculationResult with detailed tax breakdown
        """
        try:
            self.logger.info(
                f"Calculating tax for {currency} {amount} in {customer_country}"
            )

            # Validate VAT number if provided
            vat_validation_result = None
            if vat_number and customer_type == "B2B":
                vat_validation_result = await self.validate_vat_number(
                    vat_number, customer_country, tenant_id
                )

            # Determine tax behavior based on country and customer type
            tax_behavior = await self.determine_tax_behavior(
                customer_country, customer_type, vat_validation_result
            )

            # Prepare customer details for Stripe Tax
            customer_details = {
                "address": {
                    "line1": customer_address.get("line1", ""),
                    "city": customer_address.get("city", ""),
                    "state": customer_address.get("state", ""),
                    "postal_code": customer_address.get("postal_code", ""),
                    "country": customer_country,
                },
                "tax_exempt": "none",  # Default to not exempt
            }

            # Add VAT number if validated
            if vat_validation_result and vat_validation_result.is_valid:
                customer_details["tax_ids"] = [
                    {
                        "type": self._get_tax_id_type(customer_country),
                        "value": vat_number,
                    }
                ]
                if customer_type == "B2B" and customer_country in [
                    "BE",
                    "DE",
                    "FR",
                    "NL",
                    "IT",
                    "ES",
                    "GB",
                ]:
                    customer_details["tax_exempt"] = "reverse"

            # Prepare line items
            if not line_items:
                line_items = [
                    {
                        "amount": int(amount * 100),  # Convert to cents
                        "reference": "subscription",
                        "tax_behavior": tax_behavior,
                        "tax_code": "txcd_10000000",  # Software services
                    }
                ]

            # Call Stripe Tax API
            calculation = self.stripe_client.tax.Calculation.create(
                currency=currency.lower(),
                customer_details=customer_details,
                line_items=line_items,
                expand=["line_items.data.tax_breakdown"],
            )

            # Extract tax information
            line_item = calculation.line_items.data[0]
            tax_breakdown = (
                line_item.tax_breakdown[0] if line_item.tax_breakdown else None
            )

            subtotal_amount = (
                Decimal(str(calculation.amount_total - calculation.tax_amount)) / 100
            )
            tax_amount = Decimal(str(calculation.tax_amount)) / 100
            total_amount = Decimal(str(calculation.amount_total)) / 100
            tax_rate = (
                Decimal(str(tax_breakdown.tax_rate_details.percentage_decimal))
                if tax_breakdown
                else Decimal("0")
            )

            # Determine tax type and jurisdiction
            tax_type = self._determine_tax_type(customer_country)
            tax_jurisdiction = (
                tax_breakdown.jurisdiction.display_name
                if tax_breakdown and tax_breakdown.jurisdiction
                else customer_country
            )

            result = TaxCalculationResult(
                subtotal_amount=subtotal_amount,
                tax_amount=tax_amount,
                total_amount=total_amount,
                tax_rate=tax_rate,
                tax_type=tax_type,
                currency=currency.upper(),
                customer_country=customer_country,
                customer_type=customer_type,
                vat_number=vat_number,
                vat_validation_status=(
                    vat_validation_result.validation_source
                    if vat_validation_result
                    else None
                ),
                reverse_charge_applied=customer_details.get("tax_exempt") == "reverse",
                tax_jurisdiction=tax_jurisdiction,
                stripe_calculation_id=calculation.id,
            )

            self.logger.info(
                f"Tax calculation successful: {tax_amount} {currency} tax on {subtotal_amount} {currency}"
            )
            return result

        except stripe.error.StripeError as e:
            self.logger.error(f"Stripe Tax API error: {e}")
            # Fallback to local tax calculation
            return await self._fallback_tax_calculation(
                amount, currency, customer_country, customer_type, vat_validation_result
            )

        except Exception as e:
            self.logger.error(f"Tax calculation error: {e}", exc_info=True)
            raise

    async def validate_vat_number(
        self, vat_number: str, country_code: str, tenant_id: Optional[str] = None
    ) -> Optional[VATValidationResult]:
        """
        Validate VAT number using cache and Stripe Tax API with tenant isolation.

        Args:
            vat_number: VAT number to validate
            country_code: Country code for validation
            tenant_id: Tenant ID for cache isolation

        Returns:
            VATValidationResult or None if validation fails
        """
        try:
            # Clean VAT number
            clean_vat = self._clean_vat_number(vat_number, country_code)

            # Check cache first with tenant isolation
            async with get_db_session() as db:
                cache_query = (
                    select("*")
                    .select_from("tenants.vat_validation_cache")
                    .where(
                        and_(
                            "vat_number" == clean_vat,
                            "country_code" == country_code,
                            "expires_at" > datetime.utcnow(),
                        )
                    )
                )

                # Add tenant isolation if tenant_id is provided
                if tenant_id:
                    cache_query = cache_query.where("tenant_id" == tenant_id)

                cache_result = await db.execute(cache_query)
                cached = cache_result.fetchone()

                if cached:
                    self.logger.info(f"VAT validation cache hit for {clean_vat}")
                    return VATValidationResult(
                        vat_number=clean_vat,
                        country_code=country_code,
                        is_valid=cached.validation_status == "valid",
                        company_name=cached.company_name,
                        company_address=cached.company_address,
                        validation_source=cached.validation_source,
                    )

            # Validate with Stripe Tax
            self.logger.info(f"Validating VAT number {clean_vat} with Stripe")

            calculation = self.stripe_client.tax.Calculation.create(
                currency="eur",
                customer_details={
                    "address": {"country": country_code},
                    "tax_ids": [
                        {
                            "type": self._get_tax_id_type(country_code),
                            "value": clean_vat,
                        }
                    ],
                },
                line_items=[
                    {
                        "amount": 100,  # €1.00 for validation
                        "reference": "validation",
                        "tax_behavior": "exclusive",
                        "tax_code": "txcd_10000000",
                    }
                ],
            )

            # Check validation result
            customer_details = calculation.customer_details
            tax_id = customer_details.tax_ids[0] if customer_details.tax_ids else None

            is_valid = (
                tax_id
                and tax_id.verification
                and tax_id.verification.status == "verified"
            )
            company_name = (
                tax_id.verification.verified_name
                if is_valid and tax_id.verification
                else None
            )
            company_address = (
                tax_id.verification.verified_address
                if is_valid and tax_id.verification
                else None
            )

            result = VATValidationResult(
                vat_number=clean_vat,
                country_code=country_code,
                is_valid=is_valid,
                company_name=company_name,
                company_address=company_address,
                validation_source="stripe",
            )

            # Cache the result with tenant context
            await self._cache_vat_validation(result, tenant_id)

            return result

        except Exception as e:
            self.logger.error(f"VAT validation error for {vat_number}: {e}")
            return None

    async def determine_tax_behavior(
        self,
        customer_country: str,
        customer_type: str = "B2C",
        vat_validation: Optional[VATValidationResult] = None,
    ) -> str:
        """
        Determine tax behavior (inclusive/exclusive) based on country and customer type.

        Args:
            customer_country: Customer country code
            customer_type: B2C or B2B customer type
            vat_validation: VAT validation result for B2B customers

        Returns:
            "inclusive" or "exclusive" tax behavior
        """
        try:
            # EU countries typically show tax-inclusive pricing for B2C
            eu_countries = [
                "BE",
                "DE",
                "FR",
                "NL",
                "IT",
                "ES",
                "AT",
                "PT",
                "IE",
                "FI",
                "SE",
                "DK",
                "LU",
                "GR",
                "CY",
                "MT",
                "SI",
                "SK",
                "EE",
                "LV",
                "LT",
                "PL",
                "CZ",
                "HU",
                "RO",
                "BG",
                "HR",
            ]

            if customer_country in eu_countries:
                if (
                    customer_type == "B2B"
                    and vat_validation
                    and vat_validation.is_valid
                ):
                    return "exclusive"  # B2B with valid VAT - reverse charge
                return "inclusive"  # B2C or B2B without valid VAT

            # US, CA, GB typically show tax-exclusive pricing
            return "exclusive"

        except Exception as e:
            self.logger.error(f"Error determining tax behavior: {e}")
            return "exclusive"  # Default to exclusive

    async def get_tax_rates(
        self, country_code: str, state: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get tax rates for a specific country/region.

        Args:
            country_code: Country code
            state: Optional state/region code

        Returns:
            List of applicable tax rates
        """
        try:
            async with get_db_session() as db:
                query = (
                    select("*")
                    .select_from("tenants.tax_config")
                    .where(
                        and_(
                            "country_code" == country_code,
                            "is_active" == True,
                            or_(
                                "expires_date" == None,
                                "expires_date" > datetime.now().date(),
                            ),
                        )
                    )
                )

                if state:
                    query = query.where("region_code" == state)

                result = await db.execute(query)
                tax_configs = result.fetchall()

                return [
                    {
                        "country_code": config.country_code,
                        "region_code": config.region_code,
                        "tax_type": config.tax_type,
                        "tax_rate": float(config.tax_rate),
                        "applies_to": config.applies_to,
                        "requires_vat_validation": config.requires_vat_validation,
                        "reverse_charge_eligible": config.reverse_charge_eligible,
                        "description": config.description,
                    }
                    for config in tax_configs
                ]

        except Exception as e:
            self.logger.error(f"Error getting tax rates for {country_code}: {e}")
            return []

    async def save_tax_calculation(
        self, subscription_id: UUID, calculation_result: TaxCalculationResult
    ) -> UUID:
        """
        Save tax calculation to audit table.

        Args:
            subscription_id: Subscription ID
            calculation_result: Tax calculation result

        Returns:
            Tax calculation record ID
        """
        try:
            async with get_db_session() as db:
                insert_stmt = (
                    insert("tenants.tax_calculations")
                    .values(
                        subscription_id=subscription_id,
                        stripe_calculation_id=calculation_result.stripe_calculation_id,
                        customer_country=calculation_result.customer_country,
                        customer_type=calculation_result.customer_type,
                        subtotal_amount=calculation_result.subtotal_amount,
                        tax_amount=calculation_result.tax_amount,
                        total_amount=calculation_result.total_amount,
                        tax_rate=calculation_result.tax_rate,
                        tax_type=calculation_result.tax_type,
                        vat_number=calculation_result.vat_number,
                        vat_validation_status=calculation_result.vat_validation_status,
                        reverse_charge_applied=calculation_result.reverse_charge_applied,
                        currency=calculation_result.currency,
                        tax_jurisdiction=calculation_result.tax_jurisdiction,
                        calculation_source="stripe_tax",
                    )
                    .returning("id")
                )

                result = await db.execute(insert_stmt)
                calculation_id = result.scalar()
                await db.commit()

                self.logger.info(
                    f"Saved tax calculation {calculation_id} for subscription {subscription_id}"
                )
                return calculation_id

        except Exception as e:
            self.logger.error(f"Error saving tax calculation: {e}")
            raise

    # Private helper methods

    def _clean_vat_number(self, vat_number: str, country_code: str) -> str:
        """Clean and format VAT number."""
        # Remove spaces and special characters
        clean = re.sub(r"[^A-Z0-9]", "", vat_number.upper())

        # Add country prefix if missing
        if not clean.startswith(country_code):
            clean = country_code + clean

        return clean

    def _get_tax_id_type(self, country_code: str) -> str:
        """Get Stripe tax ID type for country."""
        tax_id_types = {
            "BE": "eu_vat",
            "DE": "eu_vat",
            "FR": "eu_vat",
            "NL": "eu_vat",
            "IT": "eu_vat",
            "ES": "eu_vat",
            "GB": "gb_vat",
            "US": "us_ein",
            "CA": "ca_bn",
        }
        return tax_id_types.get(country_code, "eu_vat")

    def _determine_tax_type(self, country_code: str) -> str:
        """Determine tax type based on country."""
        if country_code in [
            "BE",
            "DE",
            "FR",
            "NL",
            "IT",
            "ES",
            "AT",
            "PT",
            "IE",
            "FI",
            "SE",
            "DK",
            "LU",
            "GR",
            "CY",
            "MT",
            "SI",
            "SK",
            "EE",
            "LV",
            "LT",
            "PL",
            "CZ",
            "HU",
            "RO",
            "BG",
            "HR",
        ]:
            return "vat"
        elif country_code == "GB":
            return "vat"
        elif country_code == "US":
            return "sales_tax"
        elif country_code == "CA":
            return "gst"
        else:
            return "tax"

    async def _fallback_tax_calculation(
        self,
        amount: Decimal,
        currency: str,
        customer_country: str,
        customer_type: str,
        vat_validation: Optional[VATValidationResult],
    ) -> TaxCalculationResult:
        """Fallback tax calculation using local tax config."""
        try:
            tax_rates = await self.get_tax_rates(customer_country)

            if not tax_rates:
                # No tax if no configuration found
                return TaxCalculationResult(
                    subtotal_amount=amount,
                    tax_amount=Decimal("0"),
                    total_amount=amount,
                    tax_rate=Decimal("0"),
                    tax_type="none",
                    currency=currency,
                    customer_country=customer_country,
                    customer_type=customer_type,
                )

            # Find applicable tax rate
            applicable_rate = None
            for rate in tax_rates:
                if rate["applies_to"] in [customer_type.lower(), "both"]:
                    applicable_rate = rate
                    break

            if not applicable_rate:
                applicable_rate = tax_rates[0]  # Use first rate as fallback

            # Calculate tax
            tax_rate = Decimal(str(applicable_rate["tax_rate"]))

            # Apply reverse charge for B2B with valid VAT
            if (
                customer_type == "B2B"
                and vat_validation
                and vat_validation.is_valid
                and applicable_rate["reverse_charge_eligible"]
            ):
                tax_amount = Decimal("0")
                reverse_charge = True
            else:
                tax_amount = amount * tax_rate
                reverse_charge = False

            return TaxCalculationResult(
                subtotal_amount=amount,
                tax_amount=tax_amount,
                total_amount=amount + tax_amount,
                tax_rate=tax_rate,
                tax_type=applicable_rate["tax_type"],
                currency=currency,
                customer_country=customer_country,
                customer_type=customer_type,
                vat_number=vat_validation.vat_number if vat_validation else None,
                vat_validation_status="fallback",
                reverse_charge_applied=reverse_charge,
                tax_jurisdiction=customer_country,
                stripe_calculation_id=None,
            )

        except Exception as e:
            self.logger.error(f"Fallback tax calculation error: {e}")
            # Return zero tax as final fallback
            return TaxCalculationResult(
                subtotal_amount=amount,
                tax_amount=Decimal("0"),
                total_amount=amount,
                tax_rate=Decimal("0"),
                tax_type="fallback",
                currency=currency,
                customer_country=customer_country,
                customer_type=customer_type,
            )

    async def _cache_vat_validation(
        self, validation_result: VATValidationResult, tenant_id: Optional[str] = None
    ):
        """Cache VAT validation result."""
        try:
            async with get_db_session() as db:
                # Upsert validation result with tenant isolation
                await db.execute(
                    """
                    INSERT INTO tenants.vat_validation_cache
                    (vat_number, country_code, validation_status, company_name, company_address, validation_source, tenant_id)
                    VALUES (:vat_number, :country_code, :status, :company_name, :company_address, :source, :tenant_id)
                    ON CONFLICT (vat_number, country_code, tenant_id)
                    DO UPDATE SET
                        validation_status = EXCLUDED.validation_status,
                        company_name = EXCLUDED.company_name,
                        company_address = EXCLUDED.company_address,
                        validation_date = NOW(),
                        expires_at = NOW() + INTERVAL '24 hours',
                        updated_at = NOW()
                    """,
                    {
                        "vat_number": validation_result.vat_number,
                        "country_code": validation_result.country_code,
                        "status": "valid" if validation_result.is_valid else "invalid",
                        "company_name": validation_result.company_name,
                        "company_address": validation_result.company_address,
                        "source": validation_result.validation_source,
                        "tenant_id": tenant_id,
                    },
                )
                await db.commit()

        except Exception as e:
            self.logger.error(f"Error caching VAT validation: {e}")

    async def reconcile_fallback_calculations(
        self, tenant_id: Optional[str] = None
    ) -> Dict[str, int]:
        """
        Reconcile fallback calculations with Stripe when service is restored.

        Args:
            tenant_id: Optional tenant ID to limit reconciliation scope

        Returns:
            Dictionary with reconciliation statistics
        """
        try:
            stats = {"total_pending": 0, "reconciled": 0, "failed": 0, "skipped": 0}

            async with get_db_session() as db:
                # Find pending reconciliations
                query = (
                    select("*")
                    .select_from("tenants.tax_fallback_reconciliation")
                    .where(
                        and_(
                            "reconciliation_status" == "pending",
                            "reconciliation_attempts" < "max_attempts",
                        )
                    )
                )

                if tenant_id:
                    query = query.where("tenant_id" == tenant_id)

                result = await db.execute(query)
                pending_reconciliations = result.fetchall()
                stats["total_pending"] = len(pending_reconciliations)

                for reconciliation in pending_reconciliations:
                    try:
                        # Update status to in_progress
                        await db.execute(
                            update("tenants.tax_fallback_reconciliation")
                            .where("id" == reconciliation.id)
                            .values(
                                reconciliation_status="in_progress",
                                reconciliation_attempts=reconciliation.reconciliation_attempts
                                + 1,
                                last_attempt_at=datetime.utcnow(),
                            )
                        )

                        # Attempt reconciliation with Stripe
                        stripe_result = await self._reconcile_with_stripe(
                            reconciliation
                        )

                        if stripe_result:
                            # Calculate differences
                            amount_diff = abs(
                                stripe_result.total_amount
                                - reconciliation.fallback_amount
                            )
                            tax_diff = abs(
                                stripe_result.tax_amount
                                - reconciliation.fallback_tax_amount
                            )

                            # Update reconciliation record
                            await db.execute(
                                update("tenants.tax_fallback_reconciliation")
                                .where("id" == reconciliation.id)
                                .values(
                                    reconciliation_status="reconciled",
                                    stripe_calculation_id=stripe_result.stripe_calculation_id,
                                    stripe_amount=stripe_result.total_amount,
                                    stripe_tax_amount=stripe_result.tax_amount,
                                    amount_difference=amount_diff,
                                    tax_difference=tax_diff,
                                    reconciled_at=datetime.utcnow(),
                                    reconciliation_notes=f"Reconciled successfully. Amount diff: {amount_diff}, Tax diff: {tax_diff}",
                                )
                            )

                            # Update original calculation with Stripe ID
                            if reconciliation.original_calculation_id:
                                await db.execute(
                                    update("tenants.tax_calculations")
                                    .where(
                                        "id" == reconciliation.original_calculation_id
                                    )
                                    .values(
                                        stripe_calculation_id=stripe_result.stripe_calculation_id,
                                        calculation_source="stripe_tax_reconciled",
                                    )
                                )

                            stats["reconciled"] += 1
                            self.logger.info(
                                f"Reconciled fallback calculation {reconciliation.id}"
                            )
                        else:
                            # Mark as failed if max attempts reached
                            if (
                                reconciliation.reconciliation_attempts + 1
                                >= reconciliation.max_attempts
                            ):
                                await db.execute(
                                    update("tenants.tax_fallback_reconciliation")
                                    .where("id" == reconciliation.id)
                                    .values(
                                        reconciliation_status="failed",
                                        reconciliation_notes="Failed to reconcile after maximum attempts",
                                    )
                                )
                                stats["failed"] += 1
                            else:
                                # Reset to pending for retry
                                await db.execute(
                                    update("tenants.tax_fallback_reconciliation")
                                    .where("id" == reconciliation.id)
                                    .values(reconciliation_status="pending")
                                )
                                stats["skipped"] += 1

                    except Exception as e:
                        self.logger.error(f"Error reconciling {reconciliation.id}: {e}")
                        # Mark as failed
                        await db.execute(
                            update("tenants.tax_fallback_reconciliation")
                            .where("id" == reconciliation.id)
                            .values(
                                reconciliation_status="failed",
                                reconciliation_notes=f"Error during reconciliation: {str(e)}",
                            )
                        )
                        stats["failed"] += 1

                await db.commit()

            self.logger.info(f"Reconciliation completed: {stats}")
            return stats

        except Exception as e:
            self.logger.error(
                f"Error during fallback reconciliation: {e}", exc_info=True
            )
            return {"error": str(e)}

    async def _reconcile_with_stripe(
        self, reconciliation
    ) -> Optional[TaxCalculationResult]:
        """
        Attempt to reconcile a fallback calculation with Stripe.

        Args:
            reconciliation: Reconciliation record

        Returns:
            TaxCalculationResult if successful, None otherwise
        """
        try:
            # This would require storing the original request parameters
            # For now, we'll simulate a successful reconciliation
            # In a real implementation, you'd store the original request data
            # and replay it against Stripe Tax API

            self.logger.info(
                f"Attempting Stripe reconciliation for {reconciliation.id}"
            )

            # Placeholder for actual Stripe API call
            # In production, you would:
            # 1. Retrieve original request parameters from reconciliation record
            # 2. Call Stripe Tax API with those parameters
            # 3. Return the result

            return None  # Return None to indicate reconciliation not possible without original request data

        except Exception as e:
            self.logger.error(f"Error reconciling with Stripe: {e}")
            return None


# Global service instance
tax_calculation_service = TaxCalculationService()
