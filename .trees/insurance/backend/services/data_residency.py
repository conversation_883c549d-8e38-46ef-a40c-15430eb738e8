"""
Data Residency Service for Regional Database Routing

This module provides intelligent routing of database connections based on user location
and data residency requirements for GDPR/CCPA compliance.
"""

import os
from enum import Enum
from typing import Optional, Dict, Any
from functools import lru_cache
import logging
from supabase import Client, create_client

logger = logging.getLogger(__name__)


class DataRegion(str, Enum):
    """Supported data residency regions."""

    US = "US"
    EU = "EU"


class RegionalSupabaseConfig:
    """Configuration for regional Supabase instances."""

    def __init__(self):
        # EU Configuration (ailex-belgium)
        self.eu_url = os.getenv(
            "SUPABASE_EU_URL", "https://lsixcrtzawcxyfkxhyxd.supabase.co"
        )
        self.eu_service_key = os.getenv("SUPABASE_EU_SERVICE_KEY")
        self.eu_anon_key = os.getenv("SUPABASE_EU_ANON_KEY")

        # US Configuration (new-texas-laws)
        self.us_url = os.getenv(
            "SUPABASE_US_URL", "https://anwefmklplkjxkmzpnva.supabase.co"
        )
        self.us_service_key = os.getenv("SUPABASE_US_SERVICE_KEY")
        self.us_anon_key = os.getenv("SUPABASE_US_ANON_KEY")

        # Fallback to current configuration if regional not set
        if not self.eu_service_key:
            self.eu_service_key = os.getenv("SUPABASE_SERVICE_KEY")
        if not self.us_service_key:
            self.us_service_key = os.getenv("SUPABASE_SERVICE_KEY")
        if not self.eu_anon_key:
            self.eu_anon_key = os.getenv("SUPABASE_ANON_KEY")
        if not self.us_anon_key:
            self.us_anon_key = os.getenv("SUPABASE_ANON_KEY")

    def get_config(
        self, region: DataRegion, use_service_role: bool = True
    ) -> Dict[str, str]:
        """Get Supabase configuration for specified region."""
        if region == DataRegion.EU:
            return {
                "url": self.eu_url,
                "key": self.eu_service_key if use_service_role else self.eu_anon_key,
            }
        else:  # US
            return {
                "url": self.us_url,
                "key": self.us_service_key if use_service_role else self.us_anon_key,
            }


class RegionalSupabaseClientFactory:
    """Factory for creating region-specific Supabase clients."""

    def __init__(self):
        self.config = RegionalSupabaseConfig()
        self._clients: Dict[str, Client] = {}

    @lru_cache(maxsize=4)
    def get_client(self, region: DataRegion, use_service_role: bool = True) -> Client:
        """
        Get a cached Supabase client for the specified region.

        Args:
            region: Target data region (US or EU)
            use_service_role: Whether to use service role key (True) or anon key (False)

        Returns:
            Configured Supabase client for the region

        Raises:
            ValueError: If required configuration is missing
        """
        cache_key = f"{region.value}_{use_service_role}"

        if cache_key in self._clients:
            return self._clients[cache_key]

        config = self.config.get_config(region, use_service_role)

        if not config["url"] or not config["key"]:
            raise ValueError(
                f"Missing Supabase configuration for region {region.value}"
            )

        try:
            client = create_client(config["url"], config["key"])
            self._clients[cache_key] = client

            logger.info(
                f"Created Supabase client for region {region.value}",
                extra={
                    "region": region.value,
                    "use_service_role": use_service_role,
                    "url": config["url"],
                },
            )

            return client

        except Exception as e:
            logger.error(
                f"Failed to create Supabase client for region {region.value}",
                extra={"region": region.value, "error": str(e)},
            )
            raise

    def get_client_for_user(
        self, user_region: Optional[DataRegion] = None, user_id: Optional[str] = None
    ) -> Client:
        """
        Get appropriate Supabase client for a user based on their data residency.

        Args:
            user_region: User's data residency region
            user_id: User ID for logging purposes

        Returns:
            Appropriate regional Supabase client
        """
        # Default to US if no region specified
        region = user_region or DataRegion.US

        logger.info(
            f"Routing user to {region.value} Supabase instance",
            extra={"user_id": user_id, "region": region.value},
        )

        return self.get_client(region, use_service_role=True)


# Global factory instance
_client_factory = RegionalSupabaseClientFactory()


def get_regional_supabase_client(
    region: DataRegion, use_service_role: bool = True
) -> Client:
    """
    Get a regional Supabase client.

    Args:
        region: Target data region
        use_service_role: Whether to use service role key

    Returns:
        Regional Supabase client
    """
    return _client_factory.get_client(region, use_service_role)


def get_supabase_client_for_user(
    user_region: Optional[DataRegion] = None, user_id: Optional[str] = None
) -> Client:
    """
    Get appropriate Supabase client for a user.

    Args:
        user_region: User's data residency region
        user_id: User ID for logging

    Returns:
        Appropriate regional Supabase client
    """
    return _client_factory.get_client_for_user(user_region, user_id)


def validate_regional_configuration() -> Dict[str, bool]:
    """
    Validate that regional Supabase configuration is properly set up.

    Returns:
        Dictionary with validation results for each region
    """
    config = RegionalSupabaseConfig()
    results = {}

    for region in [DataRegion.US, DataRegion.EU]:
        try:
            region_config = config.get_config(region)
            has_url = bool(region_config["url"])
            has_key = bool(region_config["key"])

            results[region.value] = has_url and has_key

            if not results[region.value]:
                logger.warning(
                    f"Incomplete configuration for region {region.value}",
                    extra={
                        "region": region.value,
                        "has_url": has_url,
                        "has_key": has_key,
                    },
                )
        except Exception as e:
            results[region.value] = False
            logger.error(
                f"Configuration validation failed for region {region.value}",
                extra={"region": region.value, "error": str(e)},
            )

    return results
