"""
Webhook Idempotency Service

Provides idempotency handling for webhook events to prevent duplicate processing.
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dataclasses import dataclass

from backend.db.supabase_client import get_supabase_client
from backend.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class IdempotencyRecord:
    """Represents an idempotency record for a webhook event."""

    key: str
    event_id: str
    event_type: str
    processed_at: datetime
    response_data: Optional[Dict[str, Any]] = None
    status: str = "processed"


class WebhookIdempotencyService:
    """
    Service for handling webhook idempotency to prevent duplicate processing.

    Uses Supabase database to store idempotency records with automatic cleanup.
    """

    def __init__(self, ttl_hours: int = 24):
        """
        Initialize the idempotency service.

        Args:
            ttl_hours: Time-to-live for idempotency records in hours
        """
        self.ttl_hours = ttl_hours
        self.table_name = "webhook_idempotency"

    def generate_idempotency_key(self, event: Dict[str, Any]) -> str:
        """
        Generate idempotency key for a webhook event.

        Args:
            event: Stripe webhook event

        Returns:
            Unique idempotency key
        """
        # Use event type, id, and created timestamp for uniqueness
        key_data = {
            "type": event.get("type"),
            "id": event.get("id"),
            "created": event.get("created"),
        }

        # Create hash of key data for consistent key generation
        key_string = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.sha256(key_string.encode()).hexdigest()

        return f"webhook_{event.get('type')}_{event.get('id')}_{key_hash[:16]}"

    async def is_event_processed(self, event: Dict[str, Any]) -> bool:
        """
        Check if a webhook event has already been processed.

        Args:
            event: Stripe webhook event

        Returns:
            True if event has been processed, False otherwise
        """
        try:
            idempotency_key = self.generate_idempotency_key(event)

            supabase = get_supabase_client()

            # Check if idempotency record exists
            result = (
                supabase.table(self.table_name)
                .select("*")
                .eq("idempotency_key", idempotency_key)
                .execute()
            )

            if result.error:
                logger.error(f"Error checking idempotency: {result.error}")
                return False

            if result.data:
                logger.info(
                    f"Event already processed: {event.get('type')} {event.get('id')}"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking event idempotency: {e}")
            # Fail safe - assume not processed to avoid missing events
            return False

    async def mark_event_processed(
        self, event: Dict[str, Any], response_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Mark a webhook event as processed.

        Args:
            event: Stripe webhook event
            response_data: Optional response data to store

        Returns:
            True if successfully marked, False otherwise
        """
        try:
            idempotency_key = self.generate_idempotency_key(event)

            supabase = get_supabase_client()

            # Create idempotency record
            record_data = {
                "idempotency_key": idempotency_key,
                "event_id": event.get("id"),
                "event_type": event.get("type"),
                "processed_at": datetime.utcnow().isoformat(),
                "response_data": response_data,
                "status": "processed",
                "expires_at": (
                    datetime.utcnow() + timedelta(hours=self.ttl_hours)
                ).isoformat(),
            }

            result = supabase.table(self.table_name).insert(record_data).execute()

            if result.error:
                logger.error(f"Error marking event as processed: {result.error}")
                return False

            logger.info(
                f"Event marked as processed: {event.get('type')} {event.get('id')}"
            )
            return True

        except Exception as e:
            logger.error(f"Error marking event as processed: {e}")
            return False

    async def get_processed_event(
        self, event: Dict[str, Any]
    ) -> Optional[IdempotencyRecord]:
        """
        Get the idempotency record for a processed event.

        Args:
            event: Stripe webhook event

        Returns:
            IdempotencyRecord if found, None otherwise
        """
        try:
            idempotency_key = self.generate_idempotency_key(event)

            supabase = get_supabase_client()

            result = (
                supabase.table(self.table_name)
                .select("*")
                .eq("idempotency_key", idempotency_key)
                .execute()
            )

            if result.error:
                logger.error(f"Error getting processed event: {result.error}")
                return None

            if result.data:
                record_data = result.data[0]
                return IdempotencyRecord(
                    key=record_data["idempotency_key"],
                    event_id=record_data["event_id"],
                    event_type=record_data["event_type"],
                    processed_at=datetime.fromisoformat(record_data["processed_at"]),
                    response_data=record_data.get("response_data"),
                    status=record_data.get("status", "processed"),
                )

            return None

        except Exception as e:
            logger.error(f"Error getting processed event: {e}")
            return None

    async def cleanup_expired_records(self) -> int:
        """
        Clean up expired idempotency records.

        Returns:
            Number of records cleaned up
        """
        try:
            supabase = get_supabase_client()

            # Delete expired records
            cutoff_time = datetime.utcnow().isoformat()

            result = (
                supabase.table(self.table_name)
                .delete()
                .lt("expires_at", cutoff_time)
                .execute()
            )

            if result.error:
                logger.error(f"Error cleaning up expired records: {result.error}")
                return 0

            cleaned_count = len(result.data) if result.data else 0

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired idempotency records")

            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up expired records: {e}")
            return 0

    async def get_stats(self) -> Dict[str, Any]:
        """
        Get idempotency service statistics.

        Returns:
            Dictionary with statistics
        """
        try:
            supabase = get_supabase_client()

            # Get total count
            total_result = (
                supabase.table(self.table_name).select("id", count="exact").execute()
            )
            total_count = total_result.count if total_result.count is not None else 0

            # Get expired count
            cutoff_time = datetime.utcnow().isoformat()
            expired_result = (
                supabase.table(self.table_name)
                .select("id", count="exact")
                .lt("expires_at", cutoff_time)
                .execute()
            )
            expired_count = (
                expired_result.count if expired_result.count is not None else 0
            )

            # Get recent count (last 24 hours)
            recent_cutoff = (datetime.utcnow() - timedelta(hours=24)).isoformat()
            recent_result = (
                supabase.table(self.table_name)
                .select("id", count="exact")
                .gte("processed_at", recent_cutoff)
                .execute()
            )
            recent_count = recent_result.count if recent_result.count is not None else 0

            return {
                "total_records": total_count,
                "expired_records": expired_count,
                "recent_records_24h": recent_count,
                "active_records": total_count - expired_count,
                "ttl_hours": self.ttl_hours,
            }

        except Exception as e:
            logger.error(f"Error getting idempotency stats: {e}")
            return {
                "total_records": 0,
                "expired_records": 0,
                "recent_records_24h": 0,
                "active_records": 0,
                "ttl_hours": self.ttl_hours,
                "error": str(e),
            }


# Global instance
webhook_idempotency_service = WebhookIdempotencyService()


async def is_webhook_processed(event: Dict[str, Any]) -> bool:
    """
    Convenience function to check if webhook event has been processed.

    Args:
        event: Stripe webhook event

    Returns:
        True if processed, False otherwise
    """
    return await webhook_idempotency_service.is_event_processed(event)


async def mark_webhook_processed(
    event: Dict[str, Any], response_data: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Convenience function to mark webhook event as processed.

    Args:
        event: Stripe webhook event
        response_data: Optional response data

    Returns:
        True if successfully marked, False otherwise
    """
    return await webhook_idempotency_service.mark_event_processed(event, response_data)
