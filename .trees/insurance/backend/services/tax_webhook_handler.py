#!/usr/bin/env python3
"""
Tax Webhook Handler for PI Lawyer AI
Handles tax-related Stripe webhook events with multi-country support.

This service processes tax rate updates, tax calculations, and regional tax compliance.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from enum import Enum

from backend.services.multi_currency_webhook_handler import (
    SupportedRegion,
    SupportedCurrency,
)
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)


class TaxEventType(Enum):
    """Supported tax event types."""

    TAX_RATE_CREATED = "tax.rate.created"
    TAX_RATE_UPDATED = "tax.rate.updated"
    INVOICE_TAX_CALCULATION_SUCCEEDED = "invoice.tax_calculation_succeeded"
    INVOICE_TAX_CALCULATION_FAILED = "invoice.tax_calculation_failed"


class TaxWebhookHandler:
    """Handles tax-related webhook events with multi-country support."""

    def __init__(self):
        self.regional_tax_config = {
            SupportedRegion.US: {
                "tax_type": "sales_tax",
                "tax_behavior": "exclusive",
                "default_rate": 0.0,  # Varies by state
                "calculation_method": "stripe_tax_api",
                "compliance_requirements": ["sales_tax_nexus", "economic_nexus"],
            },
            SupportedRegion.EU: {
                "tax_type": "vat",
                "tax_behavior": "inclusive",
                "default_rate": 21.0,  # Standard EU VAT rate
                "calculation_method": "vat_inclusive",
                "compliance_requirements": ["vat_registration", "moss_reporting"],
            },
            SupportedRegion.UK: {
                "tax_type": "vat",
                "tax_behavior": "inclusive",
                "default_rate": 20.0,  # UK VAT rate
                "calculation_method": "vat_inclusive",
                "compliance_requirements": ["vat_registration", "mtd_vat"],
            },
            SupportedRegion.CA: {
                "tax_type": "gst_hst",
                "tax_behavior": "exclusive",
                "default_rate": 5.0,  # Federal GST, varies by province
                "calculation_method": "gst_hst_calculation",
                "compliance_requirements": ["gst_registration", "provincial_tax"],
            },
        }

    async def handle_tax_rate_event(
        self, tax_rate_data: Dict[str, Any], regional_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle tax rate created/updated events with regional context.

        Args:
            tax_rate_data: Stripe tax rate object
            regional_context: Regional processing context

        Returns:
            Processing result
        """
        try:
            tax_rate_id = tax_rate_data.get("id")
            country = tax_rate_data.get("country")
            state = tax_rate_data.get("state")
            percentage = tax_rate_data.get("percentage", 0)
            tax_type = tax_rate_data.get("tax_type", "vat")

            region = regional_context.get("region", "US")
            currency = regional_context.get("currency", "USD")

            logger.info(
                f"Processing tax rate event: {tax_rate_id} for {country} in region {region}"
            )

            # Get regional tax configuration
            region_enum = SupportedRegion(region)
            tax_config = self.regional_tax_config.get(
                region_enum, self.regional_tax_config[SupportedRegion.US]
            )

            # Validate tax rate against regional requirements
            validation_result = await self._validate_regional_tax_rate(
                tax_rate_data, tax_config, regional_context
            )

            if not validation_result["valid"]:
                logger.warning(
                    f"Tax rate validation failed: {validation_result['errors']}"
                )
                return {
                    "status": "validation_failed",
                    "errors": validation_result["errors"],
                    "tax_rate_id": tax_rate_id,
                }

            # Process tax rate based on regional requirements
            processing_result = await self._process_regional_tax_rate(
                tax_rate_data, tax_config, regional_context
            )

            # Store tax rate information for compliance
            await self._store_tax_rate_compliance_data(
                tax_rate_data, tax_config, regional_context, processing_result
            )

            logger.info(
                f"Successfully processed tax rate {tax_rate_id} for region {region}"
            )

            return {
                "status": "success",
                "tax_rate_id": tax_rate_id,
                "region": region,
                "currency": currency,
                "tax_type": tax_config["tax_type"],
                "processing_result": processing_result,
            }

        except Exception as e:
            logger.error(f"Error processing tax rate event: {e}", exc_info=True)
            return {
                "status": "error",
                "error": str(e),
                "tax_rate_id": tax_rate_data.get("id"),
            }

    async def handle_tax_calculation_event(
        self,
        invoice_data: Dict[str, Any],
        success: bool,
        regional_context: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Handle tax calculation succeeded/failed events with regional context.

        Args:
            invoice_data: Stripe invoice object
            success: Whether the tax calculation succeeded
            regional_context: Regional processing context

        Returns:
            Processing result
        """
        try:
            invoice_id = invoice_data.get("id")
            customer_id = invoice_data.get("customer")
            subscription_id = invoice_data.get("subscription")
            currency = invoice_data.get("currency", "usd").upper()

            region = regional_context.get("region", "US")

            logger.info(
                f"Processing tax calculation event: {invoice_id} (success: {success}) for region {region}"
            )

            # Get regional tax configuration
            region_enum = SupportedRegion(region)
            tax_config = self.regional_tax_config.get(
                region_enum, self.regional_tax_config[SupportedRegion.US]
            )

            if success:
                # Process successful tax calculation
                result = await self._process_successful_tax_calculation(
                    invoice_data, tax_config, regional_context
                )
            else:
                # Handle failed tax calculation
                result = await self._handle_failed_tax_calculation(
                    invoice_data, tax_config, regional_context
                )

            # Store tax calculation audit data
            await self._store_tax_calculation_audit(
                invoice_data, success, tax_config, regional_context, result
            )

            logger.info(
                f"Successfully processed tax calculation for invoice {invoice_id}"
            )

            return {
                "status": "success",
                "invoice_id": invoice_id,
                "region": region,
                "currency": currency,
                "tax_calculation_success": success,
                "processing_result": result,
            }

        except Exception as e:
            logger.error(f"Error processing tax calculation event: {e}", exc_info=True)
            return {
                "status": "error",
                "error": str(e),
                "invoice_id": invoice_data.get("id"),
            }

    async def _validate_regional_tax_rate(
        self,
        tax_rate_data: Dict[str, Any],
        tax_config: Dict[str, Any],
        regional_context: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Validate tax rate against regional requirements."""
        errors = []

        try:
            percentage = tax_rate_data.get("percentage", 0)
            country = tax_rate_data.get("country")
            tax_type = tax_rate_data.get("tax_type", "vat")

            region = regional_context.get("region")

            # Validate tax type matches regional expectations
            expected_tax_type = tax_config["tax_type"]
            if tax_type != expected_tax_type and region in ["EU", "UK"]:
                # For EU/UK, ensure VAT is used
                if tax_type not in ["vat", "gst"]:
                    errors.append(
                        f"Invalid tax type {tax_type} for region {region}, expected {expected_tax_type}"
                    )

            # Validate tax rate ranges
            if region == "EU" and percentage > 27.0:  # Maximum EU VAT rate
                errors.append(f"VAT rate {percentage}% exceeds maximum EU rate of 27%")
            elif region == "UK" and percentage > 20.0:  # UK standard VAT rate
                errors.append(f"VAT rate {percentage}% exceeds UK standard rate of 20%")
            elif region == "CA" and percentage > 15.0:  # Maximum Canadian HST
                errors.append(
                    f"Tax rate {percentage}% exceeds maximum Canadian rate of 15%"
                )

            # Validate country matches region
            region_countries = {
                "US": ["US"],
                "EU": [
                    "AT",
                    "BE",
                    "BG",
                    "HR",
                    "CY",
                    "CZ",
                    "DK",
                    "EE",
                    "FI",
                    "FR",
                    "DE",
                    "GR",
                    "HU",
                    "IE",
                    "IT",
                    "LV",
                    "LT",
                    "LU",
                    "MT",
                    "NL",
                    "PL",
                    "PT",
                    "RO",
                    "SK",
                    "SI",
                    "ES",
                    "SE",
                ],
                "UK": ["GB"],
                "CA": ["CA"],
            }

            if country and region in region_countries:
                if country not in region_countries[region]:
                    errors.append(f"Country {country} not valid for region {region}")

            return {"valid": len(errors) == 0, "errors": errors}

        except Exception as e:
            logger.error(f"Error validating tax rate: {e}")
            return {"valid": False, "errors": [f"Validation error: {str(e)}"]}

    async def _process_regional_tax_rate(
        self,
        tax_rate_data: Dict[str, Any],
        tax_config: Dict[str, Any],
        regional_context: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Process tax rate based on regional requirements."""
        try:
            region = regional_context.get("region")
            tax_behavior = tax_config["tax_behavior"]
            calculation_method = tax_config["calculation_method"]

            processing_result = {
                "tax_behavior": tax_behavior,
                "calculation_method": calculation_method,
                "compliance_requirements": tax_config["compliance_requirements"],
                "processed_at": datetime.utcnow().isoformat(),
            }

            # Region-specific processing
            if region in ["EU", "UK"]:
                # VAT-specific processing
                processing_result.update(
                    {
                        "vat_inclusive": True,
                        "reverse_charge_applicable": tax_rate_data.get("percentage", 0)
                        == 0,
                        "moss_reporting_required": region == "EU",
                    }
                )
            elif region == "US":
                # Sales tax processing
                processing_result.update(
                    {
                        "sales_tax_nexus_check": True,
                        "economic_nexus_applicable": True,
                        "state_specific": tax_rate_data.get("state") is not None,
                    }
                )
            elif region == "CA":
                # GST/HST processing
                processing_result.update(
                    {
                        "gst_applicable": True,
                        "provincial_tax_check": True,
                        "hst_provinces": ["ON", "NB", "NL", "NS", "PE"],
                    }
                )

            return processing_result

        except Exception as e:
            logger.error(f"Error processing regional tax rate: {e}")
            return {"error": str(e)}

    async def _process_successful_tax_calculation(
        self,
        invoice_data: Dict[str, Any],
        tax_config: Dict[str, Any],
        regional_context: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Process successful tax calculation."""
        try:
            region = regional_context.get("region")

            # Extract tax information from invoice
            tax_amounts = invoice_data.get("tax_amounts", [])
            total_tax = sum(tax_amount.get("amount", 0) for tax_amount in tax_amounts)

            result = {
                "total_tax_amount": total_tax,
                "tax_amounts_count": len(tax_amounts),
                "tax_behavior": tax_config["tax_behavior"],
                "calculation_method": tax_config["calculation_method"],
                "region": region,
                "processed_at": datetime.utcnow().isoformat(),
            }

            # Add region-specific tax details
            if region in ["EU", "UK"]:
                result["vat_details"] = {
                    "vat_inclusive": tax_config["tax_behavior"] == "inclusive",
                    "vat_amounts": [
                        ta
                        for ta in tax_amounts
                        if ta.get("tax_rate", {}).get("tax_type") == "vat"
                    ],
                }

            return result

        except Exception as e:
            logger.error(f"Error processing successful tax calculation: {e}")
            return {"error": str(e)}

    async def _handle_failed_tax_calculation(
        self,
        invoice_data: Dict[str, Any],
        tax_config: Dict[str, Any],
        regional_context: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Handle failed tax calculation."""
        try:
            region = regional_context.get("region")

            # Determine fallback tax calculation
            fallback_rate = tax_config["default_rate"]
            invoice_amount = invoice_data.get("amount_due", 0)

            if tax_config["tax_behavior"] == "inclusive":
                # Calculate tax from inclusive amount
                tax_amount = int(invoice_amount * fallback_rate / (100 + fallback_rate))
            else:
                # Calculate tax on exclusive amount
                tax_amount = int(invoice_amount * fallback_rate / 100)

            result = {
                "fallback_calculation": True,
                "fallback_rate": fallback_rate,
                "calculated_tax_amount": tax_amount,
                "original_amount": invoice_amount,
                "tax_behavior": tax_config["tax_behavior"],
                "region": region,
                "requires_manual_review": True,
                "processed_at": datetime.utcnow().isoformat(),
            }

            logger.warning(
                f"Tax calculation failed for invoice {invoice_data.get('id')}, using fallback calculation"
            )

            return result

        except Exception as e:
            logger.error(f"Error handling failed tax calculation: {e}")
            return {"error": str(e)}

    async def _store_tax_rate_compliance_data(
        self,
        tax_rate_data: Dict[str, Any],
        tax_config: Dict[str, Any],
        regional_context: Dict[str, Any],
        processing_result: Dict[str, Any],
    ):
        """Store tax rate compliance data for audit purposes."""
        try:
            # This would integrate with your compliance audit system
            compliance_data = {
                "event_type": "tax_rate_processed",
                "tax_rate_id": tax_rate_data.get("id"),
                "region": regional_context.get("region"),
                "currency": regional_context.get("currency"),
                "tax_config": tax_config,
                "processing_result": processing_result,
                "timestamp": datetime.utcnow().isoformat(),
            }

            logger.info(f"Tax rate compliance data stored: {compliance_data}")

        except Exception as e:
            logger.error(f"Error storing tax rate compliance data: {e}")

    async def _store_tax_calculation_audit(
        self,
        invoice_data: Dict[str, Any],
        success: bool,
        tax_config: Dict[str, Any],
        regional_context: Dict[str, Any],
        result: Dict[str, Any],
    ):
        """Store tax calculation audit data."""
        try:
            # This would integrate with your compliance audit system
            audit_data = {
                "event_type": "tax_calculation_processed",
                "invoice_id": invoice_data.get("id"),
                "calculation_success": success,
                "region": regional_context.get("region"),
                "currency": regional_context.get("currency"),
                "tax_config": tax_config,
                "calculation_result": result,
                "timestamp": datetime.utcnow().isoformat(),
            }

            logger.info(f"Tax calculation audit data stored: {audit_data}")

        except Exception as e:
            logger.error(f"Error storing tax calculation audit data: {e}")


# Global instance
tax_webhook_handler = TaxWebhookHandler()
