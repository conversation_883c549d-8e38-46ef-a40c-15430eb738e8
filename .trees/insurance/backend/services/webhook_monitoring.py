"""
Webhook monitoring and alerting service.

This module provides comprehensive monitoring for webhook deliveries,
including performance metrics, failure tracking, and alerting capabilities.
"""

import asyncio
import logging
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from enum import Enum

from backend.db.supabase_client import get_supabase_client
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class WebhookMetrics:
    """Webhook delivery metrics."""

    total_webhooks: int = 0
    successful_deliveries: int = 0
    failed_deliveries: int = 0
    pending_webhooks: int = 0
    dead_letter_webhooks: int = 0
    average_retry_count: float = 0.0
    max_retry_count: int = 0
    success_rate: float = 0.0
    failure_rate: float = 0.0
    oldest_pending_hours: float = 0.0

    def calculate_rates(self):
        """Calculate success and failure rates."""
        if self.total_webhooks > 0:
            self.success_rate = (self.successful_deliveries / self.total_webhooks) * 100
            self.failure_rate = (self.failed_deliveries / self.total_webhooks) * 100


@dataclass
class WebhookAlert:
    """Webhook alert data structure."""

    id: str
    severity: AlertSeverity
    title: str
    message: str
    metrics: Dict[str, Any]
    created_at: datetime
    resolved_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary."""
        return {
            "id": self.id,
            "severity": self.severity.value,
            "title": self.title,
            "message": self.message,
            "metrics": self.metrics,
            "created_at": self.created_at.isoformat(),
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
        }


class WebhookMonitor:
    """
    Webhook monitoring service with metrics collection and alerting.
    """

    def __init__(self):
        self.supabase = get_supabase_client()
        self._alert_handlers: List[Callable[[WebhookAlert], None]] = []
        self._monitoring_active = False
        self._monitoring_task: Optional[asyncio.Task] = None

        # Alert thresholds
        self.thresholds = {
            "failure_rate_warning": 10.0,  # 10% failure rate
            "failure_rate_critical": 25.0,  # 25% failure rate
            "pending_webhooks_warning": 50,
            "pending_webhooks_critical": 100,
            "oldest_pending_hours_warning": 2.0,  # 2 hours
            "oldest_pending_hours_critical": 6.0,  # 6 hours
            "dead_letter_warning": 10,
            "dead_letter_critical": 25,
        }

        logger.info("WebhookMonitor initialized")

    def add_alert_handler(self, handler: Callable[[WebhookAlert], None]):
        """
        Add an alert handler function.

        Args:
            handler: Function that takes a WebhookAlert and handles it
        """
        self._alert_handlers.append(handler)
        logger.info(f"Added alert handler: {handler.__name__}")

    async def get_webhook_metrics(self, hours_back: int = 24) -> WebhookMetrics:
        """
        Get webhook delivery metrics for the specified time period.

        Args:
            hours_back: Number of hours to look back for metrics

        Returns:
            WebhookMetrics object with current statistics
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)

            # Get webhook statistics
            result = (
                self.supabase.table("webhook_retry_queue")
                .select("status, retry_count, created_at")
                .gte("created_at", cutoff_time.isoformat())
                .execute()
            )

            if result.error:
                logger.error(f"Error fetching webhook metrics: {result.error}")
                return WebhookMetrics()

            webhooks = result.data
            metrics = WebhookMetrics()

            if not webhooks:
                return metrics

            # Calculate metrics
            metrics.total_webhooks = len(webhooks)
            retry_counts = []
            oldest_pending = None

            for webhook in webhooks:
                status = webhook["status"]
                retry_count = webhook.get("retry_count", 0)
                created_at = datetime.fromisoformat(
                    webhook["created_at"].replace("Z", "+00:00")
                )

                retry_counts.append(retry_count)

                if status == "success":
                    metrics.successful_deliveries += 1
                elif status == "failed":
                    metrics.failed_deliveries += 1
                elif status == "pending":
                    metrics.pending_webhooks += 1
                    if oldest_pending is None or created_at < oldest_pending:
                        oldest_pending = created_at
                elif status == "dead_letter":
                    metrics.dead_letter_webhooks += 1

            # Calculate derived metrics
            if retry_counts:
                metrics.average_retry_count = sum(retry_counts) / len(retry_counts)
                metrics.max_retry_count = max(retry_counts)

            if oldest_pending:
                metrics.oldest_pending_hours = (
                    datetime.utcnow() - oldest_pending
                ).total_seconds() / 3600

            metrics.calculate_rates()

            return metrics

        except Exception as e:
            logger.error(f"Error calculating webhook metrics: {e}")
            return WebhookMetrics()

    async def check_webhook_health(self) -> List[WebhookAlert]:
        """
        Check webhook system health and generate alerts if needed.

        Returns:
            List of alerts generated
        """
        alerts = []
        metrics = await self.get_webhook_metrics(hours_back=24)

        try:
            # Check failure rate
            if metrics.failure_rate >= self.thresholds["failure_rate_critical"]:
                alerts.append(
                    WebhookAlert(
                        id=f"failure_rate_critical_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.CRITICAL,
                        title="Critical Webhook Failure Rate",
                        message=f"Webhook failure rate is {metrics.failure_rate:.1f}% (threshold: {self.thresholds['failure_rate_critical']}%)",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )
            elif metrics.failure_rate >= self.thresholds["failure_rate_warning"]:
                alerts.append(
                    WebhookAlert(
                        id=f"failure_rate_warning_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.WARNING,
                        title="High Webhook Failure Rate",
                        message=f"Webhook failure rate is {metrics.failure_rate:.1f}% (threshold: {self.thresholds['failure_rate_warning']}%)",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )

            # Check pending webhooks
            if metrics.pending_webhooks >= self.thresholds["pending_webhooks_critical"]:
                alerts.append(
                    WebhookAlert(
                        id=f"pending_critical_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.CRITICAL,
                        title="Critical Number of Pending Webhooks",
                        message=f"{metrics.pending_webhooks} webhooks pending (threshold: {self.thresholds['pending_webhooks_critical']})",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )
            elif (
                metrics.pending_webhooks >= self.thresholds["pending_webhooks_warning"]
            ):
                alerts.append(
                    WebhookAlert(
                        id=f"pending_warning_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.WARNING,
                        title="High Number of Pending Webhooks",
                        message=f"{metrics.pending_webhooks} webhooks pending (threshold: {self.thresholds['pending_webhooks_warning']})",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )

            # Check oldest pending webhook
            if (
                metrics.oldest_pending_hours
                >= self.thresholds["oldest_pending_hours_critical"]
            ):
                alerts.append(
                    WebhookAlert(
                        id=f"old_pending_critical_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.CRITICAL,
                        title="Very Old Pending Webhooks",
                        message=f"Oldest pending webhook is {metrics.oldest_pending_hours:.1f} hours old (threshold: {self.thresholds['oldest_pending_hours_critical']} hours)",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )
            elif (
                metrics.oldest_pending_hours
                >= self.thresholds["oldest_pending_hours_warning"]
            ):
                alerts.append(
                    WebhookAlert(
                        id=f"old_pending_warning_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.WARNING,
                        title="Old Pending Webhooks",
                        message=f"Oldest pending webhook is {metrics.oldest_pending_hours:.1f} hours old (threshold: {self.thresholds['oldest_pending_hours_warning']} hours)",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )

            # Check dead letter queue
            if metrics.dead_letter_webhooks >= self.thresholds["dead_letter_critical"]:
                alerts.append(
                    WebhookAlert(
                        id=f"dead_letter_critical_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.CRITICAL,
                        title="Critical Number of Dead Letter Webhooks",
                        message=f"{metrics.dead_letter_webhooks} webhooks in dead letter queue (threshold: {self.thresholds['dead_letter_critical']})",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )
            elif metrics.dead_letter_webhooks >= self.thresholds["dead_letter_warning"]:
                alerts.append(
                    WebhookAlert(
                        id=f"dead_letter_warning_{int(datetime.utcnow().timestamp())}",
                        severity=AlertSeverity.WARNING,
                        title="High Number of Dead Letter Webhooks",
                        message=f"{metrics.dead_letter_webhooks} webhooks in dead letter queue (threshold: {self.thresholds['dead_letter_warning']})",
                        metrics=asdict(metrics),
                        created_at=datetime.utcnow(),
                    )
                )

            # Send alerts to handlers
            for alert in alerts:
                await self._send_alert(alert)

            return alerts

        except Exception as e:
            logger.error(f"Error checking webhook health: {e}")
            return []

    async def _send_alert(self, alert: WebhookAlert):
        """Send alert to all registered handlers."""
        for handler in self._alert_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(alert)
                else:
                    handler(alert)
            except Exception as e:
                logger.error(f"Error in alert handler {handler.__name__}: {e}")

    async def start_monitoring(self, check_interval_minutes: int = 5):
        """
        Start continuous webhook monitoring.

        Args:
            check_interval_minutes: Interval between health checks in minutes
        """
        if self._monitoring_active:
            logger.warning("Webhook monitoring is already active")
            return

        self._monitoring_active = True
        logger.info(
            f"Starting webhook monitoring with {check_interval_minutes} minute intervals"
        )

        async def monitoring_loop():
            while self._monitoring_active:
                try:
                    alerts = await self.check_webhook_health()
                    if alerts:
                        logger.info(f"Generated {len(alerts)} webhook alerts")

                    # Log current metrics
                    metrics = await self.get_webhook_metrics()
                    logger.info(
                        f"Webhook metrics - Success rate: {metrics.success_rate:.1f}%, "
                        f"Pending: {metrics.pending_webhooks}, "
                        f"Dead letter: {metrics.dead_letter_webhooks}"
                    )

                except Exception as e:
                    logger.error(f"Error in webhook monitoring loop: {e}")

                await asyncio.sleep(check_interval_minutes * 60)

        self._monitoring_task = asyncio.create_task(monitoring_loop())

    async def stop_monitoring(self):
        """Stop webhook monitoring."""
        if not self._monitoring_active:
            return

        self._monitoring_active = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info("Webhook monitoring stopped")

    def update_thresholds(self, thresholds: Dict[str, float]):
        """
        Update alert thresholds.

        Args:
            thresholds: Dictionary of threshold values to update
        """
        self.thresholds.update(thresholds)
        logger.info(f"Updated webhook monitoring thresholds: {thresholds}")


# Global webhook monitor instance
_webhook_monitor: Optional[WebhookMonitor] = None


def get_webhook_monitor() -> WebhookMonitor:
    """Get the global webhook monitor instance."""
    global _webhook_monitor

    if _webhook_monitor is None:
        _webhook_monitor = WebhookMonitor()

    return _webhook_monitor


# Default alert handlers
async def log_alert_handler(alert: WebhookAlert):
    """Default alert handler that logs alerts."""
    level = {
        AlertSeverity.INFO: logging.INFO,
        AlertSeverity.WARNING: logging.WARNING,
        AlertSeverity.ERROR: logging.ERROR,
        AlertSeverity.CRITICAL: logging.CRITICAL,
    }.get(alert.severity, logging.INFO)

    logger.log(
        level,
        f"WEBHOOK ALERT [{alert.severity.value.upper()}] {alert.title}: {alert.message}",
    )


def setup_default_monitoring():
    """Set up default webhook monitoring with log alerts."""
    monitor = get_webhook_monitor()
    monitor.add_alert_handler(log_alert_handler)
    return monitor
