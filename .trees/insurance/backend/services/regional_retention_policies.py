"""
Regional Data Retention Policies Service

Implements region-specific data retention policies for GDPR (EU) and CCPA (US) compliance
with automatic policy selection based on data residency and user location.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID

from backend.models.data_retention import (
    DataRegion,
    RetentionBasis,
    DataSensitivity,
    RetentionPolicyCreate,
    RetentionPolicyResponse,
    DATA_TYPE_REGISTRY,
)
from backend.services.data_residency import DataRegion as ResidencyRegion
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class RegionalRetentionPolicies:
    """Service for managing region-specific data retention policies."""

    def __init__(self):
        self.gdpr_policies = self._initialize_gdpr_policies()
        self.ccpa_policies = self._initialize_ccpa_policies()
        self.legal_profession_policies = self._initialize_legal_profession_policies()

    def _initialize_gdpr_policies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize GDPR-compliant retention policies for EU region."""
        return {
            # Personal Data (GDPR Art. 5(1)(e) - Storage Limitation)
            "user_identity_data": {
                "retention_days": 2555,  # 7 years
                "legal_basis": RetentionBasis.CONTRACT_PERFORMANCE,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": True,
                "description": "User identity data retained for contract performance and legal obligations",
                "gdpr_article": "Art. 6(1)(b) - Contract performance",
                "deletion_triggers": ["account_closure", "consent_withdrawal"],
                "exceptions": ["legal_obligation", "attorney_client_privilege"],
            },
            "authentication_data": {
                "retention_days": 730,  # 2 years
                "legal_basis": RetentionBasis.SECURITY,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Authentication data for security and fraud prevention",
                "gdpr_article": "Art. 6(1)(f) - Legitimate interest",
                "deletion_triggers": ["security_review"],
                "exceptions": ["active_investigation"],
            },
            # Professional Data
            "attorney_professional_data": {
                "retention_days": 3650,  # 10 years
                "legal_basis": RetentionBasis.PROFESSIONAL_REGULATION,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": False,  # Manual review required
                "description": "Attorney professional data for regulatory compliance",
                "gdpr_article": "Art. 6(1)(c) - Legal obligation",
                "deletion_triggers": ["professional_review"],
                "exceptions": ["bar_investigation", "disciplinary_action"],
            },
            "client_privileged_data": {
                "retention_days": 36500,  # Indefinite (100 years)
                "legal_basis": RetentionBasis.ATTORNEY_CLIENT_PRIVILEGE,
                "sensitivity": DataSensitivity.CRITICAL,
                "auto_delete": False,
                "description": "Attorney-client privileged communications - indefinite retention",
                "gdpr_article": "Art. 6(1)(c) - Legal obligation (professional ethics)",
                "deletion_triggers": [],  # Never auto-delete
                "exceptions": [],
            },
            # Technical Data
            "audit_logs": {
                "retention_days": 2555,  # 7 years
                "legal_basis": RetentionBasis.LEGAL_OBLIGATION,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Audit logs for compliance and security monitoring",
                "gdpr_article": "Art. 6(1)(c) - Legal obligation",
                "deletion_triggers": ["compliance_review"],
                "exceptions": ["ongoing_investigation"],
            },
            "security_events": {
                "retention_days": 90,
                "legal_basis": RetentionBasis.SECURITY,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Security events for incident response and monitoring",
                "gdpr_article": "Art. 6(1)(f) - Legitimate interest",
                "deletion_triggers": ["security_review"],
                "exceptions": ["active_incident"],
            },
            # Communication Data
            "marketing_data": {
                "retention_days": 730,  # 2 years
                "legal_basis": RetentionBasis.CONSENT,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Marketing communications data based on consent",
                "gdpr_article": "Art. 6(1)(a) - Consent",
                "deletion_triggers": ["consent_withdrawal"],
                "exceptions": [],
            },
            "support_communications": {
                "retention_days": 1095,  # 3 years
                "legal_basis": RetentionBasis.CONTRACT_PERFORMANCE,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Support communications for service delivery",
                "gdpr_article": "Art. 6(1)(b) - Contract performance",
                "deletion_triggers": ["service_completion"],
                "exceptions": ["ongoing_support"],
            },
            # Analytics Data
            "usage_analytics": {
                "retention_days": 365,  # 1 year
                "legal_basis": RetentionBasis.LEGITIMATE_INTEREST,
                "sensitivity": DataSensitivity.LOW,
                "auto_delete": True,
                "description": "Usage analytics for service improvement",
                "gdpr_article": "Art. 6(1)(f) - Legitimate interest",
                "deletion_triggers": ["analytics_review"],
                "exceptions": [],
            },
            # Financial Data
            "billing_records": {
                "retention_days": 2555,  # 7 years
                "legal_basis": RetentionBasis.LEGAL_OBLIGATION,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": True,
                "description": "Billing records for tax and accounting obligations",
                "gdpr_article": "Art. 6(1)(c) - Legal obligation",
                "deletion_triggers": ["tax_review"],
                "exceptions": ["tax_audit"],
            },
        }

    def _initialize_ccpa_policies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize CCPA-compliant retention policies for US region."""
        return {
            # Personal Information (CCPA § 1798.140(o))
            "personal_identifiers": {
                "retention_days": 2555,  # 7 years
                "legal_basis": RetentionBasis.BUSINESS_PURPOSE,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": True,
                "description": "Personal identifiers for business purposes",
                "ccpa_category": "Identifiers",
                "deletion_triggers": ["consumer_request", "business_purpose_fulfilled"],
                "exceptions": ["legal_obligation", "attorney_client_privilege"],
            },
            "commercial_information": {
                "retention_days": 2555,  # 7 years
                "legal_basis": RetentionBasis.BUSINESS_PURPOSE,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": True,
                "description": "Commercial information and transaction records",
                "ccpa_category": "Commercial information",
                "deletion_triggers": ["consumer_request"],
                "exceptions": ["tax_obligation"],
            },
            # Professional Information
            "professional_licenses": {
                "retention_days": 3650,  # 10 years
                "legal_basis": RetentionBasis.REGULATORY_COMPLIANCE,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": False,
                "description": "Professional license information for regulatory compliance",
                "ccpa_category": "Professional information",
                "deletion_triggers": ["regulatory_review"],
                "exceptions": ["bar_investigation"],
            },
            "attorney_client_communications": {
                "retention_days": 36500,  # Indefinite
                "legal_basis": RetentionBasis.ATTORNEY_CLIENT_PRIVILEGE,
                "sensitivity": DataSensitivity.CRITICAL,
                "auto_delete": False,
                "description": "Attorney-client privileged communications",
                "ccpa_category": "Professional information",
                "deletion_triggers": [],
                "exceptions": [],
            },
            # Internet Activity
            "internet_activity": {
                "retention_days": 730,  # 2 years
                "legal_basis": RetentionBasis.SECURITY,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Internet activity for security and service improvement",
                "ccpa_category": "Internet or other electronic network activity",
                "deletion_triggers": ["consumer_request"],
                "exceptions": ["security_investigation"],
            },
            # Geolocation Data
            "geolocation_data": {
                "retention_days": 90,
                "legal_basis": RetentionBasis.BUSINESS_PURPOSE,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Geolocation data for service provision",
                "ccpa_category": "Geolocation data",
                "deletion_triggers": ["consumer_request", "service_completion"],
                "exceptions": [],
            },
            # Biometric Information
            "device_fingerprints": {
                "retention_days": 365,  # 1 year
                "legal_basis": RetentionBasis.SECURITY,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "Device fingerprints for fraud prevention",
                "ccpa_category": "Biometric information",
                "deletion_triggers": ["consumer_request"],
                "exceptions": ["fraud_investigation"],
            },
        }

    def _initialize_legal_profession_policies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize legal profession-specific retention policies."""
        return {
            # Client Files (ABA Model Rule 1.15)
            "client_matters": {
                "retention_days": 36500,  # Indefinite
                "legal_basis": RetentionBasis.ATTORNEY_CLIENT_PRIVILEGE,
                "sensitivity": DataSensitivity.CRITICAL,
                "auto_delete": False,
                "description": "Client matter files and case documents",
                "professional_rule": "ABA Model Rule 1.15",
                "deletion_triggers": [],
                "exceptions": [],
            },
            # Trust Account Records (ABA Model Rule 1.15)
            "trust_account_records": {
                "retention_days": 2555,  # 7 years
                "legal_basis": RetentionBasis.PROFESSIONAL_REGULATION,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": False,
                "description": "Trust account records for bar compliance",
                "professional_rule": "ABA Model Rule 1.15",
                "deletion_triggers": ["bar_review"],
                "exceptions": ["disciplinary_investigation"],
            },
            # Continuing Legal Education
            "cle_records": {
                "retention_days": 1825,  # 5 years
                "legal_basis": RetentionBasis.PROFESSIONAL_REGULATION,
                "sensitivity": DataSensitivity.MEDIUM,
                "auto_delete": True,
                "description": "CLE records for bar compliance",
                "professional_rule": "State Bar CLE Requirements",
                "deletion_triggers": ["compliance_review"],
                "exceptions": ["bar_audit"],
            },
            # Malpractice Insurance
            "malpractice_insurance": {
                "retention_days": 3650,  # 10 years
                "legal_basis": RetentionBasis.PROFESSIONAL_REGULATION,
                "sensitivity": DataSensitivity.HIGH,
                "auto_delete": False,
                "description": "Malpractice insurance records",
                "professional_rule": "Professional Liability Requirements",
                "deletion_triggers": ["insurance_review"],
                "exceptions": ["malpractice_claim"],
            },
        }

    def get_retention_policy(
        self, data_type: str, region: DataRegion
    ) -> Optional[Dict[str, Any]]:
        """
        Get the appropriate retention policy for a data type and region.

        Args:
            data_type: Type of data
            region: Data region (US, EU, GLOBAL)

        Returns:
            Retention policy configuration or None if not found
        """
        try:
            # Check legal profession policies first (apply to all regions)
            if data_type in self.legal_profession_policies:
                return self.legal_profession_policies[data_type]

            # Check region-specific policies
            if region == DataRegion.EU:
                return self.gdpr_policies.get(data_type)
            elif region == DataRegion.US:
                return self.ccpa_policies.get(data_type)
            else:
                # For GLOBAL, use the most restrictive policy
                gdpr_policy = self.gdpr_policies.get(data_type)
                ccpa_policy = self.ccpa_policies.get(data_type)

                if gdpr_policy and ccpa_policy:
                    # Use the longer retention period (more restrictive)
                    if gdpr_policy["retention_days"] >= ccpa_policy["retention_days"]:
                        return gdpr_policy
                    else:
                        return ccpa_policy

                return gdpr_policy or ccpa_policy

        except Exception as e:
            logger.error(
                f"Error getting retention policy for {data_type} in {region}: {e}"
            )
            return None

    def create_retention_policy_request(
        self, data_type: str, region: DataRegion
    ) -> Optional[RetentionPolicyCreate]:
        """
        Create a retention policy request for a data type and region.

        Args:
            data_type: Type of data
            region: Data region

        Returns:
            RetentionPolicyCreate object or None if policy not found
        """
        policy_config = self.get_retention_policy(data_type, region)
        if not policy_config:
            return None

        try:
            return RetentionPolicyCreate(
                data_type=data_type,
                region=region,
                retention_period_days=policy_config["retention_days"],
                legal_basis=policy_config["legal_basis"],
                sensitivity_level=policy_config["sensitivity"],
                auto_delete=policy_config["auto_delete"],
                description=policy_config["description"],
                metadata={
                    "deletion_triggers": policy_config.get("deletion_triggers", []),
                    "exceptions": policy_config.get("exceptions", []),
                    "gdpr_article": policy_config.get("gdpr_article"),
                    "ccpa_category": policy_config.get("ccpa_category"),
                    "professional_rule": policy_config.get("professional_rule"),
                },
            )
        except Exception as e:
            logger.error(
                f"Error creating retention policy request for {data_type}: {e}"
            )
            return None

    def get_all_policies_for_region(
        self, region: DataRegion
    ) -> List[RetentionPolicyCreate]:
        """
        Get all retention policies for a specific region.

        Args:
            region: Data region

        Returns:
            List of retention policy requests
        """
        policies = []

        # Get region-specific policies
        if region == DataRegion.EU:
            policy_dict = self.gdpr_policies
        elif region == DataRegion.US:
            policy_dict = self.ccpa_policies
        else:
            # For GLOBAL, combine all policies
            policy_dict = {**self.gdpr_policies, **self.ccpa_policies}

        # Add legal profession policies (apply to all regions)
        policy_dict.update(self.legal_profession_policies)

        for data_type in policy_dict:
            policy_request = self.create_retention_policy_request(data_type, region)
            if policy_request:
                policies.append(policy_request)

        return policies

    def validate_policy_compliance(
        self, data_type: str, region: DataRegion, current_retention_days: int
    ) -> Tuple[bool, List[str]]:
        """
        Validate if current retention period complies with regional requirements.

        Args:
            data_type: Type of data
            region: Data region
            current_retention_days: Current retention period in days

        Returns:
            Tuple of (is_compliant, list_of_violations)
        """
        policy_config = self.get_retention_policy(data_type, region)
        if not policy_config:
            return False, [f"No retention policy found for {data_type} in {region}"]

        violations = []
        required_days = policy_config["retention_days"]

        # Check if retention period is appropriate
        if current_retention_days > required_days:
            if policy_config["legal_basis"] != RetentionBasis.ATTORNEY_CLIENT_PRIVILEGE:
                violations.append(
                    f"Retention period {current_retention_days} days exceeds "
                    f"maximum allowed {required_days} days for {data_type}"
                )

        if current_retention_days < required_days:
            if policy_config["legal_basis"] in [
                RetentionBasis.LEGAL_OBLIGATION,
                RetentionBasis.PROFESSIONAL_REGULATION,
            ]:
                violations.append(
                    f"Retention period {current_retention_days} days is below "
                    f"minimum required {required_days} days for {data_type}"
                )

        return len(violations) == 0, violations

    def get_policy_summary(self, region: DataRegion) -> Dict[str, Any]:
        """
        Get a summary of retention policies for a region.

        Args:
            region: Data region

        Returns:
            Policy summary with statistics and compliance information
        """
        policies = self.get_all_policies_for_region(region)

        summary = {
            "region": region.value,
            "total_policies": len(policies),
            "auto_delete_policies": sum(1 for p in policies if p.auto_delete),
            "manual_review_policies": sum(1 for p in policies if not p.auto_delete),
            "sensitivity_breakdown": {
                "critical": sum(
                    1
                    for p in policies
                    if p.sensitivity_level == DataSensitivity.CRITICAL
                ),
                "high": sum(
                    1 for p in policies if p.sensitivity_level == DataSensitivity.HIGH
                ),
                "medium": sum(
                    1 for p in policies if p.sensitivity_level == DataSensitivity.MEDIUM
                ),
                "low": sum(
                    1 for p in policies if p.sensitivity_level == DataSensitivity.LOW
                ),
            },
            "legal_basis_breakdown": {},
            "average_retention_days": 0,
        }

        # Calculate legal basis breakdown
        for policy in policies:
            basis = policy.legal_basis.value
            summary["legal_basis_breakdown"][basis] = (
                summary["legal_basis_breakdown"].get(basis, 0) + 1
            )

        # Calculate average retention (excluding indefinite)
        finite_policies = [p for p in policies if p.retention_period_days < 36500]
        if finite_policies:
            summary["average_retention_days"] = sum(
                p.retention_period_days for p in finite_policies
            ) / len(finite_policies)

        return summary


# Global instance
regional_retention_policies = RegionalRetentionPolicies()
