"""
Email notification service using Resend.

This module provides email sending functionality for deadline alerts,
morning briefings, and other notifications.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

import resend
from jinja2 import Environment, FileSystemLoader, select_autoescape

logger = logging.getLogger(__name__)

# Configure Resend
resend.api_key = os.getenv("RESEND_API_KEY")
DEFAULT_FROM_EMAIL = os.getenv("RESEND_FROM_EMAIL", "<EMAIL>")


class EmailService:
    """Service for sending emails via Resend."""

    def __init__(self):
        """Initialize the email service."""
        self.from_email = DEFAULT_FROM_EMAIL

        # Set up Jinja2 for email templates
        template_dir = os.path.join(os.path.dirname(__file__), "templates")
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(["html", "xml"]),
        )

    async def send_deadline_alert(
        self,
        recipient_email: str,
        deadline_data: Dict[str, Any],
        alert_type: str = "critical_deadline",
    ) -> Dict[str, Any]:
        """
        Send a deadline alert email.

        Args:
            recipient_email: Email address of the recipient
            deadline_data: Deadline information
            alert_type: Type of alert (critical_deadline, conflict, etc.)

        Returns:
            Dict[str, Any]: Send result with status and message ID
        """
        try:
            # Prepare email content
            subject = self._get_deadline_subject(deadline_data, alert_type)
            html_content = self._render_deadline_template(deadline_data, alert_type)
            text_content = self._get_deadline_text(deadline_data, alert_type)

            # Send email
            result = await self._send_email(
                to=recipient_email,
                subject=subject,
                html=html_content,
                text=text_content,
            )

            logger.info(
                f"Deadline alert email sent to {recipient_email}: {result.get('id')}"
            )
            return result

        except Exception as e:
            logger.error(f"Error sending deadline alert email: {e}")
            raise

    async def send_morning_briefing(
        self, recipient_email: str, briefing_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Send a morning briefing email.

        Args:
            recipient_email: Email address of the recipient
            briefing_data: Morning briefing information

        Returns:
            Dict[str, Any]: Send result with status and message ID
        """
        try:
            # Prepare email content
            subject = f"Morning Briefing - {datetime.now().strftime('%B %d, %Y')}"
            html_content = self._render_briefing_template(briefing_data)
            text_content = self._get_briefing_text(briefing_data)

            # Send email
            result = await self._send_email(
                to=recipient_email,
                subject=subject,
                html=html_content,
                text=text_content,
            )

            logger.info(
                f"Morning briefing email sent to {recipient_email}: {result.get('id')}"
            )
            return result

        except Exception as e:
            logger.error(f"Error sending morning briefing email: {e}")
            raise

    async def send_user_return_alert(
        self, recipient_email: str, insights_data: Dict[str, Any], hours_away: int
    ) -> Dict[str, Any]:
        """
        Send a user return alert email.

        Args:
            recipient_email: Email address of the recipient
            insights_data: User return insights
            hours_away: Number of hours user was away

        Returns:
            Dict[str, Any]: Send result with status and message ID
        """
        try:
            # Prepare email content
            subject = f"Welcome back! Here's what happened while you were away"
            html_content = self._render_return_template(insights_data, hours_away)
            text_content = self._get_return_text(insights_data, hours_away)

            # Send email
            result = await self._send_email(
                to=recipient_email,
                subject=subject,
                html=html_content,
                text=text_content,
            )

            logger.info(
                f"User return email sent to {recipient_email}: {result.get('id')}"
            )
            return result

        except Exception as e:
            logger.error(f"Error sending user return email: {e}")
            raise

    async def _send_email(
        self,
        to: str,
        subject: str,
        html: str,
        text: str,
        reply_to: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Send an email using Resend.

        Args:
            to: Recipient email address
            subject: Email subject
            html: HTML content
            text: Plain text content
            reply_to: Optional reply-to address

        Returns:
            Dict[str, Any]: Send result
        """
        try:
            params = {
                "from": self.from_email,
                "to": [to],
                "subject": subject,
                "html": html,
                "text": text,
            }

            if reply_to:
                params["reply_to"] = [reply_to]

            # Send via Resend
            result = resend.Emails.send(params)

            return {"success": True, "message_id": result.get("id"), "status": "sent"}

        except Exception as e:
            logger.error(f"Resend API error: {e}")
            return {"success": False, "error": str(e), "status": "failed"}

    def _get_deadline_subject(
        self, deadline_data: Dict[str, Any], alert_type: str
    ) -> str:
        """Generate subject line for deadline alert."""
        if alert_type == "critical_deadline":
            return f"🚨 CRITICAL DEADLINE: {deadline_data.get('title', 'Important Deadline')}"
        elif alert_type == "conflict":
            return f"⚠️ DEADLINE CONFLICT: {deadline_data.get('title', 'Schedule Conflict')}"
        else:
            return f"📅 Deadline Alert: {deadline_data.get('title', 'Deadline Update')}"

    def _render_deadline_template(
        self, deadline_data: Dict[str, Any], alert_type: str
    ) -> str:
        """Render HTML template for deadline alert."""
        try:
            template = self.jinja_env.get_template("deadline_alert.html")
            return template.render(
                deadline=deadline_data,
                alert_type=alert_type,
                current_date=datetime.now().strftime("%B %d, %Y"),
            )
        except Exception as e:
            logger.warning(f"Template rendering failed, using fallback: {e}")
            return self._get_deadline_fallback_html(deadline_data, alert_type)

    def _get_deadline_text(self, deadline_data: Dict[str, Any], alert_type: str) -> str:
        """Generate plain text content for deadline alert."""
        priority_emoji = "🚨" if alert_type == "critical_deadline" else "📅"

        return f"""
{priority_emoji} DEADLINE ALERT

{deadline_data.get('title', 'Important Deadline')}

Due: {deadline_data.get('due_date', 'Soon')}
Priority: {deadline_data.get('priority', 'High')}

{deadline_data.get('description', 'Please review this deadline immediately.')}

Action Required: {deadline_data.get('action_required', 'Review and take appropriate action')}

---
This is an automated message from AiLex Legal Assistant.
        """.strip()

    def _get_deadline_fallback_html(
        self, deadline_data: Dict[str, Any], alert_type: str
    ) -> str:
        """Fallback HTML content when template rendering fails."""
        priority_color = "#dc2626" if alert_type == "critical_deadline" else "#2563eb"

        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h1 style="color: {priority_color};">Deadline Alert</h1>
                <h2>{deadline_data.get('title', 'Important Deadline')}</h2>
                <p><strong>Due:</strong> {deadline_data.get('due_date', 'Soon')}</p>
                <p><strong>Priority:</strong> {deadline_data.get('priority', 'High')}</p>
                <p>{deadline_data.get('description', 'Please review this deadline immediately.')}</p>
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <strong>Action Required:</strong> {deadline_data.get('action_required', 'Review and take appropriate action')}
                </div>
                <hr>
                <p style="font-size: 12px; color: #666;">
                    This is an automated message from AiLex Legal Assistant.
                </p>
            </div>
        </body>
        </html>
        """

    def _render_briefing_template(self, briefing_data: Dict[str, Any]) -> str:
        """Render HTML template for morning briefing."""
        # Simplified HTML for now - would use Jinja2 template in production
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif;">
            <h1>Good Morning! ☀️</h1>
            <h2>Today's Briefing</h2>
            <p>Critical Deadlines: {briefing_data.get('today_summary', {}).get('critical_deadlines', 0)}</p>
            <p>Scheduled Tasks: {briefing_data.get('today_summary', {}).get('scheduled_tasks', 0)}</p>
            <!-- More briefing content would go here -->
        </body>
        </html>
        """

    def _get_briefing_text(self, briefing_data: Dict[str, Any]) -> str:
        """Generate plain text content for morning briefing."""
        return f"""
Good Morning! ☀️

Today's Briefing:
- Critical Deadlines: {briefing_data.get('today_summary', {}).get('critical_deadlines', 0)}
- Scheduled Tasks: {briefing_data.get('today_summary', {}).get('scheduled_tasks', 0)}

Have a productive day!
        """.strip()

    def _render_return_template(
        self, insights_data: Dict[str, Any], hours_away: int
    ) -> str:
        """Render HTML template for user return alert."""
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif;">
            <h1>Welcome Back! 👋</h1>
            <p>You've been away for {hours_away} hour(s).</p>
            <!-- Return insights content would go here -->
        </body>
        </html>
        """

    def _get_return_text(self, insights_data: Dict[str, Any], hours_away: int) -> str:
        """Generate plain text content for user return alert."""
        return f"""
Welcome Back! 👋

You've been away for {hours_away} hour(s).

Here's what you need to know:
- Check your deadline insights for updates
- Review any new critical deadlines

Welcome back to AiLex!
        """.strip()


# Global email service instance
email_service = EmailService()
