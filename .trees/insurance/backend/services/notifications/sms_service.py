"""
SMS notification service using Twilio.

This module provides SMS sending functionality for critical deadline alerts
and urgent notifications.
"""

import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from twilio.rest import Client

    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False
    Client = None

logger = logging.getLogger(__name__)

# Twilio configuration
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER")


class SMSService:
    """Service for sending SMS notifications via Twilio."""

    def __init__(self):
        """Initialize the SMS service."""
        self.client = None
        self.from_number = TWILIO_PHONE_NUMBER

        if TWILIO_AVAILABLE and TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN:
            try:
                self.client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
                logger.info("Twilio SMS service initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Twilio client: {e}")
        else:
            logger.warning(
                "Twilio SMS service not available - missing credentials or package"
            )

    def is_available(self) -> bool:
        """Check if SMS service is available and configured."""
        return self.client is not None and self.from_number is not None

    async def send_critical_deadline_alert(
        self, phone_number: str, deadline_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Send a critical deadline alert via SMS.

        Args:
            phone_number: Recipient phone number (E.164 format)
            deadline_data: Deadline information

        Returns:
            Dict[str, Any]: Send result with status and message ID
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "SMS service not available",
                "status": "service_unavailable",
            }

        try:
            # Format message for SMS (160 character limit consideration)
            message = self._format_critical_deadline_message(deadline_data)

            # Send SMS
            result = await self._send_sms(phone_number, message)

            logger.info(
                f"Critical deadline SMS sent to {phone_number}: {result.get('message_id')}"
            )
            return result

        except Exception as e:
            logger.error(f"Error sending critical deadline SMS: {e}")
            return {"success": False, "error": str(e), "status": "failed"}

    async def send_urgent_notification(
        self, phone_number: str, message: str, notification_type: str = "urgent"
    ) -> Dict[str, Any]:
        """
        Send an urgent notification via SMS.

        Args:
            phone_number: Recipient phone number (E.164 format)
            message: Message content
            notification_type: Type of notification

        Returns:
            Dict[str, Any]: Send result with status and message ID
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "SMS service not available",
                "status": "service_unavailable",
            }

        try:
            # Truncate message if too long for SMS
            formatted_message = self._format_urgent_message(message, notification_type)

            # Send SMS
            result = await self._send_sms(phone_number, formatted_message)

            logger.info(
                f"Urgent SMS sent to {phone_number}: {result.get('message_id')}"
            )
            return result

        except Exception as e:
            logger.error(f"Error sending urgent SMS: {e}")
            return {"success": False, "error": str(e), "status": "failed"}

    async def _send_sms(self, to_number: str, message: str) -> Dict[str, Any]:
        """
        Send an SMS using Twilio.

        Args:
            to_number: Recipient phone number
            message: Message content

        Returns:
            Dict[str, Any]: Send result
        """
        try:
            # Validate phone number format
            if not to_number.startswith("+"):
                # Assume US number if no country code
                to_number = f"+1{to_number.replace('-', '').replace('(', '').replace(')', '').replace(' ', '')}"

            # Send via Twilio
            message_obj = self.client.messages.create(
                body=message, from_=self.from_number, to=to_number
            )

            return {
                "success": True,
                "message_id": message_obj.sid,
                "status": "sent",
                "to": to_number,
                "from": self.from_number,
            }

        except Exception as e:
            logger.error(f"Twilio API error: {e}")
            return {"success": False, "error": str(e), "status": "failed"}

    def _format_critical_deadline_message(self, deadline_data: Dict[str, Any]) -> str:
        """
        Format a critical deadline message for SMS.

        SMS messages should be concise due to character limits.
        """
        title = deadline_data.get("title", "Critical Deadline")[
            :50
        ]  # Truncate long titles
        due_date = deadline_data.get("due_date", "Soon")

        # Try to parse and format date nicely
        try:
            if due_date != "Soon":
                parsed_date = datetime.fromisoformat(due_date.replace("Z", "+00:00"))
                due_date = parsed_date.strftime("%m/%d %I:%M%p")
        except:
            pass

        message = f"🚨 CRITICAL DEADLINE\n{title}\nDue: {due_date}\n\nReview immediately in AiLex app."

        # Ensure message fits in SMS limit (160 chars for single SMS)
        if len(message) > 160:
            # Truncate title further if needed
            title = title[:30] + "..."
            message = f"🚨 CRITICAL DEADLINE\n{title}\nDue: {due_date}\n\nCheck AiLex app now."

        return message

    def _format_urgent_message(self, message: str, notification_type: str) -> str:
        """
        Format an urgent message for SMS.
        """
        prefix = "🚨 URGENT: " if notification_type == "urgent" else "⚠️ ALERT: "

        # Calculate available space for message content
        suffix = "\n\nCheck AiLex app for details."
        available_chars = 160 - len(prefix) - len(suffix)

        # Truncate message if necessary
        if len(message) > available_chars:
            message = message[: available_chars - 3] + "..."

        return f"{prefix}{message}{suffix}"

    def validate_phone_number(self, phone_number: str) -> Dict[str, Any]:
        """
        Validate a phone number format.

        Args:
            phone_number: Phone number to validate

        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            # Basic validation - in production, use Twilio's lookup API
            cleaned = (
                phone_number.replace("-", "")
                .replace("(", "")
                .replace(")", "")
                .replace(" ", "")
            )

            if cleaned.startswith("+1"):
                cleaned = cleaned[2:]
            elif cleaned.startswith("1") and len(cleaned) == 11:
                cleaned = cleaned[1:]

            if len(cleaned) != 10 or not cleaned.isdigit():
                return {"valid": False, "error": "Invalid US phone number format"}

            formatted = f"+1{cleaned}"

            return {"valid": True, "formatted": formatted, "original": phone_number}

        except Exception as e:
            return {"valid": False, "error": str(e)}


# Alternative SMS service for Telnyx
class TelnyxSMSService:
    """Alternative SMS service using Telnyx."""

    def __init__(self):
        """Initialize Telnyx SMS service."""
        self.api_key = os.getenv("TELNYX_API_KEY")
        self.from_number = os.getenv("TELNYX_PHONE_NUMBER")

        if self.api_key and self.from_number:
            logger.info("Telnyx SMS service initialized successfully")
        else:
            logger.warning("Telnyx SMS service not available - missing credentials")

    def is_available(self) -> bool:
        """Check if Telnyx SMS service is available."""
        return self.api_key is not None and self.from_number is not None

    async def send_sms(self, to_number: str, message: str) -> Dict[str, Any]:
        """
        Send SMS via Telnyx API.

        This is a placeholder - would need to implement actual Telnyx API calls.
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Telnyx service not available",
                "status": "service_unavailable",
            }

        # TODO: Implement actual Telnyx API integration
        logger.info(f"Would send SMS via Telnyx to {to_number}: {message}")

        return {
            "success": True,
            "message_id": f"telnyx_{datetime.now().timestamp()}",
            "status": "sent",
            "provider": "telnyx",
        }


# Factory function to get the appropriate SMS service
def get_sms_service() -> SMSService:
    """
    Get the configured SMS service.

    Returns:
        SMSService: Configured SMS service instance
    """
    # Try Twilio first
    twilio_service = SMSService()
    if twilio_service.is_available():
        return twilio_service

    # Fall back to Telnyx if available
    telnyx_service = TelnyxSMSService()
    if telnyx_service.is_available():
        logger.info("Using Telnyx SMS service as fallback")
        # Would return a wrapper that implements the same interface

    # Return disabled service if no providers available
    logger.warning("No SMS service providers available")
    return twilio_service  # Returns with is_available() = False


# Global SMS service instance
sms_service = get_sms_service()
