"""
Compliance Audit Logging for Data Residency

This module provides comprehensive audit logging for data residency compliance,
GDPR/CCPA requirements, and regulatory reporting.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum
from uuid import uuid4

from backend.services.data_residency import DataRegion

logger = logging.getLogger(__name__)


class AuditEventType(str, Enum):
    """Types of audit events for compliance tracking."""

    REGION_ROUTING = "region_routing"
    DATA_ACCESS = "data_access"
    DATA_TRANSFER = "data_transfer"
    USER_PREFERENCE_CHANGE = "user_preference_change"
    CONSENT_CHANGE = "consent_change"
    REQUEST_COMPLETED = "request_completed"
    COMPLIANCE_VIOLATION = "compliance_violation"
    DPA_VERIFICATION = "dpa_verification"


class ComplianceAuditLogger:
    """Service for logging compliance-related events."""

    def __init__(self):
        self.audit_logger = logging.getLogger("compliance_audit")
        self.audit_logger.setLevel(logging.INFO)

        # Ensure audit logs are structured and persistent
        if not self.audit_logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - AUDIT - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            self.audit_logger.addHandler(handler)

    async def log_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        region: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> str:
        """
        Log a compliance audit event.

        Args:
            event_type: Type of audit event
            user_id: User ID associated with the event
            region: Data region involved
            metadata: Additional event metadata
            **kwargs: Additional event data

        Returns:
            Audit event ID for tracking
        """
        event_id = str(uuid4())
        timestamp = datetime.now(timezone.utc).isoformat()

        audit_record = {
            "event_id": event_id,
            "event_type": event_type.value,
            "timestamp": timestamp,
            "user_id": user_id,
            "region": region,
            "metadata": metadata or {},
            **kwargs,
        }

        # Log structured audit record
        self.audit_logger.info(
            f"COMPLIANCE_EVENT: {event_type.value}",
            extra={"audit_record": audit_record, "compliance_event": True},
        )

        # Store in database for compliance reporting
        await self._store_audit_record(audit_record)

        return event_id

    async def _store_audit_record(self, audit_record: Dict[str, Any]) -> None:
        """Store audit record in database for compliance reporting."""
        try:
            # This would typically store in a dedicated audit table
            # For now, we'll use structured logging
            logger.info(
                "Audit record stored",
                extra={
                    "audit_event_id": audit_record["event_id"],
                    "event_type": audit_record["event_type"],
                },
            )
        except Exception as e:
            logger.error(
                f"Failed to store audit record: {str(e)}",
                extra={"audit_record": audit_record},
            )


class DataResidencyComplianceTracker:
    """Tracker for data residency compliance metrics."""

    def __init__(self):
        self.audit_logger = ComplianceAuditLogger()

    async def track_region_routing(
        self,
        user_id: Optional[str],
        detected_region: DataRegion,
        final_region: DataRegion,
        detection_method: List[str],
        request_path: str,
    ) -> None:
        """Track regional routing decisions."""
        metadata = {
            "detected_region": detected_region.value,
            "final_region": final_region.value,
            "detection_method": detection_method,
            "request_path": request_path,
            "routing_override": detected_region != final_region,
        }

        await self.audit_logger.log_event(
            event_type=AuditEventType.REGION_ROUTING,
            user_id=user_id,
            region=final_region.value,
            metadata=metadata,
        )

    async def track_data_access(
        self,
        user_id: str,
        region: DataRegion,
        data_type: str,
        operation: str,
        table_name: Optional[str] = None,
    ) -> None:
        """Track data access events for compliance."""
        metadata = {
            "data_type": data_type,
            "operation": operation,
            "table_name": table_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        await self.audit_logger.log_event(
            event_type=AuditEventType.DATA_ACCESS,
            user_id=user_id,
            region=region.value,
            metadata=metadata,
        )

    async def track_cross_region_transfer(
        self,
        user_id: str,
        source_region: DataRegion,
        target_region: DataRegion,
        data_type: str,
        legal_basis: str,
    ) -> None:
        """Track cross-region data transfers."""
        metadata = {
            "source_region": source_region.value,
            "target_region": target_region.value,
            "data_type": data_type,
            "legal_basis": legal_basis,
            "transfer_mechanism": "DPA_covered",
        }

        await self.audit_logger.log_event(
            event_type=AuditEventType.DATA_TRANSFER,
            user_id=user_id,
            region=target_region.value,
            metadata=metadata,
        )

    async def track_preference_change(
        self,
        user_id: str,
        old_region: Optional[DataRegion],
        new_region: DataRegion,
        change_reason: str,
    ) -> None:
        """Track user data residency preference changes."""
        metadata = {
            "old_region": old_region.value if old_region else None,
            "new_region": new_region.value,
            "change_reason": change_reason,
            "requires_data_migration": (
                old_region != new_region if old_region else False
            ),
        }

        await self.audit_logger.log_event(
            event_type=AuditEventType.USER_PREFERENCE_CHANGE,
            user_id=user_id,
            region=new_region.value,
            metadata=metadata,
        )

    async def track_compliance_violation(
        self,
        violation_type: str,
        description: str,
        user_id: Optional[str] = None,
        region: Optional[DataRegion] = None,
        severity: str = "medium",
    ) -> None:
        """Track compliance violations for investigation."""
        metadata = {
            "violation_type": violation_type,
            "description": description,
            "severity": severity,
            "requires_investigation": True,
        }

        await self.audit_logger.log_event(
            event_type=AuditEventType.COMPLIANCE_VIOLATION,
            user_id=user_id,
            region=region.value if region else None,
            metadata=metadata,
        )


# Global instances
audit_logger = ComplianceAuditLogger()
compliance_tracker = DataResidencyComplianceTracker()


# Convenience functions for common audit events
async def log_data_residency_event(
    event_type: str,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log a data residency compliance event."""
    try:
        event_enum = AuditEventType(event_type)
    except ValueError:
        logger.warning(f"Unknown audit event type: {event_type}")
        event_enum = AuditEventType.DATA_ACCESS

    return await audit_logger.log_event(
        event_type=event_enum,
        user_id=user_id,
        region=region,
        metadata=metadata,
        **kwargs,
    )


async def log_region_routing(
    user_id: Optional[str],
    detected_region: DataRegion,
    final_region: DataRegion,
    detection_method: List[str],
    request_path: str,
) -> None:
    """Log regional routing decision."""
    await compliance_tracker.track_region_routing(
        user_id=user_id,
        detected_region=detected_region,
        final_region=final_region,
        detection_method=detection_method,
        request_path=request_path,
    )


async def log_data_access(
    user_id: str,
    region: DataRegion,
    data_type: str,
    operation: str,
    table_name: Optional[str] = None,
) -> None:
    """Log data access for compliance tracking."""
    await compliance_tracker.track_data_access(
        user_id=user_id,
        region=region,
        data_type=data_type,
        operation=operation,
        table_name=table_name,
    )
