"""
Compliance Audit Configuration

Configuration management for the comprehensive compliance audit system
including environment variables, buffer settings, and retention policies.
"""

import os
import logging
from typing import Optional
from dataclasses import dataclass

# Use basic logging to avoid import issues
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ComplianceAuditConfig:
    """Configuration for compliance audit system."""

    # Core settings
    enabled: bool = True
    log_level: str = "INFO"

    # Buffer and processing settings
    buffer_size: int = 100
    flush_interval: int = 60  # seconds
    batch_size: int = 50

    # Retention settings
    retention_days: int = 2555  # 7 years for legal compliance

    # Performance settings
    max_concurrent_flushes: int = 3
    flush_timeout: int = 30  # seconds

    # Database settings
    connection_pool_size: int = 5
    connection_timeout: int = 10  # seconds

    # Monitoring settings
    health_check_interval: int = 300  # 5 minutes
    metrics_enabled: bool = True

    @classmethod
    def from_environment(cls) -> "ComplianceAuditConfig":
        """Create configuration from environment variables."""
        try:
            return cls(
                enabled=os.getenv("COMPLIANCE_AUDIT_ENABLED", "true").lower() == "true",
                log_level=os.getenv("COMPLIANCE_AUDIT_LOG_LEVEL", "INFO").upper(),
                buffer_size=int(os.getenv("COMPLIANCE_AUDIT_BUFFER_SIZE", "100")),
                flush_interval=int(os.getenv("COMPLIANCE_AUDIT_FLUSH_INTERVAL", "60")),
                batch_size=int(os.getenv("COMPLIANCE_AUDIT_BATCH_SIZE", "50")),
                retention_days=int(
                    os.getenv("COMPLIANCE_AUDIT_RETENTION_DAYS", "2555")
                ),
                max_concurrent_flushes=int(
                    os.getenv("COMPLIANCE_AUDIT_MAX_CONCURRENT_FLUSHES", "3")
                ),
                flush_timeout=int(os.getenv("COMPLIANCE_AUDIT_FLUSH_TIMEOUT", "30")),
                connection_pool_size=int(
                    os.getenv("COMPLIANCE_AUDIT_CONNECTION_POOL_SIZE", "5")
                ),
                connection_timeout=int(
                    os.getenv("COMPLIANCE_AUDIT_CONNECTION_TIMEOUT", "10")
                ),
                health_check_interval=int(
                    os.getenv("COMPLIANCE_AUDIT_HEALTH_CHECK_INTERVAL", "300")
                ),
                metrics_enabled=os.getenv(
                    "COMPLIANCE_AUDIT_METRICS_ENABLED", "true"
                ).lower()
                == "true",
            )
        except (ValueError, TypeError) as e:
            logger.warning(
                f"Error parsing compliance audit configuration: {e}. Using defaults."
            )
            return cls()

    def validate(self) -> bool:
        """Validate configuration values."""
        try:
            # Validate buffer settings
            if self.buffer_size <= 0:
                logger.error("Buffer size must be positive")
                return False

            if self.flush_interval <= 0:
                logger.error("Flush interval must be positive")
                return False

            if self.batch_size <= 0 or self.batch_size > self.buffer_size:
                logger.error("Batch size must be positive and not exceed buffer size")
                return False

            # Validate retention settings
            if self.retention_days <= 0:
                logger.error("Retention days must be positive")
                return False

            # Validate performance settings
            if self.max_concurrent_flushes <= 0:
                logger.error("Max concurrent flushes must be positive")
                return False

            if self.flush_timeout <= 0:
                logger.error("Flush timeout must be positive")
                return False

            # Validate database settings
            if self.connection_pool_size <= 0:
                logger.error("Connection pool size must be positive")
                return False

            if self.connection_timeout <= 0:
                logger.error("Connection timeout must be positive")
                return False

            # Validate monitoring settings
            if self.health_check_interval <= 0:
                logger.error("Health check interval must be positive")
                return False

            return True

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

    def get_log_level(self) -> int:
        """Get numeric log level."""
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL,
        }
        return level_map.get(self.log_level, logging.INFO)

    def to_dict(self) -> dict:
        """Convert configuration to dictionary."""
        return {
            "enabled": self.enabled,
            "log_level": self.log_level,
            "buffer_size": self.buffer_size,
            "flush_interval": self.flush_interval,
            "batch_size": self.batch_size,
            "retention_days": self.retention_days,
            "max_concurrent_flushes": self.max_concurrent_flushes,
            "flush_timeout": self.flush_timeout,
            "connection_pool_size": self.connection_pool_size,
            "connection_timeout": self.connection_timeout,
            "health_check_interval": self.health_check_interval,
            "metrics_enabled": self.metrics_enabled,
        }


# Global configuration instance
compliance_audit_config = ComplianceAuditConfig.from_environment()

# Validate configuration on import
if not compliance_audit_config.validate():
    logger.warning("Compliance audit configuration validation failed. Using defaults.")
    compliance_audit_config = ComplianceAuditConfig()

logger.info(
    f"Compliance audit configuration loaded: enabled={compliance_audit_config.enabled}"
)


def get_compliance_audit_config() -> ComplianceAuditConfig:
    """Get the global compliance audit configuration."""
    return compliance_audit_config


def update_compliance_audit_config(**kwargs) -> bool:
    """
    Update compliance audit configuration.

    Args:
        **kwargs: Configuration parameters to update

    Returns:
        bool: True if update was successful
    """
    global compliance_audit_config

    try:
        # Create new configuration with updated values
        current_config = compliance_audit_config.to_dict()
        current_config.update(kwargs)

        new_config = ComplianceAuditConfig(**current_config)

        if new_config.validate():
            compliance_audit_config = new_config
            logger.info("Compliance audit configuration updated successfully")
            return True
        else:
            logger.error(
                "Failed to update compliance audit configuration: validation failed"
            )
            return False

    except Exception as e:
        logger.error(f"Failed to update compliance audit configuration: {e}")
        return False


def is_compliance_audit_enabled() -> bool:
    """Check if compliance audit is enabled."""
    return compliance_audit_config.enabled


def get_buffer_settings() -> tuple:
    """Get buffer configuration as tuple (buffer_size, flush_interval, batch_size)."""
    return (
        compliance_audit_config.buffer_size,
        compliance_audit_config.flush_interval,
        compliance_audit_config.batch_size,
    )


def get_retention_settings() -> dict:
    """Get retention configuration."""
    return {
        "retention_days": compliance_audit_config.retention_days,
        "health_check_interval": compliance_audit_config.health_check_interval,
    }


def get_performance_settings() -> dict:
    """Get performance configuration."""
    return {
        "max_concurrent_flushes": compliance_audit_config.max_concurrent_flushes,
        "flush_timeout": compliance_audit_config.flush_timeout,
        "connection_pool_size": compliance_audit_config.connection_pool_size,
        "connection_timeout": compliance_audit_config.connection_timeout,
    }


def get_monitoring_settings() -> dict:
    """Get monitoring configuration."""
    return {
        "metrics_enabled": compliance_audit_config.metrics_enabled,
        "health_check_interval": compliance_audit_config.health_check_interval,
        "log_level": compliance_audit_config.log_level,
    }


# Configuration validation on module import
if __name__ == "__main__":
    # Test configuration loading and validation
    config = ComplianceAuditConfig.from_environment()
    print(f"Configuration loaded: {config.to_dict()}")
    print(f"Validation result: {config.validate()}")
