#!/usr/bin/env python3
"""
Webhook Retry Service for PI Lawyer AI
Comprehensive retry system with exponential backoff, dead letter queues, and regional failure handling.

This service ensures reliable webhook processing with robust error handling and recovery mechanisms.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import uuid

from backend.utils.logging import get_logger
from backend.services.webhook_idempotency import (
    is_webhook_processed,
    mark_webhook_processed,
)

# Configure logging
logger = get_logger(__name__)


class WebhookRetryStatus(Enum):
    """Webhook retry status enumeration."""

    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    DEAD_LETTER = "dead_letter"
    CANCELLED = "cancelled"


class WebhookFailureCategory(Enum):
    """Categories of webhook processing failures."""

    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    STRIPE_API_ERROR = "stripe_api_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


class WebhookRetryService:
    """Service for handling webhook retry logic with exponential backoff."""

    def __init__(self):
        # Retry configuration
        self.max_retry_attempts = 5
        self.base_delay_seconds = 2
        self.max_delay_seconds = 300  # 5 minutes
        self.exponential_base = 2
        self.jitter_factor = 0.1

        # Dead letter queue configuration
        self.dead_letter_threshold = 5
        self.dead_letter_retention_days = 30

        # Regional retry configurations
        self.regional_retry_config = {
            "US": {"max_attempts": 5, "base_delay": 2, "timeout": 30},
            "EU": {
                "max_attempts": 3,  # Stricter for GDPR compliance
                "base_delay": 1,
                "timeout": 20,
            },
            "UK": {"max_attempts": 3, "base_delay": 1, "timeout": 20},
            "CA": {"max_attempts": 5, "base_delay": 2, "timeout": 30},
        }

        # In-memory retry queue (in production, use Redis or database)
        self.retry_queue = {}
        self.dead_letter_queue = {}
        self.processing_webhooks = set()

    async def process_webhook_with_retry(
        self,
        event: Dict[str, Any],
        handler_func,
        regional_context: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Process webhook with retry logic and failure handling.

        Args:
            event: Stripe webhook event
            handler_func: Function to process the webhook
            regional_context: Regional processing context

        Returns:
            Processing result with retry information
        """
        event_id = event.get("id")
        event_type = event.get("type")

        # Check if already processed (idempotency)
        if await is_webhook_processed(event_id):
            logger.info(f"Webhook {event_id} already processed, skipping")
            return {
                "status": "already_processed",
                "event_id": event_id,
                "message": "Webhook already processed successfully",
            }

        # Check if currently processing
        if event_id in self.processing_webhooks:
            logger.warning(f"Webhook {event_id} is currently being processed")
            return {
                "status": "already_processing",
                "event_id": event_id,
                "message": "Webhook is currently being processed",
            }

        # Get regional retry configuration
        region = regional_context.get("region", "US") if regional_context else "US"
        retry_config = self.regional_retry_config.get(
            region, self.regional_retry_config["US"]
        )

        # Create retry entry
        retry_entry = {
            "event_id": event_id,
            "event_type": event_type,
            "event": event,
            "handler_func": handler_func,
            "regional_context": regional_context,
            "retry_config": retry_config,
            "attempt_count": 0,
            "max_attempts": retry_config["max_attempts"],
            "status": WebhookRetryStatus.PENDING,
            "created_at": datetime.utcnow(),
            "last_attempt_at": None,
            "next_retry_at": datetime.utcnow(),
            "failure_history": [],
            "total_processing_time": 0,
        }

        # Add to retry queue
        self.retry_queue[event_id] = retry_entry

        # Process with retry logic
        result = await self._execute_with_retry(retry_entry)

        # Clean up
        if event_id in self.processing_webhooks:
            self.processing_webhooks.remove(event_id)

        return result

    async def _execute_with_retry(self, retry_entry: Dict[str, Any]) -> Dict[str, Any]:
        """Execute webhook processing with retry logic."""
        event_id = retry_entry["event_id"]

        while retry_entry["attempt_count"] < retry_entry["max_attempts"]:
            retry_entry["attempt_count"] += 1
            retry_entry["last_attempt_at"] = datetime.utcnow()
            retry_entry["status"] = WebhookRetryStatus.PROCESSING

            # Add to processing set
            self.processing_webhooks.add(event_id)

            logger.info(
                f"Processing webhook {event_id}, attempt {retry_entry['attempt_count']}/{retry_entry['max_attempts']}"
            )

            try:
                # Execute the webhook handler
                start_time = time.time()

                if retry_entry["regional_context"]:
                    result = await retry_entry["handler_func"](
                        retry_entry["event"],
                        regional_context=retry_entry["regional_context"],
                    )
                else:
                    result = await retry_entry["handler_func"](retry_entry["event"])

                processing_time = time.time() - start_time
                retry_entry["total_processing_time"] += processing_time

                # Success
                retry_entry["status"] = WebhookRetryStatus.SUCCESS

                # Mark as processed for idempotency
                await mark_webhook_processed(event_id)

                # Log success metrics
                await self._log_retry_metrics(retry_entry, "success", processing_time)

                logger.info(
                    f"Webhook {event_id} processed successfully on attempt {retry_entry['attempt_count']}"
                )

                return {
                    "status": "success",
                    "event_id": event_id,
                    "attempt_count": retry_entry["attempt_count"],
                    "total_processing_time": retry_entry["total_processing_time"],
                    "result": result,
                }

            except Exception as e:
                processing_time = time.time() - start_time
                retry_entry["total_processing_time"] += processing_time

                # Categorize the failure
                failure_category = self._categorize_failure(e)

                # Record failure
                failure_record = {
                    "attempt": retry_entry["attempt_count"],
                    "error": str(e),
                    "category": failure_category.value,
                    "timestamp": datetime.utcnow().isoformat(),
                    "processing_time": processing_time,
                }
                retry_entry["failure_history"].append(failure_record)

                logger.error(
                    f"Webhook {event_id} failed on attempt {retry_entry['attempt_count']}: {e}"
                )

                # Check if we should retry
                if retry_entry["attempt_count"] < retry_entry["max_attempts"]:
                    # Calculate next retry delay
                    delay = self._calculate_retry_delay(
                        retry_entry["attempt_count"],
                        failure_category,
                        retry_entry["retry_config"],
                    )

                    retry_entry["next_retry_at"] = datetime.utcnow() + timedelta(
                        seconds=delay
                    )
                    retry_entry["status"] = WebhookRetryStatus.PENDING

                    logger.info(f"Webhook {event_id} will retry in {delay} seconds")

                    # Wait for retry delay
                    await asyncio.sleep(delay)

                    # Remove from processing set for retry
                    if event_id in self.processing_webhooks:
                        self.processing_webhooks.remove(event_id)
                else:
                    # Max attempts reached
                    retry_entry["status"] = WebhookRetryStatus.FAILED

                    # Move to dead letter queue
                    await self._move_to_dead_letter_queue(retry_entry)

                    # Log failure metrics
                    await self._log_retry_metrics(
                        retry_entry, "failed", processing_time
                    )

                    logger.error(
                        f"Webhook {event_id} failed after {retry_entry['max_attempts']} attempts"
                    )

                    return {
                        "status": "failed",
                        "event_id": event_id,
                        "attempt_count": retry_entry["attempt_count"],
                        "total_processing_time": retry_entry["total_processing_time"],
                        "failure_history": retry_entry["failure_history"],
                        "moved_to_dead_letter": True,
                    }

        # Should not reach here
        return {
            "status": "error",
            "event_id": event_id,
            "message": "Unexpected end of retry logic",
        }

    def _categorize_failure(self, error: Exception) -> WebhookFailureCategory:
        """Categorize the type of failure for appropriate retry strategy."""
        error_str = str(error).lower()

        if "network" in error_str or "connection" in error_str:
            return WebhookFailureCategory.NETWORK_ERROR
        elif "database" in error_str or "sql" in error_str:
            return WebhookFailureCategory.DATABASE_ERROR
        elif "validation" in error_str or "invalid" in error_str:
            return WebhookFailureCategory.VALIDATION_ERROR
        elif "auth" in error_str or "unauthorized" in error_str:
            return WebhookFailureCategory.AUTHENTICATION_ERROR
        elif "rate limit" in error_str or "too many requests" in error_str:
            return WebhookFailureCategory.RATE_LIMIT_ERROR
        elif "stripe" in error_str:
            return WebhookFailureCategory.STRIPE_API_ERROR
        elif "timeout" in error_str:
            return WebhookFailureCategory.TIMEOUT_ERROR
        else:
            return WebhookFailureCategory.UNKNOWN_ERROR

    def _calculate_retry_delay(
        self,
        attempt: int,
        failure_category: WebhookFailureCategory,
        retry_config: Dict[str, Any],
    ) -> float:
        """Calculate retry delay with exponential backoff and jitter."""
        base_delay = retry_config.get("base_delay", self.base_delay_seconds)

        # Category-specific delay adjustments
        category_multipliers = {
            WebhookFailureCategory.NETWORK_ERROR: 1.0,
            WebhookFailureCategory.DATABASE_ERROR: 1.5,
            WebhookFailureCategory.VALIDATION_ERROR: 0.5,  # Retry quickly for validation errors
            WebhookFailureCategory.AUTHENTICATION_ERROR: 2.0,
            WebhookFailureCategory.RATE_LIMIT_ERROR: 3.0,  # Longer delay for rate limits
            WebhookFailureCategory.STRIPE_API_ERROR: 1.5,
            WebhookFailureCategory.TIMEOUT_ERROR: 1.0,
            WebhookFailureCategory.UNKNOWN_ERROR: 1.0,
        }

        multiplier = category_multipliers.get(failure_category, 1.0)

        # Exponential backoff
        delay = base_delay * (self.exponential_base ** (attempt - 1)) * multiplier

        # Apply maximum delay limit
        delay = min(delay, self.max_delay_seconds)

        # Add jitter to prevent thundering herd
        import random

        jitter = delay * self.jitter_factor * (random.random() - 0.5)
        delay += jitter

        return max(delay, 1.0)  # Minimum 1 second delay

    async def _move_to_dead_letter_queue(self, retry_entry: Dict[str, Any]):
        """Move failed webhook to dead letter queue."""
        event_id = retry_entry["event_id"]

        dead_letter_entry = {
            **retry_entry,
            "status": WebhookRetryStatus.DEAD_LETTER,
            "moved_to_dlq_at": datetime.utcnow(),
            "dlq_retention_until": datetime.utcnow()
            + timedelta(days=self.dead_letter_retention_days),
        }

        self.dead_letter_queue[event_id] = dead_letter_entry

        # Remove from retry queue
        if event_id in self.retry_queue:
            del self.retry_queue[event_id]

        logger.warning(f"Webhook {event_id} moved to dead letter queue")

        # In production, this would trigger alerts
        await self._trigger_dead_letter_alert(dead_letter_entry)

    async def _trigger_dead_letter_alert(self, dead_letter_entry: Dict[str, Any]):
        """Trigger alert for webhook moved to dead letter queue."""
        event_id = dead_letter_entry["event_id"]
        event_type = dead_letter_entry["event_type"]

        alert_data = {
            "alert_type": "webhook_dead_letter",
            "event_id": event_id,
            "event_type": event_type,
            "attempt_count": dead_letter_entry["attempt_count"],
            "failure_history": dead_letter_entry["failure_history"],
            "regional_context": dead_letter_entry.get("regional_context"),
            "timestamp": datetime.utcnow().isoformat(),
        }

        logger.critical(
            f"ALERT: Webhook {event_id} moved to dead letter queue: {alert_data}"
        )

        # In production, integrate with alerting system (PagerDuty, Slack, etc.)

    async def _log_retry_metrics(
        self, retry_entry: Dict[str, Any], outcome: str, processing_time: float
    ):
        """Log retry metrics for monitoring."""
        metrics = {
            "event_id": retry_entry["event_id"],
            "event_type": retry_entry["event_type"],
            "outcome": outcome,
            "attempt_count": retry_entry["attempt_count"],
            "total_processing_time": retry_entry["total_processing_time"],
            "current_processing_time": processing_time,
            "region": retry_entry.get("regional_context", {}).get("region", "US"),
            "failure_categories": [
                f["category"] for f in retry_entry["failure_history"]
            ],
            "timestamp": datetime.utcnow().isoformat(),
        }

        logger.info(f"Webhook retry metrics: {metrics}")

    async def get_retry_queue_status(self) -> Dict[str, Any]:
        """Get current status of retry queue."""
        pending_count = sum(
            1
            for entry in self.retry_queue.values()
            if entry["status"] == WebhookRetryStatus.PENDING
        )
        processing_count = len(self.processing_webhooks)
        dead_letter_count = len(self.dead_letter_queue)

        return {
            "retry_queue_size": len(self.retry_queue),
            "pending_webhooks": pending_count,
            "processing_webhooks": processing_count,
            "dead_letter_queue_size": dead_letter_count,
            "total_webhooks": len(self.retry_queue) + dead_letter_count,
        }

    async def reprocess_dead_letter_webhook(self, event_id: str) -> Dict[str, Any]:
        """Manually reprocess a webhook from dead letter queue."""
        if event_id not in self.dead_letter_queue:
            return {
                "status": "not_found",
                "message": f"Webhook {event_id} not found in dead letter queue",
            }

        dead_letter_entry = self.dead_letter_queue[event_id]

        # Reset retry entry
        retry_entry = {
            **dead_letter_entry,
            "attempt_count": 0,
            "status": WebhookRetryStatus.PENDING,
            "next_retry_at": datetime.utcnow(),
            "failure_history": [],
            "total_processing_time": 0,
            "reprocessed_from_dlq": True,
            "reprocessed_at": datetime.utcnow(),
        }

        # Move back to retry queue
        self.retry_queue[event_id] = retry_entry
        del self.dead_letter_queue[event_id]

        logger.info(
            f"Webhook {event_id} moved from dead letter queue back to retry queue"
        )

        # Process with retry logic
        result = await self._execute_with_retry(retry_entry)

        return result

    async def cleanup_expired_dead_letters(self):
        """Clean up expired entries from dead letter queue."""
        current_time = datetime.utcnow()
        expired_entries = []

        for event_id, entry in self.dead_letter_queue.items():
            if current_time > entry["dlq_retention_until"]:
                expired_entries.append(event_id)

        for event_id in expired_entries:
            del self.dead_letter_queue[event_id]
            logger.info(f"Removed expired webhook {event_id} from dead letter queue")

        return len(expired_entries)


# Global instance
webhook_retry_service = WebhookRetryService()
