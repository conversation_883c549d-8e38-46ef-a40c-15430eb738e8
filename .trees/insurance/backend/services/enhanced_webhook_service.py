"""
Enhanced webhook service with retry queue integration.

This module provides an enhanced webhook delivery service that integrates
with the webhook retry queue system for reliable delivery with exponential
backoff and dead letter queue support.
"""

import asyncio
import aiohttp
import hashlib
import hmac
import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional, List
import uuid

from backend.config import settings
from backend.services.webhook_retry_queue import (
    get_webhook_retry_queue,
    WebhookPayload,
    WebhookRetryConfig,
)
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class EnhancedWebhookService:
    """
    Enhanced webhook service with retry queue integration.

    This service provides reliable webhook delivery with automatic retries,
    exponential backoff, and dead letter queue for failed deliveries.
    """

    def __init__(self, retry_config: WebhookRetryConfig = None):
        self.retry_queue = get_webhook_retry_queue(retry_config)
        self._session: Optional[aiohttp.ClientSession] = None

        # Set up the delivery function for the retry queue
        self.retry_queue.set_delivery_function(self._deliver_webhook_http)

        logger.info("EnhancedWebhookService initialized")

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self._session = aiohttp.ClientSession(
                timeout=timeout, headers={"User-Agent": "AiLex-Webhook-Service/1.0"}
            )
        return self._session

    async def close(self):
        """Close HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()

    async def send_webhook(
        self,
        url: str,
        payload: Dict[str, Any],
        event_type: str = "",
        tenant_id: str = "",
        headers: Dict[str, str] = None,
        method: str = "POST",
        immediate: bool = False,
    ) -> str:
        """
        Send a webhook with retry queue support.

        Args:
            url: Webhook URL to send to
            payload: Webhook payload data
            event_type: Type of event (for logging/monitoring)
            tenant_id: Tenant ID (for logging/monitoring)
            headers: Additional HTTP headers
            method: HTTP method (default: POST)
            immediate: If True, attempt immediate delivery before queuing

        Returns:
            Webhook record ID for tracking
        """
        # Prepare headers
        webhook_headers = {
            "Content-Type": "application/json",
            "X-Event-Type": event_type,
            "X-Tenant-ID": tenant_id,
            "X-Timestamp": datetime.utcnow().isoformat(),
            "X-Request-ID": str(uuid.uuid4()),
        }

        if headers:
            webhook_headers.update(headers)

        # Create webhook payload
        webhook_payload = WebhookPayload(
            id=str(uuid.uuid4()),
            url=url,
            method=method,
            headers=webhook_headers,
            body=payload,
            event_type=event_type,
            tenant_id=tenant_id,
        )

        # If immediate delivery is requested, try once before queuing
        if immediate:
            try:
                success = await self._deliver_webhook_http(
                    url, method, webhook_headers, payload
                )
                if success:
                    logger.info(f"Webhook delivered immediately to {url}")
                    return webhook_payload.id
            except Exception as e:
                logger.warning(
                    f"Immediate webhook delivery failed, queuing for retry: {e}"
                )

        # Queue for delivery with retry support
        record_id = await self.retry_queue.enqueue_webhook(webhook_payload)
        logger.info(f"Webhook queued for delivery: {record_id} -> {url}")

        return record_id

    async def send_voice_receptionist_webhook(
        self,
        booking_id: str,
        event_type: str,
        payload: Dict[str, Any],
        immediate: bool = True,
    ) -> str:
        """
        Send webhook to Voice Receptionist with proper authentication.

        Args:
            booking_id: Booking ID for tracking
            event_type: Event type (booking.confirmed, booking.canceled, etc.)
            payload: Webhook payload
            immediate: Whether to attempt immediate delivery

        Returns:
            Webhook record ID
        """
        if not settings.voice_receptionist_api_url:
            raise ValueError("Voice Receptionist API URL not configured")

        # Add HMAC signature for authentication
        headers = {}
        if settings.voice_receptionist_hmac_secret:
            signature = self._generate_hmac_signature(
                payload, settings.voice_receptionist_hmac_secret
            )
            headers["X-Signature"] = signature

        if settings.voice_receptionist_api_key:
            headers["Authorization"] = f"Bearer {settings.voice_receptionist_api_key}"

        return await self.send_webhook(
            url=settings.voice_receptionist_api_url,
            payload=payload,
            event_type=event_type,
            tenant_id=payload.get("firm_id", ""),
            headers=headers,
            immediate=immediate,
        )

    async def _deliver_webhook_http(
        self, url: str, method: str, headers: Dict[str, str], body: Dict[str, Any]
    ) -> bool:
        """
        Deliver webhook via HTTP.

        Args:
            url: Target URL
            method: HTTP method
            headers: HTTP headers
            body: Request body

        Returns:
            True if delivery was successful, False otherwise
        """
        try:
            session = await self._get_session()

            # Prepare request data
            json_body = json.dumps(body) if body else None

            async with session.request(
                method=method, url=url, headers=headers, data=json_body
            ) as response:
                # Consider 2xx status codes as success
                if 200 <= response.status < 300:
                    logger.info(
                        f"Webhook delivered successfully: {method} {url} -> {response.status}"
                    )
                    return True
                else:
                    response_text = await response.text()
                    logger.error(
                        f"Webhook delivery failed: {method} {url} -> {response.status}: {response_text}"
                    )
                    return False

        except asyncio.TimeoutError:
            logger.error(f"Webhook delivery timeout: {method} {url}")
            return False
        except aiohttp.ClientError as e:
            logger.error(f"Webhook delivery client error: {method} {url} -> {e}")
            return False
        except Exception as e:
            logger.error(f"Webhook delivery unexpected error: {method} {url} -> {e}")
            return False

    def _generate_hmac_signature(self, payload: Dict[str, Any], secret: str) -> str:
        """
        Generate HMAC signature for webhook authentication.

        Args:
            payload: Webhook payload
            secret: HMAC secret key

        Returns:
            HMAC signature as hex string
        """
        payload_json = json.dumps(payload, sort_keys=True, separators=(",", ":"))
        signature = hmac.new(
            secret.encode("utf-8"), payload_json.encode("utf-8"), hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"

    async def start_processing(self, interval_seconds: int = 30):
        """Start the webhook retry queue processing."""
        await self.retry_queue.start_processing(interval_seconds)

    async def stop_processing(self):
        """Stop the webhook retry queue processing."""
        await self.retry_queue.stop_processing()
        await self.close()

    def get_stats(self) -> Dict[str, Any]:
        """Get webhook delivery statistics."""
        return self.retry_queue.get_stats()

    async def cleanup_old_webhooks(self, days_old: int = 7):
        """Clean up old webhook records."""
        await self.retry_queue.cleanup_old_webhooks(days_old)

    async def retry_failed_webhooks(self) -> int:
        """Process pending webhook retries."""
        return await self.retry_queue.process_pending_webhooks()

    async def cancel_webhook(self, webhook_id: str) -> bool:
        """
        Cancel a pending webhook.

        Args:
            webhook_id: Webhook record ID to cancel

        Returns:
            True if webhook was cancelled, False otherwise
        """
        try:
            supabase = self.retry_queue.supabase
            result = (
                supabase.table("webhook_retry_queue")
                .update(
                    {"status": "cancelled", "updated_at": datetime.utcnow().isoformat()}
                )
                .eq("id", webhook_id)
                .in_("status", ["pending", "failed"])
                .execute()
            )

            if result.error:
                logger.error(f"Failed to cancel webhook {webhook_id}: {result.error}")
                return False

            if result.data:
                logger.info(f"Webhook {webhook_id} cancelled successfully")
                return True
            else:
                logger.warning(f"Webhook {webhook_id} not found or not cancellable")
                return False

        except Exception as e:
            logger.error(f"Error cancelling webhook {webhook_id}: {e}")
            return False

    async def get_webhook_status(self, webhook_id: str) -> Optional[Dict[str, Any]]:
        """
        Get webhook status and details.

        Args:
            webhook_id: Webhook record ID

        Returns:
            Webhook details or None if not found
        """
        try:
            supabase = self.retry_queue.supabase
            result = (
                supabase.table("webhook_retry_queue")
                .select("*")
                .eq("id", webhook_id)
                .single()
                .execute()
            )

            if result.error:
                logger.error(f"Failed to get webhook {webhook_id}: {result.error}")
                return None

            return result.data

        except Exception as e:
            logger.error(f"Error getting webhook {webhook_id}: {e}")
            return None


# Global enhanced webhook service instance
_enhanced_webhook_service: Optional[EnhancedWebhookService] = None


def get_enhanced_webhook_service(
    retry_config: WebhookRetryConfig = None,
) -> EnhancedWebhookService:
    """Get the global enhanced webhook service instance."""
    global _enhanced_webhook_service

    if _enhanced_webhook_service is None:
        _enhanced_webhook_service = EnhancedWebhookService(retry_config)

    return _enhanced_webhook_service


async def cleanup_webhook_service():
    """Clean up the global webhook service."""
    global _enhanced_webhook_service

    if _enhanced_webhook_service:
        await _enhanced_webhook_service.stop_processing()
        _enhanced_webhook_service = None
