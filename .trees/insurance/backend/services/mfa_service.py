"""
Multi-Factor Authentication (MFA) Service

This service provides comprehensive MFA functionality for superadmin users,
including TOTP, SMS, email backup authentication, and recovery codes.
"""

import os
import secrets
import hashlib
import base64
import qrcode
import pyotp
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, Tuple
from io import BytesIO
import logging
import uuid

# Initialize logger early to avoid NameError in import blocks
logger = logging.getLogger(__name__)

# SMS and Email providers
try:
    from twilio.rest import Client as TwilioClient

    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False
    logger.warning("Twilio not available - SMS MFA will be disabled")

try:
    import resend

    RESEND_AVAILABLE = True
except ImportError:
    RESEND_AVAILABLE = False
    logger.warning("Resend not available - Email MFA will be disabled")

from backend.models.mfa import (
    SuperadminMFAConfig,
    SuperadminMFASession,
    SuperadminMFAAttempt,
    BackupMFAToken,
    SecurityEvent,
    MFAMethod,
    SecurityEventCategory,
    SecurityEventSeverity,
    TOTPSetupResponse,
    RecoveryCodesResponse,
    MFAChallengeResponse,
    MFAValidationResponse,
    MFAStatusResponse,
)
from supabase import Client, create_client
from backend.middleware.auth_middleware import UserContext


def get_supabase_client() -> Client:
    """Get Supabase client for MFA operations."""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")

    if not url or not key:
        raise ValueError(
            "SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables"
        )

    return create_client(url, key)


class MFAService:
    """Service for managing Multi-Factor Authentication operations."""

    def __init__(self):
        self.supabase = get_supabase_client()
        self.totp_issuer = os.getenv("TOTP_ISSUER_NAME", "PI Lawyer AI")
        self.encryption_key = os.getenv("MFA_ENCRYPTION_KEY")

        if not self.encryption_key:
            logger.warning("MFA_ENCRYPTION_KEY not set, using development key")
            self.encryption_key = "dev_key_32_chars_long_for_testing"

        # Initialize SMS client (Twilio)
        self.twilio_client = None
        if TWILIO_AVAILABLE:
            account_sid = os.getenv("TWILIO_ACCOUNT_SID")
            auth_token = os.getenv("TWILIO_AUTH_TOKEN")
            if account_sid and auth_token:
                self.twilio_client = TwilioClient(account_sid, auth_token)
                self.twilio_phone = os.getenv("TWILIO_PHONE_NUMBER")

        # Initialize Email client (Resend)
        self.resend_client = None
        if RESEND_AVAILABLE:
            api_key = os.getenv("RESEND_API_KEY")
            if api_key:
                resend.api_key = api_key
                self.resend_client = resend
                self.resend_from_email = os.getenv(
                    "RESEND_FROM_EMAIL", "<EMAIL>"
                )

    async def get_mfa_config(self, user_id: str) -> Optional[SuperadminMFAConfig]:
        """Get MFA configuration for a user."""
        try:
            result = (
                self.supabase.table("mfa_enhancements.superadmin_mfa_config")
                .select("*")
                .eq("user_id", user_id)
                .single()
                .execute()
            )

            if result.data:
                return SuperadminMFAConfig(**result.data)
            return None
        except Exception as e:
            logger.error(f"Error getting MFA config for user {user_id}: {e}")
            return None

    async def create_mfa_config(self, user_id: str, email: str) -> SuperadminMFAConfig:
        """Create MFA configuration for a user."""
        config = SuperadminMFAConfig(
            user_id=user_id,
            email=email,
            mfa_required=True,
            mfa_enforced_at=datetime.utcnow(),
        )

        try:
            result = (
                self.supabase.table("mfa_enhancements.superadmin_mfa_config")
                .insert(config.dict())
                .execute()
            )

            await self._log_security_event(
                user_id=user_id,
                event_type="mfa_config_created",
                event_category=SecurityEventCategory.MFA,
                severity=SecurityEventSeverity.INFO,
                description=f"MFA configuration created for user {email}",
            )

            return SuperadminMFAConfig(**result.data[0])
        except Exception as e:
            logger.error(f"Error creating MFA config for user {user_id}: {e}")
            raise

    async def setup_totp(self, user_id: str, user_email: str) -> TOTPSetupResponse:
        """Set up TOTP authentication for a user."""
        try:
            # Generate TOTP secret
            secret = pyotp.random_base32()

            # Create TOTP instance
            totp = pyotp.TOTP(secret)

            # Generate QR code URI
            qr_uri = totp.provisioning_uri(
                name=user_email, issuer_name=self.totp_issuer
            )

            # Generate recovery codes
            recovery_codes = self._generate_recovery_codes()

            # Store TOTP factor in Supabase auth.mfa_factors
            factor_result = (
                self.supabase.table("auth.mfa_factors")
                .insert(
                    {
                        "user_id": user_id,
                        "factor_type": "totp",
                        "status": "unverified",
                        "secret": self._encrypt_secret(secret),
                        "friendly_name": "Google Authenticator",
                    }
                )
                .execute()
            )

            # Update MFA config with recovery codes
            config = await self.get_mfa_config(user_id)
            if config:
                encrypted_codes = [
                    self._encrypt_secret(code) for code in recovery_codes
                ]
                self.supabase.table("mfa_enhancements.superadmin_mfa_config").update(
                    {
                        "recovery_codes": encrypted_codes,
                        "recovery_codes_generated_at": datetime.utcnow().isoformat(),
                        "updated_at": datetime.utcnow().isoformat(),
                    }
                ).eq("user_id", user_id).execute()

            await self._log_security_event(
                user_id=user_id,
                event_type="totp_setup_initiated",
                event_category=SecurityEventCategory.MFA,
                severity=SecurityEventSeverity.INFO,
                description="TOTP setup initiated",
                metadata={"factor_id": factor_result.data[0]["id"]},
            )

            return TOTPSetupResponse(
                secret=secret,
                qr_code_uri=qr_uri,
                backup_codes=recovery_codes,
                setup_instructions=(
                    "1. Install Google Authenticator or similar app\n"
                    "2. Scan the QR code or enter the secret manually\n"
                    "3. Enter the 6-digit code to verify setup\n"
                    "4. Save your recovery codes in a secure location"
                ),
            )
        except Exception as e:
            logger.error(f"Error setting up TOTP for user {user_id}: {e}")
            raise

    async def verify_totp_setup(self, user_id: str, token: str, factor_id: str) -> bool:
        """Verify TOTP setup with the provided token."""
        try:
            # Get the factor
            factor_result = (
                self.supabase.table("auth.mfa_factors")
                .select("*")
                .eq("id", factor_id)
                .eq("user_id", user_id)
                .single()
                .execute()
            )

            if not factor_result.data:
                return False

            factor = factor_result.data
            secret = self._decrypt_secret(factor["secret"])

            # Verify the token
            totp = pyotp.TOTP(secret)
            if totp.verify(token, valid_window=1):  # Allow 1 window tolerance
                # Mark factor as verified
                self.supabase.table("auth.mfa_factors").update(
                    {"status": "verified", "updated_at": datetime.utcnow().isoformat()}
                ).eq("id", factor_id).execute()

                await self._log_security_event(
                    user_id=user_id,
                    event_type="totp_setup_completed",
                    event_category=SecurityEventCategory.MFA,
                    severity=SecurityEventSeverity.INFO,
                    description="TOTP setup completed successfully",
                    metadata={"factor_id": factor_id},
                )

                return True
            else:
                await self._log_mfa_attempt(
                    user_id=user_id,
                    method=MFAMethod.TOTP,
                    success=False,
                    failure_reason="Invalid TOTP token during setup",
                )
                return False
        except Exception as e:
            logger.error(f"Error verifying TOTP setup for user {user_id}: {e}")
            return False

    async def verify_totp(self, user_id: str, token: str) -> bool:
        """Verify a TOTP token for authentication."""
        try:
            # Get verified TOTP factors for the user
            factors_result = (
                self.supabase.table("auth.mfa_factors")
                .select("*")
                .eq("user_id", user_id)
                .eq("factor_type", "totp")
                .eq("status", "verified")
                .execute()
            )

            if not factors_result.data:
                return False

            for factor in factors_result.data:
                secret = self._decrypt_secret(factor["secret"])
                totp = pyotp.TOTP(secret)

                if totp.verify(token, valid_window=1):
                    # Update last challenged timestamp
                    self.supabase.table("auth.mfa_factors").update(
                        {"last_challenged_at": datetime.utcnow().isoformat()}
                    ).eq("id", factor["id"]).execute()

                    await self._log_mfa_attempt(
                        user_id=user_id, method=MFAMethod.TOTP, success=True
                    )

                    return True

            await self._log_mfa_attempt(
                user_id=user_id,
                method=MFAMethod.TOTP,
                success=False,
                failure_reason="Invalid TOTP token",
            )

            return False
        except Exception as e:
            logger.error(f"Error verifying TOTP for user {user_id}: {e}")
            return False

    async def create_mfa_session(
        self,
        user_id: str,
        mfa_method: MFAMethod,
        ip_address: str,
        user_agent: Optional[str] = None,
        factor_id: Optional[str] = None,
    ) -> SuperadminMFASession:
        """Create a new MFA session after successful authentication."""
        try:
            # Get MFA config to determine session timeout
            config = await self.get_mfa_config(user_id)
            timeout_hours = config.session_timeout_hours if config else 8

            # Generate secure session token
            session_token = self._generate_session_token()

            session = SuperadminMFASession(
                user_id=user_id,
                session_token=session_token,
                expires_at=datetime.utcnow() + timedelta(hours=timeout_hours),
                ip_address=ip_address,
                user_agent=user_agent,
                mfa_method=mfa_method,
                factor_id=factor_id,
            )

            result = (
                self.supabase.table("mfa_enhancements.superadmin_mfa_sessions")
                .insert(session.dict())
                .execute()
            )

            await self._log_security_event(
                user_id=user_id,
                event_type="mfa_session_created",
                event_category=SecurityEventCategory.SESSION,
                severity=SecurityEventSeverity.INFO,
                description=f"MFA session created using {mfa_method.value}",
                metadata={
                    "session_id": result.data[0]["id"],
                    "expires_at": session.expires_at.isoformat(),
                    "method": mfa_method.value,
                },
                ip_address=ip_address,
                user_agent=user_agent,
            )

            return SuperadminMFASession(**result.data[0])
        except Exception as e:
            logger.error(f"Error creating MFA session for user {user_id}: {e}")
            raise

    async def validate_mfa_session(self, user_id: str, session_token: str) -> bool:
        """Validate an existing MFA session."""
        try:
            result = self.supabase.rpc(
                "mfa_enhancements.validate_mfa_session",
                {"user_id_param": user_id, "session_token_param": session_token},
            ).execute()

            return result.data if result.data is not None else False
        except Exception as e:
            logger.error(f"Error validating MFA session for user {user_id}: {e}")
            return False

    async def get_mfa_status(self, user_id: str) -> MFAStatusResponse:
        """Get comprehensive MFA status for a user."""
        try:
            config = await self.get_mfa_config(user_id)

            # Check for verified factors
            factors_result = (
                self.supabase.table("auth.mfa_factors")
                .select("factor_type, status")
                .eq("user_id", user_id)
                .eq("status", "verified")
                .execute()
            )

            verified_factors = [f["factor_type"] for f in factors_result.data]

            # Check active session
            active_session = (
                self.supabase.table("mfa_enhancements.superadmin_mfa_sessions")
                .select("expires_at")
                .eq("user_id", user_id)
                .eq("is_active", True)
                .gte("expires_at", datetime.utcnow().isoformat())
                .order("created_at", desc=True)
                .limit(1)
                .execute()
            )

            session_valid = len(active_session.data) > 0
            session_expires_at = None
            if session_valid:
                session_expires_at = datetime.fromisoformat(
                    active_session.data[0]["expires_at"]
                )

            # Count available recovery codes
            recovery_codes_count = 0
            if config and config.recovery_codes:
                recovery_codes_count = (
                    len(config.recovery_codes) - config.recovery_codes_used
                )

            return MFAStatusResponse(
                mfa_required=config.mfa_required if config else True,
                mfa_configured=len(verified_factors) > 0,
                available_methods=[MFAMethod(f) for f in verified_factors],
                backup_methods_configured={
                    "sms": bool(config and config.backup_phone),
                    "email": bool(config and config.backup_email),
                },
                recovery_codes_available=recovery_codes_count,
                session_valid=session_valid,
                session_expires_at=session_expires_at,
            )
        except Exception as e:
            logger.error(f"Error getting MFA status for user {user_id}: {e}")
            raise

    def _generate_recovery_codes(self, count: int = 10) -> List[str]:
        """Generate recovery codes for account recovery."""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = "".join(
                secrets.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789") for _ in range(8)
            )
            codes.append(code)
        return codes

    def _generate_session_token(self) -> str:
        """Generate a secure session token."""
        return secrets.token_urlsafe(32)

    def _encrypt_secret(self, secret: str) -> str:
        """Encrypt a secret for storage."""
        # Simple base64 encoding for now - in production, use proper encryption
        return base64.b64encode(secret.encode()).decode()

    def _decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt a stored secret."""
        # Simple base64 decoding for now - in production, use proper decryption
        return base64.b64decode(encrypted_secret.encode()).decode()

    async def _log_mfa_attempt(
        self,
        user_id: str,
        method: MFAMethod,
        success: bool,
        failure_reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ):
        """Log an MFA attempt for audit purposes."""
        try:
            attempt = SuperadminMFAAttempt(
                user_id=user_id,
                method=method,
                success=success,
                failure_reason=failure_reason,
                ip_address=ip_address or "unknown",
                user_agent=user_agent,
            )

            self.supabase.table("mfa_enhancements.superadmin_mfa_attempts").insert(
                attempt.dict()
            ).execute()
        except Exception as e:
            logger.error(f"Error logging MFA attempt: {e}")

    async def send_sms_token(
        self, user_id: str, phone_number: str, ip_address: str
    ) -> str:
        """Send SMS token for backup authentication."""
        if not self.twilio_client:
            raise ValueError("SMS service not configured")

        try:
            # Generate 6-digit token
            token = "".join(secrets.choice("0123456789") for _ in range(6))
            token_hash = hashlib.sha256(token.encode()).hexdigest()

            # Store token in database
            backup_token = BackupMFAToken(
                user_id=user_id,
                token_hash=token_hash,
                method="sms",
                destination=phone_number,
                expires_at=datetime.utcnow() + timedelta(minutes=10),
                ip_address=ip_address,
            )

            result = (
                self.supabase.table("mfa_enhancements.backup_mfa_tokens")
                .insert(backup_token.dict())
                .execute()
            )

            # Send SMS
            message = self.twilio_client.messages.create(
                body=f"Your PI Lawyer AI verification code is: {token}. Valid for 10 minutes.",
                from_=self.twilio_phone,
                to=phone_number,
            )

            await self._log_security_event(
                user_id=user_id,
                event_type="sms_token_sent",
                event_category=SecurityEventCategory.MFA,
                severity=SecurityEventSeverity.INFO,
                description=f"SMS token sent to {phone_number[-4:]}",
                metadata={"token_id": result.data[0]["id"], "message_sid": message.sid},
                ip_address=ip_address,
            )

            return result.data[0]["id"]
        except Exception as e:
            logger.error(f"Error sending SMS token: {e}")
            raise

    async def send_email_token(
        self, user_id: str, email_address: str, ip_address: str
    ) -> str:
        """Send email token for backup authentication."""
        if not self.resend_client:
            raise ValueError("Email service not configured")

        try:
            # Generate 6-digit token
            token = "".join(secrets.choice("0123456789") for _ in range(6))
            token_hash = hashlib.sha256(token.encode()).hexdigest()

            # Store token in database
            backup_token = BackupMFAToken(
                user_id=user_id,
                token_hash=token_hash,
                method="email",
                destination=email_address,
                expires_at=datetime.utcnow() + timedelta(minutes=15),
                ip_address=ip_address,
            )

            result = (
                self.supabase.table("mfa_enhancements.backup_mfa_tokens")
                .insert(backup_token.dict())
                .execute()
            )

            # Send email using Resend
            email_params = {
                "from": self.resend_from_email,
                "to": [email_address],
                "subject": "PI Lawyer AI - Verification Code",
                "html": f"""
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #333;">Verification Code</h2>
                    <p>Your PI Lawyer AI verification code is:</p>
                    <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0;">
                        <h1 style="color: #007bff; font-size: 32px; margin: 0; letter-spacing: 4px;">{token}</h1>
                    </div>
                    <p>This code is valid for <strong>15 minutes</strong>.</p>
                    <p style="color: #666; font-size: 14px;">If you didn't request this code, please ignore this email.</p>
                    <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                    <p style="color: #999; font-size: 12px;">PI Lawyer AI Security Team</p>
                </div>
                """,
            }

            response = self.resend_client.emails.send(email_params)

            await self._log_security_event(
                user_id=user_id,
                event_type="email_token_sent",
                event_category=SecurityEventCategory.MFA,
                severity=SecurityEventSeverity.INFO,
                description=f"Email token sent to {email_address}",
                metadata={
                    "token_id": result.data[0]["id"],
                    "status_code": response.status_code,
                },
                ip_address=ip_address,
            )

            return result.data[0]["id"]
        except Exception as e:
            logger.error(f"Error sending email token: {e}")
            raise

    async def verify_backup_token(
        self, user_id: str, token_id: str, token: str
    ) -> bool:
        """Verify a backup token (SMS or email)."""
        try:
            # Get the token record
            result = (
                self.supabase.table("mfa_enhancements.backup_mfa_tokens")
                .select("*")
                .eq("id", token_id)
                .eq("user_id", user_id)
                .single()
                .execute()
            )

            if not result.data:
                return False

            backup_token = BackupMFAToken(**result.data)

            # Check if token can be attempted
            if not backup_token.can_attempt():
                await self._log_mfa_attempt(
                    user_id=user_id,
                    method=(
                        MFAMethod.BACKUP_EMAIL
                        if backup_token.method == "email"
                        else MFAMethod.PHONE
                    ),
                    success=False,
                    failure_reason="Token expired or max attempts exceeded",
                )
                return False

            # Verify token hash
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            if token_hash == backup_token.token_hash:
                # Mark token as used
                self.supabase.table("mfa_enhancements.backup_mfa_tokens").update(
                    {"used_at": datetime.utcnow().isoformat()}
                ).eq("id", token_id).execute()

                await self._log_mfa_attempt(
                    user_id=user_id,
                    method=(
                        MFAMethod.BACKUP_EMAIL
                        if backup_token.method == "email"
                        else MFAMethod.PHONE
                    ),
                    success=True,
                )

                return True
            else:
                # Increment attempt count
                self.supabase.table("mfa_enhancements.backup_mfa_tokens").update(
                    {"attempts": backup_token.attempts + 1}
                ).eq("id", token_id).execute()

                await self._log_mfa_attempt(
                    user_id=user_id,
                    method=(
                        MFAMethod.BACKUP_EMAIL
                        if backup_token.method == "email"
                        else MFAMethod.PHONE
                    ),
                    success=False,
                    failure_reason="Invalid token",
                )

                return False
        except Exception as e:
            logger.error(f"Error verifying backup token: {e}")
            return False

    async def verify_recovery_code(self, user_id: str, recovery_code: str) -> bool:
        """Verify a recovery code."""
        try:
            config = await self.get_mfa_config(user_id)
            if not config or not config.recovery_codes:
                return False

            # Decrypt and check recovery codes
            for i, encrypted_code in enumerate(config.recovery_codes):
                decrypted_code = self._decrypt_secret(encrypted_code)
                if decrypted_code == recovery_code.upper():
                    # Remove the used recovery code
                    updated_codes = config.recovery_codes.copy()
                    updated_codes.pop(i)

                    self.supabase.table(
                        "mfa_enhancements.superadmin_mfa_config"
                    ).update(
                        {
                            "recovery_codes": updated_codes,
                            "recovery_codes_used": config.recovery_codes_used + 1,
                            "updated_at": datetime.utcnow().isoformat(),
                        }
                    ).eq(
                        "user_id", user_id
                    ).execute()

                    await self._log_mfa_attempt(
                        user_id=user_id, method=MFAMethod.RECOVERY_CODE, success=True
                    )

                    await self._log_security_event(
                        user_id=user_id,
                        event_type="recovery_code_used",
                        event_category=SecurityEventCategory.MFA,
                        severity=SecurityEventSeverity.WARNING,
                        description="Recovery code used for authentication",
                        metadata={"remaining_codes": len(updated_codes)},
                    )

                    return True

            await self._log_mfa_attempt(
                user_id=user_id,
                method=MFAMethod.RECOVERY_CODE,
                success=False,
                failure_reason="Invalid recovery code",
            )

            return False
        except Exception as e:
            logger.error(f"Error verifying recovery code: {e}")
            return False

    async def _log_security_event(
        self,
        user_id: str,
        event_type: str,
        event_category: SecurityEventCategory,
        severity: SecurityEventSeverity,
        description: str,
        metadata: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ):
        """Log a security event for audit purposes."""
        try:
            event = SecurityEvent(
                user_id=user_id,
                event_type=event_type,
                event_category=event_category,
                severity=severity,
                description=description,
                metadata=metadata or {},
                ip_address=ip_address,
                user_agent=user_agent,
            )

            self.supabase.table("mfa_enhancements.security_events").insert(
                event.dict()
            ).execute()
        except Exception as e:
            logger.error(f"Error logging security event: {e}")


# Global MFA service instance (lazy initialization)
_mfa_service = None


def get_mfa_service() -> MFAService:
    """Get the global MFA service instance with lazy initialization."""
    global _mfa_service
    if _mfa_service is None:
        _mfa_service = MFAService()
    return _mfa_service


# Only initialize if environment variables are available
try:
    mfa_service = get_mfa_service()
except ValueError:
    # Environment not configured - service will be initialized when needed
    mfa_service = None
