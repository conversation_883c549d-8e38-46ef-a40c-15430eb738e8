"""
Stripe Sync Validation Service

Comprehensive validation system for ensuring data consistency between
local database and Stripe, with automated discrepancy detection and reconciliation.
"""

import asyncio
import logging
import stripe
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

import os
from backend.db.session import get_db_session
from backend.models.subscription import SubscriptionPlan, SubscriptionAddon
from backend.models.firm import Firm
from backend.models.subscription import TenantSubscription
from backend.utils.logging import get_logger

# Configure Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

logger = get_logger(__name__)


class DiscrepancyType(Enum):
    """Types of sync discrepancies that can be detected."""

    MISSING_LOCAL = "missing_local"
    MISSING_STRIPE = "missing_stripe"
    DATA_MISMATCH = "data_mismatch"
    PRICE_MISMATCH = "price_mismatch"
    STATUS_MISMATCH = "status_mismatch"
    METADATA_MISMATCH = "metadata_mismatch"


class SeverityLevel(Enum):
    """Severity levels for discrepancies."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class SyncDiscrepancy:
    """Represents a data synchronization discrepancy."""

    type: DiscrepancyType
    entity_type: str  # 'product', 'price', 'subscription', 'customer'
    entity_id: str
    local_data: Optional[Dict[str, Any]]
    stripe_data: Optional[Dict[str, Any]]
    severity: SeverityLevel
    description: str
    detected_at: datetime
    field_differences: Optional[Dict[str, Any]] = None
    recommended_action: Optional[str] = None


@dataclass
class ValidationResult:
    """Results of a sync validation operation."""

    entity_type: str
    total_local: int
    total_stripe: int
    matched_entities: int
    discrepancies_count: int
    discrepancies: List[SyncDiscrepancy]
    validation_time: datetime
    duration_seconds: float


class StripeSyncValidator:
    """
    Comprehensive Stripe synchronization validator.

    Validates data consistency between local database and Stripe,
    detects discrepancies, and provides reconciliation recommendations.
    """

    def __init__(self):
        self.logger = logger
        self.discrepancies: List[SyncDiscrepancy] = []
        self.validation_cache: Dict[str, Any] = {}

    async def validate_all_data(self) -> Dict[str, Any]:
        """
        Perform comprehensive validation of all Stripe data.

        Returns:
            Complete validation results with discrepancies and recommendations
        """
        start_time = datetime.utcnow()
        self.logger.info("Starting comprehensive Stripe sync validation")

        try:
            # Clear previous discrepancies
            self.discrepancies = []

            # Validate each entity type
            validation_results = {
                "products": await self.validate_products(),
                "prices": await self.validate_prices(),
                "customers": await self.validate_customers(),
                "subscriptions": await self.validate_subscriptions(),
                "summary": {},
                "validation_metadata": {
                    "started_at": start_time.isoformat(),
                    "completed_at": None,
                    "duration_seconds": 0,
                    "total_discrepancies": 0,
                    "critical_issues": 0,
                    "high_priority_issues": 0,
                },
            }

            # Generate comprehensive summary
            validation_results["summary"] = self._generate_validation_summary(
                validation_results
            )

            # Update metadata
            end_time = datetime.utcnow()
            validation_results["validation_metadata"].update(
                {
                    "completed_at": end_time.isoformat(),
                    "duration_seconds": (end_time - start_time).total_seconds(),
                    "total_discrepancies": len(self.discrepancies),
                    "critical_issues": len(
                        [
                            d
                            for d in self.discrepancies
                            if d.severity == SeverityLevel.CRITICAL
                        ]
                    ),
                    "high_priority_issues": len(
                        [
                            d
                            for d in self.discrepancies
                            if d.severity == SeverityLevel.HIGH
                        ]
                    ),
                }
            )

            # Store validation results for audit trail
            await self._store_validation_results(validation_results)

            self.logger.info(
                f"Stripe sync validation completed in {validation_results['validation_metadata']['duration_seconds']:.2f}s. "
                f"Found {len(self.discrepancies)} discrepancies."
            )

            return validation_results

        except Exception as e:
            self.logger.error(f"Stripe sync validation failed: {e}", exc_info=True)
            raise

    async def validate_products(self) -> ValidationResult:
        """
        Validate product data consistency between local database and Stripe.

        Returns:
            ValidationResult with product validation details
        """
        start_time = datetime.utcnow()
        self.logger.info("Starting product validation")

        try:
            # Fetch local products
            local_products = await self._get_local_products()

            # Fetch Stripe products
            stripe_products = await self._get_stripe_products()

            # Compare data and detect discrepancies
            discrepancies = []
            matched_count = 0

            # Check for missing products in local database
            for stripe_product in stripe_products:
                local_product = self._find_local_product_by_stripe_id(
                    local_products, stripe_product.id
                )

                if not local_product:
                    discrepancies.append(
                        SyncDiscrepancy(
                            type=DiscrepancyType.MISSING_LOCAL,
                            entity_type="product",
                            entity_id=stripe_product.id,
                            local_data=None,
                            stripe_data=self._stripe_product_to_dict(stripe_product),
                            severity=SeverityLevel.HIGH,
                            description=f"Product {stripe_product.id} ({stripe_product.name}) exists in Stripe but not in local database",
                            detected_at=datetime.utcnow(),
                            recommended_action="Create local product record or remove from Stripe",
                        )
                    )
                else:
                    matched_count += 1
                    # Check for data mismatches
                    product_discrepancies = self._compare_product_data(
                        local_product, stripe_product
                    )
                    discrepancies.extend(product_discrepancies)

            # Check for missing products in Stripe
            for local_product in local_products:
                if local_product.get("stripe_product_id"):
                    stripe_product = self._find_stripe_product_by_id(
                        stripe_products, local_product["stripe_product_id"]
                    )

                    if not stripe_product:
                        discrepancies.append(
                            SyncDiscrepancy(
                                type=DiscrepancyType.MISSING_STRIPE,
                                entity_type="product",
                                entity_id=local_product["stripe_product_id"],
                                local_data=local_product,
                                stripe_data=None,
                                severity=SeverityLevel.CRITICAL,
                                description=f"Product {local_product['stripe_product_id']} exists in local database but not in Stripe",
                                detected_at=datetime.utcnow(),
                                recommended_action="Create Stripe product or update local record",
                            )
                        )

            # Add discrepancies to global list
            self.discrepancies.extend(discrepancies)

            end_time = datetime.utcnow()

            return ValidationResult(
                entity_type="products",
                total_local=len(local_products),
                total_stripe=len(stripe_products),
                matched_entities=matched_count,
                discrepancies_count=len(discrepancies),
                discrepancies=discrepancies,
                validation_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
            )

        except Exception as e:
            self.logger.error(f"Product validation failed: {e}", exc_info=True)
            raise

    async def validate_prices(self) -> ValidationResult:
        """
        Validate pricing data consistency between local database and Stripe.

        Returns:
            ValidationResult with price validation details
        """
        start_time = datetime.utcnow()
        self.logger.info("Starting price validation")

        try:
            # Fetch local prices
            local_prices = await self._get_local_prices()

            # Fetch Stripe prices
            stripe_prices = await self._get_stripe_prices()

            # Compare data and detect discrepancies
            discrepancies = []
            matched_count = 0

            # Check for missing prices in local database
            for stripe_price in stripe_prices:
                local_price = self._find_local_price_by_stripe_id(
                    local_prices, stripe_price.id
                )

                if not local_price:
                    discrepancies.append(
                        SyncDiscrepancy(
                            type=DiscrepancyType.MISSING_LOCAL,
                            entity_type="price",
                            entity_id=stripe_price.id,
                            local_data=None,
                            stripe_data=self._stripe_price_to_dict(stripe_price),
                            severity=SeverityLevel.HIGH,
                            description=f"Price {stripe_price.id} exists in Stripe but not in local database",
                            detected_at=datetime.utcnow(),
                            recommended_action="Create local price record or remove from Stripe",
                        )
                    )
                else:
                    matched_count += 1
                    # Check for price mismatches
                    price_discrepancies = self._compare_price_data(
                        local_price, stripe_price
                    )
                    discrepancies.extend(price_discrepancies)

            # Check for missing prices in Stripe
            for local_price in local_prices:
                if local_price.get("stripe_price_id"):
                    stripe_price = self._find_stripe_price_by_id(
                        stripe_prices, local_price["stripe_price_id"]
                    )

                    if not stripe_price:
                        discrepancies.append(
                            SyncDiscrepancy(
                                type=DiscrepancyType.MISSING_STRIPE,
                                entity_type="price",
                                entity_id=local_price["stripe_price_id"],
                                local_data=local_price,
                                stripe_data=None,
                                severity=SeverityLevel.CRITICAL,
                                description=f"Price {local_price['stripe_price_id']} exists in local database but not in Stripe",
                                detected_at=datetime.utcnow(),
                                recommended_action="Create Stripe price or update local record",
                            )
                        )

            # Add discrepancies to global list
            self.discrepancies.extend(discrepancies)

            end_time = datetime.utcnow()

            return ValidationResult(
                entity_type="prices",
                total_local=len(local_prices),
                total_stripe=len(stripe_prices),
                matched_entities=matched_count,
                discrepancies_count=len(discrepancies),
                discrepancies=discrepancies,
                validation_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
            )

        except Exception as e:
            self.logger.error(f"Price validation failed: {e}", exc_info=True)
            raise

    async def validate_customers(self) -> ValidationResult:
        """
        Validate customer data consistency between local database and Stripe.

        Returns:
            ValidationResult with customer validation details
        """
        start_time = datetime.utcnow()
        self.logger.info("Starting customer validation")

        try:
            # Fetch local customers (firms with Stripe customer IDs)
            local_customers = await self._get_local_customers()

            # Fetch Stripe customers
            stripe_customers = await self._get_stripe_customers()

            # Compare data and detect discrepancies
            discrepancies = []
            matched_count = 0

            # Check for missing customers in local database
            for stripe_customer in stripe_customers:
                local_customer = self._find_local_customer_by_stripe_id(
                    local_customers, stripe_customer.id
                )

                if not local_customer:
                    discrepancies.append(
                        SyncDiscrepancy(
                            type=DiscrepancyType.MISSING_LOCAL,
                            entity_type="customer",
                            entity_id=stripe_customer.id,
                            local_data=None,
                            stripe_data=self._stripe_customer_to_dict(stripe_customer),
                            severity=SeverityLevel.MEDIUM,
                            description=f"Customer {stripe_customer.id} exists in Stripe but not in local database",
                            detected_at=datetime.utcnow(),
                            recommended_action="Link to existing firm or investigate orphaned customer",
                        )
                    )
                else:
                    matched_count += 1
                    # Check for data mismatches
                    customer_discrepancies = self._compare_customer_data(
                        local_customer, stripe_customer
                    )
                    discrepancies.extend(customer_discrepancies)

            # Check for missing customers in Stripe
            for local_customer in local_customers:
                if local_customer.get("stripe_customer_id"):
                    stripe_customer = self._find_stripe_customer_by_id(
                        stripe_customers, local_customer["stripe_customer_id"]
                    )

                    if not stripe_customer:
                        discrepancies.append(
                            SyncDiscrepancy(
                                type=DiscrepancyType.MISSING_STRIPE,
                                entity_type="customer",
                                entity_id=local_customer["stripe_customer_id"],
                                local_data=local_customer,
                                stripe_data=None,
                                severity=SeverityLevel.HIGH,
                                description=f"Customer {local_customer['stripe_customer_id']} exists in local database but not in Stripe",
                                detected_at=datetime.utcnow(),
                                recommended_action="Create Stripe customer or update local record",
                            )
                        )

            # Add discrepancies to global list
            self.discrepancies.extend(discrepancies)

            end_time = datetime.utcnow()

            return ValidationResult(
                entity_type="customers",
                total_local=len(local_customers),
                total_stripe=len(stripe_customers),
                matched_entities=matched_count,
                discrepancies_count=len(discrepancies),
                discrepancies=discrepancies,
                validation_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
            )

        except Exception as e:
            self.logger.error(f"Customer validation failed: {e}", exc_info=True)
            raise

    async def validate_subscriptions(self) -> ValidationResult:
        """
        Validate subscription data consistency between local database and Stripe.

        Returns:
            ValidationResult with subscription validation details
        """
        start_time = datetime.utcnow()
        self.logger.info("Starting subscription validation")

        try:
            # Fetch local subscriptions
            local_subscriptions = await self._get_local_subscriptions()

            # Fetch Stripe subscriptions
            stripe_subscriptions = await self._get_stripe_subscriptions()

            # Compare data and detect discrepancies
            discrepancies = []
            matched_count = 0

            # Check for missing subscriptions in local database
            for stripe_subscription in stripe_subscriptions:
                local_subscription = self._find_local_subscription_by_stripe_id(
                    local_subscriptions, stripe_subscription.id
                )

                if not local_subscription:
                    discrepancies.append(
                        SyncDiscrepancy(
                            type=DiscrepancyType.MISSING_LOCAL,
                            entity_type="subscription",
                            entity_id=stripe_subscription.id,
                            local_data=None,
                            stripe_data=self._stripe_subscription_to_dict(
                                stripe_subscription
                            ),
                            severity=SeverityLevel.CRITICAL,
                            description=f"Subscription {stripe_subscription.id} exists in Stripe but not in local database",
                            detected_at=datetime.utcnow(),
                            recommended_action="Create local subscription record or investigate billing discrepancy",
                        )
                    )
                else:
                    matched_count += 1
                    # Check for data mismatches
                    subscription_discrepancies = self._compare_subscription_data(
                        local_subscription, stripe_subscription
                    )
                    discrepancies.extend(subscription_discrepancies)

            # Check for missing subscriptions in Stripe
            for local_subscription in local_subscriptions:
                if local_subscription.get("payment_provider_subscription_id"):
                    stripe_subscription = self._find_stripe_subscription_by_id(
                        stripe_subscriptions,
                        local_subscription["payment_provider_subscription_id"],
                    )

                    if not stripe_subscription:
                        discrepancies.append(
                            SyncDiscrepancy(
                                type=DiscrepancyType.MISSING_STRIPE,
                                entity_type="subscription",
                                entity_id=local_subscription[
                                    "payment_provider_subscription_id"
                                ],
                                local_data=local_subscription,
                                stripe_data=None,
                                severity=SeverityLevel.CRITICAL,
                                description=f"Subscription {local_subscription['payment_provider_subscription_id']} exists in local database but not in Stripe",
                                detected_at=datetime.utcnow(),
                                recommended_action="Create Stripe subscription or update local record",
                            )
                        )

            # Add discrepancies to global list
            self.discrepancies.extend(discrepancies)

            end_time = datetime.utcnow()

            return ValidationResult(
                entity_type="subscriptions",
                total_local=len(local_subscriptions),
                total_stripe=len(stripe_subscriptions),
                matched_entities=matched_count,
                discrepancies_count=len(discrepancies),
                discrepancies=discrepancies,
                validation_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
            )

        except Exception as e:
            self.logger.error(f"Subscription validation failed: {e}", exc_info=True)
            raise

    # =====================================================
    # Data Fetching Methods
    # =====================================================

    async def _get_local_products(self) -> List[Dict[str, Any]]:
        """Fetch all local products from database."""
        async with get_db_session() as session:
            # Get subscription plans
            plans_result = await session.execute(
                select(SubscriptionPlan).where(SubscriptionPlan.is_active == True)
            )
            plans = plans_result.scalars().all()

            # Get subscription addons
            addons_result = await session.execute(
                select(SubscriptionAddon).where(SubscriptionAddon.is_active == True)
            )
            addons = addons_result.scalars().all()

            # Convert to dictionaries
            products = []

            for plan in plans:
                products.append(
                    {
                        "id": str(plan.id),
                        "type": "plan",
                        "name": plan.name,
                        "code": plan.code,
                        "description": plan.description,
                        "stripe_product_id": plan.stripe_product_id,
                        "is_active": plan.is_active,
                        "features": plan.features,
                        "created_at": (
                            plan.created_at.isoformat() if plan.created_at else None
                        ),
                        "updated_at": (
                            plan.updated_at.isoformat() if plan.updated_at else None
                        ),
                    }
                )

            for addon in addons:
                products.append(
                    {
                        "id": str(addon.id),
                        "type": "addon",
                        "name": addon.name,
                        "code": addon.code,
                        "description": addon.description,
                        "stripe_product_id": addon.stripe_product_id,
                        "is_active": addon.is_active,
                        "category": addon.category,
                        "features": addon.features,
                        "created_at": (
                            addon.created_at.isoformat() if addon.created_at else None
                        ),
                        "updated_at": (
                            addon.updated_at.isoformat() if addon.updated_at else None
                        ),
                    }
                )

            return products

    async def _get_stripe_products(self) -> List[Any]:
        """Fetch all products from Stripe."""
        try:
            products = []

            # Fetch all products with pagination
            has_more = True
            starting_after = None

            while has_more:
                params = {"limit": 100}
                if starting_after:
                    params["starting_after"] = starting_after

                response = stripe.Product.list(**params)
                products.extend(response.data)

                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id

            return products

        except Exception as e:
            self.logger.error(f"Failed to fetch Stripe products: {e}")
            raise

    async def _get_local_prices(self) -> List[Dict[str, Any]]:
        """Fetch all local prices from database."""
        async with get_db_session() as session:
            # This would require a subscription_pricing table
            # For now, return empty list as pricing is handled differently
            # TODO: Implement when subscription_pricing table is available
            return []

    async def _get_stripe_prices(self) -> List[Any]:
        """Fetch all prices from Stripe."""
        try:
            prices = []

            # Fetch all prices with pagination
            has_more = True
            starting_after = None

            while has_more:
                params = {"limit": 100}
                if starting_after:
                    params["starting_after"] = starting_after

                response = stripe.Price.list(**params)
                prices.extend(response.data)

                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id

            return prices

        except Exception as e:
            self.logger.error(f"Failed to fetch Stripe prices: {e}")
            raise

    async def _get_local_customers(self) -> List[Dict[str, Any]]:
        """Fetch all local customers (firms) with Stripe customer IDs."""
        async with get_db_session() as session:
            result = await session.execute(
                select(Firm).where(Firm.stripe_customer_id.isnot(None))
            )
            firms = result.scalars().all()

            customers = []
            for firm in firms:
                customers.append(
                    {
                        "id": str(firm.id),
                        "name": firm.name,
                        "email": firm.email,
                        "stripe_customer_id": firm.stripe_customer_id,
                        "created_at": (
                            firm.created_at.isoformat() if firm.created_at else None
                        ),
                        "updated_at": (
                            firm.updated_at.isoformat() if firm.updated_at else None
                        ),
                    }
                )

            return customers

    async def _get_stripe_customers(self) -> List[Any]:
        """Fetch all customers from Stripe."""
        try:
            customers = []

            # Fetch all customers with pagination
            has_more = True
            starting_after = None

            while has_more:
                params = {"limit": 100}
                if starting_after:
                    params["starting_after"] = starting_after

                response = stripe.Customer.list(**params)
                customers.extend(response.data)

                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id

            return customers

        except Exception as e:
            self.logger.error(f"Failed to fetch Stripe customers: {e}")
            raise

    async def _get_local_subscriptions(self) -> List[Dict[str, Any]]:
        """Fetch all local subscriptions from database."""
        async with get_db_session() as session:
            result = await session.execute(
                select(TenantSubscription).where(
                    TenantSubscription.payment_provider_subscription_id.isnot(None)
                )
            )
            subscriptions = result.scalars().all()

            subscription_list = []
            for sub in subscriptions:
                subscription_list.append(
                    {
                        "id": str(sub.id),
                        "tenant_id": str(sub.tenant_id),
                        "plan_id": str(sub.plan_id),
                        "status": sub.status,
                        "payment_provider_subscription_id": sub.payment_provider_subscription_id,
                        "current_period_start": (
                            sub.current_period_start.isoformat()
                            if sub.current_period_start
                            else None
                        ),
                        "current_period_end": (
                            sub.current_period_end.isoformat()
                            if sub.current_period_end
                            else None
                        ),
                        "trial_start": (
                            sub.trial_start.isoformat() if sub.trial_start else None
                        ),
                        "trial_end": (
                            sub.trial_end.isoformat() if sub.trial_end else None
                        ),
                        "created_at": (
                            sub.created_at.isoformat() if sub.created_at else None
                        ),
                        "updated_at": (
                            sub.updated_at.isoformat() if sub.updated_at else None
                        ),
                    }
                )

            return subscription_list

    async def _get_stripe_subscriptions(self) -> List[Any]:
        """Fetch all subscriptions from Stripe."""
        try:
            subscriptions = []

            # Fetch all subscriptions with pagination
            has_more = True
            starting_after = None

            while has_more:
                params = {"limit": 100, "status": "all"}
                if starting_after:
                    params["starting_after"] = starting_after

                response = stripe.Subscription.list(**params)
                subscriptions.extend(response.data)

                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id

            return subscriptions

        except Exception as e:
            self.logger.error(f"Failed to fetch Stripe subscriptions: {e}")
            raise

    # =====================================================
    # Data Comparison Methods
    # =====================================================

    def _compare_product_data(
        self, local_product: Dict[str, Any], stripe_product: Any
    ) -> List[SyncDiscrepancy]:
        """Compare local product data with Stripe product data."""
        discrepancies = []

        # Compare name
        if local_product["name"] != stripe_product.name:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.DATA_MISMATCH,
                    entity_type="product",
                    entity_id=stripe_product.id,
                    local_data=local_product,
                    stripe_data=self._stripe_product_to_dict(stripe_product),
                    severity=SeverityLevel.MEDIUM,
                    description=f"Product name mismatch: local='{local_product['name']}', stripe='{stripe_product.name}'",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "name": {
                            "local": local_product["name"],
                            "stripe": stripe_product.name,
                        }
                    },
                    recommended_action="Update local or Stripe product name to match",
                )
            )

        # Compare description
        local_desc = local_product.get("description", "")
        stripe_desc = getattr(stripe_product, "description", "") or ""
        if local_desc != stripe_desc:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.DATA_MISMATCH,
                    entity_type="product",
                    entity_id=stripe_product.id,
                    local_data=local_product,
                    stripe_data=self._stripe_product_to_dict(stripe_product),
                    severity=SeverityLevel.LOW,
                    description=f"Product description mismatch",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "description": {"local": local_desc, "stripe": stripe_desc}
                    },
                    recommended_action="Update product description to match",
                )
            )

        # Compare active status
        stripe_active = getattr(stripe_product, "active", True)
        if local_product["is_active"] != stripe_active:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.STATUS_MISMATCH,
                    entity_type="product",
                    entity_id=stripe_product.id,
                    local_data=local_product,
                    stripe_data=self._stripe_product_to_dict(stripe_product),
                    severity=SeverityLevel.HIGH,
                    description=f"Product status mismatch: local={local_product['is_active']}, stripe={stripe_active}",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "active": {
                            "local": local_product["is_active"],
                            "stripe": stripe_active,
                        }
                    },
                    recommended_action="Synchronize product active status",
                )
            )

        return discrepancies

    def _compare_price_data(
        self, local_price: Dict[str, Any], stripe_price: Any
    ) -> List[SyncDiscrepancy]:
        """Compare local price data with Stripe price data."""
        discrepancies = []

        # Compare currency
        local_currency = local_price.get("currency", "").upper()
        stripe_currency = getattr(stripe_price, "currency", "").upper()
        if local_currency != stripe_currency:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.PRICE_MISMATCH,
                    entity_type="price",
                    entity_id=stripe_price.id,
                    local_data=local_price,
                    stripe_data=self._stripe_price_to_dict(stripe_price),
                    severity=SeverityLevel.HIGH,
                    description=f"Price currency mismatch: local={local_currency}, stripe={stripe_currency}",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "currency": {"local": local_currency, "stripe": stripe_currency}
                    },
                    recommended_action="Verify and synchronize currency settings",
                )
            )

        # Compare unit amount
        local_amount = local_price.get("unit_amount", 0)
        stripe_amount = getattr(stripe_price, "unit_amount", 0)
        if local_amount != stripe_amount:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.PRICE_MISMATCH,
                    entity_type="price",
                    entity_id=stripe_price.id,
                    local_data=local_price,
                    stripe_data=self._stripe_price_to_dict(stripe_price),
                    severity=SeverityLevel.CRITICAL,
                    description=f"Price amount mismatch: local={local_amount}, stripe={stripe_amount}",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "unit_amount": {"local": local_amount, "stripe": stripe_amount}
                    },
                    recommended_action="Immediately synchronize pricing to prevent billing issues",
                )
            )

        return discrepancies

    def _compare_customer_data(
        self, local_customer: Dict[str, Any], stripe_customer: Any
    ) -> List[SyncDiscrepancy]:
        """Compare local customer data with Stripe customer data."""
        discrepancies = []

        # Compare email
        local_email = local_customer.get("email", "")
        stripe_email = getattr(stripe_customer, "email", "") or ""
        if local_email != stripe_email:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.DATA_MISMATCH,
                    entity_type="customer",
                    entity_id=stripe_customer.id,
                    local_data=local_customer,
                    stripe_data=self._stripe_customer_to_dict(stripe_customer),
                    severity=SeverityLevel.MEDIUM,
                    description=f"Customer email mismatch: local='{local_email}', stripe='{stripe_email}'",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "email": {"local": local_email, "stripe": stripe_email}
                    },
                    recommended_action="Update customer email to match",
                )
            )

        # Compare name
        local_name = local_customer.get("name", "")
        stripe_name = getattr(stripe_customer, "name", "") or ""
        if local_name != stripe_name:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.DATA_MISMATCH,
                    entity_type="customer",
                    entity_id=stripe_customer.id,
                    local_data=local_customer,
                    stripe_data=self._stripe_customer_to_dict(stripe_customer),
                    severity=SeverityLevel.LOW,
                    description=f"Customer name mismatch: local='{local_name}', stripe='{stripe_name}'",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "name": {"local": local_name, "stripe": stripe_name}
                    },
                    recommended_action="Update customer name to match",
                )
            )

        return discrepancies

    def _compare_subscription_data(
        self, local_subscription: Dict[str, Any], stripe_subscription: Any
    ) -> List[SyncDiscrepancy]:
        """Compare local subscription data with Stripe subscription data."""
        discrepancies = []

        # Compare status
        local_status = local_subscription.get("status", "")
        stripe_status = getattr(stripe_subscription, "status", "")
        if local_status != stripe_status:
            discrepancies.append(
                SyncDiscrepancy(
                    type=DiscrepancyType.STATUS_MISMATCH,
                    entity_type="subscription",
                    entity_id=stripe_subscription.id,
                    local_data=local_subscription,
                    stripe_data=self._stripe_subscription_to_dict(stripe_subscription),
                    severity=SeverityLevel.HIGH,
                    description=f"Subscription status mismatch: local='{local_status}', stripe='{stripe_status}'",
                    detected_at=datetime.utcnow(),
                    field_differences={
                        "status": {"local": local_status, "stripe": stripe_status}
                    },
                    recommended_action="Synchronize subscription status",
                )
            )

        return discrepancies

    # =====================================================
    # Utility Methods
    # =====================================================

    def _find_local_product_by_stripe_id(
        self, local_products: List[Dict[str, Any]], stripe_id: str
    ) -> Optional[Dict[str, Any]]:
        """Find local product by Stripe product ID."""
        return next(
            (p for p in local_products if p.get("stripe_product_id") == stripe_id), None
        )

    def _find_stripe_product_by_id(
        self, stripe_products: List[Any], product_id: str
    ) -> Optional[Any]:
        """Find Stripe product by ID."""
        return next((p for p in stripe_products if p.id == product_id), None)

    def _find_local_price_by_stripe_id(
        self, local_prices: List[Dict[str, Any]], stripe_id: str
    ) -> Optional[Dict[str, Any]]:
        """Find local price by Stripe price ID."""
        return next(
            (p for p in local_prices if p.get("stripe_price_id") == stripe_id), None
        )

    def _find_stripe_price_by_id(
        self, stripe_prices: List[Any], price_id: str
    ) -> Optional[Any]:
        """Find Stripe price by ID."""
        return next((p for p in stripe_prices if p.id == price_id), None)

    def _find_local_customer_by_stripe_id(
        self, local_customers: List[Dict[str, Any]], stripe_id: str
    ) -> Optional[Dict[str, Any]]:
        """Find local customer by Stripe customer ID."""
        return next(
            (c for c in local_customers if c.get("stripe_customer_id") == stripe_id),
            None,
        )

    def _find_stripe_customer_by_id(
        self, stripe_customers: List[Any], customer_id: str
    ) -> Optional[Any]:
        """Find Stripe customer by ID."""
        return next((c for c in stripe_customers if c.id == customer_id), None)

    def _find_local_subscription_by_stripe_id(
        self, local_subscriptions: List[Dict[str, Any]], stripe_id: str
    ) -> Optional[Dict[str, Any]]:
        """Find local subscription by Stripe subscription ID."""
        return next(
            (
                s
                for s in local_subscriptions
                if s.get("payment_provider_subscription_id") == stripe_id
            ),
            None,
        )

    def _find_stripe_subscription_by_id(
        self, stripe_subscriptions: List[Any], subscription_id: str
    ) -> Optional[Any]:
        """Find Stripe subscription by ID."""
        return next((s for s in stripe_subscriptions if s.id == subscription_id), None)

    def _stripe_product_to_dict(self, stripe_product: Any) -> Dict[str, Any]:
        """Convert Stripe product to dictionary."""
        return {
            "id": stripe_product.id,
            "name": stripe_product.name,
            "description": getattr(stripe_product, "description", None),
            "active": getattr(stripe_product, "active", True),
            "metadata": getattr(stripe_product, "metadata", {}),
            "created": getattr(stripe_product, "created", None),
            "updated": getattr(stripe_product, "updated", None),
        }

    def _stripe_price_to_dict(self, stripe_price: Any) -> Dict[str, Any]:
        """Convert Stripe price to dictionary."""
        return {
            "id": stripe_price.id,
            "product": getattr(stripe_price, "product", None),
            "currency": getattr(stripe_price, "currency", None),
            "unit_amount": getattr(stripe_price, "unit_amount", None),
            "recurring": getattr(stripe_price, "recurring", None),
            "active": getattr(stripe_price, "active", True),
            "metadata": getattr(stripe_price, "metadata", {}),
            "created": getattr(stripe_price, "created", None),
        }

    def _stripe_customer_to_dict(self, stripe_customer: Any) -> Dict[str, Any]:
        """Convert Stripe customer to dictionary."""
        return {
            "id": stripe_customer.id,
            "email": getattr(stripe_customer, "email", None),
            "name": getattr(stripe_customer, "name", None),
            "description": getattr(stripe_customer, "description", None),
            "metadata": getattr(stripe_customer, "metadata", {}),
            "created": getattr(stripe_customer, "created", None),
        }

    def _stripe_subscription_to_dict(self, stripe_subscription: Any) -> Dict[str, Any]:
        """Convert Stripe subscription to dictionary."""
        return {
            "id": stripe_subscription.id,
            "customer": getattr(stripe_subscription, "customer", None),
            "status": getattr(stripe_subscription, "status", None),
            "current_period_start": getattr(
                stripe_subscription, "current_period_start", None
            ),
            "current_period_end": getattr(
                stripe_subscription, "current_period_end", None
            ),
            "trial_start": getattr(stripe_subscription, "trial_start", None),
            "trial_end": getattr(stripe_subscription, "trial_end", None),
            "metadata": getattr(stripe_subscription, "metadata", {}),
            "created": getattr(stripe_subscription, "created", None),
        }

    def _generate_validation_summary(
        self, validation_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive validation summary."""
        total_discrepancies = len(self.discrepancies)
        critical_count = len(
            [d for d in self.discrepancies if d.severity == SeverityLevel.CRITICAL]
        )
        high_count = len(
            [d for d in self.discrepancies if d.severity == SeverityLevel.HIGH]
        )
        medium_count = len(
            [d for d in self.discrepancies if d.severity == SeverityLevel.MEDIUM]
        )
        low_count = len(
            [d for d in self.discrepancies if d.severity == SeverityLevel.LOW]
        )

        return {
            "overall_status": (
                "healthy" if total_discrepancies == 0 else "issues_detected"
            ),
            "total_discrepancies": total_discrepancies,
            "severity_breakdown": {
                "critical": critical_count,
                "high": high_count,
                "medium": medium_count,
                "low": low_count,
            },
            "entity_summary": {
                entity_type: {
                    "total_local": result.total_local,
                    "total_stripe": result.total_stripe,
                    "matched": result.matched_entities,
                    "discrepancies": result.discrepancies_count,
                }
                for entity_type, result in validation_results.items()
                if isinstance(result, ValidationResult)
            },
            "recommendations": self._generate_recommendations(),
            "next_validation_recommended": (
                datetime.utcnow() + timedelta(hours=24)
            ).isoformat(),
        }

    def _generate_recommendations(self) -> List[str]:
        """Generate actionable recommendations based on discrepancies."""
        recommendations = []

        critical_count = len(
            [d for d in self.discrepancies if d.severity == SeverityLevel.CRITICAL]
        )
        high_count = len(
            [d for d in self.discrepancies if d.severity == SeverityLevel.HIGH]
        )

        if critical_count > 0:
            recommendations.append(
                f"URGENT: Address {critical_count} critical discrepancies immediately to prevent billing issues"
            )

        if high_count > 0:
            recommendations.append(
                f"HIGH PRIORITY: Resolve {high_count} high-priority discrepancies within 24 hours"
            )

        # Check for specific patterns
        missing_local = len(
            [d for d in self.discrepancies if d.type == DiscrepancyType.MISSING_LOCAL]
        )
        missing_stripe = len(
            [d for d in self.discrepancies if d.type == DiscrepancyType.MISSING_STRIPE]
        )

        if missing_local > 5:
            recommendations.append(
                "Consider running a full sync from Stripe to local database"
            )

        if missing_stripe > 5:
            recommendations.append(
                "Consider running a full sync from local database to Stripe"
            )

        if len(self.discrepancies) == 0:
            recommendations.append(
                "All data is synchronized. Schedule regular validation checks."
            )

        return recommendations

    async def _store_validation_results(
        self, validation_results: Dict[str, Any]
    ) -> None:
        """Store validation results for audit trail."""
        try:
            # TODO: Implement storage to validation_results table
            # For now, just log the results
            self.logger.info(
                f"Validation completed with {len(self.discrepancies)} discrepancies",
                extra={
                    "validation_summary": validation_results["summary"],
                    "total_discrepancies": len(self.discrepancies),
                    "critical_issues": len(
                        [
                            d
                            for d in self.discrepancies
                            if d.severity == SeverityLevel.CRITICAL
                        ]
                    ),
                },
            )
        except Exception as e:
            self.logger.error(f"Failed to store validation results: {e}")


# Global instance
stripe_sync_validator = StripeSyncValidator()
