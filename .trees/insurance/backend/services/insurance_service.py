"""
Insurance Service Layer

This service provides business logic for insurance battle station functionality,
including CRUD operations for policies, carriers, demands, and negotiation events.
"""

import hashlib
import math
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional
from uuid import UUID

import structlog
from sqlalchemy import and_, desc, func, select, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from backend.models.insurance import (
    CarrierStyleProfileCreate,
    CarrierStyleProfileORM,
    CarrierStyleProfileResponse,
    CoverageLimitORM,
    DemandLetterCreate,
    DemandLetterORM,
    DemandLetterResponse,
    DemandLetterUpdate,
    InsurancePolicyCreate,
    InsurancePolicyORM,
    InsurancePolicyResponse,
    InsurancePolicyUpdate,
    InsurerCreate,
    InsurerORM,
    InsurerResponse,
    InsurerUpdate,
    NegotiationEventCreate,
    NegotiationEventORM,
    NegotiationEventResponse,
    NegotiationSummary,
    OfferAnalytics,
)


class InsuranceService:
    """Service class for insurance battle station operations."""

    def __init__(
        self,
        db: AsyncSession,
        tenant_id: Optional[UUID] = None,
        user_id: Optional[UUID] = None,
    ):
        self.db = db
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.logger = structlog.get_logger(__name__).bind(
            tenant_id=str(tenant_id) if tenant_id else None,
            user_id=str(user_id) if user_id else None,
        )

    def _sanitize_for_logging(self, data: Dict) -> Dict[str, Optional[str]]:
        """Sanitize data for logging to prevent PHI exposure."""
        if not data:
            return {}

        sanitized: Dict[str, Optional[str]] = {}
        phi_fields = {
            "named_insured",
            "policy_number",
            "note",
            "insurer_name",
            "description",
        }

        for key, value in data.items():
            if key in phi_fields:
                if value:
                    # Log only first 3 chars + masked
                    sanitized[f"{key}_masked"] = f"{str(value)[:3]}***"
                else:
                    sanitized[f"{key}_masked"] = None
            elif key.endswith("_id"):
                # IDs are safe to log
                sanitized[key] = str(value) if value else None
            else:
                sanitized[key] = str(value) if value is not None else None

        return sanitized

    def _validate_tenant_access(self, record_tenant_id: UUID) -> None:
        """Validate that current user has access to tenant data."""
        if self.tenant_id and record_tenant_id != self.tenant_id:
            raise ValueError("Access denied: tenant mismatch")

    def _hash_phi_data(self, data: str) -> Optional[str]:
        """Generate hash of PHI data for deduplication without storing plaintext."""
        if not data or not data.strip():
            return None
        return hashlib.sha256(f"{data}:insurance-phi-salt".encode()).hexdigest()

    # ==================
    # Insurer Operations
    # ==================

    async def create_insurer(self, insurer_data: InsurerCreate) -> InsurerResponse:
        """Create a new insurer/carrier."""
        insurer = InsurerORM(**insurer_data.dict())
        self.db.add(insurer)
        await self.db.commit()
        await self.db.refresh(insurer)
        return InsurerResponse.from_orm(insurer)

    async def get_insurer(self, insurer_id: UUID) -> Optional[InsurerResponse]:
        """Get insurer by ID."""
        query = select(InsurerORM).where(InsurerORM.id == insurer_id)
        result = await self.db.execute(query)
        insurer = result.scalar_one_or_none()
        return InsurerResponse.from_orm(insurer) if insurer else None

    async def get_insurers(
        self, skip: int = 0, limit: int = 100
    ) -> List[InsurerResponse]:
        """Get list of insurers with pagination."""
        query = select(InsurerORM).order_by(InsurerORM.name).offset(skip).limit(limit)
        result = await self.db.execute(query)
        insurers = result.scalars().all()
        return [InsurerResponse.from_orm(insurer) for insurer in insurers]

    async def update_insurer(
        self, insurer_id: UUID, update_data: InsurerUpdate
    ) -> Optional[InsurerResponse]:
        """Update insurer information."""
        query = select(InsurerORM).where(InsurerORM.id == insurer_id)
        result = await self.db.execute(query)
        insurer = result.scalar_one_or_none()

        if not insurer:
            return None

        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(insurer, field, value)

        await self.db.commit()
        await self.db.refresh(insurer)
        return InsurerResponse.from_orm(insurer)

    async def search_insurers_by_name(
        self, name: str, limit: int = 10
    ) -> List[InsurerResponse]:
        """Search insurers by name using trigram similarity."""
        query = (
            select(InsurerORM)
            .where(func.similarity(InsurerORM.name, name) > 0.3)
            .order_by(desc(func.similarity(InsurerORM.name, name)))
            .limit(limit)
        )
        result = await self.db.execute(query)
        insurers = result.scalars().all()
        return [InsurerResponse.from_orm(insurer) for insurer in insurers]

    async def get_insurer_by_naic(self, naic_code: str) -> Optional[InsurerResponse]:
        """Get insurer by NAIC code."""
        query = select(InsurerORM).where(InsurerORM.naic_code == naic_code)
        result = await self.db.execute(query)
        insurer = result.scalar_one_or_none()
        return InsurerResponse.from_orm(insurer) if insurer else None

    # ==========================
    # Insurance Policy Operations
    # ==========================

    async def create_insurance_policy(
        self, policy_data: InsurancePolicyCreate
    ) -> InsurancePolicyResponse:
        """Create a new insurance policy with coverages."""
        # Extract coverages from policy data
        coverages_data = policy_data.coverages
        policy_dict = policy_data.dict(exclude={"coverages"})

        # Create policy
        policy = InsurancePolicyORM(**policy_dict)
        self.db.add(policy)
        await self.db.flush()  # Flush to get policy ID

        # Create coverages
        for coverage_data in coverages_data:
            coverage = CoverageLimitORM(policy_id=policy.id, **coverage_data.dict())
            self.db.add(coverage)

        await self.db.commit()

        # Refresh and load relationships
        await self.db.refresh(policy)
        query = (
            select(InsurancePolicyORM)
            .where(InsurancePolicyORM.id == policy.id)
            .options(
                selectinload(InsurancePolicyORM.coverages),
                joinedload(InsurancePolicyORM.insurer),
            )
        )
        result = await self.db.execute(query)
        policy_with_relations = result.scalar_one()

        return InsurancePolicyResponse.from_orm(policy_with_relations)

    async def get_insurance_policy(
        self, policy_id: UUID, tenant_id: UUID
    ) -> Optional[InsurancePolicyResponse]:
        """Get insurance policy by ID with tenant isolation."""
        query = (
            select(InsurancePolicyORM)
            .where(
                and_(
                    InsurancePolicyORM.id == policy_id,
                    InsurancePolicyORM.tenant_id == tenant_id,
                )
            )
            .options(
                selectinload(InsurancePolicyORM.coverages),
                joinedload(InsurancePolicyORM.insurer),
            )
        )
        result = await self.db.execute(query)
        policy = result.scalar_one_or_none()
        return InsurancePolicyResponse.from_orm(policy) if policy else None

    async def get_policies_by_matter(
        self, matter_id: UUID, tenant_id: UUID
    ) -> List[InsurancePolicyResponse]:
        """Get all insurance policies for a matter."""
        query = (
            select(InsurancePolicyORM)
            .where(
                and_(
                    InsurancePolicyORM.matter_id == matter_id,
                    InsurancePolicyORM.tenant_id == tenant_id,
                )
            )
            .options(
                selectinload(InsurancePolicyORM.coverages),
                joinedload(InsurancePolicyORM.insurer),
            )
            .order_by(InsurancePolicyORM.created_at.desc())
        )
        result = await self.db.execute(query)
        policies = result.scalars().all()
        return [InsurancePolicyResponse.from_orm(policy) for policy in policies]

    async def update_insurance_policy(
        self, policy_id: UUID, tenant_id: UUID, update_data: InsurancePolicyUpdate
    ) -> Optional[InsurancePolicyResponse]:
        """Update insurance policy."""
        query = select(InsurancePolicyORM).where(
            and_(
                InsurancePolicyORM.id == policy_id,
                InsurancePolicyORM.tenant_id == tenant_id,
            )
        )
        result = await self.db.execute(query)
        policy = result.scalar_one_or_none()

        if not policy:
            return None

        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(policy, field, value)

        await self.db.commit()

        # Refresh with relationships
        query = (
            select(InsurancePolicyORM)
            .where(InsurancePolicyORM.id == policy_id)
            .options(
                selectinload(InsurancePolicyORM.coverages),
                joinedload(InsurancePolicyORM.insurer),
            )
        )
        result = await self.db.execute(query)
        updated_policy = result.scalar_one()

        return InsurancePolicyResponse.from_orm(updated_policy)

    # ================================
    # Carrier Style Profile Operations
    # ================================

    async def create_carrier_profile(
        self, profile_data: CarrierStyleProfileCreate
    ) -> CarrierStyleProfileResponse:
        """Create carrier style profile."""
        profile = CarrierStyleProfileORM(**profile_data.dict())
        self.db.add(profile)
        await self.db.commit()
        await self.db.refresh(profile)

        # Load with insurer relationship
        query = (
            select(CarrierStyleProfileORM)
            .where(CarrierStyleProfileORM.id == profile.id)
            .options(joinedload(CarrierStyleProfileORM.insurer))
        )
        result = await self.db.execute(query)
        profile_with_insurer = result.scalar_one()

        return CarrierStyleProfileResponse.from_orm(profile_with_insurer)

    async def get_carrier_profile(
        self, insurer_id: UUID, tenant_id: Optional[UUID] = None
    ) -> Optional[CarrierStyleProfileResponse]:
        """Get carrier style profile, preferring tenant-specific override."""

        # First try to get tenant-specific profile if tenant_id provided
        if tenant_id:
            query = (
                select(CarrierStyleProfileORM)
                .where(
                    and_(
                        CarrierStyleProfileORM.insurer_id == insurer_id,
                        CarrierStyleProfileORM.tenant_id == tenant_id,
                    )
                )
                .options(joinedload(CarrierStyleProfileORM.insurer))
            )
            result = await self.db.execute(query)
            profile = result.scalar_one_or_none()

            if profile:
                return CarrierStyleProfileResponse.from_orm(profile)

        # Fallback to default profile (tenant_id is null)
        query = (
            select(CarrierStyleProfileORM)
            .where(
                and_(
                    CarrierStyleProfileORM.insurer_id == insurer_id,
                    CarrierStyleProfileORM.tenant_id.is_(None),
                )
            )
            .options(joinedload(CarrierStyleProfileORM.insurer))
        )
        result = await self.db.execute(query)
        profile = result.scalar_one_or_none()

        return CarrierStyleProfileResponse.from_orm(profile) if profile else None

    # ==========================
    # Demand Letter Operations
    # ==========================

    async def create_demand_letter(
        self, demand_data: DemandLetterCreate
    ) -> DemandLetterResponse:
        """Create demand letter."""
        demand = DemandLetterORM(**demand_data.dict())
        self.db.add(demand)
        await self.db.commit()
        await self.db.refresh(demand)

        # Load with insurer relationship
        query = (
            select(DemandLetterORM)
            .where(DemandLetterORM.id == demand.id)
            .options(joinedload(DemandLetterORM.insurer))
        )
        result = await self.db.execute(query)
        demand_with_insurer = result.scalar_one()

        return DemandLetterResponse.from_orm(demand_with_insurer)

    async def get_demands_by_matter(
        self, matter_id: UUID, tenant_id: UUID
    ) -> List[DemandLetterResponse]:
        """Get all demand letters for a matter."""
        query = (
            select(DemandLetterORM)
            .where(
                and_(
                    DemandLetterORM.matter_id == matter_id,
                    DemandLetterORM.tenant_id == tenant_id,
                )
            )
            .options(joinedload(DemandLetterORM.insurer))
            .order_by(DemandLetterORM.generated_at.desc())
        )
        result = await self.db.execute(query)
        demands = result.scalars().all()
        return [DemandLetterResponse.from_orm(demand) for demand in demands]

    async def update_demand_letter(
        self, demand_id: UUID, tenant_id: UUID, update_data: DemandLetterUpdate
    ) -> Optional[DemandLetterResponse]:
        """Update demand letter (e.g., mark as sent)."""
        query = select(DemandLetterORM).where(
            and_(
                DemandLetterORM.id == demand_id, DemandLetterORM.tenant_id == tenant_id
            )
        )
        result = await self.db.execute(query)
        demand = result.scalar_one_or_none()

        if not demand:
            return None

        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(demand, field, value)

        await self.db.commit()

        # Refresh with relationships
        query = (
            select(DemandLetterORM)
            .where(DemandLetterORM.id == demand_id)
            .options(joinedload(DemandLetterORM.insurer))
        )
        result = await self.db.execute(query)
        updated_demand = result.scalar_one()

        return DemandLetterResponse.from_orm(updated_demand)

    # ==============================
    # Negotiation Event Operations
    # ==============================

    async def create_negotiation_event(
        self, event_data: NegotiationEventCreate
    ) -> NegotiationEventResponse:
        """Create negotiation event with analytics."""
        event_dict = event_data.dict()

        # Calculate analytics if this is an offer with amount
        if event_data.event_type == "offer" and event_data.amount:
            analytics = await self._calculate_offer_analytics(
                event_data.matter_id, event_data.tenant_id, event_data.amount
            )
            event_dict.update(analytics)

        event = NegotiationEventORM(**event_dict)
        self.db.add(event)
        await self.db.commit()
        await self.db.refresh(event)

        return NegotiationEventResponse.from_orm(event)

    async def get_negotiation_events(
        self, matter_id: UUID, tenant_id: UUID, limit: int = 50
    ) -> List[NegotiationEventResponse]:
        """Get negotiation events for a matter in chronological order."""
        query = (
            select(NegotiationEventORM)
            .where(
                and_(
                    NegotiationEventORM.matter_id == matter_id,
                    NegotiationEventORM.tenant_id == tenant_id,
                )
            )
            .order_by(NegotiationEventORM.timestamp.desc())
            .limit(limit)
        )
        result = await self.db.execute(query)
        events = result.scalars().all()
        return [NegotiationEventResponse.from_orm(event) for event in events]

    async def get_negotiation_summary(
        self, matter_id: UUID, tenant_id: UUID
    ) -> NegotiationSummary:
        """Get summary analytics for matter negotiations."""
        # Get all events for the matter
        query = (
            select(NegotiationEventORM)
            .where(
                and_(
                    NegotiationEventORM.matter_id == matter_id,
                    NegotiationEventORM.tenant_id == tenant_id,
                )
            )
            .order_by(NegotiationEventORM.timestamp)
        )
        result = await self.db.execute(query)
        events = result.scalars().all()

        if not events:
            return NegotiationSummary(
                matter_id=matter_id, total_events=0, events_by_type={}
            )

        # Calculate summary stats
        first_event = events[0]
        last_event = events[-1]
        days_in_negotiation = (last_event.timestamp - first_event.timestamp).days

        # Find first demand and latest offer
        first_demand = None
        latest_offer = None

        for event in events:
            if event.event_type == "demand" and event.amount and first_demand is None:
                first_demand = Decimal(str(event.amount))
            if event.event_type == "offer" and event.amount:
                latest_offer = Decimal(str(event.amount))

        # Count events by type
        events_by_type: Dict[str, int] = {}
        for event in events:
            event_type = str(event.event_type)
            events_by_type[event_type] = (
                events_by_type.get(event_type, 0) + 1
            )

        # Get analytics for latest offer if available
        analytics = None
        if latest_offer:
            offer_analytics = await self._calculate_offer_analytics(
                matter_id, tenant_id, latest_offer
            )
            analytics = OfferAnalytics(
                z_score=offer_analytics.get("z_score"),
                expected_offer_low=offer_analytics.get("expected_offer_low"),
                expected_offer_high=offer_analytics.get("expected_offer_high"),
                is_outlier=abs(offer_analytics.get("z_score", 0)) > 2,
            )

        return NegotiationSummary(
            matter_id=matter_id,
            total_events=len(events),
            first_demand=first_demand,
            latest_offer=latest_offer,
            days_in_negotiation=(
                days_in_negotiation if days_in_negotiation > 0 else None
            ),
            events_by_type=events_by_type,
            analytics=analytics,
        )

    # ===================
    # Analytics Methods
    # ===================

    async def _calculate_offer_analytics(
        self, matter_id: UUID, tenant_id: UUID, offer_amount: Decimal
    ) -> Dict:
        """Calculate z-score and expected offer range using simple regression."""

        # Get historical offer data for this tenant (firm)
        historical_data = await self._get_historical_offers(tenant_id)

        if len(historical_data) < 10:  # Need minimum data for analytics
            return {
                "z_score": None,
                "expected_offer_low": None,
                "expected_offer_high": None,
            }

        # Simple analytics using historical averages
        # In production, this would use proper regression with specials, venue, etc.
        amounts = [float(row["amount"]) for row in historical_data]
        mean_offer = sum(amounts) / len(amounts)
        variance = sum((x - mean_offer) ** 2 for x in amounts) / len(amounts)
        std_dev = math.sqrt(variance)

        if std_dev == 0:  # All offers are the same
            z_score = 0.0
        else:
            z_score = (float(offer_amount) - mean_offer) / std_dev

        # Expected range (±1 std dev)
        expected_low = max(0, mean_offer - std_dev)
        expected_high = mean_offer + std_dev

        return {
            "z_score": round(z_score, 2),
            "expected_offer_low": Decimal(str(round(expected_low, 2))),
            "expected_offer_high": Decimal(str(round(expected_high, 2))),
        }

    async def _get_historical_offers(
        self, tenant_id: UUID, limit: int = 100
    ) -> List[Dict]:
        """Get historical offer data for analytics."""
        query = text(
            """
            SELECT amount, timestamp
            FROM negotiation_events 
            WHERE tenant_id = :tenant_id 
                AND event_type = 'offer' 
                AND amount IS NOT NULL
                AND timestamp > NOW() - INTERVAL '2 years'
            ORDER BY timestamp DESC
            LIMIT :limit
        """
        )

        result = await self.db.execute(
            query, {"tenant_id": str(tenant_id), "limit": limit}
        )

        return [{"amount": row[0], "timestamp": row[1]} for row in result.fetchall()]

    # ==================
    # Utility Methods
    # ==================

    async def get_policies_needing_analysis(
        self, tenant_id: UUID, confidence_threshold: float = 0.8
    ) -> List[InsurancePolicyResponse]:
        """Get policies with low parse confidence that need manual review."""
        query = (
            select(InsurancePolicyORM)
            .where(
                and_(
                    InsurancePolicyORM.tenant_id == tenant_id,
                    InsurancePolicyORM.parse_confidence < confidence_threshold,
                )
            )
            .options(
                selectinload(InsurancePolicyORM.coverages),
                joinedload(InsurancePolicyORM.insurer),
            )
            .order_by(InsurancePolicyORM.parse_confidence)
        )
        result = await self.db.execute(query)
        policies = result.scalars().all()
        return [InsurancePolicyResponse.from_orm(policy) for policy in policies]

    async def get_matters_with_outlier_offers(self, tenant_id: UUID) -> List[UUID]:
        """Get matters with recent outlier offers (|z_score| > 2)."""
        query = select(NegotiationEventORM.matter_id.distinct()).where(
            and_(
                NegotiationEventORM.tenant_id == tenant_id,
                NegotiationEventORM.event_type == "offer",
                func.abs(NegotiationEventORM.z_score) > 2,
                NegotiationEventORM.timestamp > datetime.now() - timedelta(days=30),
            )
        )
        result = await self.db.execute(query)
        return [row[0] for row in result.fetchall()]

    async def delete_negotiation_event(self, event_id: UUID, tenant_id: UUID) -> bool:
        """Delete negotiation event with tenant isolation."""
        query = select(NegotiationEventORM).where(
            and_(
                NegotiationEventORM.id == event_id,
                NegotiationEventORM.tenant_id == tenant_id,
            )
        )
        result = await self.db.execute(query)
        event = result.scalar_one_or_none()

        if not event:
            return False

        await self.db.delete(event)
        await self.db.commit()
        return True
