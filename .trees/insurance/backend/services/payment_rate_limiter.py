"""
Production-Grade Rate Limiting for Payment Endpoints

Implements Redis-based distributed rate limiting specifically designed for payment
processing endpoints with enhanced security and monitoring capabilities.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from utils.redis_client import get_redis_client
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class RateLimitType(Enum):
    """Types of rate limits for different payment operations."""

    CHECKOUT_SESSION = "checkout_session"
    PAYMENT_METHOD = "payment_method"
    WEBHOOK = "webhook"
    SUBSCRIPTION_CHANGE = "subscription_change"
    REFUND = "refund"


class RateLimitResult:
    """Result of a rate limit check."""

    def __init__(
        self,
        allowed: bool,
        remaining: int = 0,
        reset_time: int = 0,
        retry_after: int = 0,
        limit_type: str = "",
    ):
        self.allowed = allowed
        self.remaining = remaining
        self.reset_time = reset_time
        self.retry_after = retry_after
        self.limit_type = limit_type

    def to_headers(self) -> Dict[str, str]:
        """Convert to HTTP headers for API responses."""
        return {
            "X-RateLimit-Limit": str(self.remaining + (0 if self.allowed else 1)),
            "X-RateLimit-Remaining": str(self.remaining),
            "X-RateLimit-Reset": str(self.reset_time),
            "X-RateLimit-Type": self.limit_type,
            **({"Retry-After": str(self.retry_after)} if not self.allowed else {}),
        }


class PaymentRateLimiter:
    """Production-grade rate limiter for payment endpoints."""

    # Rate limit configurations for different payment operations
    RATE_LIMITS = {
        RateLimitType.CHECKOUT_SESSION: {
            "requests": 10,
            "window": 300,  # 5 minutes
            "burst_requests": 3,
            "burst_window": 60,  # 1 minute
        },
        RateLimitType.PAYMENT_METHOD: {
            "requests": 20,
            "window": 300,  # 5 minutes
            "burst_requests": 5,
            "burst_window": 60,  # 1 minute
        },
        RateLimitType.WEBHOOK: {
            "requests": 1000,
            "window": 60,  # 1 minute
            "burst_requests": 100,
            "burst_window": 10,  # 10 seconds
        },
        RateLimitType.SUBSCRIPTION_CHANGE: {
            "requests": 5,
            "window": 300,  # 5 minutes
            "burst_requests": 2,
            "burst_window": 60,  # 1 minute
        },
        RateLimitType.REFUND: {
            "requests": 3,
            "window": 600,  # 10 minutes
            "burst_requests": 1,
            "burst_window": 300,  # 5 minutes
        },
    }

    def __init__(self):
        self.redis_client = None

    async def _get_redis(self):
        """Get Redis client instance."""
        if self.redis_client is None:
            self.redis_client = await get_redis_client()
        return self.redis_client

    async def check_rate_limit(
        self,
        limit_type: RateLimitType,
        identifier: str,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
    ) -> RateLimitResult:
        """
        Check if a request is within rate limits.

        Args:
            limit_type: Type of rate limit to check
            identifier: Primary identifier (e.g., user_id, tenant_id)
            tenant_id: Optional tenant ID for tenant-specific limits
            user_id: Optional user ID for user-specific limits
            ip_address: Optional IP address for IP-based limits

        Returns:
            RateLimitResult with rate limit status
        """
        try:
            redis = await self._get_redis()
            config = self.RATE_LIMITS[limit_type]

            # Create composite key for rate limiting
            key_parts = [limit_type.value, identifier]
            if tenant_id:
                key_parts.append(f"tenant:{tenant_id}")
            if user_id:
                key_parts.append(f"user:{user_id}")
            if ip_address:
                key_parts.append(f"ip:{ip_address}")

            rate_limit_key = ":".join(key_parts)

            # Check both normal and burst limits
            normal_result = await self._check_sliding_window(
                redis, f"{rate_limit_key}:normal", config["requests"], config["window"]
            )

            burst_result = await self._check_sliding_window(
                redis,
                f"{rate_limit_key}:burst",
                config["burst_requests"],
                config["burst_window"],
            )

            # Request is allowed only if both limits are satisfied
            allowed = normal_result.allowed and burst_result.allowed

            # Use the more restrictive limit for response
            if not allowed:
                if not burst_result.allowed:
                    result = burst_result
                else:
                    result = normal_result
            else:
                result = normal_result

            result.limit_type = limit_type.value

            # Log rate limit events
            await self._log_rate_limit_event(
                rate_limit_key, limit_type, allowed, result
            )

            return result

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}", exc_info=True)
            # Fail open - allow request if rate limiting fails
            return RateLimitResult(
                allowed=True,
                remaining=0,
                reset_time=int(time.time() + 300),
                limit_type=limit_type.value,
            )

    async def _check_sliding_window(
        self, redis, key: str, max_requests: int, window_seconds: int
    ) -> RateLimitResult:
        """
        Implement sliding window rate limiting using Redis.

        Uses a sorted set to track request timestamps within the window.
        """
        now = time.time()
        window_start = now - window_seconds

        # Use Redis pipeline for atomic operations
        pipe = redis.pipeline()

        # Remove expired entries
        pipe.zremrangebyscore(key, 0, window_start)

        # Count current requests in window
        pipe.zcard(key)

        # Add current request
        pipe.zadd(key, {str(now): now})

        # Set expiration for cleanup
        pipe.expire(key, window_seconds + 60)

        results = await pipe.execute()
        current_count = results[1] + 1  # +1 for the request we just added

        allowed = current_count <= max_requests
        remaining = max(0, max_requests - current_count)
        reset_time = int(now + window_seconds)
        retry_after = window_seconds if not allowed else 0

        return RateLimitResult(
            allowed=allowed,
            remaining=remaining,
            reset_time=reset_time,
            retry_after=retry_after,
        )

    async def _log_rate_limit_event(
        self,
        key: str,
        limit_type: RateLimitType,
        allowed: bool,
        result: RateLimitResult,
    ):
        """Log rate limit events for monitoring and analysis."""
        try:
            redis = await self._get_redis()

            event = {
                "timestamp": datetime.now().isoformat(),
                "key": key,
                "limit_type": limit_type.value,
                "allowed": allowed,
                "remaining": result.remaining,
                "reset_time": result.reset_time,
            }

            # Store in Redis list for monitoring
            log_key = f"rate_limit_events:{datetime.now().strftime('%Y-%m-%d')}"
            await redis.lpush(log_key, json.dumps(event))
            await redis.expire(log_key, 86400 * 7)  # Keep for 7 days

            # Update metrics
            metrics_key = f"rate_limit_metrics:{limit_type.value}:{datetime.now().strftime('%Y-%m-%d:%H')}"
            if allowed:
                await redis.hincrby(metrics_key, "allowed", 1)
            else:
                await redis.hincrby(metrics_key, "blocked", 1)
            await redis.expire(metrics_key, 86400 * 30)  # Keep for 30 days

        except Exception as e:
            logger.error(f"Failed to log rate limit event: {e}")

    async def get_rate_limit_status(
        self, limit_type: RateLimitType, identifier: str
    ) -> Dict[str, Any]:
        """Get current rate limit status without incrementing counters."""
        try:
            redis = await self._get_redis()
            config = self.RATE_LIMITS[limit_type]

            key = f"{limit_type.value}:{identifier}"
            now = time.time()

            # Check normal window
            normal_key = f"{key}:normal"
            window_start = now - config["window"]
            await redis.zremrangebyscore(normal_key, 0, window_start)
            normal_count = await redis.zcard(normal_key)

            # Check burst window
            burst_key = f"{key}:burst"
            burst_window_start = now - config["burst_window"]
            await redis.zremrangebyscore(burst_key, 0, burst_window_start)
            burst_count = await redis.zcard(burst_key)

            return {
                "limit_type": limit_type.value,
                "normal": {
                    "current": normal_count,
                    "limit": config["requests"],
                    "remaining": max(0, config["requests"] - normal_count),
                    "window_seconds": config["window"],
                    "reset_time": int(now + config["window"]),
                },
                "burst": {
                    "current": burst_count,
                    "limit": config["burst_requests"],
                    "remaining": max(0, config["burst_requests"] - burst_count),
                    "window_seconds": config["burst_window"],
                    "reset_time": int(now + config["burst_window"]),
                },
            }

        except Exception as e:
            logger.error(f"Failed to get rate limit status: {e}", exc_info=True)
            return {"error": str(e)}

    async def reset_rate_limit(
        self, limit_type: RateLimitType, identifier: str
    ) -> bool:
        """Reset rate limit for a specific identifier (admin function)."""
        try:
            redis = await self._get_redis()

            key = f"{limit_type.value}:{identifier}"
            normal_key = f"{key}:normal"
            burst_key = f"{key}:burst"

            # Delete rate limit keys
            await redis.delete(normal_key, burst_key)

            logger.info(f"Rate limit reset for {limit_type.value}:{identifier}")
            return True

        except Exception as e:
            logger.error(f"Failed to reset rate limit: {e}", exc_info=True)
            return False


# Global rate limiter instance
payment_rate_limiter = PaymentRateLimiter()
