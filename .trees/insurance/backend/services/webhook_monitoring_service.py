#!/usr/bin/env python3
"""
Webhook Monitoring Service for PI Lawyer AI
Comprehensive monitoring system for regional subscription webhooks with health checks,
performance metrics, dead letter queue monitoring, and automated alerting.

This service provides real-time monitoring and alerting for webhook processing across all regions.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import statistics

from backend.utils.logging import get_logger
from backend.services.webhook_retry_service import webhook_retry_service, WebhookRetryStatus
from backend.services.regional_webhook_router import regional_webhook_router, SupportedRegion

# Configure logging
logger = get_logger(__name__)


class HealthStatus(Enum):
    """Health status levels for monitoring."""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class AlertSeverity(Enum):
    """Alert severity levels."""

    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class WebhookMetrics:
    """Webhook processing metrics."""

    total_processed: int = 0
    successful: int = 0
    failed: int = 0
    retried: int = 0
    dead_lettered: int = 0
    avg_processing_time_ms: float = 0.0
    p95_processing_time_ms: float = 0.0
    p99_processing_time_ms: float = 0.0
    success_rate: float = 0.0
    failure_rate: float = 0.0
    retry_rate: float = 0.0


@dataclass
class RegionalMetrics:
    """Regional webhook processing metrics."""

    region: str
    metrics: WebhookMetrics
    compliance_score: float = 100.0
    data_residency_violations: int = 0
    last_updated: datetime = None


@dataclass
class HealthCheckResult:
    """Health check result."""

    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = None
    timestamp: datetime = None


class WebhookMonitoringService:
    """Comprehensive webhook monitoring service."""

    def __init__(self):
        # Monitoring configuration
        self.health_check_interval = 30  # seconds
        self.metrics_retention_hours = 24
        self.alert_thresholds = {
            "success_rate_warning": 95.0,
            "success_rate_critical": 90.0,
            "avg_processing_time_warning": 500,  # ms
            "avg_processing_time_critical": 1000,  # ms
            "dead_letter_queue_warning": 10,
            "dead_letter_queue_critical": 50,
            "compliance_score_warning": 95.0,
            "compliance_score_critical": 90.0,
        }

        # In-memory storage (in production, use Redis or database)
        self.metrics_history = {}
        self.regional_metrics = {}
        self.health_status = {}
        self.active_alerts = {}
        self.processing_times = []

        # Initialize regional metrics
        for region in SupportedRegion:
            self.regional_metrics[region.value] = RegionalMetrics(
                region=region.value,
                metrics=WebhookMetrics(),
                last_updated=datetime.utcnow(),
            )

    async def start_monitoring(self):
        """Start the monitoring service."""
        logger.info("Starting webhook monitoring service...")

        # Start background monitoring tasks
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._metrics_collection_loop())
        asyncio.create_task(self._alert_processing_loop())
        asyncio.create_task(self._cleanup_loop())

        logger.info("✅ Webhook monitoring service started")

    async def record_webhook_processing(
        self,
        event_id: str,
        region: str,
        processing_time_ms: float,
        success: bool,
        retry_count: int = 0,
        moved_to_dlq: bool = False,
    ):
        """Record webhook processing metrics."""
        try:
            # Update regional metrics
            if region in self.regional_metrics:
                regional_metric = self.regional_metrics[region]
                metrics = regional_metric.metrics

                # Update counters
                metrics.total_processed += 1
                if success:
                    metrics.successful += 1
                else:
                    metrics.failed += 1

                if retry_count > 0:
                    metrics.retried += 1

                if moved_to_dlq:
                    metrics.dead_lettered += 1

                # Update processing times
                self.processing_times.append(processing_time_ms)
                if len(self.processing_times) > 1000:  # Keep last 1000 measurements
                    self.processing_times = self.processing_times[-1000:]

                # Calculate rates
                if metrics.total_processed > 0:
                    metrics.success_rate = (
                        metrics.successful / metrics.total_processed
                    ) * 100
                    metrics.failure_rate = (
                        metrics.failed / metrics.total_processed
                    ) * 100
                    metrics.retry_rate = (
                        metrics.retried / metrics.total_processed
                    ) * 100

                # Calculate processing time percentiles
                if self.processing_times:
                    metrics.avg_processing_time_ms = statistics.mean(
                        self.processing_times
                    )
                    sorted_times = sorted(self.processing_times)
                    metrics.p95_processing_time_ms = sorted_times[
                        int(len(sorted_times) * 0.95)
                    ]
                    metrics.p99_processing_time_ms = sorted_times[
                        int(len(sorted_times) * 0.99)
                    ]

                regional_metric.last_updated = datetime.utcnow()

                # Store in history
                timestamp = datetime.utcnow().isoformat()
                if region not in self.metrics_history:
                    self.metrics_history[region] = []

                self.metrics_history[region].append(
                    {
                        "timestamp": timestamp,
                        "event_id": event_id,
                        "processing_time_ms": processing_time_ms,
                        "success": success,
                        "retry_count": retry_count,
                        "moved_to_dlq": moved_to_dlq,
                    }
                )

                # Cleanup old history
                cutoff_time = datetime.utcnow() - timedelta(
                    hours=self.metrics_retention_hours
                )
                self.metrics_history[region] = [
                    entry
                    for entry in self.metrics_history[region]
                    if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
                ]

        except Exception as e:
            logger.error(f"Error recording webhook metrics: {e}", exc_info=True)

    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status."""
        try:
            health_checks = []

            # Check webhook retry service
            retry_health = await self._check_retry_service_health()
            health_checks.append(retry_health)

            # Check regional routing
            routing_health = await self._check_regional_routing_health()
            health_checks.append(routing_health)

            # Check processing performance
            performance_health = await self._check_processing_performance()
            health_checks.append(performance_health)

            # Check compliance status
            compliance_health = await self._check_compliance_status()
            health_checks.append(compliance_health)

            # Determine overall health
            overall_status = self._determine_overall_health(health_checks)

            return {
                "overall_status": overall_status.value,
                "health_checks": [asdict(check) for check in health_checks],
                "timestamp": datetime.utcnow().isoformat(),
                "uptime_seconds": self._get_uptime_seconds(),
            }

        except Exception as e:
            logger.error(f"Error getting health status: {e}", exc_info=True)
            return {
                "overall_status": HealthStatus.UNKNOWN.value,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    async def get_performance_metrics(
        self, region: Optional[str] = None, hours: int = 1
    ) -> Dict[str, Any]:
        """Get performance metrics for specified region and time period."""
        try:
            if region and region in self.regional_metrics:
                # Return metrics for specific region
                regional_metric = self.regional_metrics[region]
                return {
                    "region": region,
                    "metrics": asdict(regional_metric.metrics),
                    "compliance_score": regional_metric.compliance_score,
                    "data_residency_violations": regional_metric.data_residency_violations,
                    "last_updated": regional_metric.last_updated.isoformat(),
                    "history": self._get_regional_history(region, hours),
                }
            else:
                # Return aggregated metrics for all regions
                aggregated_metrics = self._aggregate_regional_metrics()
                return {
                    "aggregated": asdict(aggregated_metrics),
                    "regional_breakdown": {
                        region: asdict(metric.metrics)
                        for region, metric in self.regional_metrics.items()
                    },
                    "timestamp": datetime.utcnow().isoformat(),
                }

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}", exc_info=True)
            return {"error": str(e)}

    async def get_dead_letter_queue_status(self) -> Dict[str, Any]:
        """Get dead letter queue monitoring status."""
        try:
            # Get DLQ status from retry service
            retry_status = await webhook_retry_service.get_retry_queue_status()

            # Analyze DLQ entries by region
            dlq_by_region = {}
            for event_id, entry in webhook_retry_service.dead_letter_queue.items():
                region = entry.get("regional_context", {}).get("region", "US")
                if region not in dlq_by_region:
                    dlq_by_region[region] = []
                dlq_by_region[region].append(
                    {
                        "event_id": event_id,
                        "event_type": entry["event_type"],
                        "attempt_count": entry["attempt_count"],
                        "moved_to_dlq_at": entry["moved_to_dlq_at"].isoformat(),
                        "failure_history": entry["failure_history"],
                    }
                )

            # Calculate DLQ health status
            dlq_size = retry_status["dead_letter_queue_size"]
            if dlq_size == 0:
                dlq_health = HealthStatus.HEALTHY
            elif dlq_size <= self.alert_thresholds["dead_letter_queue_warning"]:
                dlq_health = HealthStatus.WARNING
            else:
                dlq_health = HealthStatus.CRITICAL

            return {
                "overall_status": dlq_health.value,
                "total_dlq_size": dlq_size,
                "regional_breakdown": dlq_by_region,
                "retry_queue_status": retry_status,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error getting DLQ status: {e}", exc_info=True)
            return {"error": str(e)}

    async def get_compliance_report(self) -> Dict[str, Any]:
        """Get regional compliance monitoring report."""
        try:
            compliance_report = {
                "overall_compliance_score": 0.0,
                "regional_compliance": {},
                "violations": [],
                "recommendations": [],
                "timestamp": datetime.utcnow().isoformat(),
            }

            total_score = 0.0
            region_count = 0

            for region, metric in self.regional_metrics.items():
                region_compliance = {
                    "compliance_score": metric.compliance_score,
                    "data_residency_violations": metric.data_residency_violations,
                    "processing_metrics": asdict(metric.metrics),
                    "status": (
                        "compliant"
                        if metric.compliance_score >= 95.0
                        else "non_compliant"
                    ),
                }

                # Check for specific compliance issues
                violations = []
                if metric.compliance_score < 95.0:
                    violations.append(
                        f"Compliance score below threshold: {metric.compliance_score}%"
                    )

                if metric.data_residency_violations > 0:
                    violations.append(
                        f"Data residency violations: {metric.data_residency_violations}"
                    )

                if metric.metrics.success_rate < 95.0:
                    violations.append(
                        f"Success rate below threshold: {metric.metrics.success_rate}%"
                    )

                region_compliance["violations"] = violations
                compliance_report["regional_compliance"][region] = region_compliance

                total_score += metric.compliance_score
                region_count += 1

            # Calculate overall compliance score
            if region_count > 0:
                compliance_report["overall_compliance_score"] = (
                    total_score / region_count
                )

            # Generate recommendations
            recommendations = []
            if compliance_report["overall_compliance_score"] < 95.0:
                recommendations.append(
                    "Review and address regional compliance violations"
                )

            dlq_status = await webhook_retry_service.get_retry_queue_status()
            if dlq_status["dead_letter_queue_size"] > 5:
                recommendations.append("Review and reprocess dead letter queue entries")

            compliance_report["recommendations"] = recommendations

            return compliance_report

        except Exception as e:
            logger.error(f"Error generating compliance report: {e}", exc_info=True)
            return {"error": str(e)}

    async def _health_check_loop(self):
        """Background health check loop."""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)

                # Perform health checks
                health_status = await self.get_health_status()
                self.health_status = health_status

                # Check for alerts
                await self._check_and_trigger_alerts(health_status)

            except Exception as e:
                logger.error(f"Error in health check loop: {e}", exc_info=True)

    async def _metrics_collection_loop(self):
        """Background metrics collection loop."""
        while True:
            try:
                await asyncio.sleep(60)  # Collect metrics every minute

                # Update regional metrics from retry service
                retry_status = await webhook_retry_service.get_retry_queue_status()

                # Log metrics for monitoring
                logger.info(f"Webhook metrics: {retry_status}")

            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}", exc_info=True)

    async def _alert_processing_loop(self):
        """Background alert processing loop."""
        while True:
            try:
                await asyncio.sleep(120)  # Process alerts every 2 minutes

                # Process and send alerts
                await self._process_pending_alerts()

            except Exception as e:
                logger.error(f"Error in alert processing loop: {e}", exc_info=True)

    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(3600)  # Cleanup every hour

                # Cleanup old metrics and alerts
                await self._cleanup_old_data()

            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}", exc_info=True)

    async def _check_retry_service_health(self) -> HealthCheckResult:
        """Check webhook retry service health."""
        try:
            status = await webhook_retry_service.get_retry_queue_status()

            if (
                status["dead_letter_queue_size"] == 0
                and status["pending_webhooks"] < 10
            ):
                return HealthCheckResult(
                    component="webhook_retry_service",
                    status=HealthStatus.HEALTHY,
                    message="Retry service operating normally",
                    details=status,
                    timestamp=datetime.utcnow(),
                )
            elif status["dead_letter_queue_size"] <= 10:
                return HealthCheckResult(
                    component="webhook_retry_service",
                    status=HealthStatus.WARNING,
                    message=f"Dead letter queue has {status['dead_letter_queue_size']} entries",
                    details=status,
                    timestamp=datetime.utcnow(),
                )
            else:
                return HealthCheckResult(
                    component="webhook_retry_service",
                    status=HealthStatus.CRITICAL,
                    message=f"High dead letter queue size: {status['dead_letter_queue_size']}",
                    details=status,
                    timestamp=datetime.utcnow(),
                )

        except Exception as e:
            return HealthCheckResult(
                component="webhook_retry_service",
                status=HealthStatus.CRITICAL,
                message=f"Health check failed: {str(e)}",
                timestamp=datetime.utcnow(),
            )

    async def _check_regional_routing_health(self) -> HealthCheckResult:
        """Check regional routing health."""
        try:
            # Check if all regions are accessible
            healthy_regions = 0
            total_regions = len(SupportedRegion)

            for region in SupportedRegion:
                try:
                    # Test regional database connectivity
                    client = await regional_webhook_router.get_regional_database_client(
                        region
                    )
                    if client:
                        healthy_regions += 1
                except:
                    pass

            health_ratio = healthy_regions / total_regions

            if health_ratio >= 1.0:
                return HealthCheckResult(
                    component="regional_routing",
                    status=HealthStatus.HEALTHY,
                    message="All regions accessible",
                    details={
                        "healthy_regions": healthy_regions,
                        "total_regions": total_regions,
                    },
                    timestamp=datetime.utcnow(),
                )
            elif health_ratio >= 0.75:
                return HealthCheckResult(
                    component="regional_routing",
                    status=HealthStatus.WARNING,
                    message=f"Some regions inaccessible: {healthy_regions}/{total_regions}",
                    details={
                        "healthy_regions": healthy_regions,
                        "total_regions": total_regions,
                    },
                    timestamp=datetime.utcnow(),
                )
            else:
                return HealthCheckResult(
                    component="regional_routing",
                    status=HealthStatus.CRITICAL,
                    message=f"Multiple regions inaccessible: {healthy_regions}/{total_regions}",
                    details={
                        "healthy_regions": healthy_regions,
                        "total_regions": total_regions,
                    },
                    timestamp=datetime.utcnow(),
                )

        except Exception as e:
            return HealthCheckResult(
                component="regional_routing",
                status=HealthStatus.CRITICAL,
                message=f"Regional routing check failed: {str(e)}",
                timestamp=datetime.utcnow(),
            )

    async def _check_processing_performance(self) -> HealthCheckResult:
        """Check webhook processing performance."""
        try:
            # Calculate overall performance metrics
            aggregated = self._aggregate_regional_metrics()

            if (
                aggregated.success_rate >= 99.0
                and aggregated.avg_processing_time_ms
                <= self.alert_thresholds["avg_processing_time_warning"]
            ):
                return HealthCheckResult(
                    component="processing_performance",
                    status=HealthStatus.HEALTHY,
                    message="Processing performance optimal",
                    details=asdict(aggregated),
                    timestamp=datetime.utcnow(),
                )
            elif (
                aggregated.success_rate >= 95.0
                and aggregated.avg_processing_time_ms
                <= self.alert_thresholds["avg_processing_time_critical"]
            ):
                return HealthCheckResult(
                    component="processing_performance",
                    status=HealthStatus.WARNING,
                    message="Processing performance degraded",
                    details=asdict(aggregated),
                    timestamp=datetime.utcnow(),
                )
            else:
                return HealthCheckResult(
                    component="processing_performance",
                    status=HealthStatus.CRITICAL,
                    message="Processing performance critical",
                    details=asdict(aggregated),
                    timestamp=datetime.utcnow(),
                )

        except Exception as e:
            return HealthCheckResult(
                component="processing_performance",
                status=HealthStatus.CRITICAL,
                message=f"Performance check failed: {str(e)}",
                timestamp=datetime.utcnow(),
            )

    async def _check_compliance_status(self) -> HealthCheckResult:
        """Check regional compliance status."""
        try:
            compliance_report = await self.get_compliance_report()
            overall_score = compliance_report["overall_compliance_score"]

            if overall_score >= 98.0:
                return HealthCheckResult(
                    component="compliance_status",
                    status=HealthStatus.HEALTHY,
                    message="All regions compliant",
                    details={"compliance_score": overall_score},
                    timestamp=datetime.utcnow(),
                )
            elif overall_score >= 95.0:
                return HealthCheckResult(
                    component="compliance_status",
                    status=HealthStatus.WARNING,
                    message=f"Compliance score: {overall_score}%",
                    details={"compliance_score": overall_score},
                    timestamp=datetime.utcnow(),
                )
            else:
                return HealthCheckResult(
                    component="compliance_status",
                    status=HealthStatus.CRITICAL,
                    message=f"Compliance issues detected: {overall_score}%",
                    details={"compliance_score": overall_score},
                    timestamp=datetime.utcnow(),
                )

        except Exception as e:
            return HealthCheckResult(
                component="compliance_status",
                status=HealthStatus.CRITICAL,
                message=f"Compliance check failed: {str(e)}",
                timestamp=datetime.utcnow(),
            )

    def _determine_overall_health(
        self, health_checks: List[HealthCheckResult]
    ) -> HealthStatus:
        """Determine overall health status from individual checks."""
        if any(check.status == HealthStatus.CRITICAL for check in health_checks):
            return HealthStatus.CRITICAL
        elif any(check.status == HealthStatus.WARNING for check in health_checks):
            return HealthStatus.WARNING
        elif all(check.status == HealthStatus.HEALTHY for check in health_checks):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN

    def _aggregate_regional_metrics(self) -> WebhookMetrics:
        """Aggregate metrics across all regions."""
        aggregated = WebhookMetrics()

        total_regions = 0
        total_processing_times = []

        for metric in self.regional_metrics.values():
            m = metric.metrics
            aggregated.total_processed += m.total_processed
            aggregated.successful += m.successful
            aggregated.failed += m.failed
            aggregated.retried += m.retried
            aggregated.dead_lettered += m.dead_lettered

            if m.avg_processing_time_ms > 0:
                total_processing_times.append(m.avg_processing_time_ms)

            total_regions += 1

        # Calculate rates
        if aggregated.total_processed > 0:
            aggregated.success_rate = (
                aggregated.successful / aggregated.total_processed
            ) * 100
            aggregated.failure_rate = (
                aggregated.failed / aggregated.total_processed
            ) * 100
            aggregated.retry_rate = (
                aggregated.retried / aggregated.total_processed
            ) * 100

        # Calculate average processing time
        if total_processing_times:
            aggregated.avg_processing_time_ms = statistics.mean(total_processing_times)

        return aggregated

    def _get_regional_history(self, region: str, hours: int) -> List[Dict[str, Any]]:
        """Get historical data for a specific region."""
        if region not in self.metrics_history:
            return []

        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [
            entry
            for entry in self.metrics_history[region]
            if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
        ]

    def _get_uptime_seconds(self) -> int:
        """Get service uptime in seconds."""
        # This would be implemented with actual service start time tracking
        return 3600  # Placeholder

    async def _check_and_trigger_alerts(self, health_status: Dict[str, Any]):
        """Check health status and trigger alerts if needed."""
        # This would integrate with alerting systems (PagerDuty, Slack, etc.)
        if health_status["overall_status"] == HealthStatus.CRITICAL.value:
            logger.critical(
                f"CRITICAL ALERT: Webhook system health critical: {health_status}"
            )
        elif health_status["overall_status"] == HealthStatus.WARNING.value:
            logger.warning(
                f"WARNING ALERT: Webhook system health degraded: {health_status}"
            )

    async def _process_pending_alerts(self):
        """Process and send pending alerts."""
        # Implementation for alert processing
        pass

    async def _cleanup_old_data(self):
        """Clean up old metrics and alert data."""
        cutoff_time = datetime.utcnow() - timedelta(hours=self.metrics_retention_hours)

        for region in self.metrics_history:
            self.metrics_history[region] = [
                entry
                for entry in self.metrics_history[region]
                if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
            ]

    async def get_real_time_dashboard_data(self) -> Dict[str, Any]:
        """Get real-time dashboard data for monitoring UI."""
        try:
            # Get current health status
            health_status = await self.get_health_status()

            # Get performance metrics
            performance_metrics = await self.get_performance_metrics()

            # Get DLQ status
            dlq_status = await self.get_dead_letter_queue_status()

            # Get compliance report
            compliance_report = await self.get_compliance_report()

            # Calculate key performance indicators
            kpis = self._calculate_kpis()

            return {
                "health_status": health_status,
                "performance_metrics": performance_metrics,
                "dead_letter_queue": dlq_status,
                "compliance": compliance_report,
                "kpis": kpis,
                "timestamp": datetime.utcnow().isoformat(),
                "refresh_interval": 30,  # seconds
            }

        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}", exc_info=True)
            return {"error": str(e)}

    def _calculate_kpis(self) -> Dict[str, Any]:
        """Calculate key performance indicators."""
        aggregated = self._aggregate_regional_metrics()

        return {
            "total_webhooks_processed": aggregated.total_processed,
            "success_rate": round(aggregated.success_rate, 2),
            "average_processing_time": round(aggregated.avg_processing_time_ms, 2),
            "active_regions": len(
                [
                    r
                    for r in self.regional_metrics.values()
                    if r.metrics.total_processed > 0
                ]
            ),
            "dead_letter_queue_size": sum(
                1 for _ in webhook_retry_service.dead_letter_queue.values()
            ),
            "compliance_score": round(
                sum(r.compliance_score for r in self.regional_metrics.values())
                / len(self.regional_metrics),
                2,
            ),
        }


# Global instance
webhook_monitoring_service = WebhookMonitoringService()
