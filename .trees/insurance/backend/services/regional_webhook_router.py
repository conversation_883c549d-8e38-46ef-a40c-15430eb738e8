#!/usr/bin/env python3
"""
Regional Webhook Router for PI Lawyer AI
Intelligent routing system for webhook events based on region, currency, and compliance requirements.

This service routes webhook events to appropriate regional handlers and databases.
"""

import logging
import os
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

from backend.services.multi_currency_webhook_handler import (
    SupportedRegion,
    SupportedCurrency,
    multi_currency_handler,
)
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)


class DatabaseRegion(Enum):
    """Database regions for data residency compliance."""

    US_EAST = "us-east-1"
    EU_CENTRAL = "eu-central-1"
    EU_WEST = "eu-west-2"
    CA_CENTRAL = "ca-central-1"


class RegionalWebhookRouter:
    """Routes webhook events to appropriate regional handlers and databases."""

    def __init__(self):
        # Regional database mapping for data residency compliance
        self.regional_database_config = {
            SupportedRegion.US: {
                "primary_db": DatabaseRegion.US_EAST,
                "supabase_project": "new-texas-laws",
                "supabase_url": os.getenv("SUPABASE_US_URL"),
                "supabase_key": os.getenv("SUPABASE_US_KEY"),
                "backup_regions": [DatabaseRegion.CA_CENTRAL],
                "data_residency_strict": False,
            },
            SupportedRegion.EU: {
                "primary_db": DatabaseRegion.EU_CENTRAL,
                "supabase_project": "ailex-belgium",
                "supabase_url": os.getenv("SUPABASE_EU_URL"),
                "supabase_key": os.getenv("SUPABASE_EU_KEY"),
                "backup_regions": [DatabaseRegion.EU_WEST],
                "data_residency_strict": True,  # GDPR compliance
            },
            SupportedRegion.UK: {
                "primary_db": DatabaseRegion.EU_WEST,
                "supabase_project": "ailex-belgium",  # UK uses EU instance post-Brexit
                "supabase_url": os.getenv("SUPABASE_EU_URL"),
                "supabase_key": os.getenv("SUPABASE_EU_KEY"),
                "backup_regions": [DatabaseRegion.EU_CENTRAL],
                "data_residency_strict": True,  # GDPR compliance
            },
            SupportedRegion.CA: {
                "primary_db": DatabaseRegion.CA_CENTRAL,
                "supabase_project": "new-texas-laws",  # CA uses US instance for now
                "supabase_url": os.getenv("SUPABASE_US_URL"),
                "supabase_key": os.getenv("SUPABASE_US_KEY"),
                "backup_regions": [DatabaseRegion.US_EAST],
                "data_residency_strict": False,  # PIPEDA allows cross-border with safeguards
            },
        }

        # Regional handler mapping
        self.regional_handlers = {
            SupportedRegion.US: "USRegionalHandler",
            SupportedRegion.EU: "EURegionalHandler",
            SupportedRegion.UK: "UKRegionalHandler",
            SupportedRegion.CA: "CARegionalHandler",
        }

        # Customer location to region mapping
        self.country_to_region_map = {
            # North America
            "US": SupportedRegion.US,
            "CA": SupportedRegion.CA,
            # European Union
            "AT": SupportedRegion.EU,
            "BE": SupportedRegion.EU,
            "BG": SupportedRegion.EU,
            "HR": SupportedRegion.EU,
            "CY": SupportedRegion.EU,
            "CZ": SupportedRegion.EU,
            "DK": SupportedRegion.EU,
            "EE": SupportedRegion.EU,
            "FI": SupportedRegion.EU,
            "FR": SupportedRegion.EU,
            "DE": SupportedRegion.EU,
            "GR": SupportedRegion.EU,
            "HU": SupportedRegion.EU,
            "IE": SupportedRegion.EU,
            "IT": SupportedRegion.EU,
            "LV": SupportedRegion.EU,
            "LT": SupportedRegion.EU,
            "LU": SupportedRegion.EU,
            "MT": SupportedRegion.EU,
            "NL": SupportedRegion.EU,
            "PL": SupportedRegion.EU,
            "PT": SupportedRegion.EU,
            "RO": SupportedRegion.EU,
            "SK": SupportedRegion.EU,
            "SI": SupportedRegion.EU,
            "ES": SupportedRegion.EU,
            "SE": SupportedRegion.EU,
            # United Kingdom
            "GB": SupportedRegion.UK,
        }

    async def route_webhook_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Route webhook event to appropriate regional handler and database.

        Args:
            event: Stripe webhook event

        Returns:
            Routing result with regional context and database configuration
        """
        try:
            # Step 1: Detect region and currency
            region, currency = multi_currency_handler.detect_region_from_event(event)

            # Step 2: Enhance region detection with customer location
            enhanced_region = await self._enhance_region_detection(event, region)

            # Step 3: Create regional context
            regional_context = multi_currency_handler.create_regional_context(
                enhanced_region, currency, event
            )

            # Step 4: Get database configuration
            db_config = self._get_database_configuration(enhanced_region)

            # Step 5: Validate data residency compliance
            compliance_result = await self._validate_data_residency_compliance(
                event, enhanced_region, db_config
            )

            if not compliance_result["compliant"]:
                logger.error(
                    f"Data residency compliance failed: {compliance_result['violations']}"
                )
                return {
                    "status": "compliance_failed",
                    "violations": compliance_result["violations"],
                    "event_id": event.get("id"),
                }

            # Step 6: Create routing result
            routing_result = {
                "status": "success",
                "region": enhanced_region.value,
                "currency": currency.value,
                "regional_context": regional_context,
                "database_config": db_config,
                "handler_class": self.regional_handlers[enhanced_region],
                "routing_metadata": {
                    "original_region": region.value,
                    "enhanced_region": enhanced_region.value,
                    "detection_method": "currency_and_location",
                    "routed_at": datetime.utcnow().isoformat(),
                },
            }

            logger.info(
                f"Webhook event {event.get('id')} routed to region {enhanced_region.value} with currency {currency.value}"
            )

            return routing_result

        except Exception as e:
            logger.error(f"Error routing webhook event: {e}", exc_info=True)
            return {
                "status": "routing_failed",
                "error": str(e),
                "event_id": event.get("id"),
            }

    async def _enhance_region_detection(
        self, event: Dict[str, Any], initial_region: SupportedRegion
    ) -> SupportedRegion:
        """
        Enhance region detection using customer location and metadata.

        Args:
            event: Stripe webhook event
            initial_region: Region detected from currency

        Returns:
            Enhanced region based on additional context
        """
        try:
            # Extract customer information
            customer_country = await self._extract_customer_country(event)

            if customer_country:
                # Map country to region
                location_region = self.country_to_region_map.get(customer_country)

                if location_region:
                    # Validate region consistency
                    if location_region != initial_region:
                        logger.info(
                            f"Region mismatch: currency suggests {initial_region.value}, location suggests {location_region.value}"
                        )

                        # Use location-based region for EU customers (GDPR compliance)
                        if location_region in [SupportedRegion.EU, SupportedRegion.UK]:
                            logger.info(
                                f"Using location-based region {location_region.value} for GDPR compliance"
                            )
                            return location_region

                        # For other regions, prefer currency-based detection
                        logger.info(
                            f"Using currency-based region {initial_region.value}"
                        )
                        return initial_region
                    else:
                        logger.info(
                            f"Region consistency confirmed: {initial_region.value}"
                        )
                        return initial_region

            # Fallback to initial region
            return initial_region

        except Exception as e:
            logger.warning(f"Error enhancing region detection: {e}")
            return initial_region

    async def _extract_customer_country(self, event: Dict[str, Any]) -> Optional[str]:
        """Extract customer country from webhook event."""
        try:
            event_data = event.get("data", {}).get("object", {})

            # Try different sources for customer country
            country_sources = [
                # Customer address
                lambda: event_data.get("customer_details", {})
                .get("address", {})
                .get("country"),
                # Billing address
                lambda: event_data.get("billing_details", {})
                .get("address", {})
                .get("country"),
                # Metadata
                lambda: event_data.get("metadata", {}).get("customer_country"),
                # Payment method country
                lambda: event_data.get("payment_method", {})
                .get("billing_details", {})
                .get("address", {})
                .get("country"),
            ]

            for source in country_sources:
                try:
                    country = source()
                    if country:
                        return country.upper()
                except:
                    continue

            # If no country found, try to get it from customer ID
            customer_id = event_data.get("customer")
            if customer_id:
                return await self._get_customer_country_from_stripe(customer_id)

            return None

        except Exception as e:
            logger.warning(f"Error extracting customer country: {e}")
            return None

    async def _get_customer_country_from_stripe(
        self, customer_id: str
    ) -> Optional[str]:
        """Get customer country from Stripe API."""
        try:
            import stripe

            customer = stripe.Customer.retrieve(customer_id)

            # Try address first
            if customer.get("address", {}).get("country"):
                return customer["address"]["country"].upper()

            # Try shipping address
            if customer.get("shipping", {}).get("address", {}).get("country"):
                return customer["shipping"]["address"]["country"].upper()

            return None

        except Exception as e:
            logger.warning(f"Error retrieving customer country from Stripe: {e}")
            return None

    def _get_database_configuration(self, region: SupportedRegion) -> Dict[str, Any]:
        """Get database configuration for region."""
        config = self.regional_database_config.get(
            region, self.regional_database_config[SupportedRegion.US]
        )

        return {
            "region": region.value,
            "primary_db": config["primary_db"].value,
            "supabase_project": config["supabase_project"],
            "supabase_url": config["supabase_url"],
            "supabase_key": config["supabase_key"],
            "backup_regions": [br.value for br in config["backup_regions"]],
            "data_residency_strict": config["data_residency_strict"],
        }

    async def _validate_data_residency_compliance(
        self, event: Dict[str, Any], region: SupportedRegion, db_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate data residency compliance requirements."""
        violations = []

        try:
            # Check GDPR compliance for EU/UK
            if region in [SupportedRegion.EU, SupportedRegion.UK]:
                if not db_config["data_residency_strict"]:
                    violations.append("GDPR requires strict data residency compliance")

                # Ensure EU data stays in EU
                primary_db = db_config["primary_db"]
                if not primary_db.startswith("eu-"):
                    violations.append(
                        f"EU/UK data must be stored in EU region, not {primary_db}"
                    )

            # Check customer consent for cross-border transfers
            event_data = event.get("data", {}).get("object", {})
            metadata = event_data.get("metadata", {})

            if region in [SupportedRegion.EU, SupportedRegion.UK]:
                if not metadata.get("gdpr_consent_recorded"):
                    # Warning but not blocking - might be legacy data
                    logger.warning("GDPR consent not recorded in metadata")

            return {
                "compliant": len(violations) == 0,
                "violations": violations,
                "region": region.value,
                "data_residency_strict": db_config["data_residency_strict"],
            }

        except Exception as e:
            logger.error(f"Error validating data residency compliance: {e}")
            return {
                "compliant": False,
                "violations": [f"Compliance validation error: {str(e)}"],
                "region": region.value,
            }

    async def get_regional_database_client(self, region: SupportedRegion):
        """Get database client for specific region."""
        try:
            from supabase import create_client

            config = self.regional_database_config.get(
                region, self.regional_database_config[SupportedRegion.US]
            )

            if not config["supabase_url"] or not config["supabase_key"]:
                logger.error(
                    f"Missing Supabase configuration for region {region.value}"
                )
                # Fallback to US configuration
                config = self.regional_database_config[SupportedRegion.US]

            client = create_client(config["supabase_url"], config["supabase_key"])

            logger.info(
                f"Created database client for region {region.value} (project: {config['supabase_project']})"
            )

            return client

        except Exception as e:
            logger.error(f"Error creating regional database client: {e}")
            # Return None to trigger fallback handling
            return None

    async def log_routing_metrics(
        self, event: Dict[str, Any], routing_result: Dict[str, Any]
    ):
        """Log routing metrics for monitoring."""
        try:
            metrics = {
                "event_id": event.get("id"),
                "event_type": event.get("type"),
                "routing_status": routing_result["status"],
                "routed_region": routing_result.get("region"),
                "routed_currency": routing_result.get("currency"),
                "handler_class": routing_result.get("handler_class"),
                "database_project": routing_result.get("database_config", {}).get(
                    "supabase_project"
                ),
                "data_residency_strict": routing_result.get("database_config", {}).get(
                    "data_residency_strict"
                ),
                "routing_metadata": routing_result.get("routing_metadata", {}),
                "timestamp": datetime.utcnow().isoformat(),
            }

            logger.info(f"Webhook routing metrics: {metrics}")

            # This would integrate with your monitoring system
            # await self._store_routing_metrics(metrics)

        except Exception as e:
            logger.error(f"Error logging routing metrics: {e}")


# Global instance
regional_webhook_router = RegionalWebhookRouter()
