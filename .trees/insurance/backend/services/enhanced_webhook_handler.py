"""
Enhanced Webhook Handler with Automatic Mapping

Handles Stripe webhook events with automatic mapping between Stripe products
and internal plans for multi-country subscription operations.
"""

import logging
from typing import Dict, Any, Optional
from uuid import UUID

from backend.services.stripe_mapping import stripe_mapper
from backend.core.database import get_async_session
from backend.models.subscription import TenantSubscription, Firm

logger = logging.getLogger(__name__)


class EnhancedWebhookHandler:
    """Enhanced webhook handler with automatic plan mapping."""

    def __init__(self):
        self.mapper = stripe_mapper

    async def handle_checkout_session_completed(
        self, event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle checkout.session.completed with automatic plan mapping.

        This enhanced handler:
        1. Extracts Stripe price ID from the session
        2. Uses automatic mapping to resolve internal plan
        3. Detects customer country and currency
        4. Creates subscription with proper regional settings
        """
        session = event["data"]["object"]
        session_id = session["id"]

        logger.info(f"Processing checkout session {session_id} with automatic mapping")

        try:
            # Extract line items and price information
            line_items = session.get("line_items", {}).get("data", [])
            if not line_items:
                logger.error(f"No line items found in session {session_id}")
                return {"status": "error", "message": "No line items found"}

            # Get the primary subscription item (first line item)
            primary_item = line_items[0]
            stripe_price_id = primary_item["price"]["id"]

            logger.info(f"Resolving Stripe price ID: {stripe_price_id}")

            # Use automatic mapping to resolve internal plan
            plan_mapping = await self.mapper.resolve_stripe_price(stripe_price_id)
            if not plan_mapping:
                logger.error(f"No mapping found for Stripe price ID: {stripe_price_id}")
                return {
                    "status": "error",
                    "message": f"Unmapped price ID: {stripe_price_id}",
                }

            logger.info(
                f"Mapped to internal plan: {plan_mapping.plan_code} "
                f"({plan_mapping.country_code}, {plan_mapping.currency})"
            )

            # Detect customer information
            customer_info = await self._extract_customer_info(session, plan_mapping)

            # Process the subscription with mapped plan
            result = await self._process_subscription_with_mapping(
                session, plan_mapping, customer_info
            )

            logger.info(f"Successfully processed checkout session {session_id}")
            return result

        except Exception as e:
            logger.error(f"Error processing checkout session {session_id}: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def handle_customer_subscription_created(
        self, event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle customer.subscription.created with automatic mapping.

        This handler processes new subscriptions and maps all line items
        to internal plans and add-ons.
        """
        subscription = event["data"]["object"]
        subscription_id = subscription["id"]

        logger.info(
            f"Processing subscription creation {subscription_id} with automatic mapping"
        )

        try:
            # Process each subscription item
            items = subscription.get("items", {}).get("data", [])
            mapped_items = []

            for item in items:
                stripe_price_id = item["price"]["id"]

                # Map each item to internal plan/add-on
                mapping = await self.mapper.resolve_stripe_price(stripe_price_id)
                if mapping:
                    mapped_items.append(
                        {
                            "stripe_item_id": item["id"],
                            "stripe_price_id": stripe_price_id,
                            "mapping": mapping,
                            "quantity": item["quantity"],
                        }
                    )
                    logger.info(f"Mapped item {item['id']} to {mapping.plan_code}")
                else:
                    logger.warning(
                        f"No mapping found for subscription item price: {stripe_price_id}"
                    )

            if not mapped_items:
                logger.error(
                    f"No mapped items found for subscription {subscription_id}"
                )
                return {"status": "error", "message": "No mapped items found"}

            # Update subscription with mapped items
            result = await self._update_subscription_with_mapping(
                subscription, mapped_items
            )

            logger.info(
                f"Successfully processed subscription creation {subscription_id}"
            )
            return result

        except Exception as e:
            logger.error(
                f"Error processing subscription creation {subscription_id}: {str(e)}"
            )
            return {"status": "error", "message": str(e)}

    async def handle_customer_subscription_updated(
        self, event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle customer.subscription.updated with automatic mapping.

        This handler processes subscription changes and updates the mapping
        for any new or changed items.
        """
        subscription = event["data"]["object"]
        subscription_id = subscription["id"]

        logger.info(
            f"Processing subscription update {subscription_id} with automatic mapping"
        )

        try:
            # Get previous attributes to detect changes
            previous_attributes = event.get("data", {}).get("previous_attributes", {})

            # Check if items changed
            if "items" in previous_attributes:
                logger.info(f"Subscription items changed for {subscription_id}")

                # Re-map all current items
                items = subscription.get("items", {}).get("data", [])
                mapped_items = []

                for item in items:
                    stripe_price_id = item["price"]["id"]
                    mapping = await self.mapper.resolve_stripe_price(stripe_price_id)

                    if mapping:
                        mapped_items.append(
                            {
                                "stripe_item_id": item["id"],
                                "stripe_price_id": stripe_price_id,
                                "mapping": mapping,
                                "quantity": item["quantity"],
                            }
                        )

                # Update subscription with new mapping
                result = await self._update_subscription_with_mapping(
                    subscription, mapped_items
                )
            else:
                # Handle other subscription updates (status, etc.)
                result = await self._update_subscription_status(subscription)

            logger.info(f"Successfully processed subscription update {subscription_id}")
            return result

        except Exception as e:
            logger.error(
                f"Error processing subscription update {subscription_id}: {str(e)}"
            )
            return {"status": "error", "message": str(e)}

    async def handle_invoice_payment_succeeded(
        self, event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle invoice.payment_succeeded with automatic mapping.

        This handler processes successful payments and updates billing
        information with country/currency context.
        """
        invoice = event["data"]["object"]
        invoice_id = invoice["id"]
        subscription_id = invoice.get("subscription")

        logger.info(f"Processing successful payment for invoice {invoice_id}")

        try:
            if not subscription_id:
                logger.info(f"Invoice {invoice_id} is not subscription-related")
                return {
                    "status": "success",
                    "message": "Non-subscription invoice processed",
                }

            # Get subscription and detect country/currency
            country_code = await self._detect_invoice_country(invoice)
            currency = invoice.get("currency", "usd").upper()

            logger.info(
                f"Invoice {invoice_id} processed for {country_code} in {currency}"
            )

            # Update subscription billing information
            result = await self._update_subscription_billing(
                subscription_id, invoice, country_code, currency
            )

            logger.info(f"Successfully processed payment for invoice {invoice_id}")
            return result

        except Exception as e:
            logger.error(f"Error processing invoice payment {invoice_id}: {str(e)}")
            return {"status": "error", "message": str(e)}

    # Private helper methods

    async def _extract_customer_info(
        self, session: Dict[str, Any], plan_mapping
    ) -> Dict[str, Any]:
        """Extract customer information with country detection."""

        # Detect customer country (priority order)
        country = (
            session.get("customer_details", {}).get("address", {}).get("country")
            or session.get("shipping", {}).get("address", {}).get("country")
            or session.get("metadata", {}).get("country")
            or plan_mapping.country_code  # Use mapping country as fallback
            or "US"  # Final fallback
        ).upper()

        # Extract customer email
        customer_email = (
            session.get("customer_details", {}).get("email")
            or session.get("customer_email")
            or session.get("metadata", {}).get("customer_email")
        )

        # Extract customer name
        customer_name = session.get("customer_details", {}).get("name")

        return {
            "country_code": country,
            "currency": plan_mapping.currency,
            "email": customer_email,
            "name": customer_name,
            "stripe_customer_id": session.get("customer"),
            "billing_cycle": plan_mapping.billing_cycle,
            "tax_behavior": plan_mapping.tax_behavior,
        }

    async def _process_subscription_with_mapping(
        self, session: Dict[str, Any], plan_mapping, customer_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process subscription creation with automatic mapping."""

        async with get_async_session() as db_session:
            try:
                # Find or create firm based on customer email
                firm = await self._find_or_create_firm(
                    customer_info["email"], customer_info, db_session
                )

                # Create or update subscription record
                subscription_data = {
                    "firm_id": firm.id,
                    "stripe_subscription_id": session.get("subscription"),
                    "stripe_customer_id": customer_info["stripe_customer_id"],
                    "plan_code": plan_mapping.plan_code,
                    "billing_cycle": plan_mapping.billing_cycle,
                    "status": "active",
                    "billing_country": customer_info["country_code"],
                    "billing_currency": customer_info["currency"],
                    "tax_behavior": customer_info["tax_behavior"],
                }

                # Create subscription record
                await self._create_subscription_record(subscription_data, db_session)

                await db_session.commit()

                return {
                    "status": "success",
                    "firm_id": str(firm.id),
                    "plan_code": plan_mapping.plan_code,
                    "country": customer_info["country_code"],
                    "currency": customer_info["currency"],
                }

            except Exception as e:
                await db_session.rollback()
                raise e

    async def _update_subscription_with_mapping(
        self, subscription: Dict[str, Any], mapped_items: list
    ) -> Dict[str, Any]:
        """Update subscription with mapped items."""

        async with get_async_session() as db_session:
            try:
                subscription_id = subscription["id"]

                # Find existing subscription record
                existing_subscription = await self._find_subscription_by_stripe_id(
                    subscription_id, db_session
                )

                if existing_subscription:
                    # Update subscription with new mapping
                    primary_item = mapped_items[0] if mapped_items else None
                    if primary_item:
                        existing_subscription.plan_code = primary_item[
                            "mapping"
                        ].plan_code
                        existing_subscription.billing_currency = primary_item[
                            "mapping"
                        ].currency
                        existing_subscription.billing_country = primary_item[
                            "mapping"
                        ].country_code

                    # Update add-ons (items beyond the first)
                    addon_codes = [
                        item["mapping"].plan_code
                        for item in mapped_items[1:]
                        if item["mapping"].internal_type == "addon"
                    ]

                    # Store add-on information (implementation depends on your schema)
                    await self._update_subscription_addons(
                        existing_subscription, addon_codes, db_session
                    )

                await db_session.commit()

                return {
                    "status": "success",
                    "subscription_id": subscription_id,
                    "mapped_items": len(mapped_items),
                }

            except Exception as e:
                await db_session.rollback()
                raise e

    async def _detect_invoice_country(self, invoice: Dict[str, Any]) -> str:
        """Detect country from invoice information."""

        # Try customer billing address
        customer_address = invoice.get("customer_address")
        if customer_address and customer_address.get("country"):
            return customer_address["country"].upper()

        # Try shipping address
        shipping_address = invoice.get("shipping", {}).get("address")
        if shipping_address and shipping_address.get("country"):
            return shipping_address["country"].upper()

        # Fallback to currency-based detection
        currency = invoice.get("currency", "usd").upper()
        if currency == "EUR":
            return "BE"  # Assume Belgium for EUR
        elif currency == "USD":
            return "US"

        return "US"  # Final fallback

    async def _find_or_create_firm(
        self, email: str, customer_info: Dict[str, Any], db_session
    ):
        """Find existing firm or create new one."""

        # Implementation depends on your firm model
        # This is a placeholder that should be implemented based on your schema

        from sqlalchemy import select

        # Try to find existing firm by email
        query = select(Firm).where(Firm.email == email)
        result = await db_session.execute(query)
        firm = result.scalar_one_or_none()

        if not firm:
            # Create new firm
            firm = Firm(
                name=customer_info.get("name", "Unknown"),
                email=email,
                stripe_customer_id=customer_info["stripe_customer_id"],
                country_code=customer_info["country_code"],
                preferred_currency=customer_info["currency"],
            )
            db_session.add(firm)
            await db_session.flush()  # Get the ID
        else:
            # Update existing firm with Stripe customer ID if missing
            if not firm.stripe_customer_id:
                firm.stripe_customer_id = customer_info["stripe_customer_id"]

            # Update country/currency if missing
            if not firm.country_code:
                firm.country_code = customer_info["country_code"]
            if not firm.preferred_currency:
                firm.preferred_currency = customer_info["currency"]

        return firm

    async def _create_subscription_record(
        self, subscription_data: Dict[str, Any], db_session
    ):
        """Create subscription record in database."""

        # Implementation depends on your subscription model
        # This is a placeholder that should be implemented based on your schema

        subscription = TenantSubscription(**subscription_data)
        db_session.add(subscription)
        await db_session.flush()

        return subscription

    async def _find_subscription_by_stripe_id(
        self, stripe_subscription_id: str, db_session
    ):
        """Find subscription by Stripe subscription ID."""

        from sqlalchemy import select

        query = select(TenantSubscription).where(
            TenantSubscription.stripe_subscription_id == stripe_subscription_id
        )
        result = await db_session.execute(query)
        return result.scalar_one_or_none()

    async def _update_subscription_addons(
        self, subscription, addon_codes: list, db_session
    ):
        """Update subscription add-ons."""

        # Implementation depends on your add-on model
        # This is a placeholder that should be implemented based on your schema

        # Store add-on codes in subscription record or related table
        # This could be a JSON field or separate table depending on your design
        pass

    async def _update_subscription_status(
        self, subscription: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update subscription status."""

        async with get_async_session() as db_session:
            try:
                subscription_id = subscription["id"]
                status = subscription["status"]

                existing_subscription = await self._find_subscription_by_stripe_id(
                    subscription_id, db_session
                )

                if existing_subscription:
                    existing_subscription.status = status
                    await db_session.commit()

                return {
                    "status": "success",
                    "subscription_id": subscription_id,
                    "new_status": status,
                }

            except Exception as e:
                await db_session.rollback()
                raise e

    async def _update_subscription_billing(
        self,
        subscription_id: str,
        invoice: Dict[str, Any],
        country_code: str,
        currency: str,
    ) -> Dict[str, Any]:
        """Update subscription billing information."""

        async with get_async_session() as db_session:
            try:
                existing_subscription = await self._find_subscription_by_stripe_id(
                    subscription_id, db_session
                )

                if existing_subscription:
                    # Update billing information
                    existing_subscription.last_payment_date = invoice.get("created")
                    existing_subscription.billing_country = country_code
                    existing_subscription.billing_currency = currency

                    # Update payment amount if available
                    if invoice.get("amount_paid"):
                        existing_subscription.last_payment_amount = (
                            invoice["amount_paid"] / 100
                        )

                await db_session.commit()

                return {
                    "status": "success",
                    "subscription_id": subscription_id,
                    "country": country_code,
                    "currency": currency,
                }

            except Exception as e:
                await db_session.rollback()
                raise e


# Global instance
enhanced_webhook_handler = EnhancedWebhookHandler()
