"""
User Location Detection and Data Residency Management

This module provides services for detecting user location, managing data residency
preferences, and ensuring compliance with regional data protection requirements.
"""

import os
import logging
import httpx
from typing import Optional, Dict, Any, Tuple
from fastapi import Request
from backend.services.data_residency import DataRegion

logger = logging.getLogger(__name__)


class LocationDetectionService:
    """Service for detecting user location and managing data residency."""

    def __init__(self):
        self.ipinfo_token = os.getenv("IPINFO_TOKEN")
        self.enable_geolocation = (
            os.getenv("ENABLE_GEOLOCATION", "true").lower() == "true"
        )

        # EU countries for GDPR compliance
        self.eu_countries = {
            "AT",
            "BE",
            "BG",
            "HR",
            "CY",
            "CZ",
            "DK",
            "EE",
            "FI",
            "FR",
            "DE",
            "GR",
            "HU",
            "IE",
            "IT",
            "LV",
            "LT",
            "LU",
            "MT",
            "NL",
            "PL",
            "PT",
            "RO",
            "SK",
            "SI",
            "ES",
            "SE",
            "IS",
            "LI",
            "NO",
        }

    async def detect_user_region_from_ip(
        self, ip_address: str
    ) -> Tuple[DataRegion, Dict[str, Any]]:
        """
        Detect user's region based on IP address.

        Args:
            ip_address: User's IP address

        Returns:
            Tuple of (detected_region, location_metadata)
        """
        if not self.enable_geolocation:
            logger.info("Geolocation disabled, defaulting to US region")
            return DataRegion.US, {"method": "disabled", "default": True}

        # Skip private/local IPs
        if self._is_private_ip(ip_address):
            logger.info(f"Private IP detected: {ip_address}, defaulting to US")
            return DataRegion.US, {"method": "private_ip", "ip": ip_address}

        try:
            location_data = await self._get_ip_location(ip_address)
            country_code = location_data.get("country", "").upper()

            # Determine region based on country
            if country_code in self.eu_countries:
                region = DataRegion.EU
            else:
                region = DataRegion.US

            metadata = {
                "method": "ip_geolocation",
                "ip": ip_address,
                "country": country_code,
                "city": location_data.get("city"),
                "region_name": location_data.get("region"),
                "detected_region": region.value,
            }

            logger.info(
                f"Detected region {region.value} for IP {ip_address}", extra=metadata
            )

            return region, metadata

        except Exception as e:
            logger.warning(
                f"Failed to detect location for IP {ip_address}, defaulting to US",
                extra={"ip": ip_address, "error": str(e)},
            )
            return DataRegion.US, {"method": "fallback", "error": str(e)}

    async def _get_ip_location(self, ip_address: str) -> Dict[str, Any]:
        """Get location data from IP geolocation service."""
        if self.ipinfo_token:
            # Use IPInfo service
            url = f"https://ipinfo.io/{ip_address}/json"
            headers = {"Authorization": f"Bearer {self.ipinfo_token}"}
        else:
            # Use free service (limited requests)
            url = f"http://ip-api.com/json/{ip_address}"
            headers = {}

        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, timeout=5.0)
            response.raise_for_status()
            return response.json()

    def _is_private_ip(self, ip_address: str) -> bool:
        """Check if IP address is private/local."""
        private_ranges = [
            "127.",
            "10.",
            "192.168.",
            "172.16.",
            "172.17.",
            "172.18.",
            "172.19.",
            "172.20.",
            "172.21.",
            "172.22.",
            "172.23.",
            "172.24.",
            "172.25.",
            "172.26.",
            "172.27.",
            "172.28.",
            "172.29.",
            "172.30.",
            "172.31.",
            "::1",
            "localhost",
        ]
        return any(ip_address.startswith(prefix) for prefix in private_ranges)

    def extract_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request headers.

        Args:
            request: FastAPI request object

        Returns:
            Client IP address
        """
        # Check for forwarded headers (common in production)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "127.0.0.1"  # Fallback for testing

    async def determine_user_region(
        self, request: Request, user_preference: Optional[DataRegion] = None
    ) -> Tuple[DataRegion, Dict[str, Any]]:
        """
        Determine user's data residency region.

        Args:
            request: FastAPI request object
            user_preference: User's explicit region preference

        Returns:
            Tuple of (final_region, determination_metadata)
        """
        metadata = {"determination_method": []}

        # 1. Check user preference first (highest priority)
        if user_preference:
            metadata["determination_method"].append("user_preference")
            metadata["user_preference"] = user_preference.value
            logger.info(
                f"Using user preference: {user_preference.value}", extra=metadata
            )
            return user_preference, metadata

        # 2. Detect from IP address
        client_ip = self.extract_client_ip(request)
        detected_region, location_data = await self.detect_user_region_from_ip(
            client_ip
        )

        metadata["determination_method"].append("ip_detection")
        metadata.update(location_data)

        return detected_region, metadata


class DataResidencyPreferenceManager:
    """Manager for user data residency preferences."""

    @staticmethod
    def validate_region_preference(region_str: Optional[str]) -> Optional[DataRegion]:
        """
        Validate and convert region string to DataRegion enum.

        Args:
            region_str: Region string from user input

        Returns:
            Valid DataRegion or None if invalid
        """
        if not region_str:
            return None

        try:
            return DataRegion(region_str.upper())
        except ValueError:
            logger.warning(f"Invalid region preference: {region_str}")
            return None

    @staticmethod
    def get_region_display_name(region: DataRegion) -> str:
        """Get user-friendly display name for region."""
        display_names = {
            DataRegion.US: "United States",
            DataRegion.EU: "European Union",
        }
        return display_names.get(region, region.value)

    @staticmethod
    def get_available_regions() -> Dict[str, str]:
        """Get available regions for user selection."""
        return {
            DataRegion.US.value: "United States",
            DataRegion.EU.value: "European Union",
        }


# Global service instances
location_service = LocationDetectionService()
preference_manager = DataResidencyPreferenceManager()


async def detect_user_region(
    request: Request, user_preference: Optional[str] = None
) -> Tuple[DataRegion, Dict[str, Any]]:
    """
    Convenience function to detect user region.

    Args:
        request: FastAPI request object
        user_preference: User's region preference string

    Returns:
        Tuple of (region, metadata)
    """
    validated_preference = preference_manager.validate_region_preference(
        user_preference
    )
    return await location_service.determine_user_region(request, validated_preference)


def get_region_for_country(country_code: str) -> DataRegion:
    """
    Get data region for a country code.

    Args:
        country_code: ISO 2-letter country code

    Returns:
        Appropriate data region
    """
    eu_countries = location_service.eu_countries
    return DataRegion.EU if country_code.upper() in eu_countries else DataRegion.US
