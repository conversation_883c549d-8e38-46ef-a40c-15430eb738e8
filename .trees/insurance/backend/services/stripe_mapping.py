"""
Stripe Product Mapping Service

Automatically maps Supabase subscription plans to Stripe products and prices,
enabling seamless multi-country subscription management.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from uuid import UUID
from datetime import datetime
from dataclasses import dataclass

import stripe
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from backend.core.database import get_async_session
from backend.models.subscription import SubscriptionPlan, SubscriptionAddon
from backend.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class SyncResult:
    """Result of a sync operation."""

    products_synced: int = 0
    prices_created: int = 0
    prices_updated: int = 0
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []


@dataclass
class PlanMapping:
    """Mapping between internal plan and Stripe price."""

    internal_id: UUID
    internal_type: str  # "plan" or "addon"
    plan_code: str
    country_code: str
    currency: str
    billing_cycle: str
    stripe_price_id: str
    tax_behavior: str


class StripeProductMapper:
    """Core service for mapping database plans to Stripe products."""

    def __init__(self):
        """Initialize the mapper with Stripe client."""
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.stripe = stripe

    async def sync_all_products(self) -> SyncResult:
        """Sync all active plans and add-ons to Stripe."""
        logger.info("Starting full product sync to Stripe")
        result = SyncResult()

        try:
            async with get_async_session() as session:
                # Sync all active subscription plans
                plans = await self._get_active_plans(session)
                for plan in plans:
                    plan_result = await self.sync_plan_to_stripe(plan, session)
                    result.products_synced += 1
                    result.prices_created += plan_result.prices_created
                    result.prices_updated += plan_result.prices_updated
                    result.errors.extend(plan_result.errors)

                # Sync all active add-ons
                addons = await self._get_active_addons(session)
                for addon in addons:
                    addon_result = await self.sync_addon_to_stripe(addon, session)
                    result.products_synced += 1
                    result.prices_created += addon_result.prices_created
                    result.prices_updated += addon_result.prices_updated
                    result.errors.extend(addon_result.errors)

        except Exception as e:
            logger.error(f"Error during full sync: {str(e)}")
            result.errors.append(f"Full sync error: {str(e)}")

        logger.info(
            f"Full sync completed: {result.products_synced} products, "
            f"{result.prices_created} prices created, {len(result.errors)} errors"
        )
        return result

    async def sync_plan_to_stripe(self, plan, session: AsyncSession) -> SyncResult:
        """Sync a single plan with all its pricing variants."""
        logger.info(f"Syncing plan {plan.code} to Stripe")
        result = SyncResult()

        try:
            # Create or update Stripe product
            stripe_product = await self._create_or_update_product(plan, "plan")

            # Update database with Stripe product ID
            if plan.stripe_product_id != stripe_product.id:
                plan.stripe_product_id = stripe_product.id
                plan.last_synced_at = datetime.utcnow()
                await session.commit()

            # Get all pricing for this plan
            pricing_records = await self._get_plan_pricing(plan.id, session)

            # Create Stripe prices for each country/currency/cycle
            for pricing in pricing_records:
                try:
                    stripe_price = await self._create_or_update_price(
                        stripe_product, pricing, plan, "plan"
                    )

                    # Update database with Stripe price ID
                    if pricing.stripe_price_id != stripe_price.id:
                        pricing.stripe_price_id = stripe_price.id
                        pricing.last_synced_at = datetime.utcnow()
                        result.prices_created += 1
                    else:
                        result.prices_updated += 1

                except Exception as e:
                    error_msg = f"Error creating price for plan {plan.code}: {str(e)}"
                    logger.error(error_msg)
                    result.errors.append(error_msg)

            await session.commit()

        except Exception as e:
            error_msg = f"Error syncing plan {plan.code}: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            await session.rollback()

        return result

    async def sync_addon_to_stripe(self, addon, session: AsyncSession) -> SyncResult:
        """Sync a single add-on with all its pricing variants."""
        logger.info(f"Syncing add-on {addon.code} to Stripe")
        result = SyncResult()

        try:
            # Create or update Stripe product
            stripe_product = await self._create_or_update_product(addon, "addon")

            # Update database with Stripe product ID
            if addon.stripe_product_id != stripe_product.id:
                addon.stripe_product_id = stripe_product.id
                addon.last_synced_at = datetime.utcnow()
                await session.commit()

            # Get all pricing for this add-on
            pricing_records = await self._get_addon_pricing(addon.id, session)

            # Create Stripe prices for each country/currency/cycle
            for pricing in pricing_records:
                try:
                    stripe_price = await self._create_or_update_price(
                        stripe_product, pricing, addon, "addon"
                    )

                    # Update database with Stripe price ID
                    if pricing.stripe_price_id != stripe_price.id:
                        pricing.stripe_price_id = stripe_price.id
                        pricing.last_synced_at = datetime.utcnow()
                        result.prices_created += 1
                    else:
                        result.prices_updated += 1

                except Exception as e:
                    error_msg = f"Error creating price for addon {addon.code}: {str(e)}"
                    logger.error(error_msg)
                    result.errors.append(error_msg)

            await session.commit()

        except Exception as e:
            error_msg = f"Error syncing addon {addon.code}: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            await session.rollback()

        return result

    async def get_stripe_price_for_plan(
        self, plan_code: str, country_code: str, billing_cycle: str
    ) -> Optional[str]:
        """Get Stripe price ID for a specific plan/country/cycle combination."""

        try:
            async with get_async_session() as session:
                # Query for the specific price
                query = """
                SELECT pp.stripe_price_id
                FROM tenants.plan_pricing pp
                JOIN tenants.subscription_plans sp ON pp.plan_id = sp.id
                WHERE sp.code = :plan_code
                  AND pp.country_code = :country_code
                  AND pp.stripe_price_id IS NOT NULL
                  AND pp.is_active = true
                """

                result = await session.execute(
                    query,
                    {"plan_code": plan_code, "country_code": country_code.upper()},
                )

                row = result.fetchone()
                return row[0] if row else None

        except Exception as e:
            logger.error(
                f"Error getting Stripe price for {plan_code}/{country_code}: {str(e)}"
            )
            return None

    async def resolve_stripe_price(self, stripe_price_id: str) -> Optional[PlanMapping]:
        """Resolve Stripe price ID to internal plan mapping."""

        try:
            async with get_async_session() as session:
                # Try to find in plan pricing first
                plan_query = """
                SELECT 
                    sp.id as internal_id,
                    'plan' as internal_type,
                    sp.code as plan_code,
                    pp.country_code,
                    pp.currency,
                    CASE 
                        WHEN pp.price_monthly = pp.price_yearly / 10 THEN 'monthly'
                        ELSE 'yearly'
                    END as billing_cycle,
                    pp.stripe_price_id,
                    CASE 
                        WHEN pp.tax_inclusive THEN 'inclusive'
                        ELSE 'exclusive'
                    END as tax_behavior
                FROM tenants.plan_pricing pp
                JOIN tenants.subscription_plans sp ON pp.plan_id = sp.id
                WHERE pp.stripe_price_id = :stripe_price_id
                """

                result = await session.execute(
                    plan_query, {"stripe_price_id": stripe_price_id}
                )
                row = result.fetchone()

                if row:
                    return PlanMapping(
                        internal_id=row.internal_id,
                        internal_type=row.internal_type,
                        plan_code=row.plan_code,
                        country_code=row.country_code,
                        currency=row.currency,
                        billing_cycle=row.billing_cycle,
                        stripe_price_id=row.stripe_price_id,
                        tax_behavior=row.tax_behavior,
                    )

                # Try add-on pricing
                addon_query = """
                SELECT 
                    sa.id as internal_id,
                    'addon' as internal_type,
                    sa.code as plan_code,
                    pp.country_code,
                    pp.currency,
                    CASE 
                        WHEN pp.price_monthly = pp.price_yearly / 10 THEN 'monthly'
                        ELSE 'yearly'
                    END as billing_cycle,
                    pp.stripe_price_id,
                    CASE 
                        WHEN pp.tax_inclusive THEN 'inclusive'
                        ELSE 'exclusive'
                    END as tax_behavior
                FROM tenants.plan_pricing pp
                JOIN tenants.subscription_addons sa ON pp.addon_id = sa.id
                WHERE pp.stripe_price_id = :stripe_price_id
                """

                result = await session.execute(
                    addon_query, {"stripe_price_id": stripe_price_id}
                )
                row = result.fetchone()

                if row:
                    return PlanMapping(
                        internal_id=row.internal_id,
                        internal_type=row.internal_type,
                        plan_code=row.plan_code,
                        country_code=row.country_code,
                        currency=row.currency,
                        billing_cycle=row.billing_cycle,
                        stripe_price_id=row.stripe_price_id,
                        tax_behavior=row.tax_behavior,
                    )

                return None

        except Exception as e:
            logger.error(f"Error resolving Stripe price {stripe_price_id}: {str(e)}")
            return None

    # Private helper methods

    async def _get_active_plans(self, session: AsyncSession):
        """Get all active subscription plans."""
        query = select(SubscriptionPlan).where(
            and_(SubscriptionPlan.is_active == True, SubscriptionPlan.is_public == True)
        )
        result = await session.execute(query)
        return result.scalars().all()

    async def _get_active_addons(self, session: AsyncSession):
        """Get all active subscription add-ons."""
        query = select(SubscriptionAddon).where(SubscriptionAddon.is_active == True)
        result = await session.execute(query)
        return result.scalars().all()

    async def _get_plan_pricing(self, plan_id: UUID, session: AsyncSession):
        """Get all pricing records for a plan."""
        query = """
        SELECT * FROM tenants.plan_pricing 
        WHERE plan_id = :plan_id AND is_active = true
        ORDER BY country_code, price_monthly
        """
        result = await session.execute(query, {"plan_id": str(plan_id)})
        return result.fetchall()

    async def _get_addon_pricing(self, addon_id: UUID, session: AsyncSession):
        """Get all pricing records for an add-on."""
        query = """
        SELECT * FROM tenants.plan_pricing 
        WHERE addon_id = :addon_id AND is_active = true
        ORDER BY country_code, price_monthly
        """
        result = await session.execute(query, {"addon_id": str(addon_id)})
        return result.fetchall()

    async def _create_or_update_product(self, item, item_type: str):
        """Create or update a Stripe product."""

        product_name = f"PI Lawyer AI {item.name}" if item_type == "plan" else item.name

        # Check if product already exists
        if hasattr(item, "stripe_product_id") and item.stripe_product_id:
            try:
                # Update existing product
                product = self.stripe.Product.modify(
                    item.stripe_product_id,
                    name=product_name,
                    description=item.description,
                    metadata={
                        "internal_type": item_type,
                        "internal_code": item.code,
                        "internal_id": str(item.id),
                        "sync_version": "1.0",
                    },
                )
                logger.info(f"Updated Stripe product {product.id} for {item.code}")
                return product
            except stripe.error.InvalidRequestError:
                # Product doesn't exist, create new one
                pass

        # Create new product
        product = self.stripe.Product.create(
            name=product_name,
            description=item.description,
            metadata={
                "internal_type": item_type,
                "internal_code": item.code,
                "internal_id": str(item.id),
                "sync_version": "1.0",
            },
        )

        logger.info(f"Created Stripe product {product.id} for {item.code}")
        return product

    async def _create_or_update_price(self, product, pricing, item, item_type: str):
        """Create or update a Stripe price."""

        # Determine billing cycle from price comparison
        is_monthly = abs(pricing.price_yearly - (pricing.price_monthly * 10)) < 1
        billing_cycle = "monthly" if is_monthly else "yearly"
        price_amount = pricing.price_monthly if is_monthly else pricing.price_yearly

        # Convert to cents/smallest currency unit
        price_in_cents = int(price_amount * 100)

        # Check if price already exists
        if hasattr(pricing, "stripe_price_id") and pricing.stripe_price_id:
            try:
                # Verify price exists
                existing_price = self.stripe.Price.retrieve(pricing.stripe_price_id)
                if existing_price.unit_amount == price_in_cents:
                    return existing_price
            except stripe.error.InvalidRequestError:
                # Price doesn't exist, create new one
                pass

        # Create new price
        price_data = {
            "product": product.id,
            "unit_amount": price_in_cents,
            "currency": pricing.currency.lower(),
            "recurring": {
                "interval": "month" if billing_cycle == "monthly" else "year"
            },
            "tax_behavior": "inclusive" if pricing.tax_inclusive else "exclusive",
            "metadata": {
                "internal_pricing_id": str(pricing.id),
                "country_code": pricing.country_code,
                "currency": pricing.currency,
                "billing_cycle": billing_cycle,
                "tax_behavior": "inclusive" if pricing.tax_inclusive else "exclusive",
                "sync_version": "1.0",
            },
        }

        price = self.stripe.Price.create(**price_data)

        logger.info(
            f"Created Stripe price {price.id} for {item.code} "
            f"({pricing.country_code}, {pricing.currency}, {billing_cycle})"
        )
        return price


# Global instance
stripe_mapper = StripeProductMapper()
