"""
Calendly API service.

This module provides functions for interacting with the Calendly API,
specifically for generating single-use scheduling links.
"""

import asyncio
import json
import logging
import os
import random
import inspect
import uuid
from typing import Any, Dict, Optional

import httpx
from pydantic import BaseModel, Field

# Configure logging
logger = logging.getLogger(__name__)

# Environment variables
CALENDLY_PAT = os.getenv("CALENDLY_PAT")
CALENDLY_ORG_URI = os.getenv("CALENDLY_ORG_URI")
CALENDLY_EVENT_TYPE_PI = os.getenv("CALENDLY_EVENT_TYPE_PI")
USE_CALCOM = os.getenv("USE_CALCOM", "false").lower() == "true"

# Constants
MAX_RETRIES = 3
BASE_DELAY = 1  # seconds
MAX_DELAY = 30  # seconds


class SchedulingLinkRequest(BaseModel):
    """Request model for creating a scheduling link."""

    event_type_uri: str = Field(..., description="The Calendly event type URI")
    owner_name: str = Field(
        ..., description="The name of the owner/creator of the link"
    )
    owner_email: Optional[str] = Field(
        None, description="The email of the owner/creator"
    )
    prospect_name: Optional[str] = Field(None, description="The name of the prospect")
    prospect_email: Optional[str] = Field(None, description="The email of the prospect")
    prospect_phone: Optional[str] = Field(
        None, description="The phone number of the prospect"
    )
    firm_id: uuid.UUID = Field(..., description="The firm/tenant ID")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for the booking"
    )


class SchedulingLinkResponse(BaseModel):
    """Response model for a created scheduling link."""

    url: str = Field(..., description="The scheduling link URL")
    booking_id: str = Field(..., description="The booking ID (Calendly URI)")
    expires_at: Optional[str] = Field(
        None, description="When the link expires (ISO format)"
    )


class CalendlyService:
    """Service for interacting with the Calendly API."""

    def __init__(self, pat: Optional[str] = None, org_uri: Optional[str] = None):
        """
        Initialize the Calendly service.

        Args:
            pat: Calendly Personal Access Token (defaults to env var)
            org_uri: Calendly Organization URI (defaults to env var)
        """
        # Prefer runtime environment variables (so tests with monkeypatch work),
        # then fall back to module-level constants.
        self.pat = pat or os.getenv("CALENDLY_PAT") or CALENDLY_PAT
        self.org_uri = org_uri or os.getenv("CALENDLY_ORG_URI") or CALENDLY_ORG_URI

        if not self.pat:
            logger.warning("CALENDLY_PAT not set. Calendly API calls will fail.")

        if not self.org_uri:
            logger.warning("CALENDLY_ORG_URI not set. Calendly API calls will fail.")

    async def create_scheduling_link(
        self, request: SchedulingLinkRequest
    ) -> SchedulingLinkResponse:
        """
        Create a single-use scheduling link.

        Args:
            request: The scheduling link request

        Returns:
            SchedulingLinkResponse: The created scheduling link

        Raises:
            ValueError: If required parameters are missing
            httpx.HTTPError: If the API request fails
        """
        if USE_CALCOM:
            # If Cal.com is enabled, use that instead
            # This is a placeholder for future implementation
            raise NotImplementedError("Cal.com integration not yet implemented")

        if not self.pat or not self.org_uri:
            raise ValueError("Calendly PAT and Organization URI must be set")

        # Prepare the request payload
        payload = {
            "max_event_count": 1,
            "owner": request.event_type_uri,
            "owner_type": "EventType",
            "organization": self.org_uri,
        }

        # Add custom invitee fields if provided
        custom_fields = []

        if request.prospect_name:
            custom_fields.append({"name": "name", "value": request.prospect_name})

        if request.prospect_email:
            custom_fields.append({"name": "email", "value": request.prospect_email})

        if request.prospect_phone:
            custom_fields.append(
                {"name": "phone_number", "value": request.prospect_phone}
            )

        if custom_fields:
            payload["custom_invitee_fields"] = custom_fields

        # Add metadata if provided
        if request.metadata:
            # Calendly doesn't directly support metadata in scheduling links
            # We'll add it to the custom fields as a workaround
            metadata_str = json.dumps(request.metadata)
            custom_fields.append(
                {
                    "name": "metadata",
                    "value": metadata_str[:100],  # Truncate if too long
                }
            )

        # Make the API request with retries
        return await self._make_request_with_retries(
            method="POST",
            url="https://api.calendly.com/scheduling_links",
            json=payload,
            firm_id=request.firm_id,
        )

    async def _make_request_with_retries(
        self, method: str, url: str, json: Dict[str, Any], firm_id: uuid.UUID
    ) -> SchedulingLinkResponse:
        """
        Make an API request with retries and exponential backoff.

        Args:
            method: HTTP method
            url: API endpoint URL
            json: Request payload
            firm_id: The firm/tenant ID for logging

        Returns:
            SchedulingLinkResponse: The API response

        Raises:
            httpx.HTTPError: If all retries fail
        """
        headers = {
            "Authorization": f"Bearer {self.pat}",
            "Content-Type": "application/json",
        }

        retries = 0
        last_exception = None

        while retries < MAX_RETRIES:
            try:
                client = httpx.AsyncClient()
                try:
                    logger.info(
                        "Making Calendly API request",
                        extra={
                            "firm_id": str(firm_id),
                            "url": url,
                            "method": method,
                            "retry": retries,
                        },
                    )

                    resp_or_coro = client.request(
                        method=method,
                        url=url,
                        json=json,
                        headers=headers,
                        timeout=30.0,  # 30 second timeout
                    )
                    response = (
                        await resp_or_coro
                        if inspect.isawaitable(resp_or_coro)
                        else resp_or_coro
                    )

                    # Raise for 4xx/5xx status codes
                    rfs_result = response.raise_for_status()
                    if inspect.isawaitable(rfs_result):
                        await rfs_result

                    # Parse the response
                    data = response.json()
                    if inspect.isawaitable(data):
                        data = await data
                    # If json() returns a callable MagicMock, call it to extract dict
                    if callable(data) and not isinstance(data, dict):
                        possibly = data()
                        data = (
                            await possibly
                            if inspect.isawaitable(possibly)
                            else possibly
                        )
                    resource = data.get("resource", {})

                    return SchedulingLinkResponse(
                        url=resource.get("booking_url", ""),
                        booking_id=resource.get("uri", ""),
                        expires_at=resource.get("expires_at"),
                    )
                finally:
                    try:
                        close_result = client.aclose()
                        if inspect.isawaitable(close_result):
                            await close_result
                    except Exception:
                        pass

            except httpx.HTTPStatusError as e:
                status_code = e.response.status_code

                # Don't retry 4xx errors (except 429 Too Many Requests)
                if 400 <= status_code < 500 and status_code != 429:
                    logger.error(
                        "Calendly API client error",
                        extra={
                            "firm_id": str(firm_id),
                            "status_code": status_code,
                            "response": e.response.text,
                            "retry": retries,
                        },
                    )
                    raise

                last_exception = e
                logger.warning(
                    "Calendly API server error, retrying",
                    extra={
                        "firm_id": str(firm_id),
                        "status_code": status_code,
                        "response": e.response.text,
                        "retry": retries,
                    },
                )

            except (httpx.RequestError, httpx.TimeoutException) as e:
                last_exception = e
                logger.warning(
                    "Calendly API request failed, retrying",
                    extra={"firm_id": str(firm_id), "error": str(e), "retry": retries},
                )

            # Exponential backoff with jitter
            delay = min(BASE_DELAY * (2**retries) + (0.1 * random.random()), MAX_DELAY)
            await asyncio.sleep(delay)
            retries += 1

        # All retries failed
        logger.error(
            f"Calendly API request failed after {MAX_RETRIES} retries",
            extra={
                "firm_id": str(firm_id),
                "url": url,
                "method": method,
                "error": str(last_exception),
            },
        )

        raise last_exception


# Create a singleton instance
calendly_service = CalendlyService()


async def create_scheduling_link(
    request: SchedulingLinkRequest,
) -> SchedulingLinkResponse:
    """
    Create a single-use scheduling link.

    This is a convenience function that uses the singleton CalendlyService instance.

    Args:
        request: The scheduling link request

    Returns:
        SchedulingLinkResponse: The created scheduling link
    """
    return await calendly_service.create_scheduling_link(request)
