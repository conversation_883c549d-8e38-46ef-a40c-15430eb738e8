"""
Automated Cleanup Jobs Service

Implements scheduled data cleanup jobs with proper error handling,
monitoring, and compliance with regional retention policies.
"""

import asyncio
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4
from concurrent.futures import ThreadPoolExecutor
import threading

from backend.models.data_retention import (
    DataRegion,
    CleanupCriteria,
    CleanupStatus,
    DATA_TYPE_REGISTRY,
)
from backend.services.data_retention_service import data_retention_service
from backend.services.regional_retention_policies import regional_retention_policies
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class AutomatedCleanupJobsService:
    """Service for managing automated data cleanup jobs."""

    def __init__(self):
        self.scheduler_running = False
        self.cleanup_executor = ThreadPoolExecutor(
            max_workers=4, thread_name_prefix="cleanup"
        )
        self.active_jobs = {}
        self.job_history = []
        self.max_history = 1000

        # Default cleanup schedules
        self.default_schedules = {
            # Daily cleanup for short-term data
            "security_events": {
                "schedule": "daily",
                "time": "02:00",
                "retention_days": 90,
                "batch_size": 5000,
            },
            # Weekly cleanup for medium-term data
            "usage_analytics": {
                "schedule": "weekly",
                "day": "sunday",
                "time": "03:00",
                "retention_days": 365,
                "batch_size": 10000,
            },
            # Monthly cleanup for long-term data
            "audit_logs": {
                "schedule": "monthly",
                "day": 1,
                "time": "04:00",
                "retention_days": 2555,  # 7 years
                "batch_size": 1000,
            },
            # Quarterly cleanup for user data
            "user_identity_data": {
                "schedule": "quarterly",
                "month": [1, 4, 7, 10],
                "day": 15,
                "time": "05:00",
                "retention_days": 2555,  # 7 years
                "batch_size": 500,
            },
            # Annual cleanup for billing data
            "billing_data": {
                "schedule": "annually",
                "month": 1,
                "day": 31,
                "time": "06:00",
                "retention_days": 2555,  # 7 years
                "batch_size": 1000,
            },
        }

    def start_scheduler(self) -> None:
        """Start the automated cleanup scheduler."""
        if self.scheduler_running:
            logger.warning("Cleanup scheduler is already running")
            return

        try:
            # Schedule cleanup jobs for each data type and region
            self._schedule_cleanup_jobs()

            # Start the scheduler in a separate thread
            scheduler_thread = threading.Thread(
                target=self._run_scheduler, name="cleanup-scheduler", daemon=True
            )
            scheduler_thread.start()

            self.scheduler_running = True
            logger.info("Automated cleanup scheduler started successfully")

        except Exception as e:
            logger.error(f"Error starting cleanup scheduler: {e}")
            raise

    def stop_scheduler(self) -> None:
        """Stop the automated cleanup scheduler."""
        try:
            self.scheduler_running = False
            schedule.clear()

            # Wait for active jobs to complete
            self.cleanup_executor.shutdown(wait=True, timeout=300)  # 5 minutes timeout

            logger.info("Automated cleanup scheduler stopped")

        except Exception as e:
            logger.error(f"Error stopping cleanup scheduler: {e}")

    def _schedule_cleanup_jobs(self) -> None:
        """Schedule cleanup jobs based on default schedules."""
        for data_type, schedule_config in self.default_schedules.items():
            for region in [DataRegion.US, DataRegion.EU]:
                self._schedule_data_type_cleanup(data_type, region, schedule_config)

    def _schedule_data_type_cleanup(
        self, data_type: str, region: DataRegion, schedule_config: Dict[str, Any]
    ) -> None:
        """Schedule cleanup for a specific data type and region."""
        try:
            schedule_type = schedule_config["schedule"]
            time_str = schedule_config["time"]

            # Create the cleanup function
            cleanup_func = lambda: asyncio.run(
                self._execute_scheduled_cleanup(data_type, region, schedule_config)
            )

            # Schedule based on type
            if schedule_type == "daily":
                schedule.every().day.at(time_str).do(cleanup_func)

            elif schedule_type == "weekly":
                day = schedule_config.get("day", "sunday")
                getattr(schedule.every(), day.lower()).at(time_str).do(cleanup_func)

            elif schedule_type == "monthly":
                # For monthly, we'll check on the first of each month
                schedule.every().day.at(time_str).do(
                    lambda: self._check_monthly_cleanup(
                        data_type, region, schedule_config
                    )
                )

            elif schedule_type == "quarterly":
                # For quarterly, we'll check daily and run on specified months/days
                schedule.every().day.at(time_str).do(
                    lambda: self._check_quarterly_cleanup(
                        data_type, region, schedule_config
                    )
                )

            elif schedule_type == "annually":
                # For annual, we'll check daily and run on specified month/day
                schedule.every().day.at(time_str).do(
                    lambda: self._check_annual_cleanup(
                        data_type, region, schedule_config
                    )
                )

            logger.info(
                f"Scheduled {schedule_type} cleanup for {data_type} in {region} at {time_str}"
            )

        except Exception as e:
            logger.error(f"Error scheduling cleanup for {data_type} in {region}: {e}")

    def _run_scheduler(self) -> None:
        """Run the scheduler loop."""
        logger.info("Cleanup scheduler loop started")

        while self.scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(60)  # Continue after error

        logger.info("Cleanup scheduler loop stopped")

    async def _execute_scheduled_cleanup(
        self, data_type: str, region: DataRegion, schedule_config: Dict[str, Any]
    ) -> None:
        """Execute a scheduled cleanup job."""
        job_id = str(uuid4())

        try:
            logger.info(
                f"Starting scheduled cleanup job {job_id} for {data_type} in {region}"
            )

            # Calculate retention date
            retention_days = schedule_config["retention_days"]
            retention_date = datetime.utcnow() - timedelta(days=retention_days)

            # Create cleanup criteria
            criteria = CleanupCriteria(
                retention_date=retention_date,
                exclude_holds=True,
                batch_size=schedule_config.get("batch_size", 1000),
                dry_run=False,
            )

            # Record job start
            job_info = {
                "job_id": job_id,
                "data_type": data_type,
                "region": region.value,
                "schedule_type": schedule_config["schedule"],
                "started_at": datetime.utcnow(),
                "status": CleanupStatus.RUNNING,
                "criteria": criteria.dict(),
            }

            self.active_jobs[job_id] = job_info

            # Schedule the cleanup job
            scheduled_job_id = await data_retention_service.schedule_cleanup(
                data_type=data_type,
                region=region,
                criteria=criteria,
                user_id=UUID("00000000-0000-0000-0000-000000000000"),  # System user
                job_name=f"Scheduled {schedule_config['schedule']} cleanup - {data_type}",
            )

            # Execute the cleanup
            result = await data_retention_service.execute_cleanup(
                scheduled_job_id, region
            )

            # Update job info
            job_info.update(
                {
                    "completed_at": datetime.utcnow(),
                    "status": result.status,
                    "records_processed": result.records_processed,
                    "records_deleted": result.records_deleted,
                    "records_skipped": result.records_skipped,
                    "error_count": result.error_count,
                    "duration_seconds": result.duration_seconds,
                }
            )

            # Move to history
            self._move_job_to_history(job_id)

            logger.info(
                f"Completed scheduled cleanup job {job_id}: "
                f"{result.records_deleted} deleted, {result.records_skipped} skipped"
            )

        except Exception as e:
            logger.error(f"Error in scheduled cleanup job {job_id}: {e}")

            # Update job with error
            if job_id in self.active_jobs:
                self.active_jobs[job_id].update(
                    {
                        "completed_at": datetime.utcnow(),
                        "status": CleanupStatus.FAILED,
                        "error": str(e),
                    }
                )
                self._move_job_to_history(job_id)

    def _check_monthly_cleanup(
        self, data_type: str, region: DataRegion, schedule_config: Dict[str, Any]
    ) -> None:
        """Check if monthly cleanup should run today."""
        today = datetime.utcnow()
        target_day = schedule_config.get("day", 1)

        if today.day == target_day:
            asyncio.run(
                self._execute_scheduled_cleanup(data_type, region, schedule_config)
            )

    def _check_quarterly_cleanup(
        self, data_type: str, region: DataRegion, schedule_config: Dict[str, Any]
    ) -> None:
        """Check if quarterly cleanup should run today."""
        today = datetime.utcnow()
        target_months = schedule_config.get("month", [1, 4, 7, 10])
        target_day = schedule_config.get("day", 15)

        if today.month in target_months and today.day == target_day:
            asyncio.run(
                self._execute_scheduled_cleanup(data_type, region, schedule_config)
            )

    def _check_annual_cleanup(
        self, data_type: str, region: DataRegion, schedule_config: Dict[str, Any]
    ) -> None:
        """Check if annual cleanup should run today."""
        today = datetime.utcnow()
        target_month = schedule_config.get("month", 1)
        target_day = schedule_config.get("day", 31)

        if today.month == target_month and today.day == target_day:
            asyncio.run(
                self._execute_scheduled_cleanup(data_type, region, schedule_config)
            )

    def _move_job_to_history(self, job_id: str) -> None:
        """Move completed job to history."""
        if job_id in self.active_jobs:
            job_info = self.active_jobs.pop(job_id)
            self.job_history.append(job_info)

            # Trim history if too long
            if len(self.job_history) > self.max_history:
                self.job_history = self.job_history[-self.max_history :]

    async def trigger_manual_cleanup(
        self,
        data_type: str,
        region: DataRegion,
        retention_days: int,
        dry_run: bool = True,
        user_id: Optional[UUID] = None,
    ) -> str:
        """
        Trigger a manual cleanup job.

        Args:
            data_type: Type of data to clean up
            region: Data region
            retention_days: Number of days to retain data
            dry_run: Whether to perform a dry run
            user_id: User triggering the cleanup

        Returns:
            Job ID
        """
        try:
            # Calculate retention date
            retention_date = datetime.utcnow() - timedelta(days=retention_days)

            # Create cleanup criteria
            criteria = CleanupCriteria(
                retention_date=retention_date,
                exclude_holds=True,
                batch_size=1000,
                dry_run=dry_run,
            )

            # Schedule the cleanup job
            job_id = await data_retention_service.schedule_cleanup(
                data_type=data_type,
                region=region,
                criteria=criteria,
                user_id=user_id or UUID("00000000-0000-0000-0000-000000000000"),
                job_name=f"Manual cleanup - {data_type} ({'dry run' if dry_run else 'live'})",
            )

            logger.info(
                f"Triggered manual cleanup job {job_id} for {data_type} in {region}"
            )
            return job_id

        except Exception as e:
            logger.error(f"Error triggering manual cleanup: {e}")
            raise

    async def get_cleanup_status(self) -> Dict[str, Any]:
        """Get current cleanup job status."""
        try:
            # Get recent job history
            recent_jobs = self.job_history[-50:] if self.job_history else []

            # Calculate statistics
            total_jobs = len(self.job_history)
            successful_jobs = sum(
                1
                for job in self.job_history
                if job.get("status") == CleanupStatus.COMPLETED
            )
            failed_jobs = sum(
                1
                for job in self.job_history
                if job.get("status") == CleanupStatus.FAILED
            )

            # Get active jobs
            active_jobs = list(self.active_jobs.values())

            return {
                "scheduler_running": self.scheduler_running,
                "active_jobs": len(active_jobs),
                "active_job_details": active_jobs,
                "total_jobs_executed": total_jobs,
                "successful_jobs": successful_jobs,
                "failed_jobs": failed_jobs,
                "success_rate": (
                    (successful_jobs / total_jobs * 100) if total_jobs > 0 else 0
                ),
                "recent_jobs": recent_jobs,
                "scheduled_data_types": list(self.default_schedules.keys()),
                "next_scheduled_jobs": self._get_next_scheduled_jobs(),
            }

        except Exception as e:
            logger.error(f"Error getting cleanup status: {e}")
            return {"error": str(e)}

    def _get_next_scheduled_jobs(self) -> List[Dict[str, Any]]:
        """Get information about next scheduled jobs."""
        try:
            next_jobs = []

            for job in schedule.jobs:
                next_run = job.next_run
                if next_run:
                    next_jobs.append(
                        {
                            "next_run": next_run.isoformat(),
                            "job_info": str(job.job_func),
                            "interval": str(job.interval),
                            "unit": job.unit,
                        }
                    )

            # Sort by next run time
            next_jobs.sort(key=lambda x: x["next_run"])

            return next_jobs[:10]  # Return next 10 jobs

        except Exception as e:
            logger.error(f"Error getting next scheduled jobs: {e}")
            return []

    async def validate_cleanup_policies(self) -> Dict[str, Any]:
        """Validate that cleanup policies are properly configured."""
        try:
            validation_results = {
                "overall_status": "healthy",
                "policy_coverage": {},
                "issues": [],
                "recommendations": [],
            }

            # Check each data type has appropriate cleanup schedule
            for data_type in DATA_TYPE_REGISTRY.keys():
                if data_type not in self.default_schedules:
                    validation_results["issues"].append(
                        f"No cleanup schedule defined for data type: {data_type}"
                    )
                    validation_results["overall_status"] = "warning"
                else:
                    schedule_config = self.default_schedules[data_type]

                    # Validate schedule configuration
                    if schedule_config["retention_days"] < 30:
                        validation_results["issues"].append(
                            f"Very short retention period for {data_type}: {schedule_config['retention_days']} days"
                        )

                    validation_results["policy_coverage"][data_type] = {
                        "has_schedule": True,
                        "schedule_type": schedule_config["schedule"],
                        "retention_days": schedule_config["retention_days"],
                    }

            # Generate recommendations
            if validation_results["issues"]:
                validation_results["recommendations"].append(
                    "Review and configure cleanup schedules for all data types"
                )

            if not self.scheduler_running:
                validation_results["issues"].append("Cleanup scheduler is not running")
                validation_results["overall_status"] = "error"
                validation_results["recommendations"].append(
                    "Start the automated cleanup scheduler"
                )

            return validation_results

        except Exception as e:
            logger.error(f"Error validating cleanup policies: {e}")
            return {"overall_status": "error", "error": str(e)}


# Global service instance
automated_cleanup_service = AutomatedCleanupJobsService()
