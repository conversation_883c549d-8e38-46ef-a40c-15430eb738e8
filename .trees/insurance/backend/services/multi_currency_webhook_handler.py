#!/usr/bin/env python3
"""
Multi-Currency Webhook Handler for PI Lawyer AI
Handles Stripe webhook events with multi-currency support and regional routing.

This service implements the webhook endpoint strategy for multi-country operations.
"""

import logging
import os
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

from backend.db.session import get_db_session
from backend.models.subscription import TenantSubscription, TenantAddon
from backend.models.firm import Firm
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)


class SupportedRegion(Enum):
    """Supported regions for multi-country operations."""

    US = "US"
    EU = "EU"
    UK = "UK"
    CA = "CA"


class SupportedCurrency(Enum):
    """Supported currencies for multi-country operations."""

    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"


class MultiCurrencyWebhookHandler:
    """Enhanced webhook handler with multi-currency and regional support."""

    def __init__(self):
        self.currency_to_region_map = {
            SupportedCurrency.USD: SupportedRegion.US,
            SupportedCurrency.EUR: SupportedRegion.EU,
            SupportedCurrency.GBP: SupportedRegion.UK,
            SupportedCurrency.CAD: SupportedRegion.CA,
        }

        self.region_config = {
            SupportedRegion.US: {
                "tax_behavior": "exclusive",
                "compliance": ["hipaa", "ccpa"],
                "data_residency": "us-east-1",
                "supabase_project": "new-texas-laws",
            },
            SupportedRegion.EU: {
                "tax_behavior": "inclusive",
                "compliance": ["gdpr", "dpa"],
                "data_residency": "eu-central-1",
                "supabase_project": "ailex-belgium",
            },
            SupportedRegion.UK: {
                "tax_behavior": "inclusive",
                "compliance": ["gdpr", "dpa"],
                "data_residency": "eu-west-2",
                "supabase_project": "ailex-belgium",  # UK uses EU instance
            },
            SupportedRegion.CA: {
                "tax_behavior": "exclusive",
                "compliance": ["pipeda", "privacy_act"],
                "data_residency": "ca-central-1",
                "supabase_project": "new-texas-laws",  # CA uses US instance for now
            },
        }

    def detect_region_from_event(
        self, event: Dict[str, Any]
    ) -> Tuple[SupportedRegion, SupportedCurrency]:
        """
        Detect region and currency from Stripe webhook event.

        Args:
            event: Stripe webhook event

        Returns:
            Tuple of (region, currency)
        """
        currency = self._extract_currency_from_event(event)
        region = self.currency_to_region_map.get(currency, SupportedRegion.US)

        logger.info(
            f"Detected region {region.value} and currency {currency.value} from event {event.get('type')}"
        )

        return region, currency

    def _extract_currency_from_event(self, event: Dict[str, Any]) -> SupportedCurrency:
        """Extract currency from various Stripe event types."""
        event_type = event.get("type", "")
        event_data = event.get("data", {}).get("object", {})

        currency_str = None

        if event_type == "checkout.session.completed":
            currency_str = event_data.get("currency")

        elif "subscription" in event_type:
            # Get currency from subscription items or customer
            items = event_data.get("items", {}).get("data", [])
            if items:
                price = items[0].get("price", {})
                currency_str = price.get("currency")

            # Fallback to customer currency if available
            if not currency_str:
                customer_id = event_data.get("customer")
                if customer_id:
                    currency_str = self._get_customer_currency(customer_id)

        elif "invoice" in event_type:
            currency_str = event_data.get("currency")

        elif "price" in event_type:
            currency_str = event_data.get("currency")

        # Convert to enum, default to USD
        try:
            currency = SupportedCurrency(
                currency_str.upper() if currency_str else "USD"
            )
        except (ValueError, AttributeError):
            logger.warning(f"Unsupported currency {currency_str}, defaulting to USD")
            currency = SupportedCurrency.USD

        return currency

    def _get_customer_currency(self, customer_id: str) -> Optional[str]:
        """Get customer's preferred currency from Stripe."""
        try:
            import stripe

            customer = stripe.Customer.retrieve(customer_id)
            return customer.get("currency")
        except Exception as e:
            logger.warning(
                f"Could not retrieve customer currency for {customer_id}: {e}"
            )
            return None

    def create_regional_context(
        self,
        region: SupportedRegion,
        currency: SupportedCurrency,
        event: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Create regional context for webhook processing.

        Args:
            region: Detected region
            currency: Detected currency
            event: Original Stripe event

        Returns:
            Regional context dictionary
        """
        config = self.region_config[region]

        context = {
            "region": region.value,
            "currency": currency.value,
            "tax_behavior": config["tax_behavior"],
            "compliance_requirements": config["compliance"],
            "data_residency": config["data_residency"],
            "supabase_project": config["supabase_project"],
            "event_id": event.get("id"),
            "event_type": event.get("type"),
            "processed_at": datetime.utcnow().isoformat(),
            "webhook_version": "multi_currency_v1.0",
        }

        # Add currency-specific context
        context.update(self._get_currency_specific_context(currency))

        return context

    def _get_currency_specific_context(
        self, currency: SupportedCurrency
    ) -> Dict[str, Any]:
        """Get currency-specific processing context."""
        currency_config = {
            SupportedCurrency.USD: {
                "decimal_places": 2,
                "symbol": "$",
                "payment_methods": ["card", "ach", "apple_pay", "google_pay"],
                "tax_calculation": "stripe_tax_api",
            },
            SupportedCurrency.EUR: {
                "decimal_places": 2,
                "symbol": "€",
                "payment_methods": ["card", "sepa_debit", "apple_pay", "google_pay"],
                "tax_calculation": "vat_inclusive",
            },
            SupportedCurrency.GBP: {
                "decimal_places": 2,
                "symbol": "£",
                "payment_methods": ["card", "bacs_debit", "apple_pay", "google_pay"],
                "tax_calculation": "vat_inclusive",
            },
            SupportedCurrency.CAD: {
                "decimal_places": 2,
                "symbol": "C$",
                "payment_methods": ["card", "acss_debit", "apple_pay", "google_pay"],
                "tax_calculation": "gst_hst",
            },
        }

        return currency_config.get(currency, currency_config[SupportedCurrency.USD])

    async def validate_regional_compliance(
        self, event: Dict[str, Any], context: Dict[str, Any]
    ) -> bool:
        """
        Validate regional compliance requirements.

        Args:
            event: Stripe webhook event
            context: Regional context

        Returns:
            True if compliant, False otherwise
        """
        region = context["region"]
        compliance_requirements = context["compliance_requirements"]

        try:
            # GDPR compliance validation for EU/UK
            if "gdpr" in compliance_requirements:
                if not await self._validate_gdpr_compliance(event, context):
                    logger.error(
                        f"GDPR compliance validation failed for event {event.get('id')}"
                    )
                    return False

            # CCPA compliance validation for US
            if "ccpa" in compliance_requirements:
                if not await self._validate_ccpa_compliance(event, context):
                    logger.error(
                        f"CCPA compliance validation failed for event {event.get('id')}"
                    )
                    return False

            # PIPEDA compliance validation for CA
            if "pipeda" in compliance_requirements:
                if not await self._validate_pipeda_compliance(event, context):
                    logger.error(
                        f"PIPEDA compliance validation failed for event {event.get('id')}"
                    )
                    return False

            logger.info(f"Regional compliance validated for {region}")
            return True

        except Exception as e:
            logger.error(f"Error validating regional compliance: {e}")
            return False

    async def _validate_gdpr_compliance(
        self, event: Dict[str, Any], context: Dict[str, Any]
    ) -> bool:
        """Validate GDPR compliance for EU/UK events."""
        # Check for required GDPR metadata
        event_data = event.get("data", {}).get("object", {})
        metadata = event_data.get("metadata", {})

        # Ensure data processing consent is recorded
        if not metadata.get("gdpr_consent_recorded"):
            logger.warning("GDPR consent not recorded in event metadata")
            # Don't fail - this might be legacy data

        # Validate data residency
        if context["data_residency"] not in ["eu-central-1", "eu-west-2"]:
            logger.error(
                f"Invalid data residency for GDPR: {context['data_residency']}"
            )
            return False

        return True

    async def _validate_ccpa_compliance(
        self, event: Dict[str, Any], context: Dict[str, Any]
    ) -> bool:
        """Validate CCPA compliance for US events."""
        # CCPA validation logic
        return True  # Simplified for now

    async def _validate_pipeda_compliance(
        self, event: Dict[str, Any], context: Dict[str, Any]
    ) -> bool:
        """Validate PIPEDA compliance for CA events."""
        # PIPEDA validation logic
        return True  # Simplified for now

    def format_currency_amount(
        self, amount_cents: int, currency: SupportedCurrency
    ) -> str:
        """
        Format currency amount for display.

        Args:
            amount_cents: Amount in cents
            currency: Currency enum

        Returns:
            Formatted currency string
        """
        amount = amount_cents / 100
        currency_config = self._get_currency_specific_context(currency)
        symbol = currency_config["symbol"]
        decimal_places = currency_config["decimal_places"]

        return f"{symbol}{amount:.{decimal_places}f}"

    async def log_multi_currency_event(
        self, event: Dict[str, Any], context: Dict[str, Any], result: Dict[str, Any]
    ):
        """
        Log multi-currency webhook event for monitoring.

        Args:
            event: Original Stripe event
            context: Regional context
            result: Processing result
        """
        log_data = {
            "event_id": event.get("id"),
            "event_type": event.get("type"),
            "region": context["region"],
            "currency": context["currency"],
            "tax_behavior": context["tax_behavior"],
            "compliance_requirements": context["compliance_requirements"],
            "processing_status": result.get("status", "unknown"),
            "processing_time_ms": result.get("processing_time_ms", 0),
            "errors": result.get("errors", []),
            "timestamp": datetime.utcnow().isoformat(),
        }

        logger.info(f"Multi-currency webhook processed: {log_data}")

        # Store metrics for monitoring dashboard
        await self._store_webhook_metrics(log_data)

    async def _store_webhook_metrics(self, log_data: Dict[str, Any]):
        """Store webhook metrics for monitoring dashboard."""
        try:
            # This would integrate with your monitoring system
            # For now, just log the metrics
            logger.info(f"Webhook metrics: {log_data}")
        except Exception as e:
            logger.error(f"Error storing webhook metrics: {e}")

    def get_regional_database_config(self, region: SupportedRegion) -> Dict[str, str]:
        """
        Get database configuration for region.

        Args:
            region: Target region

        Returns:
            Database configuration
        """
        config = self.region_config[region]
        project = config["supabase_project"]

        # Map project names to environment variables
        env_map = {
            "new-texas-laws": {
                "url": os.getenv("SUPABASE_US_URL"),
                "key": os.getenv("SUPABASE_US_KEY"),
            },
            "ailex-belgium": {
                "url": os.getenv("SUPABASE_EU_URL"),
                "key": os.getenv("SUPABASE_EU_KEY"),
            },
        }

        return env_map.get(project, env_map["new-texas-laws"])


# Global instance
multi_currency_handler = MultiCurrencyWebhookHandler()
