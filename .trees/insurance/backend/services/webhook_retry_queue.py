"""
Enhanced webhook retry queue system with exponential backoff and dead letter queue.

This module provides a robust webhook retry mechanism that handles failures gracefully
with configurable retry policies, exponential backoff, and dead letter queue for
permanently failed webhooks.
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
import uuid

from backend.db.supabase_client import get_supabase_client
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class WebhookStatus(Enum):
    """Webhook delivery status."""

    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    DEAD_LETTER = "dead_letter"
    CANCELLED = "cancelled"


@dataclass
class WebhookRetryConfig:
    """Configuration for webhook retry behavior."""

    max_retries: int = 5
    initial_delay_seconds: int = 30
    max_delay_seconds: int = 3600  # 1 hour
    backoff_multiplier: float = 2.0
    jitter_factor: float = 0.1
    dead_letter_after_hours: int = 24
    batch_size: int = 10


@dataclass
class WebhookPayload:
    """Webhook payload data structure."""

    id: str
    url: str
    method: str = "POST"
    headers: Dict[str, str] = None
    body: Dict[str, Any] = None
    event_type: str = ""
    tenant_id: str = ""
    created_at: datetime = None

    def __post_init__(self):
        if self.headers is None:
            self.headers = {"Content-Type": "application/json"}
        if self.created_at is None:
            self.created_at = datetime.utcnow()


@dataclass
class WebhookRetryRecord:
    """Webhook retry record for tracking delivery attempts."""

    id: str
    payload: WebhookPayload
    status: WebhookStatus
    retry_count: int = 0
    next_retry_at: Optional[datetime] = None
    last_error: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()


class WebhookRetryQueue:
    """
    Enhanced webhook retry queue with exponential backoff and dead letter queue.
    """

    def __init__(self, config: WebhookRetryConfig = None):
        self.config = config or WebhookRetryConfig()
        self.supabase = get_supabase_client()
        self._running = False
        self._task: Optional[asyncio.Task] = None

        # Webhook delivery function - can be customized
        self._delivery_function: Optional[Callable] = None

        logger.info(f"WebhookRetryQueue initialized with config: {asdict(self.config)}")

    def set_delivery_function(self, func: Callable):
        """
        Set the webhook delivery function.

        Args:
            func: Async function that takes (url, method, headers, body) and returns success boolean
        """
        self._delivery_function = func
        logger.info("Webhook delivery function set")

    async def enqueue_webhook(self, payload: WebhookPayload) -> str:
        """
        Enqueue a webhook for delivery with retry support.

        Args:
            payload: Webhook payload to deliver

        Returns:
            Webhook record ID
        """
        record = WebhookRetryRecord(
            id=str(uuid.uuid4()),
            payload=payload,
            status=WebhookStatus.PENDING,
            next_retry_at=datetime.utcnow(),
        )

        try:
            # Store in database
            webhook_data = {
                "id": record.id,
                "payload": json.dumps(asdict(payload), default=str),
                "status": record.status.value,
                "retry_count": record.retry_count,
                "next_retry_at": record.next_retry_at.isoformat(),
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
            }

            result = (
                self.supabase.table("webhook_retry_queue")
                .insert(webhook_data)
                .execute()
            )

            if result.error:
                logger.error(f"Failed to enqueue webhook: {result.error}")
                raise Exception(f"Database error: {result.error}")

            logger.info(f"Webhook enqueued: {record.id} for {payload.url}")
            return record.id

        except Exception as e:
            logger.error(f"Error enqueuing webhook: {e}")
            raise

    async def process_pending_webhooks(self) -> int:
        """
        Process pending webhooks that are ready for retry.

        Returns:
            Number of webhooks processed
        """
        try:
            # Get pending webhooks ready for retry
            now = datetime.utcnow()
            result = (
                self.supabase.table("webhook_retry_queue")
                .select("*")
                .in_(
                    "status", [WebhookStatus.PENDING.value, WebhookStatus.FAILED.value]
                )
                .lte("next_retry_at", now.isoformat())
                .order("next_retry_at")
                .limit(self.config.batch_size)
                .execute()
            )

            if result.error:
                logger.error(f"Error fetching pending webhooks: {result.error}")
                return 0

            webhooks = result.data
            processed_count = 0

            for webhook_data in webhooks:
                try:
                    await self._process_single_webhook(webhook_data)
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Error processing webhook {webhook_data['id']}: {e}")

            if processed_count > 0:
                logger.info(f"Processed {processed_count} webhooks")

            return processed_count

        except Exception as e:
            logger.error(f"Error in process_pending_webhooks: {e}")
            return 0

    async def _process_single_webhook(self, webhook_data: Dict[str, Any]):
        """Process a single webhook delivery attempt."""
        webhook_id = webhook_data["id"]

        try:
            # Mark as processing
            await self._update_webhook_status(webhook_id, WebhookStatus.PROCESSING)

            # Parse payload
            payload_data = json.loads(webhook_data["payload"])
            payload = WebhookPayload(**payload_data)

            # Attempt delivery
            success = await self._deliver_webhook(payload)

            if success:
                # Mark as successful
                await self._update_webhook_status(webhook_id, WebhookStatus.SUCCESS)
                logger.info(f"Webhook {webhook_id} delivered successfully")
            else:
                # Handle failure
                await self._handle_webhook_failure(webhook_data)

        except Exception as e:
            logger.error(f"Error processing webhook {webhook_id}: {e}")
            await self._handle_webhook_failure(webhook_data, str(e))

    async def _deliver_webhook(self, payload: WebhookPayload) -> bool:
        """
        Deliver a webhook using the configured delivery function.

        Args:
            payload: Webhook payload to deliver

        Returns:
            True if delivery was successful, False otherwise
        """
        if self._delivery_function is None:
            logger.error("No webhook delivery function configured")
            return False

        try:
            return await self._delivery_function(
                payload.url, payload.method, payload.headers, payload.body
            )
        except Exception as e:
            logger.error(f"Webhook delivery failed: {e}")
            return False

    async def _handle_webhook_failure(
        self, webhook_data: Dict[str, Any], error_message: str = None
    ):
        """Handle webhook delivery failure with retry logic."""
        webhook_id = webhook_data["id"]
        retry_count = webhook_data["retry_count"]

        # Check if we should retry or move to dead letter queue
        if retry_count >= self.config.max_retries:
            await self._move_to_dead_letter(webhook_id, "Max retries exceeded")
            return

        # Calculate next retry time with exponential backoff
        delay = min(
            self.config.initial_delay_seconds
            * (self.config.backoff_multiplier**retry_count),
            self.config.max_delay_seconds,
        )

        # Add jitter to prevent thundering herd
        jitter = (
            delay
            * self.config.jitter_factor
            * (0.5 - asyncio.get_event_loop().time() % 1)
        )
        delay += jitter

        next_retry_at = datetime.utcnow() + timedelta(seconds=delay)

        # Update webhook record
        update_data = {
            "status": WebhookStatus.FAILED.value,
            "retry_count": retry_count + 1,
            "next_retry_at": next_retry_at.isoformat(),
            "last_error": error_message,
            "updated_at": datetime.utcnow().isoformat(),
        }

        result = (
            self.supabase.table("webhook_retry_queue")
            .update(update_data)
            .eq("id", webhook_id)
            .execute()
        )

        if result.error:
            logger.error(f"Failed to update webhook {webhook_id}: {result.error}")
        else:
            logger.info(
                f"Webhook {webhook_id} scheduled for retry {retry_count + 1} at {next_retry_at}"
            )

    async def _move_to_dead_letter(self, webhook_id: str, reason: str):
        """Move webhook to dead letter queue."""
        update_data = {
            "status": WebhookStatus.DEAD_LETTER.value,
            "last_error": reason,
            "updated_at": datetime.utcnow().isoformat(),
        }

        result = (
            self.supabase.table("webhook_retry_queue")
            .update(update_data)
            .eq("id", webhook_id)
            .execute()
        )

        if result.error:
            logger.error(
                f"Failed to move webhook {webhook_id} to dead letter: {result.error}"
            )
        else:
            logger.warning(f"Webhook {webhook_id} moved to dead letter queue: {reason}")

    async def _update_webhook_status(self, webhook_id: str, status: WebhookStatus):
        """Update webhook status in database."""
        update_data = {
            "status": status.value,
            "updated_at": datetime.utcnow().isoformat(),
        }

        result = (
            self.supabase.table("webhook_retry_queue")
            .update(update_data)
            .eq("id", webhook_id)
            .execute()
        )

        if result.error:
            logger.error(
                f"Failed to update webhook {webhook_id} status: {result.error}"
            )

    async def cleanup_old_webhooks(self, days_old: int = 7):
        """Clean up old successful and dead letter webhooks."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)

        try:
            result = (
                self.supabase.table("webhook_retry_queue")
                .delete()
                .in_(
                    "status",
                    [WebhookStatus.SUCCESS.value, WebhookStatus.DEAD_LETTER.value],
                )
                .lt("created_at", cutoff_date.isoformat())
                .execute()
            )

            if result.error:
                logger.error(f"Error cleaning up old webhooks: {result.error}")
            else:
                logger.info(f"Cleaned up old webhooks older than {days_old} days")

        except Exception as e:
            logger.error(f"Error in cleanup_old_webhooks: {e}")

    async def start_processing(self, interval_seconds: int = 30):
        """Start the webhook retry processing loop."""
        if self._running:
            logger.warning("Webhook retry queue is already running")
            return

        self._running = True
        logger.info(
            f"Starting webhook retry queue processing with {interval_seconds}s interval"
        )

        async def processing_loop():
            while self._running:
                try:
                    await self.process_pending_webhooks()
                    await asyncio.sleep(interval_seconds)
                except Exception as e:
                    logger.error(f"Error in webhook processing loop: {e}")
                    await asyncio.sleep(interval_seconds)

        self._task = asyncio.create_task(processing_loop())

    async def stop_processing(self):
        """Stop the webhook retry processing loop."""
        if not self._running:
            return

        self._running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass

        logger.info("Webhook retry queue processing stopped")

    def get_stats(self) -> Dict[str, Any]:
        """Get webhook queue statistics."""
        try:
            # Get counts by status
            stats = {}
            for status in WebhookStatus:
                result = (
                    self.supabase.table("webhook_retry_queue")
                    .select("id", count="exact")
                    .eq("status", status.value)
                    .execute()
                )
                stats[status.value] = result.count if result.count is not None else 0

            return stats

        except Exception as e:
            logger.error(f"Error getting webhook stats: {e}")
            return {}


# Global webhook retry queue instance
_webhook_retry_queue: Optional[WebhookRetryQueue] = None


def get_webhook_retry_queue(config: WebhookRetryConfig = None) -> WebhookRetryQueue:
    """Get the global webhook retry queue instance."""
    global _webhook_retry_queue

    if _webhook_retry_queue is None:
        _webhook_retry_queue = WebhookRetryQueue(config)

    return _webhook_retry_queue
