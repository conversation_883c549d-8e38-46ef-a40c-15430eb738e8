"""
Data Retention Service

Core service for managing automated data retention policies, legal holds,
and compliance-driven data cleanup operations.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID, uuid4

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.db.supabase_client import get_supabase_client
from backend.models.data_retention import (
    DataRegion,
    RetentionBasis,
    DataSensitivity,
    LegalHoldType,
    CleanupStatus,
    RetentionEventType,
    RetentionPolicyCreate,
    RetentionPolicyResponse,
    LegalHoldCreate,
    LegalHoldResponse,
    CleanupCriteria,
    CleanupResult,
    RetentionReport,
    DATA_TYPE_REGISTRY,
)
from backend.services.regional_retention_policies import regional_retention_policies
from backend.services.data_residency import DataRegion as ResidencyRegion
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class DataRetentionService:
    """Core service for data retention management."""

    def __init__(self):
        self.regional_policies = regional_retention_policies

    async def create_retention_policy(
        self, policy: RetentionPolicyCreate, user_id: Optional[UUID] = None
    ) -> str:
        """
        Create a new data retention policy.

        Args:
            policy: Retention policy configuration
            user_id: User creating the policy

        Returns:
            Policy ID
        """
        try:
            supabase = get_supabase_client(DataRegion(policy.region.value))

            # Convert retention period to PostgreSQL interval
            retention_interval = f"{policy.retention_period_days} days"

            policy_data = {
                "data_type": policy.data_type,
                "region": policy.region.value,
                "retention_period": retention_interval,
                "legal_basis": policy.legal_basis.value,
                "sensitivity_level": policy.sensitivity_level.value,
                "auto_delete": policy.auto_delete,
                "description": policy.description,
                "metadata": policy.metadata,
                "created_by": str(user_id) if user_id else None,
            }

            result = supabase.table("retention_policies").insert(policy_data).execute()

            if result.data:
                policy_id = result.data[0]["id"]

                # Log policy creation event
                await self._log_retention_event(
                    event_type=RetentionEventType.POLICY_CREATED,
                    data_type=policy.data_type,
                    retention_policy_id=policy_id,
                    user_id=user_id,
                    region=policy.region.value,
                    metadata={"policy_data": policy_data},
                )

                logger.info(
                    f"Created retention policy {policy_id} for {policy.data_type} in {policy.region}"
                )
                return policy_id
            else:
                raise Exception("Failed to create retention policy")

        except Exception as e:
            logger.error(f"Error creating retention policy: {e}")
            raise

    async def get_retention_policy(
        self, data_type: str, region: DataRegion
    ) -> Optional[RetentionPolicyResponse]:
        """
        Get retention policy for a data type and region.

        Args:
            data_type: Type of data
            region: Data region

        Returns:
            Retention policy or None if not found
        """
        try:
            supabase = get_supabase_client(region)

            result = (
                supabase.table("retention_policies")
                .select("*")
                .eq("data_type", data_type)
                .eq("region", region.value)
                .execute()
            )

            if result.data:
                policy_data = result.data[0]

                # Convert interval to days
                retention_period_days = self._interval_to_days(
                    policy_data["retention_period"]
                )

                return RetentionPolicyResponse(
                    id=policy_data["id"],
                    data_type=policy_data["data_type"],
                    region=DataRegion(policy_data["region"]),
                    retention_period_days=retention_period_days,
                    legal_basis=RetentionBasis(policy_data["legal_basis"]),
                    sensitivity_level=DataSensitivity(policy_data["sensitivity_level"]),
                    auto_delete=policy_data["auto_delete"],
                    description=policy_data["description"],
                    metadata=policy_data["metadata"] or {},
                    created_at=datetime.fromisoformat(policy_data["created_at"]),
                    updated_at=datetime.fromisoformat(policy_data["updated_at"]),
                )

            return None

        except Exception as e:
            logger.error(
                f"Error getting retention policy for {data_type} in {region}: {e}"
            )
            return None

    async def create_legal_hold(self, hold: LegalHoldCreate, user_id: UUID) -> str:
        """
        Create a new legal hold.

        Args:
            hold: Legal hold configuration
            user_id: User creating the hold

        Returns:
            Legal hold ID
        """
        try:
            # Create hold in all regions to ensure global coverage
            hold_ids = []

            for region in [DataRegion.US, DataRegion.EU]:
                supabase = get_supabase_client(region)

                hold_data = {
                    "hold_name": hold.hold_name,
                    "hold_type": hold.hold_type.value,
                    "description": hold.description,
                    "data_criteria": hold.data_criteria,
                    "case_number": hold.case_number,
                    "matter_id": str(hold.matter_id) if hold.matter_id else None,
                    "external_reference": hold.external_reference,
                    "metadata": hold.metadata,
                    "created_by": str(user_id),
                }

                result = supabase.table("legal_holds").insert(hold_data).execute()

                if result.data:
                    hold_id = result.data[0]["id"]
                    hold_ids.append(hold_id)

                    # Log hold creation event
                    await self._log_retention_event(
                        event_type=RetentionEventType.LEGAL_HOLD_CREATED,
                        data_type=hold.data_criteria.get("data_type", "all"),
                        legal_hold_id=hold_id,
                        user_id=user_id,
                        region=region.value,
                        metadata={"hold_data": hold_data},
                    )

            primary_hold_id = hold_ids[0] if hold_ids else None
            logger.info(
                f"Created legal hold {primary_hold_id} across {len(hold_ids)} regions"
            )
            return primary_hold_id

        except Exception as e:
            logger.error(f"Error creating legal hold: {e}")
            raise

    async def release_legal_hold(
        self, hold_id: str, user_id: UUID, release_reason: str
    ) -> bool:
        """
        Release a legal hold.

        Args:
            hold_id: Legal hold ID
            user_id: User releasing the hold
            release_reason: Reason for release

        Returns:
            True if successful
        """
        try:
            success_count = 0

            # Release hold in all regions
            for region in [DataRegion.US, DataRegion.EU]:
                supabase = get_supabase_client(region)

                result = (
                    supabase.table("legal_holds")
                    .update(
                        {
                            "released_at": datetime.utcnow().isoformat(),
                            "released_by": str(user_id),
                            "release_reason": release_reason,
                        }
                    )
                    .eq("id", hold_id)
                    .execute()
                )

                if result.data:
                    success_count += 1

                    # Log hold release event
                    await self._log_retention_event(
                        event_type=RetentionEventType.LEGAL_HOLD_RELEASED,
                        data_type="all",
                        legal_hold_id=hold_id,
                        user_id=user_id,
                        region=region.value,
                        metadata={"release_reason": release_reason},
                    )

            logger.info(f"Released legal hold {hold_id} in {success_count} regions")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error releasing legal hold {hold_id}: {e}")
            return False

    async def check_legal_holds(
        self, data_id: str, data_type: str, region: DataRegion
    ) -> List[str]:
        """
        Check if data is under any legal holds.

        Args:
            data_id: Data record ID
            data_type: Type of data
            region: Data region

        Returns:
            List of legal hold IDs affecting this data
        """
        try:
            supabase = get_supabase_client(region)

            # Get active legal holds
            result = (
                supabase.table("legal_holds")
                .select("*")
                .is_("released_at", "null")
                .execute()
            )

            affecting_holds = []

            if result.data:
                for hold in result.data:
                    criteria = hold["data_criteria"]

                    # Check if this data matches the hold criteria
                    if self._matches_hold_criteria(data_id, data_type, criteria):
                        affecting_holds.append(hold["id"])

            return affecting_holds

        except Exception as e:
            logger.error(f"Error checking legal holds for {data_id}: {e}")
            return []

    def _matches_hold_criteria(
        self, data_id: str, data_type: str, criteria: Dict[str, Any]
    ) -> bool:
        """Check if data matches legal hold criteria."""
        # Check data type match
        if "data_type" in criteria:
            if criteria["data_type"] != "all" and criteria["data_type"] != data_type:
                return False

        # Check specific data ID match
        if "data_id" in criteria:
            if criteria["data_id"] != data_id:
                return False

        # Check table name match
        if "table_name" in criteria:
            # This would need to be passed in or derived from data_type
            pass

        # Check date range match
        if "date_range" in criteria:
            # This would need additional date information
            pass

        # Check matter ID match
        if "matter_id" in criteria:
            # This would need to be checked against the actual data
            pass

        return True

    def _interval_to_days(self, interval_str: str) -> int:
        """Convert PostgreSQL interval string to days."""
        try:
            # Simple parsing for "X days" format
            if "days" in interval_str:
                return int(interval_str.split()[0])
            elif "years" in interval_str:
                return int(interval_str.split()[0]) * 365
            elif "months" in interval_str:
                return int(interval_str.split()[0]) * 30
            else:
                return 365  # Default to 1 year
        except:
            return 365

    async def _log_retention_event(
        self,
        event_type: RetentionEventType,
        data_type: str,
        data_id: Optional[str] = None,
        retention_policy_id: Optional[str] = None,
        legal_hold_id: Optional[str] = None,
        user_id: Optional[UUID] = None,
        region: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Log a retention event for audit purposes."""
        try:
            # Log to the appropriate region
            target_region = DataRegion(region) if region else DataRegion.US
            supabase = get_supabase_client(target_region)

            event_data = {
                "event_type": event_type.value,
                "data_type": data_type,
                "data_id": data_id,
                "retention_policy_id": retention_policy_id,
                "legal_hold_id": legal_hold_id,
                "user_id": str(user_id) if user_id else None,
                "region": region,
                "metadata": metadata or {},
            }

            supabase.table("retention_events").insert(event_data).execute()

        except Exception as e:
            logger.error(f"Error logging retention event: {e}")
            # Don't raise - logging failures shouldn't break the main operation

    async def schedule_cleanup(
        self,
        data_type: str,
        region: DataRegion,
        criteria: CleanupCriteria,
        user_id: UUID,
        job_name: Optional[str] = None,
    ) -> str:
        """
        Schedule a data cleanup job.

        Args:
            data_type: Type of data to clean up
            region: Data region
            criteria: Cleanup criteria
            user_id: User scheduling the cleanup
            job_name: Optional job name

        Returns:
            Cleanup job ID
        """
        try:
            supabase = get_supabase_client(region)

            job_data = {
                "job_name": job_name
                or f"Cleanup {data_type} - {datetime.utcnow().isoformat()}",
                "data_type": data_type,
                "region": region.value,
                "scheduled_at": criteria.retention_date.isoformat(),
                "dry_run": criteria.dry_run,
                "criteria": {
                    "retention_date": criteria.retention_date.isoformat(),
                    "exclude_holds": criteria.exclude_holds,
                    "batch_size": criteria.batch_size,
                    "max_records": criteria.max_records,
                    "table_filters": criteria.table_filters,
                },
                "created_by": str(user_id),
            }

            result = supabase.table("cleanup_jobs").insert(job_data).execute()

            if result.data:
                job_id = result.data[0]["id"]

                # Log cleanup scheduling event
                await self._log_retention_event(
                    event_type=RetentionEventType.CLEANUP_SCHEDULED,
                    data_type=data_type,
                    user_id=user_id,
                    region=region.value,
                    metadata={"job_id": job_id, "criteria": job_data["criteria"]},
                )

                logger.info(
                    f"Scheduled cleanup job {job_id} for {data_type} in {region}"
                )
                return job_id
            else:
                raise Exception("Failed to schedule cleanup job")

        except Exception as e:
            logger.error(f"Error scheduling cleanup: {e}")
            raise

    async def execute_cleanup(self, job_id: str, region: DataRegion) -> CleanupResult:
        """
        Execute a scheduled cleanup job.

        Args:
            job_id: Cleanup job ID
            region: Data region

        Returns:
            Cleanup results
        """
        try:
            supabase = get_supabase_client(region)

            # Get job details
            job_result = (
                supabase.table("cleanup_jobs").select("*").eq("id", job_id).execute()
            )

            if not job_result.data:
                raise Exception(f"Cleanup job {job_id} not found")

            job = job_result.data[0]
            criteria = job["criteria"]

            # Update job status to running
            supabase.table("cleanup_jobs").update(
                {
                    "status": CleanupStatus.RUNNING.value,
                    "started_at": datetime.utcnow().isoformat(),
                }
            ).eq("id", job_id).execute()

            # Execute cleanup based on data type
            result = await self._execute_data_cleanup(
                data_type=job["data_type"],
                region=region,
                criteria=criteria,
                dry_run=job["dry_run"],
            )

            # Update job with results
            supabase.table("cleanup_jobs").update(
                {
                    "status": CleanupStatus.COMPLETED.value,
                    "completed_at": datetime.utcnow().isoformat(),
                    "records_processed": result.records_processed,
                    "records_deleted": result.records_deleted,
                    "records_skipped": result.records_skipped,
                    "error_count": result.error_count,
                    "results": {
                        "duration_seconds": result.duration_seconds,
                        "errors": result.errors,
                        "dry_run_results": result.dry_run_results,
                    },
                }
            ).eq("id", job_id).execute()

            # Log cleanup execution event
            await self._log_retention_event(
                event_type=RetentionEventType.CLEANUP_EXECUTED,
                data_type=job["data_type"],
                region=region.value,
                metadata={
                    "job_id": job_id,
                    "records_processed": result.records_processed,
                    "records_deleted": result.records_deleted,
                    "records_skipped": result.records_skipped,
                },
            )

            logger.info(
                f"Completed cleanup job {job_id}: {result.records_deleted} deleted, {result.records_skipped} skipped"
            )
            return result

        except Exception as e:
            logger.error(f"Error executing cleanup job {job_id}: {e}")

            # Update job status to failed
            try:
                supabase = get_supabase_client(region)
                supabase.table("cleanup_jobs").update(
                    {
                        "status": CleanupStatus.FAILED.value,
                        "completed_at": datetime.utcnow().isoformat(),
                        "error_count": 1,
                        "results": {"error": str(e)},
                    }
                ).eq("id", job_id).execute()

                # Log cleanup failure event
                await self._log_retention_event(
                    event_type=RetentionEventType.CLEANUP_FAILED,
                    data_type="unknown",
                    region=region.value,
                    metadata={"job_id": job_id, "error": str(e)},
                )
            except:
                pass  # Don't fail on logging errors

            raise

    async def _execute_data_cleanup(
        self,
        data_type: str,
        region: DataRegion,
        criteria: Dict[str, Any],
        dry_run: bool = True,
    ) -> CleanupResult:
        """Execute cleanup for a specific data type."""
        start_time = datetime.utcnow()

        try:
            # Get table mapping for data type
            table_info = DATA_TYPE_REGISTRY.get(data_type)
            if not table_info:
                raise Exception(f"Unknown data type: {data_type}")

            tables = table_info["tables"]
            total_processed = 0
            total_deleted = 0
            total_skipped = 0
            errors = []

            # Process each table
            for table_name in tables:
                try:
                    processed, deleted, skipped = await self._cleanup_table(
                        table_name=table_name,
                        data_type=data_type,
                        region=region,
                        criteria=criteria,
                        dry_run=dry_run,
                    )

                    total_processed += processed
                    total_deleted += deleted
                    total_skipped += skipped

                except Exception as e:
                    error_msg = f"Error cleaning table {table_name}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            duration = (datetime.utcnow() - start_time).total_seconds()

            return CleanupResult(
                job_id=UUID(str(uuid4())),  # This would be the actual job ID
                status=CleanupStatus.COMPLETED,
                records_processed=total_processed,
                records_deleted=total_deleted,
                records_skipped=total_skipped,
                error_count=len(errors),
                duration_seconds=duration,
                errors=errors,
                dry_run_results={"dry_run": dry_run} if dry_run else None,
            )

        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds()
            logger.error(f"Error in data cleanup: {e}")

            return CleanupResult(
                job_id=UUID(str(uuid4())),
                status=CleanupStatus.FAILED,
                records_processed=0,
                records_deleted=0,
                records_skipped=0,
                error_count=1,
                duration_seconds=duration,
                errors=[str(e)],
            )

    async def _cleanup_table(
        self,
        table_name: str,
        data_type: str,
        region: DataRegion,
        criteria: Dict[str, Any],
        dry_run: bool = True,
    ) -> Tuple[int, int, int]:
        """Clean up expired data from a specific table."""
        try:
            supabase = get_supabase_client(region)

            # Build query to find expired records
            retention_date = criteria["retention_date"]
            batch_size = criteria.get("batch_size", 1000)
            exclude_holds = criteria.get("exclude_holds", True)

            # Get expired records
            query = (
                supabase.table(table_name)
                .select("id, retention_date, legal_hold_ids")
                .lt("retention_date", retention_date)
                .eq("retention_policy_applied", True)
            )

            if criteria.get("max_records"):
                query = query.limit(criteria["max_records"])
            else:
                query = query.limit(batch_size)

            result = query.execute()

            if not result.data:
                return 0, 0, 0

            processed = 0
            deleted = 0
            skipped = 0

            for record in result.data:
                processed += 1

                # Check for legal holds
                if exclude_holds and record.get("legal_hold_ids"):
                    skipped += 1
                    continue

                # Perform deletion if not dry run
                if not dry_run:
                    try:
                        delete_result = (
                            supabase.table(table_name)
                            .delete()
                            .eq("id", record["id"])
                            .execute()
                        )

                        if delete_result.data:
                            deleted += 1

                            # Log deletion event
                            await self._log_retention_event(
                                event_type=RetentionEventType.DATA_DELETED,
                                data_type=data_type,
                                data_id=record["id"],
                                region=region.value,
                                metadata={
                                    "table_name": table_name,
                                    "retention_date": record["retention_date"],
                                },
                            )
                        else:
                            skipped += 1
                    except Exception as e:
                        logger.error(
                            f"Error deleting record {record['id']} from {table_name}: {e}"
                        )
                        skipped += 1
                else:
                    deleted += 1  # Count as deleted for dry run

            return processed, deleted, skipped

        except Exception as e:
            logger.error(f"Error cleaning table {table_name}: {e}")
            raise

    async def generate_retention_report(self, region: DataRegion) -> RetentionReport:
        """
        Generate a comprehensive retention compliance report.

        Args:
            region: Data region to report on

        Returns:
            Retention compliance report
        """
        try:
            supabase = get_supabase_client(region)

            # Get policy statistics
            policies_result = (
                supabase.table("retention_policies")
                .select("*")
                .eq("region", region.value)
                .execute()
            )
            total_policies = len(policies_result.data) if policies_result.data else 0

            # Get active legal holds
            holds_result = (
                supabase.table("legal_holds")
                .select("id")
                .is_("released_at", "null")
                .execute()
            )
            active_holds = len(holds_result.data) if holds_result.data else 0

            # Get recent cleanup jobs
            recent_date = (datetime.utcnow() - timedelta(days=7)).isoformat()
            cleanups_result = (
                supabase.table("cleanup_jobs")
                .select("id")
                .gte("created_at", recent_date)
                .eq("region", region.value)
                .execute()
            )
            recent_cleanups = len(cleanups_result.data) if cleanups_result.data else 0

            # Calculate compliance metrics
            compliant_types = 0
            non_compliant_types = 0
            violations = []

            # Check each data type for compliance
            for data_type in DATA_TYPE_REGISTRY.keys():
                policy = await self.get_retention_policy(data_type, region)
                if policy:
                    compliant_types += 1
                else:
                    non_compliant_types += 1
                    violations.append(
                        {
                            "data_type": data_type,
                            "issue": "No retention policy defined",
                            "severity": "high",
                        }
                    )

            compliance_percentage = (
                (compliant_types / (compliant_types + non_compliant_types)) * 100
                if (compliant_types + non_compliant_types) > 0
                else 0
            )

            # Generate recommendations
            recommendations = []
            if non_compliant_types > 0:
                recommendations.append(
                    f"Define retention policies for {non_compliant_types} data types"
                )
            if active_holds > 10:
                recommendations.append(
                    "Review active legal holds for potential release"
                )
            if recent_cleanups == 0:
                recommendations.append(
                    "Schedule regular cleanup jobs for automated data retention"
                )

            return RetentionReport(
                region=region,
                report_date=datetime.utcnow(),
                total_policies=total_policies,
                compliant_data_types=compliant_types,
                non_compliant_data_types=non_compliant_types,
                active_legal_holds=active_holds,
                recent_cleanups=recent_cleanups,
                compliance_percentage=compliance_percentage,
                policy_violations=violations,
                recommendations=recommendations,
            )

        except Exception as e:
            logger.error(f"Error generating retention report for {region}: {e}")
            raise

    async def initialize_default_policies(
        self, region: DataRegion, user_id: UUID
    ) -> List[str]:
        """
        Initialize default retention policies for a region.

        Args:
            region: Data region
            user_id: User initializing policies

        Returns:
            List of created policy IDs
        """
        try:
            policy_requests = self.regional_policies.get_all_policies_for_region(region)
            created_policies = []

            for policy_request in policy_requests:
                try:
                    # Check if policy already exists
                    existing_policy = await self.get_retention_policy(
                        policy_request.data_type, region
                    )

                    if not existing_policy:
                        policy_id = await self.create_retention_policy(
                            policy_request, user_id
                        )
                        created_policies.append(policy_id)
                        logger.info(
                            f"Created default policy for {policy_request.data_type} in {region}"
                        )
                    else:
                        logger.info(
                            f"Policy for {policy_request.data_type} in {region} already exists"
                        )

                except Exception as e:
                    logger.error(
                        f"Error creating policy for {policy_request.data_type}: {e}"
                    )
                    continue

            logger.info(
                f"Initialized {len(created_policies)} default policies for {region}"
            )
            return created_policies

        except Exception as e:
            logger.error(f"Error initializing default policies for {region}: {e}")
            raise

    async def validate_compliance(self, region: DataRegion) -> Dict[str, Any]:
        """
        Validate retention compliance for a region.

        Args:
            region: Data region to validate

        Returns:
            Compliance validation results
        """
        try:
            validation_results = {
                "region": region.value,
                "validation_date": datetime.utcnow().isoformat(),
                "overall_compliant": True,
                "policy_coverage": {},
                "violations": [],
                "recommendations": [],
            }

            # Check policy coverage for each data type
            for data_type, type_info in DATA_TYPE_REGISTRY.items():
                policy = await self.get_retention_policy(data_type, region)

                if policy:
                    # Validate policy compliance with regional requirements
                    is_compliant, violations = (
                        self.regional_policies.validate_policy_compliance(
                            data_type, region, policy.retention_period_days
                        )
                    )

                    validation_results["policy_coverage"][data_type] = {
                        "has_policy": True,
                        "compliant": is_compliant,
                        "violations": violations,
                    }

                    if not is_compliant:
                        validation_results["overall_compliant"] = False
                        validation_results["violations"].extend(violations)
                else:
                    validation_results["policy_coverage"][data_type] = {
                        "has_policy": False,
                        "compliant": False,
                        "violations": [f"No retention policy defined for {data_type}"],
                    }
                    validation_results["overall_compliant"] = False
                    validation_results["violations"].append(
                        f"Missing retention policy for {data_type}"
                    )

            # Generate recommendations
            if not validation_results["overall_compliant"]:
                validation_results["recommendations"].append(
                    "Initialize default retention policies"
                )
                validation_results["recommendations"].append(
                    "Review and update non-compliant policies"
                )

            return validation_results

        except Exception as e:
            logger.error(f"Error validating compliance for {region}: {e}")
            raise


# Global service instance
data_retention_service = DataRetentionService()
