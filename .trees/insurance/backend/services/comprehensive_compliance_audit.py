"""
Comprehensive Compliance Audit Trail System

This module provides a unified audit trail system that integrates all compliance
systems including data retention, consent management, regional disclaimers,
data residency, and GDPR/CCPA compliance requirements.
"""

import asyncio
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List, Union
from enum import Enum
from uuid import uuid4, UUID
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

from backend.models.data_retention import DataRegion
from backend.services.compliance_audit import ComplianceAuditLogger, AuditEventType
from backend.db.supabase_client import get_supabase_client
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class ComplianceFramework(str, Enum):
    """Compliance frameworks for audit categorization."""

    GDPR = "gdpr"
    CCPA = "ccpa"
    PROFESSIONAL_RESPONSIBILITY = "professional_responsibility"
    DATA_RETENTION = "data_retention"
    CONSENT_MANAGEMENT = "consent_management"
    DATA_RESIDENCY = "data_residency"
    REGIONAL_DISCLAIMERS = "regional_disclaimers"
    SECURITY = "security"
    FINANCIAL = "financial"


class ComplianceEventType(str, Enum):
    """Comprehensive compliance event types."""

    # Data Retention Events
    RETENTION_POLICY_APPLIED = "retention_policy_applied"
    RETENTION_POLICY_CREATED = "retention_policy_created"
    RETENTION_POLICY_UPDATED = "retention_policy_updated"
    DATA_CLEANUP_EXECUTED = "data_cleanup_executed"
    LEGAL_HOLD_CREATED = "legal_hold_created"
    LEGAL_HOLD_RELEASED = "legal_hold_released"

    # Consent Management Events
    CONSENT_GRANTED = "consent_granted"
    CONSENT_WITHDRAWN = "consent_withdrawn"
    CONSENT_UPDATED = "consent_updated"
    DATA_EXPORT_REQUESTED = "data_export_requested"
    DATA_DELETION_REQUESTED = "data_deletion_requested"

    # Data Residency Events
    REGION_ROUTING = "region_routing"
    DATA_TRANSFER = "data_transfer"
    RESIDENCY_PREFERENCE_CHANGED = "residency_preference_changed"
    CROSS_BORDER_TRANSFER = "cross_border_transfer"

    # Regional Disclaimers Events
    DISCLAIMER_DISPLAYED = "disclaimer_displayed"
    DISCLAIMER_ACKNOWLEDGED = "disclaimer_acknowledged"
    DISCLAIMER_UPDATED = "disclaimer_updated"
    PROFESSIONAL_NOTICE_SHOWN = "professional_notice_shown"

    # Security Events
    AUTHENTICATION_EVENT = "authentication_event"
    AUTHORIZATION_EVENT = "authorization_event"
    SECURITY_VIOLATION = "security_violation"
    ACCESS_DENIED = "access_denied"

    # Professional Responsibility Events
    ATTORNEY_CLIENT_PRIVILEGE_ACCESS = "attorney_client_privilege_access"
    PROFESSIONAL_DISCLAIMER_SHOWN = "professional_disclaimer_shown"
    CONFLICT_CHECK_PERFORMED = "conflict_check_performed"
    TRUST_ACCOUNT_ACCESS = "trust_account_access"

    # General Compliance Events
    COMPLIANCE_VIOLATION = "compliance_violation"
    AUDIT_REPORT_GENERATED = "audit_report_generated"
    POLICY_VIOLATION = "policy_violation"
    REGULATORY_REPORT_GENERATED = "regulatory_report_generated"


class ComplianceSeverity(str, Enum):
    """Severity levels for compliance events."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class ComplianceAuditEvent:
    """Structured compliance audit event."""

    event_id: str
    event_type: ComplianceEventType
    framework: ComplianceFramework
    severity: ComplianceSeverity
    timestamp: datetime
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None
    region: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: Optional[str] = None
    outcome: Optional[str] = None
    ip_address_hash: Optional[str] = None
    user_agent_hash: Optional[str] = None
    metadata: Dict[str, Any] = None
    compliance_status: str = "COMPLIANT"
    requires_review: bool = False
    legal_basis: Optional[str] = None
    retention_period: Optional[int] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if isinstance(self.timestamp, str):
            self.timestamp = datetime.fromisoformat(
                self.timestamp.replace("Z", "+00:00")
            )


class ComprehensiveComplianceAuditService:
    """Comprehensive compliance audit trail service."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.legacy_audit_logger = ComplianceAuditLogger()
        self.event_buffer = []

        # Load configuration
        try:
            from backend.services.compliance_audit_config import (
                get_compliance_audit_config,
            )

            self.config = get_compliance_audit_config()
            self.buffer_size = self.config.buffer_size
            self.flush_interval = self.config.flush_interval
            self.batch_size = self.config.batch_size
            self.enabled = self.config.enabled
        except ImportError:
            # Fallback to defaults if config not available
            self.buffer_size = 100
            self.flush_interval = 60
            self.batch_size = 50
            self.enabled = True
            self.logger.warning("Using default compliance audit configuration")

        self._flush_task = None

    async def start_background_tasks(self):
        """Start background tasks for audit processing."""
        if not self.enabled:
            self.logger.info("Compliance audit is disabled, skipping background tasks")
            return

        if self._flush_task is None:
            self._flush_task = asyncio.create_task(self._periodic_flush())
            self.logger.info("Compliance audit background tasks started")

    async def stop_background_tasks(self):
        """Stop background tasks."""
        if self._flush_task:
            self._flush_task.cancel()
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass
            self._flush_task = None

        # Flush any remaining events
        await self._flush_events()

    async def log_compliance_event(
        self,
        event_type: ComplianceEventType,
        framework: ComplianceFramework,
        severity: ComplianceSeverity = ComplianceSeverity.INFO,
        user_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        region: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: Optional[str] = None,
        outcome: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        compliance_status: str = "COMPLIANT",
        requires_review: bool = False,
        legal_basis: Optional[str] = None,
        retention_period: Optional[int] = None,
        **kwargs,
    ) -> str:
        """
        Log a comprehensive compliance audit event.

        Args:
            event_type: Type of compliance event
            framework: Compliance framework (GDPR, CCPA, etc.)
            severity: Event severity level
            user_id: User ID associated with the event
            tenant_id: Tenant ID for multi-tenant filtering
            region: Data region involved
            resource_type: Type of resource accessed/modified
            resource_id: ID of resource accessed/modified
            action: Action performed
            outcome: Outcome of the action
            metadata: Additional event metadata
            compliance_status: Compliance status (COMPLIANT, VIOLATION, REVIEW_REQUIRED)
            requires_review: Whether event requires manual review
            legal_basis: Legal basis for data processing
            retention_period: Data retention period in days
            **kwargs: Additional event data

        Returns:
            Event ID for tracking
        """
        # Check if audit is enabled
        if not self.enabled:
            return str(uuid4())  # Return dummy ID when disabled

        try:
            event_id = str(uuid4())

            # Create structured audit event
            audit_event = ComplianceAuditEvent(
                event_id=event_id,
                event_type=event_type,
                framework=framework,
                severity=severity,
                timestamp=datetime.now(timezone.utc),
                user_id=user_id,
                tenant_id=tenant_id,
                region=region,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action,
                outcome=outcome,
                metadata={**(metadata or {}), **kwargs},
                compliance_status=compliance_status,
                requires_review=requires_review,
                legal_basis=legal_basis,
                retention_period=retention_period,
            )

            # Add to buffer for batch processing
            self.event_buffer.append(audit_event)

            # Flush if buffer is full
            if len(self.event_buffer) >= self.buffer_size:
                await self._flush_events()

            # Log critical events immediately
            if severity in [ComplianceSeverity.CRITICAL, ComplianceSeverity.HIGH]:
                await self._log_immediate_event(audit_event)

            self.logger.info(
                f"Compliance audit event logged: {event_type.value}",
                extra={
                    "event_id": event_id,
                    "framework": framework.value,
                    "severity": severity.value,
                    "user_id": user_id,
                    "compliance_status": compliance_status,
                },
            )

            return event_id

        except Exception as e:
            self.logger.error(f"Error logging compliance event: {e}")
            raise

    async def _flush_events(self):
        """Flush buffered events to database."""
        if not self.event_buffer:
            return

        events_to_flush = self.event_buffer.copy()
        self.event_buffer.clear()

        try:
            # Get appropriate Supabase client (default to US for audit logs)
            supabase = get_supabase_client(DataRegion.US)

            # Prepare batch insert data
            batch_data = []
            for event in events_to_flush:
                event_data = {
                    "event_id": event.event_id,
                    "event_type": event.event_type.value,
                    "framework": event.framework.value,
                    "severity": event.severity.value,
                    "event_timestamp": event.timestamp.isoformat(),
                    "user_id": event.user_id,
                    "tenant_id": event.tenant_id,
                    "region": event.region,
                    "resource_type": event.resource_type,
                    "resource_id": event.resource_id,
                    "action": event.action,
                    "outcome": event.outcome,
                    "metadata": event.metadata,
                    "compliance_status": event.compliance_status,
                    "requires_review": event.requires_review,
                    "legal_basis": event.legal_basis,
                    "retention_period": event.retention_period,
                }
                batch_data.append(event_data)

            # Insert events in batches
            batch_size = 50
            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i : i + batch_size]
                result = supabase.table("compliance_audit_log").insert(batch).execute()

                if not result.data:
                    self.logger.warning(
                        f"Failed to insert audit batch {i//batch_size + 1}"
                    )

            self.logger.info(f"Flushed {len(events_to_flush)} compliance audit events")

        except Exception as e:
            self.logger.error(f"Error flushing audit events: {e}")
            # Re-add events to buffer for retry
            self.event_buffer.extend(events_to_flush)

    async def _log_immediate_event(self, event: ComplianceAuditEvent):
        """Log critical events immediately."""
        try:
            supabase = get_supabase_client(DataRegion.US)

            event_data = {
                "event_id": event.event_id,
                "event_type": event.event_type.value,
                "framework": event.framework.value,
                "severity": event.severity.value,
                "event_timestamp": event.timestamp.isoformat(),
                "user_id": event.user_id,
                "tenant_id": event.tenant_id,
                "region": event.region,
                "resource_type": event.resource_type,
                "resource_id": event.resource_id,
                "action": event.action,
                "outcome": event.outcome,
                "metadata": event.metadata,
                "compliance_status": event.compliance_status,
                "requires_review": event.requires_review,
                "legal_basis": event.legal_basis,
                "retention_period": event.retention_period,
            }

            result = supabase.table("compliance_audit_log").insert(event_data).execute()

            if result.data:
                self.logger.info(
                    f"Critical compliance event logged immediately: {event.event_id}"
                )

        except Exception as e:
            self.logger.error(f"Error logging immediate compliance event: {e}")

    async def _periodic_flush(self):
        """Periodically flush buffered events."""
        while True:
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush_events()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in periodic flush: {e}")


# Global service instance
comprehensive_audit_service = ComprehensiveComplianceAuditService()


# Convenience functions for different compliance frameworks
async def log_data_retention_event(
    event_type: ComplianceEventType,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log data retention compliance event."""
    return await comprehensive_audit_service.log_compliance_event(
        event_type=event_type,
        framework=ComplianceFramework.DATA_RETENTION,
        user_id=user_id,
        region=region,
        resource_type=resource_type,
        resource_id=resource_id,
        metadata=metadata,
        **kwargs,
    )


async def log_consent_management_event(
    event_type: ComplianceEventType,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    consent_type: Optional[str] = None,
    consent_given: Optional[bool] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log consent management compliance event."""
    enhanced_metadata = {
        **(metadata or {}),
        "consent_type": consent_type,
        "consent_given": consent_given,
    }
    return await comprehensive_audit_service.log_compliance_event(
        event_type=event_type,
        framework=ComplianceFramework.CONSENT_MANAGEMENT,
        user_id=user_id,
        region=region,
        metadata=enhanced_metadata,
        **kwargs,
    )


async def log_data_residency_event(
    event_type: ComplianceEventType,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    detected_region: Optional[str] = None,
    final_region: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log data residency compliance event."""
    enhanced_metadata = {
        **(metadata or {}),
        "detected_region": detected_region,
        "final_region": final_region,
    }
    return await comprehensive_audit_service.log_compliance_event(
        event_type=event_type,
        framework=ComplianceFramework.DATA_RESIDENCY,
        user_id=user_id,
        region=region,
        metadata=enhanced_metadata,
        **kwargs,
    )


async def log_regional_disclaimer_event(
    event_type: ComplianceEventType,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    disclaimer_type: Optional[str] = None,
    placement: Optional[str] = None,
    acknowledged: Optional[bool] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log regional disclaimer compliance event."""
    enhanced_metadata = {
        **(metadata or {}),
        "disclaimer_type": disclaimer_type,
        "placement": placement,
        "acknowledged": acknowledged,
    }
    return await comprehensive_audit_service.log_compliance_event(
        event_type=event_type,
        framework=ComplianceFramework.REGIONAL_DISCLAIMERS,
        user_id=user_id,
        region=region,
        metadata=enhanced_metadata,
        **kwargs,
    )


async def log_professional_responsibility_event(
    event_type: ComplianceEventType,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    professional_rule: Optional[str] = None,
    client_id: Optional[str] = None,
    matter_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log professional responsibility compliance event."""
    enhanced_metadata = {
        **(metadata or {}),
        "professional_rule": professional_rule,
        "client_id": client_id,
        "matter_id": matter_id,
    }
    return await comprehensive_audit_service.log_compliance_event(
        event_type=event_type,
        framework=ComplianceFramework.PROFESSIONAL_RESPONSIBILITY,
        severity=ComplianceSeverity.HIGH,  # Professional responsibility is always high priority
        user_id=user_id,
        region=region,
        metadata=enhanced_metadata,
        **kwargs,
    )


async def log_security_event(
    event_type: ComplianceEventType,
    user_id: Optional[str] = None,
    region: Optional[str] = None,
    security_level: Optional[str] = None,
    threat_type: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> str:
    """Log security compliance event."""
    enhanced_metadata = {
        **(metadata or {}),
        "security_level": security_level,
        "threat_type": threat_type,
    }

    # Determine severity based on event type
    severity = ComplianceSeverity.MEDIUM
    if event_type in [
        ComplianceEventType.SECURITY_VIOLATION,
        ComplianceEventType.ACCESS_DENIED,
    ]:
        severity = ComplianceSeverity.HIGH

    return await comprehensive_audit_service.log_compliance_event(
        event_type=event_type,
        framework=ComplianceFramework.SECURITY,
        severity=severity,
        user_id=user_id,
        region=region,
        metadata=enhanced_metadata,
        **kwargs,
    )


# Context manager for audit sessions
@asynccontextmanager
async def audit_session(
    user_id: Optional[str] = None,
    tenant_id: Optional[str] = None,
    region: Optional[str] = None,
    session_type: str = "general",
):
    """Context manager for audit sessions with automatic start/end logging."""
    session_id = str(uuid4())
    start_time = datetime.now(timezone.utc)

    try:
        # Log session start
        await comprehensive_audit_service.log_compliance_event(
            event_type=ComplianceEventType.AUTHENTICATION_EVENT,
            framework=ComplianceFramework.SECURITY,
            severity=ComplianceSeverity.INFO,
            user_id=user_id,
            tenant_id=tenant_id,
            region=region,
            action="session_start",
            metadata={
                "session_id": session_id,
                "session_type": session_type,
                "start_time": start_time.isoformat(),
            },
        )

        yield session_id

    except Exception as e:
        # Log session error
        await comprehensive_audit_service.log_compliance_event(
            event_type=ComplianceEventType.SECURITY_VIOLATION,
            framework=ComplianceFramework.SECURITY,
            severity=ComplianceSeverity.HIGH,
            user_id=user_id,
            tenant_id=tenant_id,
            region=region,
            action="session_error",
            outcome="FAILED",
            metadata={
                "session_id": session_id,
                "session_type": session_type,
                "error": str(e),
                "duration_seconds": (
                    datetime.now(timezone.utc) - start_time
                ).total_seconds(),
            },
        )
        raise

    finally:
        # Log session end
        end_time = datetime.now(timezone.utc)
        duration = (end_time - start_time).total_seconds()

        await comprehensive_audit_service.log_compliance_event(
            event_type=ComplianceEventType.AUTHENTICATION_EVENT,
            framework=ComplianceFramework.SECURITY,
            severity=ComplianceSeverity.INFO,
            user_id=user_id,
            tenant_id=tenant_id,
            region=region,
            action="session_end",
            outcome="SUCCESS",
            metadata={
                "session_id": session_id,
                "session_type": session_type,
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
            },
        )
