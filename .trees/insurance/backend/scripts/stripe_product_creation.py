#!/usr/bin/env python3
"""
Stripe Product & Price Creation Script for PI Lawyer AI
Creates Solo/Team/Scale products with multi-currency pricing and regional variants.

This script implements Phase 6.3 Product & Price Creation Guide.
"""

import os
import sys
import asyncio
import stripe
from decimal import Decimal
from typing import Dict, List, Optional, Any
import logging
from supabase import create_client, Client

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class StripeProductManager:
    """Manages Stripe product and price creation for multi-country subscriptions."""

    def __init__(self):
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)

        # Product definitions based on current database schema
        self.products = {
            "solo": {
                "name": "Solo Plan",
                "description": "Perfect for individual lawyers and small practices",
                "features": [
                    "Up to 3 users",
                    "10GB storage",
                    "1,000 documents",
                    "Personal Injury practice area included",
                    "Document analysis & voice transcription",
                    "GDPR compliant",
                ],
                "metadata": {
                    "plan_code": "solo",
                    "max_users": "3",
                    "max_storage_gb": "10",
                    "max_documents": "1000",
                    "compliance_level": "standard",
                    "practice_areas": "Personal Injury",
                    "ai_receptionist": "false",
                },
            },
            "team": {
                "name": "Team Plan",
                "description": "Ideal for growing law firms with multiple attorneys",
                "features": [
                    "Up to 10 users",
                    "50GB storage",
                    "5,000 documents",
                    "All 7 practice areas included",
                    "Advanced analytics & document analysis",
                    "Voice transcription",
                    "GDPR compliant",
                ],
                "metadata": {
                    "plan_code": "team",
                    "max_users": "10",
                    "max_storage_gb": "50",
                    "max_documents": "5000",
                    "compliance_level": "standard",
                    "practice_areas": "All 7 areas",
                    "ai_receptionist": "false",
                },
            },
            "scale": {
                "name": "Scale Plan",
                "description": "Enterprise solution for large law firms",
                "features": [
                    "Up to 50 users",
                    "200GB storage",
                    "20,000 documents",
                    "All 7 practice areas included",
                    "AI Receptionist included",
                    "Advanced analytics & document analysis",
                    "Voice transcription",
                    "Enterprise compliance",
                    "Configurable data residency",
                ],
                "metadata": {
                    "plan_code": "scale",
                    "max_users": "50",
                    "max_storage_gb": "200",
                    "max_documents": "20000",
                    "compliance_level": "enterprise",
                    "practice_areas": "All 7 areas",
                    "ai_receptionist": "true",
                },
            },
        }

        # Pricing based on current database (corrected pricing)
        self.pricing = {
            "solo": {
                "monthly": {"USD": 99.00, "EUR": 99.00, "GBP": 99.00, "CAD": 99.00},
                "yearly": {"USD": 990.00, "EUR": 990.00, "GBP": 990.00, "CAD": 990.00},
            },
            "team": {
                "monthly": {"USD": 199.00, "EUR": 199.00, "GBP": 199.00, "CAD": 199.00},
                "yearly": {
                    "USD": 1990.00,
                    "EUR": 1990.00,
                    "GBP": 1990.00,
                    "CAD": 1990.00,
                },
            },
            "scale": {
                "monthly": {"USD": 299.00, "EUR": 299.00, "GBP": 299.00, "CAD": 299.00},
                "yearly": {
                    "USD": 2990.00,
                    "EUR": 2990.00,
                    "GBP": 2990.00,
                    "CAD": 2990.00,
                },
            },
        }

        # Regional availability
        self.regions = {"US": ["USD"], "EU": ["EUR"], "UK": ["GBP"], "CA": ["CAD"]}

    async def create_stripe_products(self) -> Dict[str, Any]:
        """Create all Stripe products and prices."""
        results = {"products_created": [], "prices_created": [], "errors": []}

        try:
            for plan_code, product_info in self.products.items():
                logger.info(f"Creating Stripe product for {plan_code}")

                # Create Stripe product
                stripe_product = await self._create_stripe_product(
                    plan_code, product_info
                )
                if stripe_product:
                    results["products_created"].append(
                        {
                            "plan_code": plan_code,
                            "stripe_product_id": stripe_product.id,
                            "name": stripe_product.name,
                        }
                    )

                    # Create prices for all currencies and billing cycles
                    for billing_cycle in ["monthly", "yearly"]:
                        for currency, amount in self.pricing[plan_code][
                            billing_cycle
                        ].items():
                            price_result = await self._create_stripe_price(
                                stripe_product.id,
                                plan_code,
                                currency,
                                amount,
                                billing_cycle,
                            )
                            if price_result:
                                results["prices_created"].append(price_result)

                    # Update database with Stripe product ID
                    await self._update_database_product(plan_code, stripe_product.id)

        except Exception as e:
            logger.error(f"Error creating Stripe products: {e}")
            results["errors"].append(str(e))

        return results

    async def _create_stripe_product(
        self, plan_code: str, product_info: Dict[str, Any]
    ) -> Optional[stripe.Product]:
        """Create a single Stripe product."""
        try:
            # Check if product already exists
            existing_products = stripe.Product.list(limit=100)
            for product in existing_products.data:
                if product.metadata.get("plan_code") == plan_code:
                    logger.info(f"Product {plan_code} already exists: {product.id}")
                    return product

            # Create new product (features are stored in metadata)
            metadata = product_info["metadata"].copy()
            metadata["features"] = "; ".join(product_info["features"])

            stripe_product = stripe.Product.create(
                name=product_info["name"],
                description=product_info["description"],
                metadata=metadata,
                active=True,
            )

            logger.info(f"Created Stripe product: {stripe_product.id} for {plan_code}")
            return stripe_product

        except Exception as e:
            logger.error(f"Error creating Stripe product for {plan_code}: {e}")
            return None

    async def _create_stripe_price(
        self,
        product_id: str,
        plan_code: str,
        currency: str,
        amount: float,
        billing_cycle: str,
    ) -> Optional[Dict[str, Any]]:
        """Create a Stripe price for a product."""
        try:
            # Convert amount to cents
            amount_cents = int(amount * 100)

            # Determine interval
            interval = "month" if billing_cycle == "monthly" else "year"

            # Create price ID following our naming convention
            price_id = f"price_{plan_code}_{currency.lower()}_{billing_cycle}"

            # Check if price already exists
            existing_prices = stripe.Price.list(product=product_id, limit=100)
            for price in existing_prices.data:
                if (
                    price.currency.upper() == currency
                    and price.recurring.interval == interval
                    and price.unit_amount == amount_cents
                ):
                    logger.info(f"Price already exists: {price.id}")
                    return {
                        "plan_code": plan_code,
                        "currency": currency,
                        "billing_cycle": billing_cycle,
                        "stripe_price_id": price.id,
                        "amount": amount,
                    }

            # Create new price
            stripe_price = stripe.Price.create(
                product=product_id,
                unit_amount=amount_cents,
                currency=currency.lower(),
                recurring={"interval": interval},
                metadata={
                    "plan_code": plan_code,
                    "billing_cycle": billing_cycle,
                    "region": self._get_region_for_currency(currency),
                },
                active=True,
            )

            logger.info(
                f"Created Stripe price: {stripe_price.id} for {plan_code} {currency} {billing_cycle}"
            )

            return {
                "plan_code": plan_code,
                "currency": currency,
                "billing_cycle": billing_cycle,
                "stripe_price_id": stripe_price.id,
                "amount": amount,
            }

        except Exception as e:
            logger.error(
                f"Error creating Stripe price for {plan_code} {currency} {billing_cycle}: {e}"
            )
            return None

    def _get_region_for_currency(self, currency: str) -> str:
        """Get region code for currency."""
        currency_to_region = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_to_region.get(currency, "US")

    async def _update_database_product(self, plan_code: str, stripe_product_id: str):
        """Update database with Stripe product ID."""
        try:
            # Update subscription_plans table in tenants schema
            result = (
                self.supabase.table("subscription_plans")
                .update(
                    {"stripe_product_id": stripe_product_id, "last_synced_at": "now()"}
                )
                .eq("code", plan_code)
                .execute()
            )

            if result.data:
                logger.info(
                    f"Updated database for {plan_code} with product ID {stripe_product_id}"
                )
            else:
                logger.warning(f"No database record found for plan code: {plan_code}")

        except Exception as e:
            logger.error(f"Error updating database for {plan_code}: {e}")


async def main():
    """Main execution function."""
    logger.info("Starting Stripe product and price creation...")

    if not stripe.api_key:
        logger.error("STRIPE_SECRET_KEY environment variable not set")
        return

    manager = StripeProductManager()
    results = await manager.create_stripe_products()

    # Print results
    logger.info("=== STRIPE PRODUCT CREATION RESULTS ===")
    logger.info(f"Products created: {len(results['products_created'])}")
    for product in results["products_created"]:
        logger.info(f"  - {product['plan_code']}: {product['stripe_product_id']}")

    logger.info(f"Prices created: {len(results['prices_created'])}")
    for price in results["prices_created"]:
        logger.info(
            f"  - {price['plan_code']} {price['currency']} {price['billing_cycle']}: {price['stripe_price_id']}"
        )

    if results["errors"]:
        logger.error(f"Errors encountered: {len(results['errors'])}")
        for error in results["errors"]:
            logger.error(f"  - {error}")

    logger.info("Stripe product creation completed!")


if __name__ == "__main__":
    asyncio.run(main())
