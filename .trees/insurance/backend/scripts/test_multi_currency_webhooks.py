#!/usr/bin/env python3
"""
Multi-Currency Webhook Testing Script for PI Lawyer AI
Tests webhook handling across different currencies and regions.

This script validates the multi-currency webhook implementation.
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.multi_currency_webhook_handler import (
    multi_currency_handler,
    SupportedRegion,
    SupportedCurrency,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MultiCurrencyWebhookTester:
    """Test multi-currency webhook handling functionality."""

    def __init__(self):
        self.handler = multi_currency_handler
        self.test_events = self._create_test_events()

    def _create_test_events(self) -> Dict[str, Dict[str, Any]]:
        """Create test webhook events for different currencies and regions."""

        base_timestamp = int(datetime.utcnow().timestamp())

        return {
            "checkout_usd": {
                "id": "evt_test_checkout_usd_001",
                "type": "checkout.session.completed",
                "created": base_timestamp,
                "data": {
                    "object": {
                        "id": "cs_test_usd_001",
                        "currency": "usd",
                        "customer": "cus_test_us_001",
                        "subscription": "sub_test_usd_001",
                        "customer_details": {"email": "<EMAIL>"},
                        "metadata": {"region": "US", "plan_code": "team"},
                    }
                },
            },
            "checkout_eur": {
                "id": "evt_test_checkout_eur_001",
                "type": "checkout.session.completed",
                "created": base_timestamp,
                "data": {
                    "object": {
                        "id": "cs_test_eur_001",
                        "currency": "eur",
                        "customer": "cus_test_eu_001",
                        "subscription": "sub_test_eur_001",
                        "customer_details": {"email": "<EMAIL>"},
                        "metadata": {"region": "EU", "plan_code": "scale"},
                    }
                },
            },
            "subscription_gbp": {
                "id": "evt_test_subscription_gbp_001",
                "type": "customer.subscription.created",
                "created": base_timestamp,
                "data": {
                    "object": {
                        "id": "sub_test_gbp_001",
                        "customer": "cus_test_uk_001",
                        "status": "active",
                        "items": {
                            "data": [
                                {
                                    "price": {
                                        "id": "price_test_gbp_001",
                                        "currency": "gbp",
                                        "unit_amount": 19900,
                                        "recurring": {"interval": "month"},
                                    }
                                }
                            ]
                        },
                        "metadata": {"region": "UK", "plan_code": "team"},
                    }
                },
            },
            "invoice_cad": {
                "id": "evt_test_invoice_cad_001",
                "type": "invoice.payment_succeeded",
                "created": base_timestamp,
                "data": {
                    "object": {
                        "id": "in_test_cad_001",
                        "currency": "cad",
                        "customer": "cus_test_ca_001",
                        "subscription": "sub_test_cad_001",
                        "amount_paid": 29900,
                        "status": "paid",
                        "metadata": {"region": "CA", "plan_code": "scale"},
                    }
                },
            },
            "unsupported_currency": {
                "id": "evt_test_unsupported_001",
                "type": "checkout.session.completed",
                "created": base_timestamp,
                "data": {
                    "object": {
                        "id": "cs_test_jpy_001",
                        "currency": "jpy",  # Unsupported currency
                        "customer": "cus_test_jp_001",
                        "subscription": "sub_test_jpy_001",
                        "customer_details": {"email": "<EMAIL>"},
                    }
                },
            },
        }

    async def test_currency_detection(self) -> Dict[str, Any]:
        """Test currency and region detection from webhook events."""
        results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

        expected_mappings = {
            "checkout_usd": (SupportedRegion.US, SupportedCurrency.USD),
            "checkout_eur": (SupportedRegion.EU, SupportedCurrency.EUR),
            "subscription_gbp": (SupportedRegion.UK, SupportedCurrency.GBP),
            "invoice_cad": (SupportedRegion.CA, SupportedCurrency.CAD),
            "unsupported_currency": (
                SupportedRegion.US,
                SupportedCurrency.USD,
            ),  # Should default to US/USD
        }

        for test_name, event in self.test_events.items():
            try:
                region, currency = self.handler.detect_region_from_event(event)
                expected_region, expected_currency = expected_mappings[test_name]

                if region == expected_region and currency == expected_currency:
                    results["tests_passed"] += 1
                    status = "PASS"
                    logger.info(
                        f"✅ {test_name}: Detected {region.value}/{currency.value} as expected"
                    )
                else:
                    results["tests_failed"] += 1
                    status = "FAIL"
                    logger.error(
                        f"❌ {test_name}: Expected {expected_region.value}/{expected_currency.value}, got {region.value}/{currency.value}"
                    )

                results["test_results"].append(
                    {
                        "test_name": test_name,
                        "status": status,
                        "detected_region": region.value,
                        "detected_currency": currency.value,
                        "expected_region": expected_region.value,
                        "expected_currency": expected_currency.value,
                    }
                )

            except Exception as e:
                results["tests_failed"] += 1
                logger.error(f"❌ {test_name}: Exception during detection - {e}")
                results["test_results"].append(
                    {"test_name": test_name, "status": "ERROR", "error": str(e)}
                )

        return results

    async def test_regional_context_creation(self) -> Dict[str, Any]:
        """Test regional context creation for different regions."""
        results = {"tests_passed": 0, "tests_failed": 0, "context_tests": []}

        test_cases = [
            (SupportedRegion.US, SupportedCurrency.USD),
            (SupportedRegion.EU, SupportedCurrency.EUR),
            (SupportedRegion.UK, SupportedCurrency.GBP),
            (SupportedRegion.CA, SupportedCurrency.CAD),
        ]

        for region, currency in test_cases:
            try:
                event = self.test_events["checkout_usd"]  # Use any event as template
                context = self.handler.create_regional_context(region, currency, event)

                # Validate required context fields
                required_fields = [
                    "region",
                    "currency",
                    "tax_behavior",
                    "compliance_requirements",
                    "data_residency",
                    "supabase_project",
                    "event_id",
                    "event_type",
                ]

                missing_fields = [
                    field for field in required_fields if field not in context
                ]

                if not missing_fields:
                    results["tests_passed"] += 1
                    status = "PASS"
                    logger.info(
                        f"✅ Regional context for {region.value}/{currency.value}: All required fields present"
                    )
                else:
                    results["tests_failed"] += 1
                    status = "FAIL"
                    logger.error(
                        f"❌ Regional context for {region.value}/{currency.value}: Missing fields {missing_fields}"
                    )

                results["context_tests"].append(
                    {
                        "region": region.value,
                        "currency": currency.value,
                        "status": status,
                        "context_fields": list(context.keys()),
                        "missing_fields": missing_fields,
                        "tax_behavior": context.get("tax_behavior"),
                        "compliance_requirements": context.get(
                            "compliance_requirements"
                        ),
                    }
                )

            except Exception as e:
                results["tests_failed"] += 1
                logger.error(
                    f"❌ Regional context for {region.value}/{currency.value}: Exception - {e}"
                )
                results["context_tests"].append(
                    {
                        "region": region.value,
                        "currency": currency.value,
                        "status": "ERROR",
                        "error": str(e),
                    }
                )

        return results

    async def test_compliance_validation(self) -> Dict[str, Any]:
        """Test regional compliance validation."""
        results = {"tests_passed": 0, "tests_failed": 0, "compliance_tests": []}

        test_cases = [
            (SupportedRegion.US, SupportedCurrency.USD, ["ccpa"]),
            (SupportedRegion.EU, SupportedCurrency.EUR, ["gdpr", "dpa"]),
            (SupportedRegion.UK, SupportedCurrency.GBP, ["gdpr", "dpa"]),
            (SupportedRegion.CA, SupportedCurrency.CAD, ["pipeda", "privacy_act"]),
        ]

        for region, currency, expected_compliance in test_cases:
            try:
                event = self.test_events["checkout_usd"]
                context = self.handler.create_regional_context(region, currency, event)

                # Test compliance validation
                is_compliant = await self.handler.validate_regional_compliance(
                    event, context
                )

                if is_compliant:
                    results["tests_passed"] += 1
                    status = "PASS"
                    logger.info(f"✅ Compliance validation for {region.value}: Passed")
                else:
                    results["tests_failed"] += 1
                    status = "FAIL"
                    logger.error(f"❌ Compliance validation for {region.value}: Failed")

                results["compliance_tests"].append(
                    {
                        "region": region.value,
                        "currency": currency.value,
                        "status": status,
                        "is_compliant": is_compliant,
                        "expected_compliance": expected_compliance,
                        "actual_compliance": context.get("compliance_requirements", []),
                    }
                )

            except Exception as e:
                results["tests_failed"] += 1
                logger.error(
                    f"❌ Compliance validation for {region.value}: Exception - {e}"
                )
                results["compliance_tests"].append(
                    {
                        "region": region.value,
                        "currency": currency.value,
                        "status": "ERROR",
                        "error": str(e),
                    }
                )

        return results

    async def test_currency_formatting(self) -> Dict[str, Any]:
        """Test currency formatting for different regions."""
        results = {"tests_passed": 0, "tests_failed": 0, "formatting_tests": []}

        test_cases = [
            (SupportedCurrency.USD, 9900, "$99.00"),
            (SupportedCurrency.EUR, 19900, "€199.00"),
            (SupportedCurrency.GBP, 29900, "£299.00"),
            (SupportedCurrency.CAD, 9900, "C$99.00"),
        ]

        for currency, amount_cents, expected_format in test_cases:
            try:
                formatted = self.handler.format_currency_amount(amount_cents, currency)

                if formatted == expected_format:
                    results["tests_passed"] += 1
                    status = "PASS"
                    logger.info(
                        f"✅ Currency formatting for {currency.value}: {formatted}"
                    )
                else:
                    results["tests_failed"] += 1
                    status = "FAIL"
                    logger.error(
                        f"❌ Currency formatting for {currency.value}: Expected {expected_format}, got {formatted}"
                    )

                results["formatting_tests"].append(
                    {
                        "currency": currency.value,
                        "amount_cents": amount_cents,
                        "expected_format": expected_format,
                        "actual_format": formatted,
                        "status": status,
                    }
                )

            except Exception as e:
                results["tests_failed"] += 1
                logger.error(
                    f"❌ Currency formatting for {currency.value}: Exception - {e}"
                )
                results["formatting_tests"].append(
                    {
                        "currency": currency.value,
                        "amount_cents": amount_cents,
                        "status": "ERROR",
                        "error": str(e),
                    }
                )

        return results

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all multi-currency webhook tests."""
        logger.info("🚀 Starting multi-currency webhook tests...")

        # Run all test suites
        currency_detection_results = await self.test_currency_detection()
        context_creation_results = await self.test_regional_context_creation()
        compliance_validation_results = await self.test_compliance_validation()
        currency_formatting_results = await self.test_currency_formatting()

        # Aggregate results
        total_passed = (
            currency_detection_results["tests_passed"]
            + context_creation_results["tests_passed"]
            + compliance_validation_results["tests_passed"]
            + currency_formatting_results["tests_passed"]
        )

        total_failed = (
            currency_detection_results["tests_failed"]
            + context_creation_results["tests_failed"]
            + compliance_validation_results["tests_failed"]
            + currency_formatting_results["tests_failed"]
        )

        total_tests = total_passed + total_failed
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

        summary = {
            "total_tests": total_tests,
            "tests_passed": total_passed,
            "tests_failed": total_failed,
            "success_rate": f"{success_rate:.1f}%",
            "test_suites": {
                "currency_detection": currency_detection_results,
                "context_creation": context_creation_results,
                "compliance_validation": compliance_validation_results,
                "currency_formatting": currency_formatting_results,
            },
        }

        # Print summary
        logger.info("=" * 60)
        logger.info("🎯 MULTI-CURRENCY WEBHOOK TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {total_passed}")
        logger.info(f"Failed: {total_failed}")
        logger.info(f"Success Rate: {success_rate:.1f}%")

        if total_failed == 0:
            logger.info(
                "🎉 All tests passed! Multi-currency webhook handling is working correctly."
            )
        else:
            logger.warning(
                f"⚠️  {total_failed} tests failed. Please review the results above."
            )

        return summary


async def main():
    """Main execution function."""
    tester = MultiCurrencyWebhookTester()
    results = await tester.run_all_tests()

    # Save results to file
    with open("multi_currency_webhook_test_results.json", "w") as f:
        json.dump(results, f, indent=2)

    logger.info("📄 Test results saved to multi_currency_webhook_test_results.json")


if __name__ == "__main__":
    asyncio.run(main())
