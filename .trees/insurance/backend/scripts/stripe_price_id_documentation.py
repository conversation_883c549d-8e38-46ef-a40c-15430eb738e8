#!/usr/bin/env python3
"""
Stripe Price ID Documentation Generator for PI Lawyer AI
Creates comprehensive documentation for price ID management by country and currency.

This script implements Phase 6.3 Price ID Management Documentation.
"""

import os
import sys
import asyncio
import stripe
import json
from typing import Dict, List, Optional, Any
import logging
from supabase import create_client, Client
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class StripePriceDocumentationGenerator:
    """Generates comprehensive documentation for Stripe price ID management."""

    def __init__(self):
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)

        # Product IDs
        self.product_ids = {
            "solo": "prod_Sm5hwvCqErJzFi",
            "team": "prod_Sm5hPLh8EaQuqr",
            "scale": "prod_Sm5hh7hogBZ2LG",
            "ai_receptionist": "prod_Sm61AsIuJan645",
            "extra_storage": "prod_Sm60j3ylUFc14n",
            "extra_users": "prod_Sm61shYVSztz3W",
            "advanced_analytics": "prod_Sm6073zvpUuDRU",
            "intake_agent": "prod_Sm617Mpuk0vZ4S",
        }

    async def generate_comprehensive_documentation(self) -> Dict[str, Any]:
        """Generate comprehensive price ID documentation."""
        results = {
            "documentation_files": [],
            "price_mappings": {},
            "regional_guides": {},
            "errors": [],
        }

        try:
            # Generate price mappings
            price_mappings = await self._generate_price_mappings()
            results["price_mappings"] = price_mappings

            # Generate regional guides
            regional_guides = await self._generate_regional_guides()
            results["regional_guides"] = regional_guides

            # Create documentation files
            await self._create_markdown_documentation(price_mappings, regional_guides)
            await self._create_json_reference(price_mappings)
            await self._create_implementation_guide()

            results["documentation_files"] = [
                "STRIPE_PRICE_ID_REFERENCE.md",
                "STRIPE_REGIONAL_GUIDES.md",
                "STRIPE_IMPLEMENTATION_GUIDE.md",
                "stripe_price_mappings.json",
            ]

        except Exception as e:
            logger.error(f"Error generating documentation: {e}")
            results["errors"].append(str(e))

        return results

    async def _generate_price_mappings(self) -> Dict[str, Any]:
        """Generate complete price mappings for all products."""
        mappings = {}

        try:
            for plan_code, product_id in self.product_ids.items():
                prices = stripe.Price.list(product=product_id, limit=100)

                plan_mappings = {"product_id": product_id, "currencies": {}}

                for price in prices.data:
                    currency = price.currency.upper()
                    billing_cycle = (
                        price.recurring.interval if price.recurring else "one_time"
                    )

                    if currency not in plan_mappings["currencies"]:
                        plan_mappings["currencies"][currency] = {}

                    plan_mappings["currencies"][currency][billing_cycle] = {
                        "price_id": price.id,
                        "amount": price.unit_amount / 100,
                        "amount_cents": price.unit_amount,
                        "region": self._get_region_for_currency(currency),
                        "active": price.active,
                        "created": price.created,
                    }

                mappings[plan_code] = plan_mappings
                logger.info(f"Generated price mappings for {plan_code}")

        except Exception as e:
            logger.error(f"Error generating price mappings: {e}")

        return mappings

    async def _generate_regional_guides(self) -> Dict[str, Any]:
        """Generate regional implementation guides."""
        guides = {}

        regions = {
            "US": {
                "currency": "USD",
                "tax_behavior": "exclusive",
                "compliance": ["HIPAA", "CCPA"],
                "payment_methods": ["card", "ach", "apple_pay", "google_pay"],
                "data_residency": "us-east-1",
            },
            "EU": {
                "currency": "EUR",
                "tax_behavior": "inclusive",
                "compliance": ["GDPR", "DPA"],
                "payment_methods": ["card", "sepa_debit", "apple_pay", "google_pay"],
                "data_residency": "eu-central-1",
            },
            "UK": {
                "currency": "GBP",
                "tax_behavior": "inclusive",
                "compliance": ["GDPR", "DPA"],
                "payment_methods": ["card", "bacs_debit", "apple_pay", "google_pay"],
                "data_residency": "eu-west-2",
            },
            "CA": {
                "currency": "CAD",
                "tax_behavior": "exclusive",
                "compliance": ["PIPEDA", "Privacy Act"],
                "payment_methods": ["card", "acss_debit", "apple_pay", "google_pay"],
                "data_residency": "ca-central-1",
            },
        }

        for region, config in regions.items():
            guides[region] = config
            logger.info(f"Generated regional guide for {region}")

        return guides

    def _get_region_for_currency(self, currency: str) -> str:
        """Get region code for currency."""
        currency_to_region = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_to_region.get(currency, "US")

    async def _create_markdown_documentation(
        self, price_mappings: Dict, regional_guides: Dict
    ):
        """Create comprehensive Markdown documentation."""

        # Create main price reference
        price_reference_content = f"""# Stripe Price ID Reference for PI Lawyer AI

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This document provides a comprehensive reference for all Stripe price IDs organized by product, currency, and billing cycle for PI Lawyer AI's multi-country subscription system.

## Subscription Plans

### Solo Plan (prod_Sm5hwvCqErJzFi)
Perfect for individual lawyers and small practices
- Max Users: 3
- Storage: 10GB
- Documents: 1,000
- Practice Areas: Personal Injury (+ add-ons available)

"""

        # Add price tables for each plan
        for plan_code, plan_data in price_mappings.items():
            if plan_code in ["solo", "team", "scale"]:
                price_reference_content += (
                    f"\n#### {plan_code.title()} Plan Pricing\n\n"
                )
                price_reference_content += "| Currency | Monthly Price ID | Yearly Price ID | Monthly Amount | Yearly Amount |\n"
                price_reference_content += "|----------|------------------|-----------------|----------------|---------------|\n"

                currencies = plan_data.get("currencies", {})
                for currency in ["USD", "EUR", "GBP", "CAD"]:
                    if currency in currencies:
                        monthly = currencies[currency].get("month", {})
                        yearly = currencies[currency].get("year", {})
                        price_reference_content += f"| {currency} | `{monthly.get('price_id', 'N/A')}` | `{yearly.get('price_id', 'N/A')}` | ${monthly.get('amount', 0):.2f} | ${yearly.get('amount', 0):.2f} |\n"

        # Add add-ons section
        price_reference_content += "\n## Add-ons\n\n"

        for plan_code, plan_data in price_mappings.items():
            if plan_code not in ["solo", "team", "scale"]:
                price_reference_content += (
                    f"\n### {plan_code.replace('_', ' ').title()}\n\n"
                )
                price_reference_content += "| Currency | Monthly Price ID | Yearly Price ID | Monthly Amount | Yearly Amount |\n"
                price_reference_content += "|----------|------------------|-----------------|----------------|---------------|\n"

                currencies = plan_data.get("currencies", {})
                for currency in ["USD", "EUR", "GBP", "CAD"]:
                    if currency in currencies:
                        monthly = currencies[currency].get("month", {})
                        yearly = currencies[currency].get("year", {})
                        price_reference_content += f"| {currency} | `{monthly.get('price_id', 'N/A')}` | `{yearly.get('price_id', 'N/A')}` | ${monthly.get('amount', 0):.2f} | ${yearly.get('amount', 0):.2f} |\n"

        # Write price reference file
        with open("STRIPE_PRICE_ID_REFERENCE.md", "w") as f:
            f.write(price_reference_content)

        # Create regional guides
        regional_content = f"""# Stripe Regional Implementation Guides

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This document provides region-specific implementation guides for Stripe integration across different markets.

"""

        for region, config in regional_guides.items():
            regional_content += f"""
## {region} Region Implementation

### Configuration
- **Currency**: {config['currency']}
- **Tax Behavior**: {config['tax_behavior']}
- **Data Residency**: {config['data_residency']}

### Compliance Requirements
{chr(10).join([f"- {req}" for req in config['compliance']])}

### Supported Payment Methods
{chr(10).join([f"- {method}" for method in config['payment_methods']])}

### Implementation Notes
- Use {config['currency']} prices for this region
- Configure tax behavior as "{config['tax_behavior']}"
- Ensure data residency compliance with {config['data_residency']}

"""

        with open("STRIPE_REGIONAL_GUIDES.md", "w") as f:
            f.write(regional_content)

        logger.info("Created Markdown documentation files")

    async def _create_json_reference(self, price_mappings: Dict):
        """Create JSON reference file for programmatic access."""

        json_data = {
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "description": "PI Lawyer AI Stripe Price ID Reference",
            "products": price_mappings,
            "regions": {
                "US": {"currency": "USD", "region_code": "US"},
                "EU": {"currency": "EUR", "region_code": "EU"},
                "UK": {"currency": "GBP", "region_code": "UK"},
                "CA": {"currency": "CAD", "region_code": "CA"},
            },
        }

        with open("stripe_price_mappings.json", "w") as f:
            json.dump(json_data, f, indent=2)

        logger.info("Created JSON reference file")

    async def _create_implementation_guide(self):
        """Create implementation guide for developers."""

        implementation_content = f"""# Stripe Implementation Guide for PI Lawyer AI

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Quick Start

### 1. Price ID Lookup
```python
# Example: Get Solo plan monthly price for US customers
price_id = get_price_id("solo", "USD", "monthly")
# Returns: price_1RqXVsPJIgTfSDHOw0Uee5Hd
```

### 2. Regional Detection
```python
# Detect customer region and get appropriate currency
region = detect_customer_region(customer_country)
currency = get_currency_for_region(region)
```

### 3. Checkout Session Creation
```python
stripe.checkout.Session.create(
    payment_method_types=get_payment_methods_for_region(region),
    line_items=[{{
        'price': price_id,
        'quantity': 1,
    }}],
    mode='subscription',
    success_url='https://yourapp.com/success',
    cancel_url='https://yourapp.com/cancel',
)
```

## Price ID Patterns

All price IDs follow this pattern:
- `price_1RqXV[s-z]PJIgTfSDHO[random]` for Solo plan
- `price_1RqXW[3-A]PJIgTfSDHO[random]` for Team plan  
- `price_1RqXW[C-J]PJIgTfSDHO[random]` for Scale plan
- `price_1RqXo[m-u]PJIgTfSDHO[random]` for Extra Storage
- `price_1RqXp[H-Z]PJIgTfSDHO[random]` for AI Receptionist

## Regional Considerations

### United States (USD)
- Tax behavior: exclusive (add tax at checkout)
- Payment methods: card, ach, apple_pay, google_pay
- Compliance: HIPAA, CCPA

### European Union (EUR)
- Tax behavior: inclusive (tax included in price)
- Payment methods: card, sepa_debit, apple_pay, google_pay
- Compliance: GDPR, DPA

### United Kingdom (GBP)
- Tax behavior: inclusive (tax included in price)
- Payment methods: card, bacs_debit, apple_pay, google_pay
- Compliance: GDPR, DPA

### Canada (CAD)
- Tax behavior: exclusive (add tax at checkout)
- Payment methods: card, acss_debit, apple_pay, google_pay
- Compliance: PIPEDA, Privacy Act

## Error Handling

Always implement proper error handling for:
- Invalid price IDs
- Currency mismatches
- Regional restrictions
- Payment method availability

## Testing

Use Stripe's test mode with these test price IDs during development.
All production price IDs are documented in STRIPE_PRICE_ID_REFERENCE.md.

"""

        with open("STRIPE_IMPLEMENTATION_GUIDE.md", "w") as f:
            f.write(implementation_content)

        logger.info("Created implementation guide")


async def main():
    """Main execution function."""
    logger.info("Starting Stripe price ID documentation generation...")

    if not stripe.api_key:
        logger.error("STRIPE_SECRET_KEY environment variable not set")
        return

    generator = StripePriceDocumentationGenerator()
    results = await generator.generate_comprehensive_documentation()

    # Print results
    logger.info("=== STRIPE PRICE ID DOCUMENTATION RESULTS ===")
    logger.info(f"Documentation files created: {len(results['documentation_files'])}")
    for file in results["documentation_files"]:
        logger.info(f"  - {file}")

    logger.info(f"Price mappings documented: {len(results['price_mappings'])}")
    logger.info(f"Regional guides created: {len(results['regional_guides'])}")

    if results["errors"]:
        logger.error(f"Errors encountered: {len(results['errors'])}")
        for error in results["errors"]:
            logger.error(f"  - {error}")

    logger.info("Stripe price ID documentation generation completed!")


if __name__ == "__main__":
    asyncio.run(main())
