#!/usr/bin/env python3
"""
Regional Webhook Routing Test Script
Tests the regional routing system for webhook events.
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Mock the regional router for testing
class MockRegionalWebhookRouter:
    """Mock regional webhook router for testing."""

    def __init__(self):
        self.country_to_region_map = {
            "US": "US",
            "CA": "CA",
            "DE": "EU",
            "FR": "EU",
            "BE": "EU",
            "NL": "EU",
            "GB": "UK",
        }

        self.currency_to_region_map = {
            "USD": "US",
            "CAD": "CA",
            "EUR": "EU",
            "GBP": "UK",
        }

        self.regional_database_config = {
            "US": {
                "supabase_project": "new-texas-laws",
                "primary_db": "us-east-1",
                "data_residency_strict": False,
            },
            "EU": {
                "supabase_project": "ailex-belgium",
                "primary_db": "eu-central-1",
                "data_residency_strict": True,
            },
            "UK": {
                "supabase_project": "ailex-belgium",
                "primary_db": "eu-west-2",
                "data_residency_strict": True,
            },
            "CA": {
                "supabase_project": "new-texas-laws",
                "primary_db": "ca-central-1",
                "data_residency_strict": False,
            },
        }

    async def route_webhook_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Mock webhook event routing."""
        try:
            # Extract currency
            currency = self._extract_currency(event)
            currency_region = self.currency_to_region_map.get(currency, "US")

            # Extract customer country
            customer_country = self._extract_customer_country(event)
            location_region = self.country_to_region_map.get(
                customer_country, currency_region
            )

            # Determine final region (prefer location for EU/UK due to GDPR)
            if location_region in ["EU", "UK"] and currency_region != location_region:
                final_region = location_region
                detection_method = "location_override_for_gdpr"
            else:
                final_region = currency_region
                detection_method = "currency_based"

            # Get database configuration
            db_config = self.regional_database_config[final_region]

            # Validate compliance
            compliance_result = self._validate_compliance(
                event, final_region, db_config
            )

            if not compliance_result["compliant"]:
                return {
                    "status": "compliance_failed",
                    "violations": compliance_result["violations"],
                    "event_id": event.get("id"),
                }

            return {
                "status": "success",
                "region": final_region,
                "currency": currency,
                "regional_context": {
                    "region": final_region,
                    "currency": currency,
                    "detection_method": detection_method,
                },
                "database_config": db_config,
                "handler_class": f"{final_region}RegionalHandler",
                "routing_metadata": {
                    "original_region": currency_region,
                    "enhanced_region": final_region,
                    "customer_country": customer_country,
                    "detection_method": detection_method,
                },
            }

        except Exception as e:
            return {
                "status": "routing_failed",
                "error": str(e),
                "event_id": event.get("id"),
            }

    def _extract_currency(self, event: Dict[str, Any]) -> str:
        """Extract currency from event."""
        event_data = event.get("data", {}).get("object", {})

        if event["type"] == "checkout.session.completed":
            return event_data.get("currency", "usd").upper()
        elif "subscription" in event["type"]:
            items = event_data.get("items", {}).get("data", [])
            if items:
                return items[0].get("price", {}).get("currency", "usd").upper()
        elif "invoice" in event["type"]:
            return event_data.get("currency", "usd").upper()

        return "USD"

    def _extract_customer_country(self, event: Dict[str, Any]) -> str:
        """Extract customer country from event."""
        event_data = event.get("data", {}).get("object", {})

        # Try customer details
        customer_details = event_data.get("customer_details", {})
        if customer_details.get("address", {}).get("country"):
            return customer_details["address"]["country"]

        # Try metadata
        metadata = event_data.get("metadata", {})
        if metadata.get("customer_country"):
            return metadata["customer_country"]

        # Default based on currency
        currency = self._extract_currency(event)
        return {"USD": "US", "EUR": "DE", "GBP": "GB", "CAD": "CA"}.get(currency, "US")

    def _validate_compliance(
        self, event: Dict[str, Any], region: str, db_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate compliance requirements."""
        violations = []

        # Check GDPR compliance for EU/UK
        if region in ["EU", "UK"]:
            if not db_config["data_residency_strict"]:
                violations.append("GDPR requires strict data residency")

            if not db_config["primary_db"].startswith("eu-"):
                violations.append(
                    f"EU/UK data must be in EU region, not {db_config['primary_db']}"
                )

        return {"compliant": len(violations) == 0, "violations": violations}


def create_routing_test_events() -> Dict[str, Dict[str, Any]]:
    """Create test events for routing validation."""

    base_timestamp = int(datetime.utcnow().timestamp())

    return {
        "us_customer_usd": {
            "id": "evt_us_usd_001",
            "type": "checkout.session.completed",
            "created": base_timestamp,
            "data": {
                "object": {
                    "id": "cs_us_001",
                    "currency": "usd",
                    "customer_details": {"address": {"country": "US"}},
                    "metadata": {"customer_country": "US"},
                }
            },
        },
        "eu_customer_eur": {
            "id": "evt_eu_eur_001",
            "type": "checkout.session.completed",
            "created": base_timestamp,
            "data": {
                "object": {
                    "id": "cs_eu_001",
                    "currency": "eur",
                    "customer_details": {"address": {"country": "DE"}},
                    "metadata": {"customer_country": "DE"},
                }
            },
        },
        "uk_customer_gbp": {
            "id": "evt_uk_gbp_001",
            "type": "checkout.session.completed",
            "created": base_timestamp,
            "data": {
                "object": {
                    "id": "cs_uk_001",
                    "currency": "gbp",
                    "customer_details": {"address": {"country": "GB"}},
                    "metadata": {"customer_country": "GB"},
                }
            },
        },
        "eu_customer_usd_currency": {
            "id": "evt_eu_usd_001",
            "type": "checkout.session.completed",
            "created": base_timestamp,
            "data": {
                "object": {
                    "id": "cs_eu_usd_001",
                    "currency": "usd",  # USD currency but EU customer
                    "customer_details": {"address": {"country": "DE"}},
                    "metadata": {"customer_country": "DE"},
                }
            },
        },
        "ca_customer_cad": {
            "id": "evt_ca_cad_001",
            "type": "customer.subscription.created",
            "created": base_timestamp,
            "data": {
                "object": {
                    "id": "sub_ca_001",
                    "customer": "cus_ca_001",
                    "items": {
                        "data": [{"price": {"currency": "cad", "unit_amount": 9900}}]
                    },
                    "metadata": {"customer_country": "CA"},
                }
            },
        },
    }


async def test_regional_routing():
    """Test regional routing logic."""
    router = MockRegionalWebhookRouter()
    test_events = create_routing_test_events()

    expected_results = {
        "us_customer_usd": {"region": "US", "project": "new-texas-laws"},
        "eu_customer_eur": {"region": "EU", "project": "ailex-belgium"},
        "uk_customer_gbp": {"region": "UK", "project": "ailex-belgium"},
        "eu_customer_usd_currency": {
            "region": "EU",
            "project": "ailex-belgium",
        },  # Should route to EU due to GDPR
        "ca_customer_cad": {"region": "CA", "project": "new-texas-laws"},
    }

    results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

    logger.info("🧪 Testing regional webhook routing...")

    for test_name, event in test_events.items():
        try:
            routing_result = await router.route_webhook_event(event)
            expected = expected_results[test_name]

            if (
                routing_result["status"] == "success"
                and routing_result["region"] == expected["region"]
                and routing_result["database_config"]["supabase_project"]
                == expected["project"]
            ):
                results["tests_passed"] += 1
                status = "PASS"
                logger.info(
                    f"✅ {test_name}: Routed to {routing_result['region']} ({routing_result['database_config']['supabase_project']})"
                )
            else:
                results["tests_failed"] += 1
                status = "FAIL"
                logger.error(
                    f"❌ {test_name}: Expected {expected}, got {routing_result}"
                )

            results["test_results"].append(
                {
                    "test_name": test_name,
                    "status": status,
                    "expected_region": expected["region"],
                    "actual_region": routing_result.get("region"),
                    "expected_project": expected["project"],
                    "actual_project": routing_result.get("database_config", {}).get(
                        "supabase_project"
                    ),
                    "routing_result": routing_result,
                }
            )

        except Exception as e:
            results["tests_failed"] += 1
            logger.error(f"❌ {test_name}: Exception during routing - {e}")
            results["test_results"].append(
                {"test_name": test_name, "status": "ERROR", "error": str(e)}
            )

    return results


async def test_compliance_validation():
    """Test compliance validation logic."""
    router = MockRegionalWebhookRouter()

    # Create test event that should trigger compliance violation
    compliance_test_event = {
        "id": "evt_compliance_test_001",
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "currency": "eur",
                "customer_details": {"address": {"country": "DE"}},
            }
        },
    }

    results = {"tests_passed": 0, "tests_failed": 0, "compliance_tests": []}

    logger.info("🧪 Testing compliance validation...")

    try:
        routing_result = await router.route_webhook_event(compliance_test_event)

        if routing_result["status"] == "success":
            # Check that EU routing has strict data residency
            db_config = routing_result["database_config"]
            if db_config["data_residency_strict"] and db_config[
                "primary_db"
            ].startswith("eu-"):
                results["tests_passed"] += 1
                status = "PASS"
                logger.info(
                    "✅ Compliance validation: EU data properly configured for GDPR"
                )
            else:
                results["tests_failed"] += 1
                status = "FAIL"
                logger.error(
                    "❌ Compliance validation: EU data not properly configured"
                )
        else:
            results["tests_failed"] += 1
            status = "FAIL"
            logger.error(f"❌ Compliance validation: Routing failed - {routing_result}")

        results["compliance_tests"].append(
            {
                "test_name": "eu_gdpr_compliance",
                "status": status,
                "routing_result": routing_result,
            }
        )

    except Exception as e:
        results["tests_failed"] += 1
        logger.error(f"❌ Compliance validation: Exception - {e}")
        results["compliance_tests"].append(
            {"test_name": "eu_gdpr_compliance", "status": "ERROR", "error": str(e)}
        )

    return results


async def main():
    """Main execution function."""
    logger.info("🚀 Starting regional webhook routing tests...")

    # Run tests
    routing_results = await test_regional_routing()
    compliance_results = await test_compliance_validation()

    # Aggregate results
    total_passed = routing_results["tests_passed"] + compliance_results["tests_passed"]
    total_failed = routing_results["tests_failed"] + compliance_results["tests_failed"]
    total_tests = total_passed + total_failed
    success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

    # Print summary
    logger.info("=" * 60)
    logger.info("🎯 REGIONAL WEBHOOK ROUTING TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total Tests: {total_tests}")
    logger.info(f"Passed: {total_passed}")
    logger.info(f"Failed: {total_failed}")
    logger.info(f"Success Rate: {success_rate:.1f}%")

    if total_failed == 0:
        logger.info(
            "🎉 All tests passed! Regional webhook routing is working correctly."
        )
    else:
        logger.warning(
            f"⚠️  {total_failed} tests failed. Please review the results above."
        )

    # Save results
    summary = {
        "total_tests": total_tests,
        "tests_passed": total_passed,
        "tests_failed": total_failed,
        "success_rate": f"{success_rate:.1f}%",
        "regional_routing": routing_results,
        "compliance_validation": compliance_results,
    }

    with open("regional_routing_test_results.json", "w") as f:
        json.dump(summary, f, indent=2)

    logger.info("📄 Test results saved to regional_routing_test_results.json")

    return summary


if __name__ == "__main__":
    asyncio.run(main())
