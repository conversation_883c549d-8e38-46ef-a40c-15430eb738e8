#!/usr/bin/env python3
"""
Stripe Price Mapping Script for PI Lawyer AI
Maps Stripe price IDs to subscription pricing table for multi-currency support.

This script implements Phase 6.3 Multi-Currency Pricing Setup.
"""

import os
import sys
import asyncio
from typing import Dict, List, Any
import logging
from supabase import create_client, Client

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class StripePriceMapper:
    """Maps Stripe price IDs to database for multi-currency support."""

    def __init__(self):
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)

        # Price mappings from the successful Stripe creation
        self.price_mappings = {
            "solo": {
                "monthly": {
                    "USD": "price_1RqXVsPJIgTfSDHOw0Uee5Hd",
                    "EUR": "price_1RqXVtPJIgTfSDHO3PsZjQXp",
                    "GBP": "price_1RqXVuPJIgTfSDHO9SsvIXyP",
                    "CAD": "price_1RqXVvPJIgTfSDHOs1ehB3PL",
                },
                "yearly": {
                    "USD": "price_1RqXVwPJIgTfSDHOjJ5D3TX7",
                    "EUR": "price_1RqXVxPJIgTfSDHOTI6SurcU",
                    "GBP": "price_1RqXVyPJIgTfSDHOoDiNS8Pp",
                    "CAD": "price_1RqXVzPJIgTfSDHO6uLoU8va",
                },
            },
            "team": {
                "monthly": {
                    "USD": "price_1RqXW3PJIgTfSDHOBbL4QTbB",
                    "EUR": "price_1RqXW4PJIgTfSDHO4rM6TwR8",
                    "GBP": "price_1RqXW5PJIgTfSDHOZOBQl4fX",
                    "CAD": "price_1RqXW6PJIgTfSDHOLYHeuf0O",
                },
                "yearly": {
                    "USD": "price_1RqXW7PJIgTfSDHOfcjuYxWi",
                    "EUR": "price_1RqXW8PJIgTfSDHOgyJa92YI",
                    "GBP": "price_1RqXW9PJIgTfSDHOEINaqjMN",
                    "CAD": "price_1RqXWAPJIgTfSDHONXBNtILX",
                },
            },
            "scale": {
                "monthly": {
                    "USD": "price_1RqXWCPJIgTfSDHOfmHwSFNf",
                    "EUR": "price_1RqXWDPJIgTfSDHOcmMkwF3v",
                    "GBP": "price_1RqXWEPJIgTfSDHOsdzAiKHQ",
                    "CAD": "price_1RqXWFPJIgTfSDHOd0dA2CRE",
                },
                "yearly": {
                    "USD": "price_1RqXWGPJIgTfSDHO061FM6wg",
                    "EUR": "price_1RqXWHPJIgTfSDHOUPah6oRz",
                    "GBP": "price_1RqXWIPJIgTfSDHOF5yJqAyU",
                    "CAD": "price_1RqXWJPJIgTfSDHO8fG7PE9v",
                },
            },
        }

        # Pricing amounts (same numerical value across currencies)
        self.pricing_amounts = {
            "solo": {"monthly": 99.00, "yearly": 990.00},
            "team": {"monthly": 199.00, "yearly": 1990.00},
            "scale": {"monthly": 299.00, "yearly": 2990.00},
        }

    async def update_subscription_pricing(self) -> Dict[str, Any]:
        """Update subscription_pricing table with Stripe price IDs."""
        results = {"updated_records": [], "created_records": [], "errors": []}

        try:
            # Get plan IDs from database (tenants schema)
            plans_result = (
                self.supabase.schema("tenants")
                .table("subscription_plans")
                .select("id, code")
                .execute()
            )
            plan_id_map = {plan["code"]: plan["id"] for plan in plans_result.data}

            logger.info(f"Found plans: {plan_id_map}")

            for plan_code, billing_cycles in self.price_mappings.items():
                if plan_code not in plan_id_map:
                    logger.warning(f"Plan {plan_code} not found in database")
                    continue

                plan_id = plan_id_map[plan_code]

                for billing_cycle, currencies in billing_cycles.items():
                    for currency, stripe_price_id in currencies.items():
                        # Check if record exists
                        existing = (
                            self.supabase.schema("tenants")
                            .table("subscription_pricing")
                            .select("*")
                            .eq("plan_tier", plan_code)
                            .eq("currency", currency)
                            .eq("billing_cycle", billing_cycle)
                            .execute()
                        )

                        amount = self.pricing_amounts[plan_code][billing_cycle]

                        # Map currency to region
                        currency_to_region = {
                            "USD": "US",
                            "EUR": "EU",
                            "GBP": "UK",
                            "CAD": "CA",
                        }
                        region = currency_to_region.get(currency, "US")

                        record_data = {
                            "plan_tier": plan_code,
                            "currency": currency,
                            "region": region,
                            "amount": amount,
                            "billing_cycle": billing_cycle,
                            "stripe_price_id": stripe_price_id,
                            "price_parity_base_usd": amount,  # Same numerical value strategy
                            "active": True,
                            "metadata": {
                                "created_by": "stripe_price_mapping_script",
                                "stripe_product_id": f"prod_Sm5h{'w' if plan_code == 'solo' else 'P' if plan_code == 'team' else 'h'}{'vCqErJzFi' if plan_code == 'solo' else 'Lh8EaQuqr' if plan_code == 'team' else '7hogBZ2LG'}",
                            },
                        }

                        if existing.data:
                            # Update existing record
                            result = (
                                self.supabase.schema("tenants")
                                .table("subscription_pricing")
                                .update(record_data)
                                .eq("id", existing.data[0]["id"])
                                .execute()
                            )

                            if result.data:
                                results["updated_records"].append(
                                    {
                                        "plan_code": plan_code,
                                        "currency": currency,
                                        "billing_cycle": billing_cycle,
                                        "stripe_price_id": stripe_price_id,
                                        "amount": amount,
                                    }
                                )
                                logger.info(
                                    f"Updated {plan_code} {currency} {billing_cycle}: {stripe_price_id}"
                                )
                        else:
                            # Create new record
                            result = (
                                self.supabase.schema("tenants")
                                .table("subscription_pricing")
                                .insert(record_data)
                                .execute()
                            )

                            if result.data:
                                results["created_records"].append(
                                    {
                                        "plan_code": plan_code,
                                        "currency": currency,
                                        "billing_cycle": billing_cycle,
                                        "stripe_price_id": stripe_price_id,
                                        "amount": amount,
                                    }
                                )
                                logger.info(
                                    f"Created {plan_code} {currency} {billing_cycle}: {stripe_price_id}"
                                )

        except Exception as e:
            logger.error(f"Error updating subscription pricing: {e}")
            results["errors"].append(str(e))

        return results

    async def create_regional_variants(self) -> Dict[str, Any]:
        """Create regional variants for subscription plans."""
        results = {"regional_variants": [], "errors": []}

        try:
            # Regional configurations
            regions = {
                "US": {
                    "currencies": ["USD"],
                    "tax_behavior": "exclusive",
                    "compliance_level": "standard",
                    "data_residency": "us-east-1",
                },
                "EU": {
                    "currencies": ["EUR"],
                    "tax_behavior": "inclusive",
                    "compliance_level": "gdpr",
                    "data_residency": "eu-central-1",
                },
                "UK": {
                    "currencies": ["GBP"],
                    "tax_behavior": "inclusive",
                    "compliance_level": "gdpr",
                    "data_residency": "eu-west-2",
                },
                "CA": {
                    "currencies": ["CAD"],
                    "tax_behavior": "exclusive",
                    "compliance_level": "pipeda",
                    "data_residency": "ca-central-1",
                },
            }

            # Get base plans
            plans_result = (
                self.supabase.schema("tenants")
                .table("subscription_plans")
                .select("*")
                .eq("code", "solo")
                .eq("code", "team")
                .eq("code", "scale")
                .execute()
            )

            for region, config in regions.items():
                for currency in config["currencies"]:
                    results["regional_variants"].append(
                        {
                            "region": region,
                            "currency": currency,
                            "tax_behavior": config["tax_behavior"],
                            "compliance_level": config["compliance_level"],
                            "data_residency": config["data_residency"],
                        }
                    )

                    logger.info(f"Regional variant configured: {region} ({currency})")

        except Exception as e:
            logger.error(f"Error creating regional variants: {e}")
            results["errors"].append(str(e))

        return results

    async def verify_price_mapping(self) -> Dict[str, Any]:
        """Verify that all price mappings are correct."""
        results = {"verified_prices": [], "missing_prices": [], "errors": []}

        try:
            # Check all pricing records
            pricing_result = (
                self.supabase.schema("tenants")
                .table("subscription_pricing")
                .select("*, subscription_plans(code)")
                .execute()
            )

            for record in pricing_result.data:
                plan_code = record["subscription_plans"]["code"]
                currency = record["currency"]
                billing_cycle = record["billing_cycle"]
                stripe_price_id = record["stripe_price_id"]

                # Verify against our mappings
                expected_price_id = (
                    self.price_mappings.get(plan_code, {})
                    .get(billing_cycle, {})
                    .get(currency)
                )

                if expected_price_id and stripe_price_id == expected_price_id:
                    results["verified_prices"].append(
                        {
                            "plan_code": plan_code,
                            "currency": currency,
                            "billing_cycle": billing_cycle,
                            "stripe_price_id": stripe_price_id,
                            "status": "verified",
                        }
                    )
                else:
                    results["missing_prices"].append(
                        {
                            "plan_code": plan_code,
                            "currency": currency,
                            "billing_cycle": billing_cycle,
                            "expected": expected_price_id,
                            "actual": stripe_price_id,
                            "status": "mismatch",
                        }
                    )

        except Exception as e:
            logger.error(f"Error verifying price mapping: {e}")
            results["errors"].append(str(e))

        return results


async def main():
    """Main execution function."""
    logger.info("Starting Stripe price mapping...")

    mapper = StripePriceMapper()

    # Update subscription pricing
    pricing_results = await mapper.update_subscription_pricing()
    logger.info("=== SUBSCRIPTION PRICING UPDATE RESULTS ===")
    logger.info(f"Updated records: {len(pricing_results['updated_records'])}")
    logger.info(f"Created records: {len(pricing_results['created_records'])}")

    # Create regional variants
    regional_results = await mapper.create_regional_variants()
    logger.info("=== REGIONAL VARIANTS RESULTS ===")
    logger.info(f"Regional variants: {len(regional_results['regional_variants'])}")

    # Verify price mapping
    verification_results = await mapper.verify_price_mapping()
    logger.info("=== PRICE MAPPING VERIFICATION ===")
    logger.info(f"Verified prices: {len(verification_results['verified_prices'])}")
    logger.info(
        f"Missing/mismatched prices: {len(verification_results['missing_prices'])}"
    )

    if verification_results["missing_prices"]:
        logger.warning("Missing or mismatched prices found:")
        for missing in verification_results["missing_prices"]:
            logger.warning(f"  - {missing}")

    logger.info("Stripe price mapping completed!")


if __name__ == "__main__":
    asyncio.run(main())
