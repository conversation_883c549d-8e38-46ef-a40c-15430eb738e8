#!/usr/bin/env python3
"""
Apply Regional Disclaimers Database Schema

This script applies the regional disclaimers database schema to both US and EU Supabase instances.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.supabase_client import get_supabase_client
from services.data_residency import DataRegion

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def read_schema_file():
    """Read the regional disclaimers schema SQL file."""
    schema_file = (
        Path(__file__).parent.parent
        / "migrations"
        / "add_regional_disclaimers_schema.sql"
    )

    if not schema_file.exists():
        raise FileNotFoundError(f"Schema file not found: {schema_file}")

    with open(schema_file, "r") as f:
        return f.read()


async def apply_schema_to_region(region: DataRegion, sql_content: str):
    """Apply schema to a specific region."""
    try:
        logger.info(f"Applying regional disclaimers schema to {region.value} region...")

        # Get Supabase client for the region
        supabase = get_supabase_client(region)

        # Split SQL into individual statements
        statements = [stmt.strip() for stmt in sql_content.split(";") if stmt.strip()]

        success_count = 0
        error_count = 0

        for i, statement in enumerate(statements):
            if not statement:
                continue

            try:
                logger.info(
                    f"Executing statement {i+1}/{len(statements)} for {region.value}..."
                )

                # Execute the SQL statement
                result = supabase.rpc("execute_sql", {"sql_query": statement}).execute()

                if result.data:
                    logger.info(
                        f"✅ Statement {i+1} executed successfully for {region.value}"
                    )
                    success_count += 1
                else:
                    logger.warning(
                        f"⚠️ Statement {i+1} returned no data for {region.value}"
                    )
                    success_count += 1

            except Exception as e:
                logger.error(
                    f"❌ Error executing statement {i+1} for {region.value}: {str(e)}"
                )
                error_count += 1

                # Continue with other statements
                continue

        logger.info(
            f"Schema application completed for {region.value}: {success_count} success, {error_count} errors"
        )
        return success_count, error_count

    except Exception as e:
        logger.error(f"Failed to apply schema to {region.value}: {str(e)}")
        return 0, 1


async def verify_schema_application(region: DataRegion):
    """Verify that the schema was applied correctly."""
    try:
        logger.info(f"Verifying schema application for {region.value}...")

        supabase = get_supabase_client(region)

        # Check if tables exist
        tables_to_check = [
            "legal_disclaimers",
            "disclaimer_acknowledgments",
            "disclaimer_audit_log",
        ]

        for table in tables_to_check:
            try:
                result = supabase.table(table).select("*").limit(1).execute()
                logger.info(f"✅ Table '{table}' exists in {region.value}")
            except Exception as e:
                logger.error(
                    f"❌ Table '{table}' not found in {region.value}: {str(e)}"
                )
                return False

        # Check if default disclaimers were inserted
        try:
            result = supabase.table("legal_disclaimers").select("*").execute()
            disclaimer_count = len(result.data) if result.data else 0
            logger.info(f"✅ Found {disclaimer_count} disclaimers in {region.value}")

            if disclaimer_count == 0:
                logger.warning(f"⚠️ No default disclaimers found in {region.value}")
                return False

        except Exception as e:
            logger.error(f"❌ Error checking disclaimers in {region.value}: {str(e)}")
            return False

        logger.info(f"✅ Schema verification successful for {region.value}")
        return True

    except Exception as e:
        logger.error(f"Failed to verify schema for {region.value}: {str(e)}")
        return False


async def main():
    """Main function to apply schema to both regions."""
    try:
        logger.info("🚀 Starting regional disclaimers schema application...")

        # Read the schema file
        sql_content = await read_schema_file()
        logger.info(f"📄 Schema file loaded ({len(sql_content)} characters)")

        # Apply to both regions
        regions = [DataRegion.US, DataRegion.EU]
        total_success = 0
        total_errors = 0

        for region in regions:
            success, errors = await apply_schema_to_region(region, sql_content)
            total_success += success
            total_errors += errors

        logger.info(
            f"📊 Schema application summary: {total_success} success, {total_errors} errors"
        )

        # Verify schema application
        logger.info("🔍 Verifying schema application...")
        verification_results = {}

        for region in regions:
            verification_results[region] = await verify_schema_application(region)

        # Summary
        logger.info("=" * 60)
        logger.info("📋 REGIONAL DISCLAIMERS SCHEMA APPLICATION SUMMARY")
        logger.info("=" * 60)

        for region in regions:
            status = "✅ SUCCESS" if verification_results[region] else "❌ FAILED"
            logger.info(f"{region.value} Region: {status}")

        all_successful = all(verification_results.values())

        if all_successful:
            logger.info(
                "🎉 All regions successfully configured with regional disclaimers schema!"
            )
            return 0
        else:
            logger.error("💥 Some regions failed schema application. Check logs above.")
            return 1

    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
