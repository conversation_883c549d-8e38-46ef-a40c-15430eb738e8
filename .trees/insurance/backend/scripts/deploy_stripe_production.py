#!/usr/bin/env python3
"""
Stripe Production Deployment Script for PI Lawyer AI
Automates the deployment of Stripe products and configuration to production environment.

This script handles the transition from sandbox to production with proper validation.
"""

import os
import sys
import asyncio
import stripe
import json
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class StripeProductionDeployer:
    """Handles deployment of Stripe products to production environment."""

    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.deployment_results = {
            "environment": "production",
            "dry_run": dry_run,
            "timestamp": datetime.utcnow().isoformat(),
            "products_created": [],
            "addons_created": [],
            "errors": [],
            "warnings": [],
        }

        # Validate environment
        self._validate_environment()

        # Initialize Stripe
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

        # Product definitions (same as sandbox)
        self.products = {
            "solo": {
                "name": "Solo Plan",
                "description": "Perfect for individual lawyers and small practices",
                "features": [
                    "Up to 3 users",
                    "10GB storage",
                    "1,000 documents",
                    "Personal Injury practice area included",
                    "Document analysis & voice transcription",
                    "GDPR compliant",
                ],
                "metadata": {
                    "plan_code": "solo",
                    "max_users": "3",
                    "max_storage_gb": "10",
                    "max_documents": "1000",
                    "compliance_level": "standard",
                    "practice_areas": "Personal Injury",
                    "ai_receptionist": "false",
                },
            },
            "team": {
                "name": "Team Plan",
                "description": "Designed for growing law firms and teams",
                "features": [
                    "Up to 10 users",
                    "100GB storage",
                    "10,000 documents",
                    "All practice areas included",
                    "Advanced document analysis",
                    "Team collaboration tools",
                    "Priority support",
                    "GDPR compliant",
                ],
                "metadata": {
                    "plan_code": "team",
                    "max_users": "10",
                    "max_storage_gb": "100",
                    "max_documents": "10000",
                    "compliance_level": "enhanced",
                    "practice_areas": "All",
                    "ai_receptionist": "false",
                },
            },
            "scale": {
                "name": "Scale Plan",
                "description": "Enterprise solution for large law firms",
                "features": [
                    "Unlimited users",
                    "1TB storage",
                    "Unlimited documents",
                    "All practice areas included",
                    "AI Receptionist included",
                    "Advanced analytics & reporting",
                    "Custom integrations",
                    "Dedicated support",
                    "GDPR compliant",
                ],
                "metadata": {
                    "plan_code": "scale",
                    "max_users": "unlimited",
                    "max_storage_gb": "1000",
                    "max_documents": "unlimited",
                    "compliance_level": "enterprise",
                    "practice_areas": "All",
                    "ai_receptionist": "true",
                },
            },
        }

        # Pricing (same as sandbox)
        self.pricing = {
            "solo": {
                "monthly": {"USD": 99.00, "EUR": 99.00, "GBP": 99.00, "CAD": 99.00},
                "yearly": {"USD": 990.00, "EUR": 990.00, "GBP": 990.00, "CAD": 990.00},
            },
            "team": {
                "monthly": {"USD": 199.00, "EUR": 199.00, "GBP": 199.00, "CAD": 199.00},
                "yearly": {
                    "USD": 1990.00,
                    "EUR": 1990.00,
                    "GBP": 1990.00,
                    "CAD": 1990.00,
                },
            },
            "scale": {
                "monthly": {"USD": 299.00, "EUR": 299.00, "GBP": 299.00, "CAD": 299.00},
                "yearly": {
                    "USD": 2990.00,
                    "EUR": 2990.00,
                    "GBP": 2990.00,
                    "CAD": 2990.00,
                },
            },
        }

        # Add-on definitions
        self.addons = {
            "ai_receptionist": {
                "name": "AI Receptionist",
                "description": "24/7 AI-powered client intake and call handling",
                "pricing": {"USD": 49.00, "EUR": 49.00, "GBP": 49.00, "CAD": 49.00},
            },
            "extra_storage": {
                "name": "Extra Storage",
                "description": "Additional 100GB storage per month",
                "pricing": {"USD": 19.00, "EUR": 19.00, "GBP": 19.00, "CAD": 19.00},
            },
            "extra_users": {
                "name": "Extra Users",
                "description": "Additional 5 user seats per month",
                "pricing": {"USD": 29.00, "EUR": 29.00, "GBP": 29.00, "CAD": 29.00},
            },
            "advanced_analytics": {
                "name": "Advanced Analytics",
                "description": "Comprehensive analytics and reporting dashboard",
                "pricing": {"USD": 99.00, "EUR": 99.00, "GBP": 99.00, "CAD": 99.00},
            },
            "intake_agent": {
                "name": "Intake Agent",
                "description": "AI-powered client intake and qualification system",
                "pricing": {"USD": 149.00, "EUR": 149.00, "GBP": 149.00, "CAD": 149.00},
            },
        }

    def _validate_environment(self):
        """Validate production environment configuration."""
        logger.info("Validating production environment...")

        # Check required environment variables
        required_vars = [
            "STRIPE_SECRET_KEY",
            "STRIPE_WEBHOOK_SECRET",
            "SUPABASE_URL",
            "SUPABASE_SERVICE_KEY",
        ]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            error_msg = (
                f"Missing required environment variables: {', '.join(missing_vars)}"
            )
            logger.error(error_msg)
            self.deployment_results["errors"].append(error_msg)
            raise ValueError(error_msg)

        # Validate Stripe key is production key
        stripe_key = os.getenv("STRIPE_SECRET_KEY")
        if not stripe_key.startswith("sk_live_"):
            if stripe_key.startswith("sk_test_"):
                warning_msg = "Using test Stripe key - this will create products in sandbox, not production"
                logger.warning(warning_msg)
                self.deployment_results["warnings"].append(warning_msg)
            else:
                error_msg = "Invalid Stripe secret key format"
                logger.error(error_msg)
                self.deployment_results["errors"].append(error_msg)
                raise ValueError(error_msg)

        logger.info("✅ Environment validation passed")

    async def deploy_products(self) -> Dict[str, Any]:
        """Deploy all products to production Stripe."""
        logger.info("Starting production product deployment...")

        if self.dry_run:
            logger.info("🔍 DRY RUN MODE - No actual changes will be made")

        try:
            # Deploy main subscription products
            for plan_code, product_info in self.products.items():
                logger.info(f"Deploying product: {plan_code}")

                if not self.dry_run:
                    # Create Stripe product
                    stripe_product = await self._create_stripe_product(
                        plan_code, product_info
                    )
                    if stripe_product:
                        self.deployment_results["products_created"].append(
                            {
                                "plan_code": plan_code,
                                "stripe_product_id": stripe_product.id,
                                "name": stripe_product.name,
                            }
                        )

                        # Create prices for all currencies and billing cycles
                        for billing_cycle in ["monthly", "yearly"]:
                            for currency, amount in self.pricing[plan_code][
                                billing_cycle
                            ].items():
                                await self._create_stripe_price(
                                    stripe_product.id,
                                    plan_code,
                                    currency,
                                    amount,
                                    billing_cycle,
                                )
                else:
                    logger.info(
                        f"  [DRY RUN] Would create product: {product_info['name']}"
                    )
                    self.deployment_results["products_created"].append(
                        {
                            "plan_code": plan_code,
                            "stripe_product_id": f"prod_dry_run_{plan_code}",
                            "name": product_info["name"],
                        }
                    )

            # Deploy add-on products
            for addon_code, addon_info in self.addons.items():
                logger.info(f"Deploying add-on: {addon_code}")

                if not self.dry_run:
                    stripe_addon = await self._create_stripe_addon(
                        addon_code, addon_info
                    )
                    if stripe_addon:
                        self.deployment_results["addons_created"].append(
                            {
                                "addon_code": addon_code,
                                "stripe_product_id": stripe_addon.id,
                                "name": stripe_addon.name,
                            }
                        )
                else:
                    logger.info(
                        f"  [DRY RUN] Would create add-on: {addon_info['name']}"
                    )
                    self.deployment_results["addons_created"].append(
                        {
                            "addon_code": addon_code,
                            "stripe_product_id": f"prod_dry_run_{addon_code}",
                            "name": addon_info["name"],
                        }
                    )

            logger.info("✅ Product deployment completed successfully")

        except Exception as e:
            error_msg = f"Product deployment failed: {str(e)}"
            logger.error(error_msg)
            self.deployment_results["errors"].append(error_msg)
            raise

        return self.deployment_results

    async def _create_stripe_product(
        self, plan_code: str, product_info: Dict[str, Any]
    ):
        """Create a Stripe product."""
        try:
            product = stripe.Product.create(
                name=product_info["name"],
                description=product_info["description"],
                metadata=product_info["metadata"],
                features=[{"name": feature} for feature in product_info["features"]],
            )
            logger.info(f"  ✅ Created product: {product.id}")
            return product
        except Exception as e:
            logger.error(f"  ❌ Failed to create product {plan_code}: {e}")
            return None

    async def _create_stripe_price(
        self,
        product_id: str,
        plan_code: str,
        currency: str,
        amount: float,
        billing_cycle: str,
    ):
        """Create a Stripe price."""
        try:
            price = stripe.Price.create(
                product=product_id,
                unit_amount=int(amount * 100),  # Convert to cents
                currency=currency.lower(),
                recurring={
                    "interval": "month" if billing_cycle == "monthly" else "year"
                },
                metadata={
                    "plan_code": plan_code,
                    "billing_cycle": billing_cycle,
                    "region": self._get_region_for_currency(currency),
                },
            )
            logger.info(
                f"    ✅ Created price: {price.id} ({currency} {amount}/{billing_cycle})"
            )
            return price
        except Exception as e:
            logger.error(
                f"    ❌ Failed to create price for {plan_code} {currency} {billing_cycle}: {e}"
            )
            return None

    async def _create_stripe_addon(self, addon_code: str, addon_info: Dict[str, Any]):
        """Create a Stripe add-on product."""
        try:
            product = stripe.Product.create(
                name=addon_info["name"],
                description=addon_info["description"],
                metadata={"addon_code": addon_code, "type": "addon"},
            )

            # Create prices for all currencies
            for currency, amount in addon_info["pricing"].items():
                await self._create_stripe_addon_price(
                    product.id, addon_code, currency, amount
                )

            logger.info(f"  ✅ Created add-on: {product.id}")
            return product
        except Exception as e:
            logger.error(f"  ❌ Failed to create add-on {addon_code}: {e}")
            return None

    async def _create_stripe_addon_price(
        self, product_id: str, addon_code: str, currency: str, amount: float
    ):
        """Create a Stripe add-on price."""
        try:
            price = stripe.Price.create(
                product=product_id,
                unit_amount=int(amount * 100),
                currency=currency.lower(),
                recurring={"interval": "month"},
                metadata={
                    "addon_code": addon_code,
                    "region": self._get_region_for_currency(currency),
                },
            )
            logger.info(
                f"    ✅ Created add-on price: {price.id} ({currency} {amount}/month)"
            )
            return price
        except Exception as e:
            logger.error(
                f"    ❌ Failed to create add-on price for {addon_code} {currency}: {e}"
            )
            return None

    def _get_region_for_currency(self, currency: str) -> str:
        """Get region for currency."""
        currency_to_region = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_to_region.get(currency.upper(), "US")

    def generate_deployment_report(self) -> str:
        """Generate a comprehensive deployment report."""
        report = f"""
# Stripe Production Deployment Report

**Deployment Date:** {self.deployment_results['timestamp']}
**Environment:** {self.deployment_results['environment']}
**Dry Run:** {self.deployment_results['dry_run']}

## Products Created ({len(self.deployment_results['products_created'])})

"""
        for product in self.deployment_results["products_created"]:
            report += f"- **{product['plan_code']}**: {product['stripe_product_id']} ({product['name']})\n"

        report += f"\n## Add-ons Created ({len(self.deployment_results['addons_created'])})\n\n"

        for addon in self.deployment_results["addons_created"]:
            report += f"- **{addon['addon_code']}**: {addon['stripe_product_id']} ({addon['name']})\n"

        if self.deployment_results["warnings"]:
            report += f"\n## Warnings ({len(self.deployment_results['warnings'])})\n\n"
            for warning in self.deployment_results["warnings"]:
                report += f"⚠️ {warning}\n"

        if self.deployment_results["errors"]:
            report += f"\n## Errors ({len(self.deployment_results['errors'])})\n\n"
            for error in self.deployment_results["errors"]:
                report += f"❌ {error}\n"

        report += "\n## Next Steps\n\n"
        if not self.dry_run and not self.deployment_results["errors"]:
            report += "1. Update webhook configuration in Stripe Dashboard\n"
            report += "2. Update metadata configuration script with new product IDs\n"
            report += "3. Test webhook delivery to production endpoint\n"
            report += "4. Validate regional routing and tax calculations\n"
            report += "5. Monitor webhook processing and retry mechanisms\n"
        elif self.dry_run:
            report += "1. Review this dry run report\n"
            report += "2. Run deployment with --production flag when ready\n"
            report += (
                "3. Ensure all environment variables are configured for production\n"
            )

        return report


async def main():
    """Main execution function."""
    import argparse

    parser = argparse.ArgumentParser(description="Deploy Stripe products to production")
    parser.add_argument(
        "--production",
        action="store_true",
        help="Deploy to production (default is dry run)",
    )
    parser.add_argument("--output", type=str, help="Output file for deployment report")

    args = parser.parse_args()

    # Determine if this is a dry run
    dry_run = not args.production

    if dry_run:
        logger.info("🔍 Running in DRY RUN mode - use --production flag to deploy")
    else:
        logger.info(
            "🚀 Running in PRODUCTION mode - will create actual Stripe products"
        )

        # Confirm production deployment
        confirm = input("Are you sure you want to deploy to production? (yes/no): ")
        if confirm.lower() != "yes":
            logger.info("Production deployment cancelled")
            return

    try:
        # Initialize deployer
        deployer = StripeProductionDeployer(dry_run=dry_run)

        # Deploy products
        results = await deployer.deploy_products()

        # Generate report
        report = deployer.generate_deployment_report()

        # Output report
        if args.output:
            with open(args.output, "w") as f:
                f.write(report)
            logger.info(f"📄 Deployment report saved to {args.output}")
        else:
            print(report)

        # Save results as JSON
        results_file = f"stripe_deployment_results_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)
        logger.info(f"📄 Deployment results saved to {results_file}")

        if not dry_run and not results["errors"]:
            logger.info("🎉 Production deployment completed successfully!")
        elif dry_run:
            logger.info(
                "🔍 Dry run completed - review results and run with --production when ready"
            )

    except Exception as e:
        logger.error(f"❌ Deployment failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
