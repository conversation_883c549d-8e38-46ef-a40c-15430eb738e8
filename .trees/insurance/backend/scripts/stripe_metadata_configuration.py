#!/usr/bin/env python3
"""
Stripe Metadata Configuration Script for PI Lawyer AI
Configures comprehensive metadata for country and subscription plan mapping.

This script implements Phase 6.3 Metadata Configuration for Mapping.
"""

import os
import sys
import asyncio
import stripe
from typing import Dict, List, Optional, Any
import logging
from supabase import create_client, Client

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class StripeMetadataManager:
    """Manages Stripe metadata configuration for multi-country subscription plan mapping."""

    def __init__(self):
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)

        # Product IDs from previous creation
        self.product_ids = {
            "solo": "prod_Sm5hwvCqErJzFi",
            "team": "prod_Sm5hPLh8EaQuqr",
            "scale": "prod_Sm5hh7hogBZ2LG",
        }

        # Add-on product IDs
        self.addon_product_ids = {
            "ai_receptionist": "prod_Sm61AsIuJan645",
            "extra_storage": "prod_Sm60j3ylUFc14n",
            "extra_users": "prod_Sm61shYVSztz3W",
            "advanced_analytics": "prod_Sm6073zvpUuDRU",
            "intake_agent": "prod_Sm617Mpuk0vZ4S",
        }

    async def configure_product_metadata(self) -> Dict[str, Any]:
        """Configure comprehensive metadata for all Stripe products."""
        results = {"products_updated": [], "prices_updated": [], "errors": []}

        try:
            # Update main subscription products
            for plan_code, product_id in self.product_ids.items():
                metadata_result = await self._update_product_metadata(
                    plan_code, product_id, "subscription"
                )
                if metadata_result:
                    results["products_updated"].append(metadata_result)

                # Update prices for this product
                price_results = await self._update_product_prices_metadata(
                    plan_code, product_id
                )
                results["prices_updated"].extend(price_results)

            # Update add-on products
            for addon_code, product_id in self.addon_product_ids.items():
                metadata_result = await self._update_product_metadata(
                    addon_code, product_id, "addon"
                )
                if metadata_result:
                    results["products_updated"].append(metadata_result)

                # Update prices for this add-on
                price_results = await self._update_product_prices_metadata(
                    addon_code, product_id
                )
                results["prices_updated"].extend(price_results)

        except Exception as e:
            logger.error(f"Error configuring product metadata: {e}")
            results["errors"].append(str(e))

        return results

    async def _update_product_metadata(
        self, code: str, product_id: str, product_type: str
    ) -> Optional[Dict[str, Any]]:
        """Update metadata for a single product."""
        try:
            # Get product details from database
            if product_type == "subscription":
                db_result = (
                    self.supabase.schema("tenants")
                    .table("subscription_plans")
                    .select("*")
                    .eq("code", code)
                    .execute()
                )
            else:
                db_result = (
                    self.supabase.schema("tenants")
                    .table("subscription_addons")
                    .select("*")
                    .eq("code", code)
                    .execute()
                )

            if not db_result.data:
                logger.warning(f"No database record found for {code}")
                return None

            db_record = db_result.data[0]

            # Build comprehensive metadata
            metadata = {
                "product_type": product_type,
                "plan_code": code,
                "internal_id": db_record["id"],
                "available_countries": ",".join(
                    db_record.get("available_countries", [])
                ),
                "compliance_level": db_record.get("compliance_level", "standard"),
                "created_by": "pi_lawyer_ai",
                "version": "1.0",
            }

            # Add subscription-specific metadata
            if product_type == "subscription":
                features = db_record.get("features", {})
                metadata.update(
                    {
                        "max_users": str(
                            features.get("core_limits", {}).get("maxUsers", 0)
                        ),
                        "max_storage_gb": str(
                            features.get("core_limits", {}).get("maxStorage", 0)
                        ),
                        "max_documents": str(
                            features.get("core_limits", {}).get("maxDocuments", 0)
                        ),
                        "practice_areas": ",".join(
                            features.get("practice_areas", {}).get("included", [])
                        ),
                        "has_ai_receptionist": str(
                            features.get("ai_features", {}).get(
                                "hasAIReceptionist", False
                            )
                        ),
                        "has_advanced_analytics": str(
                            features.get("ai_features", {}).get(
                                "hasAdvancedAnalytics", False
                            )
                        ),
                        "data_residency": features.get("compliance", {}).get(
                            "data_residency", "flexible"
                        ),
                    }
                )

            # Add add-on specific metadata
            elif product_type == "addon":
                features = db_record.get("features", {})
                metadata.update(
                    {
                        "category": db_record.get("category", ""),
                        "addon_features": str(features) if features else "",
                    }
                )

            # Update Stripe product
            stripe.Product.modify(product_id, metadata=metadata)

            logger.info(f"Updated metadata for {product_type} {code}: {product_id}")

            return {
                "code": code,
                "product_id": product_id,
                "product_type": product_type,
                "metadata_keys": len(metadata),
            }

        except Exception as e:
            logger.error(f"Error updating metadata for {code}: {e}")
            return None

    async def _update_product_prices_metadata(
        self, code: str, product_id: str
    ) -> List[Dict[str, Any]]:
        """Update metadata for all prices of a product."""
        results = []

        try:
            # Get all prices for this product
            prices = stripe.Price.list(product=product_id, limit=100)

            for price in prices.data:
                # Build price metadata
                price_metadata = {
                    "plan_code": code,
                    "currency": price.currency.upper(),
                    "billing_cycle": (
                        price.recurring.interval if price.recurring else "one_time"
                    ),
                    "amount_cents": str(price.unit_amount),
                    "amount_decimal": str(price.unit_amount / 100),
                    "region": self._get_region_for_currency(price.currency.upper()),
                    "created_by": "pi_lawyer_ai",
                    "price_strategy": "same_numerical_value",
                }

                # Add regional pricing information
                region = self._get_region_for_currency(price.currency.upper())
                price_metadata.update(
                    {
                        "tax_behavior": (
                            "exclusive" if region in ["US", "CA"] else "inclusive"
                        ),
                        "regional_compliance": self._get_regional_compliance(region),
                        "payment_methods": self._get_regional_payment_methods(region),
                    }
                )

                # Update Stripe price
                stripe.Price.modify(price.id, metadata=price_metadata)

                results.append(
                    {
                        "price_id": price.id,
                        "plan_code": code,
                        "currency": price.currency.upper(),
                        "billing_cycle": price_metadata["billing_cycle"],
                        "metadata_keys": len(price_metadata),
                    }
                )

                logger.info(
                    f"Updated price metadata: {price.id} ({code} {price.currency.upper()})"
                )

        except Exception as e:
            logger.error(f"Error updating price metadata for {code}: {e}")

        return results

    def _get_region_for_currency(self, currency: str) -> str:
        """Get region code for currency."""
        currency_to_region = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_to_region.get(currency, "US")

    def _get_regional_compliance(self, region: str) -> str:
        """Get compliance requirements for region."""
        compliance_map = {
            "US": "hipaa,ccpa",
            "EU": "gdpr,dpa",
            "UK": "gdpr,dpa",
            "CA": "pipeda,privacy_act",
        }
        return compliance_map.get(region, "standard")

    def _get_regional_payment_methods(self, region: str) -> str:
        """Get available payment methods for region."""
        payment_methods_map = {
            "US": "card,ach,apple_pay,google_pay",
            "EU": "card,sepa_debit,apple_pay,google_pay",
            "UK": "card,bacs_debit,apple_pay,google_pay",
            "CA": "card,acss_debit,apple_pay,google_pay",
        }
        return payment_methods_map.get(region, "card")

    async def create_country_mapping_metadata(self) -> Dict[str, Any]:
        """Create comprehensive country mapping metadata."""
        results = {"country_mappings": [], "errors": []}

        try:
            # Country to region mappings
            country_mappings = {
                "US": {
                    "region": "US",
                    "currency": "USD",
                    "tax_system": "sales_tax",
                    "compliance": ["hipaa", "ccpa"],
                    "data_residency": "us-east-1",
                    "payment_methods": ["card", "ach", "apple_pay", "google_pay"],
                    "languages": ["en-US"],
                    "timezone": "America/New_York",
                },
                "BE": {
                    "region": "EU",
                    "currency": "EUR",
                    "tax_system": "vat",
                    "compliance": ["gdpr", "dpa"],
                    "data_residency": "eu-central-1",
                    "payment_methods": [
                        "card",
                        "sepa_debit",
                        "apple_pay",
                        "google_pay",
                    ],
                    "languages": ["nl-BE", "fr-BE", "de-BE"],
                    "timezone": "Europe/Brussels",
                },
                "GB": {
                    "region": "UK",
                    "currency": "GBP",
                    "tax_system": "vat",
                    "compliance": ["gdpr", "dpa"],
                    "data_residency": "eu-west-2",
                    "payment_methods": [
                        "card",
                        "bacs_debit",
                        "apple_pay",
                        "google_pay",
                    ],
                    "languages": ["en-GB"],
                    "timezone": "Europe/London",
                },
                "CA": {
                    "region": "CA",
                    "currency": "CAD",
                    "tax_system": "gst_hst",
                    "compliance": ["pipeda", "privacy_act"],
                    "data_residency": "ca-central-1",
                    "payment_methods": [
                        "card",
                        "acss_debit",
                        "apple_pay",
                        "google_pay",
                    ],
                    "languages": ["en-CA", "fr-CA"],
                    "timezone": "America/Toronto",
                },
            }

            for country_code, config in country_mappings.items():
                results["country_mappings"].append(
                    {
                        "country": country_code,
                        "region": config["region"],
                        "currency": config["currency"],
                        "features": len(config),
                    }
                )

                logger.info(
                    f"Country mapping configured: {country_code} -> {config['region']} ({config['currency']})"
                )

        except Exception as e:
            logger.error(f"Error creating country mapping metadata: {e}")
            results["errors"].append(str(e))

        return results

    async def create_price_id_management_system(self) -> Dict[str, Any]:
        """Create organized price ID management system."""
        results = {"price_mappings": {}, "errors": []}

        try:
            # Get all prices and organize by plan and currency
            for plan_code, product_id in {
                **self.product_ids,
                **self.addon_product_ids,
            }.items():
                prices = stripe.Price.list(product=product_id, limit=100)

                plan_prices = {}
                for price in prices.data:
                    currency = price.currency.upper()
                    billing_cycle = (
                        price.recurring.interval if price.recurring else "one_time"
                    )

                    if currency not in plan_prices:
                        plan_prices[currency] = {}

                    plan_prices[currency][billing_cycle] = {
                        "price_id": price.id,
                        "amount": price.unit_amount / 100,
                        "region": self._get_region_for_currency(currency),
                    }

                results["price_mappings"][plan_code] = plan_prices
                logger.info(
                    f"Organized price IDs for {plan_code}: {len(plan_prices)} currencies"
                )

        except Exception as e:
            logger.error(f"Error creating price ID management system: {e}")
            results["errors"].append(str(e))

        return results


async def main():
    """Main execution function."""
    logger.info("Starting Stripe metadata configuration...")

    if not stripe.api_key:
        logger.error("STRIPE_SECRET_KEY environment variable not set")
        return

    manager = StripeMetadataManager()

    # Configure product metadata
    metadata_results = await manager.configure_product_metadata()

    # Create country mapping metadata
    country_results = await manager.create_country_mapping_metadata()

    # Create price ID management system
    price_management_results = await manager.create_price_id_management_system()

    # Print results
    logger.info("=== STRIPE METADATA CONFIGURATION RESULTS ===")
    logger.info(f"Products updated: {len(metadata_results['products_updated'])}")
    for product in metadata_results["products_updated"]:
        logger.info(
            f"  - {product['code']} ({product['product_type']}): {product['metadata_keys']} metadata keys"
        )

    logger.info(f"Prices updated: {len(metadata_results['prices_updated'])}")
    logger.info(f"Country mappings created: {len(country_results['country_mappings'])}")
    logger.info(
        f"Price mappings organized: {len(price_management_results['price_mappings'])}"
    )

    if metadata_results["errors"]:
        logger.error(f"Errors encountered: {len(metadata_results['errors'])}")
        for error in metadata_results["errors"]:
            logger.error(f"  - {error}")

    logger.info("Stripe metadata configuration completed!")


if __name__ == "__main__":
    asyncio.run(main())
