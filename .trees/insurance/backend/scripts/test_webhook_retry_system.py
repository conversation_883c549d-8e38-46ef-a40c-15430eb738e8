#!/usr/bin/env python3
"""
Webhook Retry System Test Script
Comprehensive testing of webhook retry mechanisms with exponential backoff and dead letter queues.
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Mock the webhook retry service for testing
class MockWebhookRetryService:
    """Mock webhook retry service for testing."""

    def __init__(self):
        self.max_retry_attempts = 3  # Reduced for testing
        self.base_delay_seconds = 0.1  # Reduced for testing
        self.retry_queue = {}
        self.dead_letter_queue = {}
        self.processing_webhooks = set()
        self.call_count = 0

    async def process_webhook_with_retry(
        self,
        event: Dict[str, Any],
        handler_func,
        regional_context: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """Mock webhook processing with retry logic."""
        event_id = event.get("id")

        # Simulate retry logic
        attempt_count = 0
        max_attempts = self.max_retry_attempts

        while attempt_count < max_attempts:
            attempt_count += 1
            self.call_count += 1

            try:
                # Call the handler function
                result = await handler_func(event, regional_context)

                # Success
                return {
                    "status": "success",
                    "event_id": event_id,
                    "attempt_count": attempt_count,
                    "result": result,
                }

            except Exception as e:
                logger.info(
                    f"Attempt {attempt_count}/{max_attempts} failed for {event_id}: {e}"
                )

                if attempt_count < max_attempts:
                    # Wait before retry (reduced delay for testing)
                    await asyncio.sleep(0.1)
                else:
                    # Move to dead letter queue
                    self.dead_letter_queue[event_id] = {
                        "event_id": event_id,
                        "event": event,
                        "attempt_count": attempt_count,
                        "final_error": str(e),
                    }

                    return {
                        "status": "failed",
                        "event_id": event_id,
                        "attempt_count": attempt_count,
                        "moved_to_dead_letter": True,
                    }

        return {"status": "error", "event_id": event_id}

    async def get_retry_queue_status(self) -> Dict[str, Any]:
        """Get retry queue status."""
        return {
            "retry_queue_size": len(self.retry_queue),
            "dead_letter_queue_size": len(self.dead_letter_queue),
            "total_calls": self.call_count,
        }

    async def reprocess_dead_letter_webhook(self, event_id: str) -> Dict[str, Any]:
        """Reprocess webhook from dead letter queue."""
        if event_id not in self.dead_letter_queue:
            return {"status": "not_found", "message": f"Webhook {event_id} not found"}

        entry = self.dead_letter_queue[event_id]
        del self.dead_letter_queue[event_id]

        # Simulate successful reprocessing
        return {"status": "success", "event_id": event_id, "reprocessed": True}


def create_test_webhook_events() -> Dict[str, Dict[str, Any]]:
    """Create test webhook events for retry testing."""

    base_timestamp = int(datetime.utcnow().timestamp())

    return {
        "success_event": {
            "id": "evt_success_001",
            "type": "checkout.session.completed",
            "created": base_timestamp,
            "data": {
                "object": {
                    "id": "cs_success_001",
                    "currency": "usd",
                    "customer": "cus_success_001",
                }
            },
        },
        "retry_then_success": {
            "id": "evt_retry_success_001",
            "type": "customer.subscription.created",
            "created": base_timestamp,
            "data": {"object": {"id": "sub_retry_001", "customer": "cus_retry_001"}},
        },
        "permanent_failure": {
            "id": "evt_failure_001",
            "type": "invoice.payment_failed",
            "created": base_timestamp,
            "data": {"object": {"id": "in_failure_001", "customer": "cus_failure_001"}},
        },
    }


async def test_successful_webhook_processing():
    """Test successful webhook processing without retries."""
    retry_service = MockWebhookRetryService()
    test_events = create_test_webhook_events()

    results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

    logger.info("🧪 Testing successful webhook processing...")

    async def success_handler(event, regional_context=None):
        """Handler that always succeeds."""
        return {"processed": True, "event_id": event["id"]}

    try:
        event = test_events["success_event"]
        result = await retry_service.process_webhook_with_retry(event, success_handler)

        if result["status"] == "success" and result["attempt_count"] == 1:
            results["tests_passed"] += 1
            status = "PASS"
            logger.info("✅ Successful webhook processing: Processed on first attempt")
        else:
            results["tests_failed"] += 1
            status = "FAIL"
            logger.error(
                f"❌ Successful webhook processing: Expected success on first attempt, got {result}"
            )

        results["test_results"].append(
            {"test_name": "successful_processing", "status": status, "result": result}
        )

    except Exception as e:
        results["tests_failed"] += 1
        logger.error(f"❌ Successful webhook processing: Exception - {e}")
        results["test_results"].append(
            {"test_name": "successful_processing", "status": "ERROR", "error": str(e)}
        )

    return results


async def test_retry_then_success():
    """Test webhook that fails initially but succeeds on retry."""
    retry_service = MockWebhookRetryService()
    test_events = create_test_webhook_events()

    results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

    logger.info("🧪 Testing retry then success scenario...")

    call_count = 0

    async def retry_then_success_handler(event, regional_context=None):
        """Handler that fails first time, succeeds second time."""
        nonlocal call_count
        call_count += 1

        if call_count == 1:
            raise Exception("Simulated network error")
        else:
            return {"processed": True, "event_id": event["id"], "attempt": call_count}

    try:
        event = test_events["retry_then_success"]
        result = await retry_service.process_webhook_with_retry(
            event, retry_then_success_handler
        )

        if result["status"] == "success" and result["attempt_count"] == 2:
            results["tests_passed"] += 1
            status = "PASS"
            logger.info("✅ Retry then success: Succeeded on second attempt")
        else:
            results["tests_failed"] += 1
            status = "FAIL"
            logger.error(
                f"❌ Retry then success: Expected success on second attempt, got {result}"
            )

        results["test_results"].append(
            {
                "test_name": "retry_then_success",
                "status": status,
                "result": result,
                "call_count": call_count,
            }
        )

    except Exception as e:
        results["tests_failed"] += 1
        logger.error(f"❌ Retry then success: Exception - {e}")
        results["test_results"].append(
            {"test_name": "retry_then_success", "status": "ERROR", "error": str(e)}
        )

    return results


async def test_permanent_failure_dead_letter():
    """Test webhook that permanently fails and goes to dead letter queue."""
    retry_service = MockWebhookRetryService()
    test_events = create_test_webhook_events()

    results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

    logger.info("🧪 Testing permanent failure and dead letter queue...")

    async def permanent_failure_handler(event, regional_context=None):
        """Handler that always fails."""
        raise Exception("Permanent failure - database connection lost")

    try:
        event = test_events["permanent_failure"]
        result = await retry_service.process_webhook_with_retry(
            event, permanent_failure_handler
        )

        if (
            result["status"] == "failed"
            and result["attempt_count"] == retry_service.max_retry_attempts
            and result["moved_to_dead_letter"]
        ):
            results["tests_passed"] += 1
            status = "PASS"
            logger.info("✅ Permanent failure: Correctly moved to dead letter queue")
        else:
            results["tests_failed"] += 1
            status = "FAIL"
            logger.error(
                f"❌ Permanent failure: Expected dead letter queue, got {result}"
            )

        # Check dead letter queue
        dlq_status = await retry_service.get_retry_queue_status()
        if dlq_status["dead_letter_queue_size"] == 1:
            logger.info("✅ Dead letter queue: Entry correctly added")
        else:
            logger.error(
                f"❌ Dead letter queue: Expected 1 entry, got {dlq_status['dead_letter_queue_size']}"
            )

        results["test_results"].append(
            {
                "test_name": "permanent_failure",
                "status": status,
                "result": result,
                "dlq_status": dlq_status,
            }
        )

    except Exception as e:
        results["tests_failed"] += 1
        logger.error(f"❌ Permanent failure: Exception - {e}")
        results["test_results"].append(
            {"test_name": "permanent_failure", "status": "ERROR", "error": str(e)}
        )

    return results


async def test_dead_letter_reprocessing():
    """Test reprocessing webhooks from dead letter queue."""
    retry_service = MockWebhookRetryService()

    results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

    logger.info("🧪 Testing dead letter queue reprocessing...")

    # First, add an entry to dead letter queue
    event_id = "evt_dlq_reprocess_001"
    retry_service.dead_letter_queue[event_id] = {
        "event_id": event_id,
        "event": {"id": event_id, "type": "test"},
        "attempt_count": 3,
        "final_error": "Test error",
    }

    try:
        # Test reprocessing
        result = await retry_service.reprocess_dead_letter_webhook(event_id)

        if result["status"] == "success" and result["reprocessed"]:
            results["tests_passed"] += 1
            status = "PASS"
            logger.info("✅ Dead letter reprocessing: Successfully reprocessed")
        else:
            results["tests_failed"] += 1
            status = "FAIL"
            logger.error(f"❌ Dead letter reprocessing: Expected success, got {result}")

        # Check that entry was removed from dead letter queue
        dlq_status = await retry_service.get_retry_queue_status()
        if dlq_status["dead_letter_queue_size"] == 0:
            logger.info("✅ Dead letter reprocessing: Entry removed from queue")
        else:
            logger.error(
                f"❌ Dead letter reprocessing: Entry not removed, queue size: {dlq_status['dead_letter_queue_size']}"
            )

        results["test_results"].append(
            {
                "test_name": "dlq_reprocessing",
                "status": status,
                "result": result,
                "dlq_status": dlq_status,
            }
        )

    except Exception as e:
        results["tests_failed"] += 1
        logger.error(f"❌ Dead letter reprocessing: Exception - {e}")
        results["test_results"].append(
            {"test_name": "dlq_reprocessing", "status": "ERROR", "error": str(e)}
        )

    return results


async def test_regional_retry_configuration():
    """Test regional retry configurations."""
    retry_service = MockWebhookRetryService()

    results = {"tests_passed": 0, "tests_failed": 0, "test_results": []}

    logger.info("🧪 Testing regional retry configurations...")

    # Test different regional contexts
    regional_contexts = [
        {"region": "US", "currency": "USD"},
        {"region": "EU", "currency": "EUR"},
        {"region": "UK", "currency": "GBP"},
        {"region": "CA", "currency": "CAD"},
    ]

    async def test_handler(event, regional_context=None):
        """Test handler that logs regional context."""
        region = regional_context.get("region", "US") if regional_context else "US"
        return {"processed": True, "region": region}

    for context in regional_contexts:
        try:
            event = {
                "id": f"evt_regional_{context['region']}_001",
                "type": "test",
                "data": {"object": {}},
            }

            result = await retry_service.process_webhook_with_retry(
                event, test_handler, context
            )

            if result["status"] == "success":
                results["tests_passed"] += 1
                status = "PASS"
                logger.info(
                    f"✅ Regional config {context['region']}: Processed successfully"
                )
            else:
                results["tests_failed"] += 1
                status = "FAIL"
                logger.error(
                    f"❌ Regional config {context['region']}: Failed to process"
                )

            results["test_results"].append(
                {
                    "test_name": f"regional_{context['region']}",
                    "status": status,
                    "result": result,
                    "context": context,
                }
            )

        except Exception as e:
            results["tests_failed"] += 1
            logger.error(f"❌ Regional config {context['region']}: Exception - {e}")
            results["test_results"].append(
                {
                    "test_name": f"regional_{context['region']}",
                    "status": "ERROR",
                    "error": str(e),
                }
            )

    return results


async def main():
    """Main execution function."""
    logger.info("🚀 Starting webhook retry system tests...")

    # Run all test suites
    success_results = await test_successful_webhook_processing()
    retry_results = await test_retry_then_success()
    failure_results = await test_permanent_failure_dead_letter()
    reprocess_results = await test_dead_letter_reprocessing()
    regional_results = await test_regional_retry_configuration()

    # Aggregate results
    all_results = [
        success_results,
        retry_results,
        failure_results,
        reprocess_results,
        regional_results,
    ]
    total_passed = sum(r["tests_passed"] for r in all_results)
    total_failed = sum(r["tests_failed"] for r in all_results)
    total_tests = total_passed + total_failed
    success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

    # Print summary
    logger.info("=" * 60)
    logger.info("🎯 WEBHOOK RETRY SYSTEM TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total Tests: {total_tests}")
    logger.info(f"Passed: {total_passed}")
    logger.info(f"Failed: {total_failed}")
    logger.info(f"Success Rate: {success_rate:.1f}%")

    if total_failed == 0:
        logger.info("🎉 All tests passed! Webhook retry system is working correctly.")
    else:
        logger.warning(
            f"⚠️  {total_failed} tests failed. Please review the results above."
        )

    # Save results
    summary = {
        "total_tests": total_tests,
        "tests_passed": total_passed,
        "tests_failed": total_failed,
        "success_rate": f"{success_rate:.1f}%",
        "test_suites": {
            "successful_processing": success_results,
            "retry_then_success": retry_results,
            "permanent_failure": failure_results,
            "dead_letter_reprocessing": reprocess_results,
            "regional_configurations": regional_results,
        },
    }

    with open("webhook_retry_test_results.json", "w") as f:
        json.dump(summary, f, indent=2)

    logger.info("📄 Test results saved to webhook_retry_test_results.json")

    return summary


if __name__ == "__main__":
    asyncio.run(main())
