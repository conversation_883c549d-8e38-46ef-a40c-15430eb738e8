#!/usr/bin/env python3
"""
Initialize Default Retention Policies

This script initializes default retention policies for both US and EU regions
based on GDPR, CCPA, and legal profession compliance requirements.
"""

import os
import json
from typing import Dict, List, Any

try:
    from supabase import create_client, Client
except ImportError:
    print("❌ Error: supabase-py not installed. Run: pip install supabase")
    exit(1)

# Supabase configuration
US_SUPABASE_URL = "https://anwefmklplkjxkmzpnva.supabase.co"
EU_SUPABASE_URL = "https://lsixcrtzawcxyfkxhyxd.supabase.co"
SUPABASE_KEY = os.getenv(
    "SUPABASE_ANON_KEY",
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzE0NzQsImV4cCI6MjA1MDU0NzQ3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8",
)


# Define retention policies directly
US_POLICIES = [
    {
        "data_type": "user_identity_data",
        "region": "US",
        "retention_period": "2555 days",  # 7 years
        "legal_basis": "contract_performance",
        "sensitivity_level": "high",
        "auto_delete": True,
        "description": "User identity data retained for contract performance and legal obligations",
        "metadata": {"compliance_frameworks": ["CCPA"], "practice_areas": ["all"]},
    },
    {
        "data_type": "client_privileged_data",
        "region": "US",
        "retention_period": "36500 days",  # Indefinite (100 years)
        "legal_basis": "attorney_client_privilege",
        "sensitivity_level": "critical",
        "auto_delete": False,
        "description": "Attorney-client privileged communications - indefinite retention",
        "metadata": {
            "professional_rule": "ABA Model Rule 1.15",
            "practice_areas": ["all"],
        },
    },
    {
        "data_type": "billing_data",
        "region": "US",
        "retention_period": "2555 days",  # 7 years
        "legal_basis": "legal_obligation",
        "sensitivity_level": "high",
        "auto_delete": True,
        "description": "Billing records for tax and accounting obligations",
        "metadata": {"compliance_frameworks": ["Tax Law"], "practice_areas": ["all"]},
    },
    {
        "data_type": "audit_logs",
        "region": "US",
        "retention_period": "2555 days",  # 7 years
        "legal_basis": "legal_obligation",
        "sensitivity_level": "medium",
        "auto_delete": True,
        "description": "Audit logs for compliance and security monitoring",
        "metadata": {
            "compliance_frameworks": ["SOX", "CCPA"],
            "practice_areas": ["all"],
        },
    },
    {
        "data_type": "security_events",
        "region": "US",
        "retention_period": "90 days",
        "legal_basis": "security",
        "sensitivity_level": "medium",
        "auto_delete": True,
        "description": "Security events for incident response and monitoring",
        "metadata": {"compliance_frameworks": ["Security"], "practice_areas": ["all"]},
    },
    {
        "data_type": "usage_analytics",
        "region": "US",
        "retention_period": "365 days",  # 1 year
        "legal_basis": "legitimate_interest",
        "sensitivity_level": "low",
        "auto_delete": True,
        "description": "Usage analytics for service improvement",
        "metadata": {"compliance_frameworks": ["CCPA"], "practice_areas": ["all"]},
    },
]

EU_POLICIES = [
    {
        "data_type": "user_identity_data",
        "region": "EU",
        "retention_period": "2555 days",  # 7 years
        "legal_basis": "contract_performance",
        "sensitivity_level": "high",
        "auto_delete": True,
        "description": "User identity data retained for contract performance and legal obligations",
        "metadata": {"gdpr_article": "Art. 6(1)(b)", "practice_areas": ["all"]},
    },
    {
        "data_type": "client_privileged_data",
        "region": "EU",
        "retention_period": "36500 days",  # Indefinite (100 years)
        "legal_basis": "attorney_client_privilege",
        "sensitivity_level": "critical",
        "auto_delete": False,
        "description": "Attorney-client privileged communications - indefinite retention",
        "metadata": {"gdpr_article": "Art. 6(1)(c)", "practice_areas": ["all"]},
    },
    {
        "data_type": "billing_data",
        "region": "EU",
        "retention_period": "2555 days",  # 7 years
        "legal_basis": "legal_obligation",
        "sensitivity_level": "high",
        "auto_delete": True,
        "description": "Billing records for tax and accounting obligations",
        "metadata": {"gdpr_article": "Art. 6(1)(c)", "practice_areas": ["all"]},
    },
    {
        "data_type": "audit_logs",
        "region": "EU",
        "retention_period": "2555 days",  # 7 years
        "legal_basis": "legal_obligation",
        "sensitivity_level": "medium",
        "auto_delete": True,
        "description": "Audit logs for compliance and security monitoring",
        "metadata": {"gdpr_article": "Art. 6(1)(c)", "practice_areas": ["all"]},
    },
    {
        "data_type": "security_events",
        "region": "EU",
        "retention_period": "90 days",
        "legal_basis": "security",
        "sensitivity_level": "medium",
        "auto_delete": True,
        "description": "Security events for incident response and monitoring",
        "metadata": {"gdpr_article": "Art. 6(1)(f)", "practice_areas": ["all"]},
    },
    {
        "data_type": "usage_analytics",
        "region": "EU",
        "retention_period": "365 days",  # 1 year
        "legal_basis": "legitimate_interest",
        "sensitivity_level": "low",
        "auto_delete": True,
        "description": "Usage analytics for service improvement",
        "metadata": {"gdpr_article": "Art. 6(1)(f)", "practice_areas": ["all"]},
    },
]


class RetentionPolicyInitializer:
    """Service for initializing default retention policies."""

    def __init__(self):
        self.us_client = create_client(US_SUPABASE_URL, SUPABASE_KEY)
        self.eu_client = create_client(EU_SUPABASE_URL, SUPABASE_KEY)

    def initialize_all_policies(self) -> Dict[str, Any]:
        """Initialize default policies for both US and EU regions."""
        try:
            logger.info("🚀 Starting retention policy initialization...")

            results = {
                "overall_status": "success",
                "regions": {},
                "total_policies_created": 0,
                "errors": [],
            }

            # Initialize policies for both regions
            for region in [DataRegion.US, DataRegion.EU]:
                try:
                    region_result = await self.initialize_region_policies(region)
                    results["regions"][region.value] = region_result
                    results["total_policies_created"] += region_result[
                        "policies_created"
                    ]

                    if region_result["status"] != "success":
                        results["overall_status"] = "partial_success"

                except Exception as e:
                    error_msg = (
                        f"Failed to initialize {region.value} policies: {str(e)}"
                    )
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
                    results["overall_status"] = "partial_success"
                    results["regions"][region.value] = {
                        "status": "failed",
                        "error": str(e),
                        "policies_created": 0,
                    }

            # Log summary
            logger.info(
                f"✅ Policy initialization complete: {results['total_policies_created']} policies created"
            )
            if results["errors"]:
                logger.warning(
                    f"⚠️ {len(results['errors'])} errors occurred during initialization"
                )

            return results

        except Exception as e:
            logger.error(f"💥 Fatal error in policy initialization: {e}")
            return {
                "overall_status": "failed",
                "error": str(e),
                "total_policies_created": 0,
            }

    async def initialize_region_policies(self, region: DataRegion) -> Dict[str, Any]:
        """Initialize default policies for a specific region."""
        try:
            logger.info(f"📋 Initializing {region.value} retention policies...")

            # Get all policies for the region
            policy_requests = self.regional_policies.get_all_policies_for_region(region)

            result = {
                "status": "success",
                "region": region.value,
                "policies_created": 0,
                "policies_skipped": 0,
                "policies_failed": 0,
                "policy_details": [],
                "errors": [],
            }

            supabase = get_supabase_client(region)

            for policy_request in policy_requests:
                try:
                    # Check if policy already exists
                    existing_check = (
                        supabase.table("retention_policies")
                        .select("id")
                        .eq("data_type", policy_request.data_type)
                        .eq("region", region.value)
                        .execute()
                    )

                    if existing_check.data:
                        logger.info(
                            f"⏭️ Policy for {policy_request.data_type} already exists in {region.value}"
                        )
                        result["policies_skipped"] += 1
                        result["policy_details"].append(
                            {
                                "data_type": policy_request.data_type,
                                "status": "skipped",
                                "reason": "already_exists",
                            }
                        )
                        continue

                    # Create the policy
                    policy_data = {
                        "data_type": policy_request.data_type,
                        "region": region.value,
                        "retention_period": f"{policy_request.retention_period_days} days",
                        "legal_basis": policy_request.legal_basis.value,
                        "sensitivity_level": policy_request.sensitivity_level.value,
                        "auto_delete": policy_request.auto_delete,
                        "description": policy_request.description,
                        "metadata": policy_request.metadata,
                        "created_by": None,  # System initialization
                    }

                    create_result = (
                        supabase.table("retention_policies")
                        .insert(policy_data)
                        .execute()
                    )

                    if create_result.data:
                        policy_id = create_result.data[0]["id"]
                        logger.info(
                            f"✅ Created policy {policy_id} for {policy_request.data_type} in {region.value}"
                        )

                        result["policies_created"] += 1
                        result["policy_details"].append(
                            {
                                "data_type": policy_request.data_type,
                                "policy_id": policy_id,
                                "status": "created",
                                "retention_days": policy_request.retention_period_days,
                                "legal_basis": policy_request.legal_basis.value,
                            }
                        )

                        # Log policy creation event
                        await self._log_policy_event(
                            supabase=supabase,
                            policy_id=policy_id,
                            data_type=policy_request.data_type,
                            region=region.value,
                        )

                    else:
                        raise Exception("Policy creation returned no data")

                except Exception as e:
                    error_msg = f"Failed to create policy for {policy_request.data_type}: {str(e)}"
                    logger.error(error_msg)
                    result["policies_failed"] += 1
                    result["errors"].append(error_msg)
                    result["policy_details"].append(
                        {
                            "data_type": policy_request.data_type,
                            "status": "failed",
                            "error": str(e),
                        }
                    )

            # Update overall status
            if result["policies_failed"] > 0:
                result["status"] = (
                    "partial_success" if result["policies_created"] > 0 else "failed"
                )

            logger.info(
                f"📊 {region.value} summary: {result['policies_created']} created, "
                f"{result['policies_skipped']} skipped, {result['policies_failed']} failed"
            )

            return result

        except Exception as e:
            logger.error(f"Error initializing {region.value} policies: {e}")
            return {
                "status": "failed",
                "region": region.value,
                "error": str(e),
                "policies_created": 0,
                "policies_skipped": 0,
                "policies_failed": 0,
            }

    async def _log_policy_event(
        self, supabase, policy_id: str, data_type: str, region: str
    ):
        """Log policy creation event."""
        try:
            event_data = {
                "event_type": "policy_created",
                "data_type": data_type,
                "retention_policy_id": policy_id,
                "region": region,
                "metadata": {"initialization": True, "created_by": "system"},
            }

            supabase.table("retention_events").insert(event_data).execute()

        except Exception as e:
            logger.warning(f"Failed to log policy creation event: {e}")
            # Don't fail the main operation for logging errors

    async def validate_policies(self, region: DataRegion) -> Dict[str, Any]:
        """Validate that all expected policies exist for a region."""
        try:
            logger.info(f"🔍 Validating {region.value} retention policies...")

            supabase = get_supabase_client(region)

            # Get all existing policies
            existing_result = (
                supabase.table("retention_policies")
                .select("*")
                .eq("region", region.value)
                .execute()
            )
            existing_policies = (
                {p["data_type"]: p for p in existing_result.data}
                if existing_result.data
                else {}
            )

            # Get expected policies
            expected_policies = self.regional_policies.get_all_policies_for_region(
                region
            )
            expected_data_types = {p.data_type for p in expected_policies}

            validation_result = {
                "region": region.value,
                "status": "valid",
                "total_expected": len(expected_data_types),
                "total_existing": len(existing_policies),
                "missing_policies": [],
                "extra_policies": [],
                "policy_details": [],
            }

            # Check for missing policies
            for expected_policy in expected_policies:
                if expected_policy.data_type not in existing_policies:
                    validation_result["missing_policies"].append(
                        expected_policy.data_type
                    )
                    validation_result["status"] = "incomplete"
                else:
                    existing_policy = existing_policies[expected_policy.data_type]
                    validation_result["policy_details"].append(
                        {
                            "data_type": expected_policy.data_type,
                            "status": "exists",
                            "policy_id": existing_policy["id"],
                            "retention_period": existing_policy["retention_period"],
                            "legal_basis": existing_policy["legal_basis"],
                        }
                    )

            # Check for extra policies (not in expected list)
            for existing_data_type in existing_policies.keys():
                if existing_data_type not in expected_data_types:
                    validation_result["extra_policies"].append(existing_data_type)

            logger.info(
                f"📊 {region.value} validation: {validation_result['total_existing']}/{validation_result['total_expected']} policies exist"
            )

            if validation_result["missing_policies"]:
                logger.warning(
                    f"⚠️ Missing policies in {region.value}: {validation_result['missing_policies']}"
                )

            return validation_result

        except Exception as e:
            logger.error(f"Error validating {region.value} policies: {e}")
            return {"region": region.value, "status": "error", "error": str(e)}

    async def get_policy_summary(self) -> Dict[str, Any]:
        """Get a summary of all retention policies across regions."""
        try:
            logger.info("📊 Generating retention policy summary...")

            summary = {
                "timestamp": asyncio.get_event_loop().time(),
                "regions": {},
                "total_policies": 0,
                "policy_breakdown": {
                    "by_legal_basis": {},
                    "by_sensitivity": {},
                    "by_auto_delete": {"enabled": 0, "disabled": 0},
                },
            }

            for region in [DataRegion.US, DataRegion.EU]:
                try:
                    supabase = get_supabase_client(region)

                    # Get all policies for the region
                    policies_result = (
                        supabase.table("retention_policies")
                        .select("*")
                        .eq("region", region.value)
                        .execute()
                    )
                    policies = policies_result.data if policies_result.data else []

                    region_summary = {
                        "total_policies": len(policies),
                        "auto_delete_enabled": sum(
                            1 for p in policies if p["auto_delete"]
                        ),
                        "auto_delete_disabled": sum(
                            1 for p in policies if not p["auto_delete"]
                        ),
                        "legal_basis_breakdown": {},
                        "sensitivity_breakdown": {},
                        "policies": [],
                    }

                    # Analyze policies
                    for policy in policies:
                        # Legal basis breakdown
                        basis = policy["legal_basis"]
                        region_summary["legal_basis_breakdown"][basis] = (
                            region_summary["legal_basis_breakdown"].get(basis, 0) + 1
                        )
                        summary["policy_breakdown"]["by_legal_basis"][basis] = (
                            summary["policy_breakdown"]["by_legal_basis"].get(basis, 0)
                            + 1
                        )

                        # Sensitivity breakdown
                        sensitivity = policy["sensitivity_level"]
                        region_summary["sensitivity_breakdown"][sensitivity] = (
                            region_summary["sensitivity_breakdown"].get(sensitivity, 0)
                            + 1
                        )
                        summary["policy_breakdown"]["by_sensitivity"][sensitivity] = (
                            summary["policy_breakdown"]["by_sensitivity"].get(
                                sensitivity, 0
                            )
                            + 1
                        )

                        # Auto-delete breakdown
                        if policy["auto_delete"]:
                            summary["policy_breakdown"]["by_auto_delete"][
                                "enabled"
                            ] += 1
                        else:
                            summary["policy_breakdown"]["by_auto_delete"][
                                "disabled"
                            ] += 1

                        region_summary["policies"].append(
                            {
                                "data_type": policy["data_type"],
                                "retention_period": policy["retention_period"],
                                "legal_basis": policy["legal_basis"],
                                "sensitivity_level": policy["sensitivity_level"],
                                "auto_delete": policy["auto_delete"],
                            }
                        )

                    summary["regions"][region.value] = region_summary
                    summary["total_policies"] += len(policies)

                except Exception as e:
                    logger.error(f"Error getting summary for {region.value}: {e}")
                    summary["regions"][region.value] = {"error": str(e)}

            return summary

        except Exception as e:
            logger.error(f"Error generating policy summary: {e}")
            return {"error": str(e)}


async def main():
    """Main function to initialize retention policies."""
    try:
        initializer = RetentionPolicyInitializer()

        print("🚀 PI Lawyer AI - Retention Policy Initialization")
        print("=" * 60)

        # Initialize policies
        results = await initializer.initialize_all_policies()

        # Validate policies
        print("\n🔍 Validating policy initialization...")
        for region in [DataRegion.US, DataRegion.EU]:
            validation = await initializer.validate_policies(region)
            print(
                f"📋 {region.value}: {validation['total_existing']}/{validation['total_expected']} policies - {validation['status']}"
            )

        # Generate summary
        print("\n📊 Generating policy summary...")
        summary = await initializer.get_policy_summary()

        print("\n" + "=" * 60)
        print("📋 RETENTION POLICY INITIALIZATION SUMMARY")
        print("=" * 60)
        print(f"Overall Status: {results['overall_status']}")
        print(f"Total Policies Created: {results['total_policies_created']}")
        print(f"Total Policies Across Regions: {summary['total_policies']}")

        for region_name, region_data in results["regions"].items():
            if "error" not in region_data:
                print(f"\n{region_name} Region:")
                print(f"  ✅ Created: {region_data['policies_created']}")
                print(f"  ⏭️ Skipped: {region_data['policies_skipped']}")
                print(f"  ❌ Failed: {region_data['policies_failed']}")
            else:
                print(f"\n{region_name} Region: ❌ FAILED - {region_data['error']}")

        if results["errors"]:
            print(f"\n⚠️ Errors encountered:")
            for error in results["errors"]:
                print(f"  - {error}")

        print("\n🎉 Retention policy initialization complete!")

        return 0 if results["overall_status"] == "success" else 1

    except Exception as e:
        print(f"💥 Fatal error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
