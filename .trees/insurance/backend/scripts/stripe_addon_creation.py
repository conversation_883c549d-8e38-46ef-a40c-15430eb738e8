#!/usr/bin/env python3
"""
Stripe Add-on Product Creation Script for PI Lawyer AI
Creates regional add-on products with multi-currency pricing.

This script implements Phase 6.3 Regional Add-ons Creation.
"""

import os
import sys
import asyncio
import stripe
from typing import Dict, List, Optional, Any
import logging
from supabase import create_client, Client

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class StripeAddonManager:
    """Manages Stripe add-on product and price creation for multi-country subscriptions."""

    def __init__(self):
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)

        # Key add-ons to create in Stripe (focusing on most important ones)
        self.key_addons = [
            "ai_receptionist",
            "extra_storage",
            "extra_users",
            "advanced_analytics",
            "intake_agent",
        ]

        # Regional availability mapping
        self.regional_availability = {
            "ai_receptionist": ["US", "EU", "UK", "CA"],  # Available globally
            "extra_storage": ["US", "EU", "UK", "CA"],  # Available globally
            "extra_users": ["US", "EU", "UK", "CA"],  # Available globally
            "advanced_analytics": ["US", "EU", "UK", "CA"],  # Available globally
            "intake_agent": ["US", "EU", "UK", "CA"],  # Available globally
        }

        # Currency mapping
        self.currencies = ["USD", "EUR", "GBP", "CAD"]

    async def get_addon_data(self) -> Dict[str, Any]:
        """Get add-on data from database."""
        try:
            result = (
                self.supabase.schema("tenants")
                .table("subscription_addons")
                .select("*")
                .in_("code", self.key_addons)
                .execute()
            )

            addon_data = {}
            for addon in result.data:
                addon_data[addon["code"]] = addon

            logger.info(f"Retrieved {len(addon_data)} add-ons from database")
            return addon_data

        except Exception as e:
            logger.error(f"Error retrieving add-on data: {e}")
            return {}

    async def create_stripe_addon_products(self) -> Dict[str, Any]:
        """Create Stripe products for add-ons."""
        results = {"products_created": [], "prices_created": [], "errors": []}

        try:
            addon_data = await self.get_addon_data()

            for addon_code, addon_info in addon_data.items():
                logger.info(f"Creating Stripe product for add-on: {addon_code}")

                # Create Stripe product for add-on
                stripe_product = await self._create_stripe_addon_product(
                    addon_code, addon_info
                )
                if stripe_product:
                    results["products_created"].append(
                        {
                            "addon_code": addon_code,
                            "stripe_product_id": stripe_product.id,
                            "name": stripe_product.name,
                        }
                    )

                    # Create prices for all currencies and billing cycles
                    for billing_cycle in ["monthly", "yearly"]:
                        for currency in self.currencies:
                            # Check if this add-on is available in the region for this currency
                            region = self._get_region_for_currency(currency)
                            if region in self.regional_availability.get(addon_code, []):
                                price_result = await self._create_stripe_addon_price(
                                    stripe_product.id,
                                    addon_code,
                                    addon_info,
                                    currency,
                                    billing_cycle,
                                )
                                if price_result:
                                    results["prices_created"].append(price_result)

                    # Update database with Stripe product ID
                    await self._update_database_addon(addon_code, stripe_product.id)

        except Exception as e:
            logger.error(f"Error creating Stripe add-on products: {e}")
            results["errors"].append(str(e))

        return results

    async def _create_stripe_addon_product(
        self, addon_code: str, addon_info: Dict[str, Any]
    ) -> Optional[stripe.Product]:
        """Create a single Stripe product for an add-on."""
        try:
            # Check if product already exists
            existing_products = stripe.Product.list(limit=100)
            for product in existing_products.data:
                if product.metadata.get("addon_code") == addon_code:
                    logger.info(
                        f"Add-on product {addon_code} already exists: {product.id}"
                    )
                    return product

            # Create metadata
            metadata = {
                "addon_code": addon_code,
                "category": addon_info.get("category", ""),
                "type": "addon",
            }

            # Add features to metadata
            if addon_info.get("features"):
                metadata["features"] = str(addon_info["features"])

            # Create new product
            stripe_product = stripe.Product.create(
                name=addon_info["name"],
                description=addon_info["description"],
                metadata=metadata,
                active=True,
            )

            logger.info(
                f"Created Stripe add-on product: {stripe_product.id} for {addon_code}"
            )
            return stripe_product

        except Exception as e:
            logger.error(f"Error creating Stripe add-on product for {addon_code}: {e}")
            return None

    async def _create_stripe_addon_price(
        self,
        product_id: str,
        addon_code: str,
        addon_info: Dict[str, Any],
        currency: str,
        billing_cycle: str,
    ) -> Optional[Dict[str, Any]]:
        """Create a Stripe price for an add-on."""
        try:
            # Get price from add-on info
            price_field = f"price_{billing_cycle}"
            if price_field not in addon_info:
                logger.warning(f"No {price_field} found for {addon_code}")
                return None

            amount = float(addon_info[price_field])
            amount_cents = int(amount * 100)

            # Determine interval
            interval = "month" if billing_cycle == "monthly" else "year"

            # Check if price already exists
            existing_prices = stripe.Price.list(product=product_id, limit=100)
            for price in existing_prices.data:
                if (
                    price.currency.upper() == currency
                    and price.recurring.interval == interval
                    and price.unit_amount == amount_cents
                ):
                    logger.info(f"Add-on price already exists: {price.id}")
                    return {
                        "addon_code": addon_code,
                        "currency": currency,
                        "billing_cycle": billing_cycle,
                        "stripe_price_id": price.id,
                        "amount": amount,
                    }

            # Create new price
            stripe_price = stripe.Price.create(
                product=product_id,
                unit_amount=amount_cents,
                currency=currency.lower(),
                recurring={"interval": interval},
                metadata={
                    "addon_code": addon_code,
                    "billing_cycle": billing_cycle,
                    "region": self._get_region_for_currency(currency),
                },
                active=True,
            )

            logger.info(
                f"Created Stripe add-on price: {stripe_price.id} for {addon_code} {currency} {billing_cycle}"
            )

            return {
                "addon_code": addon_code,
                "currency": currency,
                "billing_cycle": billing_cycle,
                "stripe_price_id": stripe_price.id,
                "amount": amount,
            }

        except Exception as e:
            logger.error(
                f"Error creating Stripe add-on price for {addon_code} {currency} {billing_cycle}: {e}"
            )
            return None

    def _get_region_for_currency(self, currency: str) -> str:
        """Get region code for currency."""
        currency_to_region = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_to_region.get(currency, "US")

    async def _update_database_addon(self, addon_code: str, stripe_product_id: str):
        """Update database with Stripe product ID for add-on."""
        try:
            # Update subscription_addons table
            result = (
                self.supabase.schema("tenants")
                .table("subscription_addons")
                .update(
                    {"stripe_product_id": stripe_product_id, "last_synced_at": "now()"}
                )
                .eq("code", addon_code)
                .execute()
            )

            if result.data:
                logger.info(
                    f"Updated database for add-on {addon_code} with product ID {stripe_product_id}"
                )
            else:
                logger.warning(
                    f"No database record found for add-on code: {addon_code}"
                )

        except Exception as e:
            logger.error(f"Error updating database for add-on {addon_code}: {e}")

    async def create_regional_addon_variants(self) -> Dict[str, Any]:
        """Create regional variants for add-ons with specific features."""
        results = {"regional_variants": [], "errors": []}

        try:
            # Special handling for AI Receptionist with regional features
            ai_receptionist_variants = {
                "US": {
                    "features": [
                        "english_voice",
                        "us_timezone_support",
                        "us_phone_numbers",
                    ],
                    "compliance": ["hipaa_compliant"],
                },
                "EU": {
                    "features": [
                        "multilingual_voice",
                        "gdpr_compliant_recording",
                        "eu_phone_numbers",
                    ],
                    "compliance": ["gdpr_compliant", "data_residency_eu"],
                },
                "UK": {
                    "features": [
                        "british_english_voice",
                        "uk_timezone_support",
                        "uk_phone_numbers",
                    ],
                    "compliance": ["gdpr_compliant", "data_residency_uk"],
                },
                "CA": {
                    "features": [
                        "english_french_voice",
                        "ca_timezone_support",
                        "ca_phone_numbers",
                    ],
                    "compliance": ["pipeda_compliant"],
                },
            }

            for region, config in ai_receptionist_variants.items():
                results["regional_variants"].append(
                    {
                        "addon_code": "ai_receptionist",
                        "region": region,
                        "features": config["features"],
                        "compliance": config["compliance"],
                    }
                )

                logger.info(f"AI Receptionist regional variant configured: {region}")

        except Exception as e:
            logger.error(f"Error creating regional add-on variants: {e}")
            results["errors"].append(str(e))

        return results


async def main():
    """Main execution function."""
    logger.info("Starting Stripe add-on product and price creation...")

    if not stripe.api_key:
        logger.error("STRIPE_SECRET_KEY environment variable not set")
        return

    manager = StripeAddonManager()

    # Create add-on products and prices
    results = await manager.create_stripe_addon_products()

    # Create regional variants
    regional_results = await manager.create_regional_addon_variants()

    # Print results
    logger.info("=== STRIPE ADD-ON CREATION RESULTS ===")
    logger.info(f"Add-on products created: {len(results['products_created'])}")
    for product in results["products_created"]:
        logger.info(f"  - {product['addon_code']}: {product['stripe_product_id']}")

    logger.info(f"Add-on prices created: {len(results['prices_created'])}")
    for price in results["prices_created"]:
        logger.info(
            f"  - {price['addon_code']} {price['currency']} {price['billing_cycle']}: {price['stripe_price_id']}"
        )

    logger.info(
        f"Regional variants created: {len(regional_results['regional_variants'])}"
    )
    for variant in regional_results["regional_variants"]:
        logger.info(
            f"  - {variant['addon_code']} {variant['region']}: {len(variant['features'])} features"
        )

    if results["errors"]:
        logger.error(f"Errors encountered: {len(results['errors'])}")
        for error in results["errors"]:
            logger.error(f"  - {error}")

    logger.info("Stripe add-on creation completed!")


if __name__ == "__main__":
    asyncio.run(main())
