#!/usr/bin/env python3
"""
Production Metadata Configuration Script for PI Lawyer AI
Updates Stripe metadata configuration with production product IDs.

This script dynamically fetches production product IDs and configures metadata.
"""

import os
import sys
import asyncio
import stripe
import json
from typing import Dict, List, Optional, Any
import logging
from supabase import create_client, Client

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class ProductionMetadataManager:
    """Manages Stripe metadata configuration for production environment."""

    def __init__(self):
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)

        # Will be populated by fetching from Stripe
        self.product_ids = {}
        self.addon_product_ids = {}

        # Country to region mapping
        self.country_region_mapping = {
            # North America
            "US": "US",
            "CA": "CA",
            # European Union
            "AT": "EU",
            "BE": "EU",
            "BG": "EU",
            "HR": "EU",
            "CY": "EU",
            "CZ": "EU",
            "DK": "EU",
            "EE": "EU",
            "FI": "EU",
            "FR": "EU",
            "DE": "EU",
            "GR": "EU",
            "HU": "EU",
            "IE": "EU",
            "IT": "EU",
            "LV": "EU",
            "LT": "EU",
            "LU": "EU",
            "MT": "EU",
            "NL": "EU",
            "PL": "EU",
            "PT": "EU",
            "RO": "EU",
            "SK": "EU",
            "SI": "EU",
            "ES": "EU",
            "SE": "EU",
            # United Kingdom
            "GB": "UK",
        }

    async def fetch_production_product_ids(self) -> Dict[str, Any]:
        """Fetch product IDs from production Stripe account."""
        logger.info("Fetching product IDs from production Stripe...")

        try:
            # Fetch all products
            products = stripe.Product.list(limit=100, active=True)

            # Categorize products
            main_products = {}
            addon_products = {}

            for product in products.data:
                metadata = product.metadata or {}

                # Check if it's a main subscription product
                if "plan_code" in metadata:
                    plan_code = metadata["plan_code"]
                    main_products[plan_code] = product.id
                    logger.info(f"Found main product: {plan_code} -> {product.id}")

                # Check if it's an add-on product
                elif "addon_code" in metadata:
                    addon_code = metadata["addon_code"]
                    addon_products[addon_code] = product.id
                    logger.info(f"Found add-on product: {addon_code} -> {product.id}")

                # Try to identify by name if metadata is missing
                else:
                    name_lower = product.name.lower()
                    if "solo" in name_lower:
                        main_products["solo"] = product.id
                        logger.info(f"Identified by name: solo -> {product.id}")
                    elif "team" in name_lower:
                        main_products["team"] = product.id
                        logger.info(f"Identified by name: team -> {product.id}")
                    elif "scale" in name_lower:
                        main_products["scale"] = product.id
                        logger.info(f"Identified by name: scale -> {product.id}")
                    elif "receptionist" in name_lower:
                        addon_products["ai_receptionist"] = product.id
                        logger.info(
                            f"Identified by name: ai_receptionist -> {product.id}"
                        )
                    elif "storage" in name_lower:
                        addon_products["extra_storage"] = product.id
                        logger.info(
                            f"Identified by name: extra_storage -> {product.id}"
                        )
                    elif "users" in name_lower:
                        addon_products["extra_users"] = product.id
                        logger.info(f"Identified by name: extra_users -> {product.id}")
                    elif "analytics" in name_lower:
                        addon_products["advanced_analytics"] = product.id
                        logger.info(
                            f"Identified by name: advanced_analytics -> {product.id}"
                        )
                    elif "intake" in name_lower:
                        addon_products["intake_agent"] = product.id
                        logger.info(f"Identified by name: intake_agent -> {product.id}")

            self.product_ids = main_products
            self.addon_product_ids = addon_products

            logger.info(
                f"✅ Found {len(main_products)} main products and {len(addon_products)} add-on products"
            )

            return {
                "main_products": main_products,
                "addon_products": addon_products,
                "total_products": len(products.data),
            }

        except Exception as e:
            logger.error(f"Failed to fetch product IDs: {e}")
            raise

    async def configure_product_metadata(self) -> Dict[str, Any]:
        """Configure comprehensive metadata for all Stripe products."""
        logger.info("Configuring product metadata...")

        results = {"products_updated": [], "addons_updated": [], "errors": []}

        try:
            # Configure main product metadata
            for plan_code, product_id in self.product_ids.items():
                try:
                    metadata_result = await self._configure_main_product_metadata(
                        plan_code, product_id
                    )
                    results["products_updated"].append(metadata_result)
                except Exception as e:
                    error_msg = f"Failed to configure metadata for {plan_code}: {e}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)

            # Configure add-on metadata
            for addon_code, product_id in self.addon_product_ids.items():
                try:
                    metadata_result = await self._configure_addon_metadata(
                        addon_code, product_id
                    )
                    results["addons_updated"].append(metadata_result)
                except Exception as e:
                    error_msg = f"Failed to configure metadata for {addon_code}: {e}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)

            logger.info(
                f"✅ Metadata configuration completed: {len(results['products_updated'])} products, {len(results['addons_updated'])} add-ons"
            )

        except Exception as e:
            logger.error(f"Metadata configuration failed: {e}")
            results["errors"].append(str(e))

        return results

    async def _configure_main_product_metadata(
        self, plan_code: str, product_id: str
    ) -> Dict[str, Any]:
        """Configure metadata for a main subscription product."""

        # Define metadata for each plan
        plan_metadata = {
            "solo": {
                "plan_code": "solo",
                "max_users": "3",
                "max_storage_gb": "10",
                "max_documents": "1000",
                "compliance_level": "standard",
                "practice_areas": "Personal Injury",
                "ai_receptionist": "false",
                "priority_support": "false",
                "custom_integrations": "false",
                "advanced_analytics": "false",
            },
            "team": {
                "plan_code": "team",
                "max_users": "10",
                "max_storage_gb": "100",
                "max_documents": "10000",
                "compliance_level": "enhanced",
                "practice_areas": "All",
                "ai_receptionist": "false",
                "priority_support": "true",
                "custom_integrations": "false",
                "advanced_analytics": "true",
            },
            "scale": {
                "plan_code": "scale",
                "max_users": "unlimited",
                "max_storage_gb": "1000",
                "max_documents": "unlimited",
                "compliance_level": "enterprise",
                "practice_areas": "All",
                "ai_receptionist": "true",
                "priority_support": "true",
                "custom_integrations": "true",
                "advanced_analytics": "true",
            },
        }

        metadata = plan_metadata.get(plan_code, {})

        # Add regional and compliance metadata
        metadata.update(
            {
                "multi_country": "true",
                "supported_currencies": "USD,EUR,GBP,CAD",
                "supported_regions": "US,EU,UK,CA",
                "gdpr_compliant": "true",
                "ccpa_compliant": "true",
                "pipeda_compliant": "true",
                "data_residency": "regional",
                "webhook_enabled": "true",
                "tax_calculation": "automatic",
                "billing_cycles": "monthly,yearly",
            }
        )

        # Update product metadata
        stripe.Product.modify(product_id, metadata=metadata)

        logger.info(f"  ✅ Updated metadata for {plan_code} ({product_id})")

        return {
            "plan_code": plan_code,
            "product_id": product_id,
            "metadata_keys": len(metadata),
        }

    async def _configure_addon_metadata(
        self, addon_code: str, product_id: str
    ) -> Dict[str, Any]:
        """Configure metadata for an add-on product."""

        # Define metadata for each add-on
        addon_metadata = {
            "ai_receptionist": {
                "addon_code": "ai_receptionist",
                "type": "addon",
                "category": "ai_services",
                "compatible_plans": "solo,team,scale",
                "regional_availability": "US,EU,UK,CA",
                "requires_setup": "true",
                "billing_model": "per_month",
            },
            "extra_storage": {
                "addon_code": "extra_storage",
                "type": "addon",
                "category": "storage",
                "compatible_plans": "solo,team,scale",
                "regional_availability": "US,EU,UK,CA",
                "unit_size": "100GB",
                "billing_model": "per_unit_per_month",
            },
            "extra_users": {
                "addon_code": "extra_users",
                "type": "addon",
                "category": "users",
                "compatible_plans": "solo,team",
                "regional_availability": "US,EU,UK,CA",
                "unit_size": "5_users",
                "billing_model": "per_unit_per_month",
            },
            "advanced_analytics": {
                "addon_code": "advanced_analytics",
                "type": "addon",
                "category": "analytics",
                "compatible_plans": "solo,team",
                "regional_availability": "US,EU,UK,CA",
                "requires_setup": "false",
                "billing_model": "per_month",
            },
            "intake_agent": {
                "addon_code": "intake_agent",
                "type": "addon",
                "category": "ai_services",
                "compatible_plans": "team,scale",
                "regional_availability": "US,EU,UK,CA",
                "requires_setup": "true",
                "billing_model": "per_month",
            },
        }

        metadata = addon_metadata.get(addon_code, {})

        # Add common add-on metadata
        metadata.update(
            {
                "multi_country": "true",
                "supported_currencies": "USD,EUR,GBP,CAD",
                "gdpr_compliant": "true",
                "webhook_enabled": "true",
                "tax_calculation": "automatic",
            }
        )

        # Update product metadata
        stripe.Product.modify(product_id, metadata=metadata)

        logger.info(f"  ✅ Updated metadata for {addon_code} ({product_id})")

        return {
            "addon_code": addon_code,
            "product_id": product_id,
            "metadata_keys": len(metadata),
        }

    async def configure_price_metadata(self) -> Dict[str, Any]:
        """Configure metadata for all prices."""
        logger.info("Configuring price metadata...")

        results = {"prices_updated": 0, "errors": []}

        try:
            # Fetch all prices
            prices = stripe.Price.list(limit=100, active=True)

            for price in prices.data:
                try:
                    # Determine region from currency
                    currency = price.currency.upper()
                    region = self._get_region_for_currency(currency)

                    # Get product to determine type
                    product = stripe.Product.retrieve(price.product)
                    product_metadata = product.metadata or {}

                    # Create price metadata
                    price_metadata = {
                        "currency": currency,
                        "region": region,
                        "billing_interval": (
                            price.recurring.interval if price.recurring else "one_time"
                        ),
                        "amount_decimal": str(price.unit_amount / 100),
                        "tax_behavior": (
                            "exclusive" if region in ["US", "CA"] else "inclusive"
                        ),
                    }

                    # Add product-specific metadata
                    if "plan_code" in product_metadata:
                        price_metadata["plan_code"] = product_metadata["plan_code"]
                        price_metadata["product_type"] = "subscription"
                    elif "addon_code" in product_metadata:
                        price_metadata["addon_code"] = product_metadata["addon_code"]
                        price_metadata["product_type"] = "addon"

                    # Update price metadata
                    stripe.Price.modify(price.id, metadata=price_metadata)
                    results["prices_updated"] += 1

                except Exception as e:
                    error_msg = f"Failed to update price {price.id}: {e}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)

            logger.info(f"✅ Updated metadata for {results['prices_updated']} prices")

        except Exception as e:
            logger.error(f"Price metadata configuration failed: {e}")
            results["errors"].append(str(e))

        return results

    def _get_region_for_currency(self, currency: str) -> str:
        """Get region for currency."""
        currency_to_region = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_to_region.get(currency, "US")

    async def generate_configuration_summary(self) -> str:
        """Generate a summary of the configuration."""
        summary = f"""
# Production Metadata Configuration Summary

## Product IDs Configured

### Main Products ({len(self.product_ids)})
"""
        for plan_code, product_id in self.product_ids.items():
            summary += f"- **{plan_code}**: {product_id}\n"

        summary += f"\n### Add-on Products ({len(self.addon_product_ids)})\n"
        for addon_code, product_id in self.addon_product_ids.items():
            summary += f"- **{addon_code}**: {product_id}\n"

        summary += """
## Metadata Configuration

### Main Product Metadata
- Plan code and limits (users, storage, documents)
- Compliance levels and practice areas
- Multi-country support flags
- Regional and currency support
- GDPR/CCPA/PIPEDA compliance flags

### Add-on Metadata  
- Add-on code and category
- Compatible plans and regional availability
- Billing model and unit sizing
- Setup requirements

### Price Metadata
- Currency and regional mapping
- Tax behavior configuration
- Billing interval and amount
- Product type association

## Next Steps
1. Verify metadata in Stripe Dashboard
2. Test webhook processing with new product IDs
3. Validate regional routing and tax calculations
4. Update any hardcoded references in application code
"""

        return summary


async def main():
    """Main execution function."""
    logger.info("Starting production metadata configuration...")

    if not stripe.api_key:
        logger.error("STRIPE_SECRET_KEY environment variable not set")
        return

    # Validate we're using production keys
    if not stripe.api_key.startswith("sk_live_"):
        logger.warning(
            "Not using production Stripe key - metadata will be configured in sandbox"
        )

    try:
        manager = ProductionMetadataManager()

        # Fetch product IDs from Stripe
        product_fetch_result = await manager.fetch_production_product_ids()
        logger.info(
            f"Fetched {product_fetch_result['total_products']} products from Stripe"
        )

        # Configure product metadata
        metadata_result = await manager.configure_product_metadata()

        # Configure price metadata
        price_result = await manager.configure_price_metadata()

        # Generate summary
        summary = await manager.generate_configuration_summary()

        # Save results
        results = {
            "product_fetch": product_fetch_result,
            "metadata_configuration": metadata_result,
            "price_configuration": price_result,
            "timestamp": stripe.api_key[:7] + "..." if stripe.api_key else "unknown",
        }

        with open("production_metadata_results.json", "w") as f:
            json.dump(results, f, indent=2)

        with open("production_metadata_summary.md", "w") as f:
            f.write(summary)

        logger.info("✅ Production metadata configuration completed successfully")
        logger.info("📄 Results saved to production_metadata_results.json")
        logger.info("📄 Summary saved to production_metadata_summary.md")

        print(summary)

    except Exception as e:
        logger.error(f"❌ Metadata configuration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
