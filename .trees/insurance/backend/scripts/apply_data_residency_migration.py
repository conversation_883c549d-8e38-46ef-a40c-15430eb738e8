#!/usr/bin/env python3
"""
Data Residency Migration Script

This script applies the data residency schema migration to both US and EU
Supabase instances to ensure consistent schema across regions.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.services.data_residency import (
    DataRegion,
    get_regional_supabase_client,
    validate_regional_configuration,
)

logger = logging.getLogger(__name__)


class DataResidencyMigrator:
    """Handles data residency schema migrations."""

    def __init__(self):
        self.migration_file = (
            Path(__file__).parent.parent
            / "migrations"
            / "add_data_residency_schema.sql"
        )

    async def load_migration_sql(self) -> str:
        """Load the migration SQL from file."""
        try:
            with open(self.migration_file, "r") as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"Migration file not found: {self.migration_file}")

    async def apply_migration_to_region(self, region: DataRegion) -> bool:
        """Apply migration to a specific region."""
        try:
            logger.info(f"Applying data residency migration to {region.value} region")

            # Get regional client
            client = get_regional_supabase_client(region)

            # Load migration SQL
            migration_sql = await self.load_migration_sql()

            # Split SQL into individual statements
            statements = [
                stmt.strip() for stmt in migration_sql.split(";") if stmt.strip()
            ]

            # Execute each statement
            for i, statement in enumerate(statements):
                if statement.startswith("--") or not statement:
                    continue

                try:
                    logger.info(
                        f"Executing statement {i+1}/{len(statements)} in {region.value}"
                    )

                    # Use RPC to execute raw SQL
                    result = client.rpc("exec_sql", {"sql": statement}).execute()

                    if result.error:
                        logger.error(f"Error in statement {i+1}: {result.error}")
                        return False

                except Exception as e:
                    logger.error(
                        f"Failed to execute statement {i+1} in {region.value}: {str(e)}"
                    )
                    # Continue with other statements for non-critical errors
                    continue

            logger.info(f"Successfully applied migration to {region.value} region")
            return True

        except Exception as e:
            logger.error(f"Failed to apply migration to {region.value}: {str(e)}")
            return False

    async def verify_migration(self, region: DataRegion) -> bool:
        """Verify that migration was applied successfully."""
        try:
            client = get_regional_supabase_client(region)

            # Check if key tables exist
            tables_to_check = ["data_residency_audit_log", "regional_data_transfers"]

            for table in tables_to_check:
                result = client.table(table).select("*").limit(1).execute()
                if result.error:
                    logger.error(
                        f"Table {table} not found in {region.value}: {result.error}"
                    )
                    return False

            # Check if user_profiles has new columns
            result = (
                client.table("user_profiles")
                .select("data_residency_region")
                .limit(1)
                .execute()
            )
            if result.error:
                logger.error(
                    f"data_residency_region column not found in {region.value}: {result.error}"
                )
                return False

            logger.info(f"Migration verification successful for {region.value}")
            return True

        except Exception as e:
            logger.error(f"Migration verification failed for {region.value}: {str(e)}")
            return False

    async def run_migration(self, regions: list = None) -> dict:
        """Run migration for specified regions."""
        if regions is None:
            regions = [DataRegion.US, DataRegion.EU]

        results = {}

        # Validate configuration first
        config_status = validate_regional_configuration()
        logger.info(f"Regional configuration status: {config_status}")

        for region in regions:
            if not config_status.get(region.value, False):
                logger.warning(f"Skipping {region.value} - configuration invalid")
                results[region.value] = {
                    "applied": False,
                    "verified": False,
                    "reason": "invalid_config",
                }
                continue

            # Apply migration
            applied = await self.apply_migration_to_region(region)

            # Verify migration
            verified = False
            if applied:
                verified = await self.verify_migration(region)

            results[region.value] = {
                "applied": applied,
                "verified": verified,
                "reason": "success" if applied and verified else "failed",
            }

        return results


async def main():
    """Main migration execution function."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    logger.info("Starting data residency migration")

    migrator = DataResidencyMigrator()

    # Check command line arguments
    regions_to_migrate = []
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            try:
                regions_to_migrate.append(DataRegion(arg.upper()))
            except ValueError:
                logger.error(f"Invalid region: {arg}")
                sys.exit(1)

    # Run migration
    results = await migrator.run_migration(regions_to_migrate or None)

    # Print results
    print("\n" + "=" * 50)
    print("DATA RESIDENCY MIGRATION RESULTS")
    print("=" * 50)

    all_successful = True
    for region, result in results.items():
        status = (
            "✅ SUCCESS" if result["applied"] and result["verified"] else "❌ FAILED"
        )
        print(f"{region}: {status}")
        print(f"  Applied: {'✅' if result['applied'] else '❌'}")
        print(f"  Verified: {'✅' if result['verified'] else '❌'}")
        print(f"  Reason: {result['reason']}")
        print()

        if not (result["applied"] and result["verified"]):
            all_successful = False

    if all_successful:
        print("🎉 All migrations completed successfully!")
        print("\nNext steps:")
        print("1. Update your .env file with regional Supabase credentials")
        print("2. Test the data residency endpoints")
        print("3. Verify compliance audit logging")
    else:
        print("⚠️  Some migrations failed. Check the logs above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
