#!/usr/bin/env python3
"""
Multi-Country Subscription Testing Framework
Comprehensive testing suite for validating multi-country subscription scenarios,
webhook processing, regional routing, currency handling, and database updates.

This script tests the complete subscription flow across US/EU/UK/CA regions.
"""

import asyncio
import json
import time
import random
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import stripe
import requests
from dotenv import load_dotenv

# Add the project root to the Python path
import sys
import os

project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.append(project_root)

# Load environment variables from .env file
env_path = os.path.join(project_root, ".env")
load_dotenv(env_path)

# Simple logging setup instead of importing services
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MultiCountrySubscriptionTester:
    """Comprehensive tester for multi-country subscription scenarios."""

    def __init__(self):
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "regional_results": {},
            "currency_results": {},
            "webhook_results": {},
            "error_summary": [],
        }

        # Test configuration
        self.test_regions = ["US", "EU", "UK", "CA"]
        self.test_currencies = ["USD", "EUR", "GBP", "CAD"]
        self.test_plans = ["Solo", "Team", "Scale"]
        self.test_addons = ["AI Receptionist", "Extra Storage", "Extra Users"]

        # Stripe configuration
        self.stripe_api_key = os.getenv("STRIPE_SECRET_KEY")
        self.webhook_endpoint = os.getenv(
            "WEBHOOK_ENDPOINT_URL", "https://your-domain.com/webhooks/stripe"
        )

        if not self.stripe_api_key:
            raise ValueError("STRIPE_SECRET_KEY environment variable is required")

        stripe.api_key = self.stripe_api_key

    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive multi-country subscription tests."""
        logger.info("🚀 Starting comprehensive multi-country subscription tests...")

        try:
            # Test 1: Regional Subscription Creation
            await self._test_regional_subscription_creation()

            # Test 2: Multi-Currency Pricing Validation
            await self._test_multi_currency_pricing()

            # Test 3: Webhook Regional Routing
            await self._test_webhook_regional_routing()

            # Test 4: Tax Calculation by Region
            await self._test_regional_tax_calculations()

            # Test 5: Currency-Specific Event Processing
            await self._test_currency_event_processing()

            # Test 6: Regional Compliance Validation
            await self._test_regional_compliance()

            # Test 7: Database Regional Updates
            await self._test_database_regional_updates()

            # Test 8: Subscription Lifecycle by Region
            await self._test_subscription_lifecycle()

            # Test 9: Add-on Management by Region
            await self._test_regional_addon_management()

            # Test 10: Error Handling and Recovery
            await self._test_error_handling_scenarios()

            # Generate final report
            await self._generate_test_report()

            return self.test_results

        except Exception as e:
            logger.error(f"Critical error in comprehensive tests: {e}", exc_info=True)
            self._record_test_failure("comprehensive_tests", f"Critical error: {e}")
            return self.test_results

    async def _test_regional_subscription_creation(self):
        """Test subscription creation for each region."""
        logger.info("Testing regional subscription creation...")

        for region in self.test_regions:
            try:
                # Get region-specific currency
                currency = self._get_region_currency(region)

                # Create test customer for region
                customer = await self._create_test_customer(region, currency)

                # Test each plan in the region
                for plan in self.test_plans:
                    subscription = await self._create_test_subscription(
                        customer, plan, currency, region
                    )

                    if subscription:
                        # Validate subscription properties
                        await self._validate_subscription_properties(
                            subscription, region, currency, plan
                        )

                        # Clean up
                        await self._cleanup_test_subscription(subscription["id"])

                # Clean up customer
                await self._cleanup_test_customer(customer["id"])

                self._record_test_success(
                    f"regional_subscription_creation_{region}",
                    f"Successfully created and validated subscriptions for {region}",
                )

            except Exception as e:
                self._record_test_failure(
                    f"regional_subscription_creation_{region}",
                    f"Failed to create subscriptions for {region}: {e}",
                )

    async def _test_multi_currency_pricing(self):
        """Test multi-currency pricing validation."""
        logger.info("Testing multi-currency pricing...")

        try:
            # Test pricing consistency across currencies
            pricing_data = {}

            for currency in self.test_currencies:
                currency_pricing = await self._get_currency_pricing(currency)
                pricing_data[currency] = currency_pricing

                # Validate pricing structure
                await self._validate_pricing_structure(currency_pricing, currency)

            # Validate pricing relationships
            await self._validate_pricing_relationships(pricing_data)

            self._record_test_success(
                "multi_currency_pricing", "Multi-currency pricing validation successful"
            )

        except Exception as e:
            self._record_test_failure(
                "multi_currency_pricing", f"Multi-currency pricing failed: {e}"
            )

    async def _test_webhook_regional_routing(self):
        """Test webhook regional routing functionality."""
        logger.info("Testing webhook regional routing...")

        try:
            # Test routing for each region
            for region in self.test_regions:
                # Create test webhook event
                test_event = await self._create_test_webhook_event(region)

                # Simulate regional router processing
                routing_result = await self._simulate_regional_routing(
                    test_event, region
                )

                # Validate routing decision
                await self._validate_routing_result(routing_result, region)

                # Test compliance validation
                compliance_result = await self._test_regional_compliance_routing(
                    test_event, region
                )

                if compliance_result:
                    self._record_test_success(
                        f"webhook_routing_{region}",
                        f"Webhook routing successful for {region}",
                    )
                else:
                    self._record_test_failure(
                        f"webhook_routing_{region}",
                        f"Webhook routing failed for {region}",
                    )

        except Exception as e:
            self._record_test_failure(
                "webhook_regional_routing", f"Webhook routing failed: {e}"
            )

    async def _test_regional_tax_calculations(self):
        """Test tax calculations for each region."""
        logger.info("Testing regional tax calculations...")

        try:
            tax_test_scenarios = [
                {"region": "US", "state": "CA", "expected_tax_type": "sales_tax"},
                {"region": "EU", "country": "DE", "expected_tax_type": "vat"},
                {"region": "UK", "country": "GB", "expected_tax_type": "vat"},
                {"region": "CA", "province": "ON", "expected_tax_type": "gst_hst"},
            ]

            for scenario in tax_test_scenarios:
                # Create test tax calculation
                tax_result = await self._test_tax_calculation(scenario)

                # Validate tax calculation
                if await self._validate_tax_calculation(tax_result, scenario):
                    self._record_test_success(
                        f"tax_calculation_{scenario['region']}",
                        f"Tax calculation successful for {scenario['region']}",
                    )
                else:
                    self._record_test_failure(
                        f"tax_calculation_{scenario['region']}",
                        f"Tax calculation failed for {scenario['region']}",
                    )

        except Exception as e:
            self._record_test_failure(
                "regional_tax_calculations", f"Tax calculations failed: {e}"
            )

    async def _test_currency_event_processing(self):
        """Test currency-specific event processing."""
        logger.info("Testing currency-specific event processing...")

        try:
            for currency in self.test_currencies:
                # Create currency-specific events
                events = await self._create_currency_test_events(currency)

                for event in events:
                    # Simulate multi-currency handler processing
                    processing_result = await self._simulate_currency_processing(
                        event, currency
                    )

                    # Validate processing result
                    if await self._validate_currency_processing(
                        processing_result, currency
                    ):
                        self._record_test_success(
                            f"currency_processing_{currency}_{event['type']}",
                            f"Currency processing successful for {currency}",
                        )
                    else:
                        self._record_test_failure(
                            f"currency_processing_{currency}_{event['type']}",
                            f"Currency processing failed for {currency}",
                        )

        except Exception as e:
            self._record_test_failure(
                "currency_event_processing", f"Currency processing failed: {e}"
            )

    async def _test_regional_compliance(self):
        """Test regional compliance validation."""
        logger.info("Testing regional compliance...")

        try:
            compliance_scenarios = [
                {"region": "EU", "compliance_type": "GDPR", "data_residency": "EU"},
                {"region": "UK", "compliance_type": "GDPR", "data_residency": "UK"},
                {"region": "US", "compliance_type": "CCPA", "data_residency": "US"},
                {"region": "CA", "compliance_type": "PIPEDA", "data_residency": "CA"},
            ]

            for scenario in compliance_scenarios:
                # Test compliance validation
                compliance_result = await self._test_compliance_scenario(scenario)

                if compliance_result:
                    self._record_test_success(
                        f"compliance_{scenario['region']}",
                        f"Compliance validation successful for {scenario['region']}",
                    )
                else:
                    self._record_test_failure(
                        f"compliance_{scenario['region']}",
                        f"Compliance validation failed for {scenario['region']}",
                    )

        except Exception as e:
            self._record_test_failure(
                "regional_compliance", f"Compliance testing failed: {e}"
            )

    async def _test_database_regional_updates(self):
        """Test database updates for regional data."""
        logger.info("Testing database regional updates...")

        try:
            # Test database routing and updates for each region
            for region in self.test_regions:
                # Create test data for region
                test_data = await self._create_regional_test_data(region)

                # Test database update
                update_result = await self._test_regional_database_update(
                    test_data, region
                )

                # Validate data residency
                residency_valid = await self._validate_data_residency(test_data, region)

                if update_result and residency_valid:
                    self._record_test_success(
                        f"database_update_{region}",
                        f"Database update successful for {region}",
                    )
                else:
                    self._record_test_failure(
                        f"database_update_{region}",
                        f"Database update failed for {region}",
                    )

                # Clean up test data
                await self._cleanup_regional_test_data(test_data, region)

        except Exception as e:
            self._record_test_failure(
                "database_regional_updates", f"Database updates failed: {e}"
            )

    async def _test_subscription_lifecycle(self):
        """Test complete subscription lifecycle for each region."""
        logger.info("Testing subscription lifecycle...")

        try:
            for region in self.test_regions:
                currency = self._get_region_currency(region)

                # Create customer and subscription
                customer = await self._create_test_customer(region, currency)
                subscription = await self._create_test_subscription(
                    customer, "Team", currency, region
                )

                if subscription:
                    # Test subscription updates
                    update_result = await self._test_subscription_update(
                        subscription, region
                    )

                    # Test subscription cancellation
                    cancel_result = await self._test_subscription_cancellation(
                        subscription, region
                    )

                    if update_result and cancel_result:
                        self._record_test_success(
                            f"subscription_lifecycle_{region}",
                            f"Subscription lifecycle successful for {region}",
                        )
                    else:
                        self._record_test_failure(
                            f"subscription_lifecycle_{region}",
                            f"Subscription lifecycle failed for {region}",
                        )

                # Clean up
                await self._cleanup_test_customer(customer["id"])

        except Exception as e:
            self._record_test_failure(
                "subscription_lifecycle", f"Subscription lifecycle failed: {e}"
            )

    async def _test_regional_addon_management(self):
        """Test add-on management for each region."""
        logger.info("Testing regional add-on management...")

        try:
            for region in self.test_regions:
                currency = self._get_region_currency(region)

                # Test add-on availability for region
                available_addons = await self._get_regional_addons(region)

                # Test add-on pricing for currency
                addon_pricing = await self._get_addon_pricing(currency)

                # Validate regional add-on features
                features_valid = await self._validate_regional_addon_features(
                    region, available_addons
                )

                if available_addons and addon_pricing and features_valid:
                    self._record_test_success(
                        f"addon_management_{region}",
                        f"Add-on management successful for {region}",
                    )
                else:
                    self._record_test_failure(
                        f"addon_management_{region}",
                        f"Add-on management failed for {region}",
                    )

        except Exception as e:
            self._record_test_failure(
                "regional_addon_management", f"Add-on management failed: {e}"
            )

    async def _test_error_handling_scenarios(self):
        """Test error handling and recovery scenarios."""
        logger.info("Testing error handling scenarios...")

        try:
            error_scenarios = [
                {"type": "invalid_currency", "data": {"currency": "INVALID"}},
                {"type": "unsupported_region", "data": {"region": "INVALID"}},
                {"type": "network_timeout", "data": {"timeout": True}},
                {"type": "database_error", "data": {"db_error": True}},
                {"type": "stripe_api_error", "data": {"stripe_error": True}},
            ]

            for scenario in error_scenarios:
                # Test error scenario
                error_result = await self._test_error_scenario(scenario)

                # Validate error handling
                if await self._validate_error_handling(error_result, scenario):
                    self._record_test_success(
                        f"error_handling_{scenario['type']}",
                        f"Error handling successful for {scenario['type']}",
                    )
                else:
                    self._record_test_failure(
                        f"error_handling_{scenario['type']}",
                        f"Error handling failed for {scenario['type']}",
                    )

        except Exception as e:
            self._record_test_failure(
                "error_handling_scenarios", f"Error handling tests failed: {e}"
            )

    # Helper methods for test implementation
    def _get_region_currency(self, region: str) -> str:
        """Get the primary currency for a region."""
        currency_map = {"US": "USD", "EU": "EUR", "UK": "GBP", "CA": "CAD"}
        return currency_map.get(region, "USD")

    async def _create_test_customer(self, region: str, currency: str) -> Dict[str, Any]:
        """Create a test customer for the specified region."""
        try:
            customer_data = {
                "email": f"test-{region.lower()}-{int(time.time())}@example.com",
                "name": f"Test Customer {region}",
                "metadata": {
                    "region": region,
                    "currency": currency,
                    "test_customer": "true",
                    "created_by": "multi_country_test",
                },
            }

            customer = stripe.Customer.create(**customer_data)
            return customer

        except Exception as e:
            logger.error(f"Failed to create test customer for {region}: {e}")
            raise

    async def _create_test_subscription(
        self, customer: Dict[str, Any], plan: str, currency: str, region: str
    ) -> Optional[Dict[str, Any]]:
        """Create a test subscription for the customer."""
        try:
            # Get price ID for plan and currency
            price_id = await self._get_price_id(plan, currency, "monthly")

            if not price_id:
                logger.error(f"No price ID found for {plan} in {currency}")
                return None

            subscription_data = {
                "customer": customer["id"],
                "items": [{"price": price_id}],
                "metadata": {
                    "region": region,
                    "plan": plan,
                    "currency": currency,
                    "test_subscription": "true",
                },
            }

            subscription = stripe.Subscription.create(**subscription_data)
            return subscription

        except Exception as e:
            logger.error(f"Failed to create test subscription: {e}")
            return None

    async def _get_price_id(
        self, plan: str, currency: str, billing_cycle: str
    ) -> Optional[str]:
        """Get the Stripe price ID for a plan, currency, and billing cycle."""
        # This would query your database or use the price mapping
        # For now, return a placeholder
        return f"price_test_{plan.lower()}_{currency.lower()}_{billing_cycle}"

    async def _validate_subscription_properties(
        self, subscription: Dict[str, Any], region: str, currency: str, plan: str
    ) -> bool:
        """Validate subscription properties match expected values."""
        try:
            # Validate currency
            if subscription.get("currency") != currency.lower():
                logger.error(
                    f"Currency mismatch: expected {currency}, got {subscription.get('currency')}"
                )
                return False

            # Validate metadata
            metadata = subscription.get("metadata", {})
            if metadata.get("region") != region:
                logger.error(
                    f"Region mismatch: expected {region}, got {metadata.get('region')}"
                )
                return False

            # Validate plan
            if metadata.get("plan") != plan:
                logger.error(
                    f"Plan mismatch: expected {plan}, got {metadata.get('plan')}"
                )
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating subscription properties: {e}")
            return False

    async def _cleanup_test_subscription(self, subscription_id: str):
        """Clean up test subscription."""
        try:
            stripe.Subscription.delete(subscription_id)
            logger.info(f"Cleaned up test subscription: {subscription_id}")
        except Exception as e:
            logger.warning(f"Failed to clean up subscription {subscription_id}: {e}")

    async def _cleanup_test_customer(self, customer_id: str):
        """Clean up test customer."""
        try:
            stripe.Customer.delete(customer_id)
            logger.info(f"Cleaned up test customer: {customer_id}")
        except Exception as e:
            logger.warning(f"Failed to clean up customer {customer_id}: {e}")

    async def _get_currency_pricing(self, currency: str) -> Dict[str, Any]:
        """Get pricing data for a specific currency."""
        try:
            # Query pricing from database or Stripe
            pricing_data = {"currency": currency, "plans": {}, "addons": {}}

            # Get plan pricing
            for plan in self.test_plans:
                monthly_price = await self._get_price_id(plan, currency, "monthly")
                yearly_price = await self._get_price_id(plan, currency, "yearly")

                pricing_data["plans"][plan] = {
                    "monthly": monthly_price,
                    "yearly": yearly_price,
                }

            # Get addon pricing
            for addon in self.test_addons:
                monthly_price = await self._get_price_id(addon, currency, "monthly")
                yearly_price = await self._get_price_id(addon, currency, "yearly")

                pricing_data["addons"][addon] = {
                    "monthly": monthly_price,
                    "yearly": yearly_price,
                }

            return pricing_data

        except Exception as e:
            logger.error(f"Failed to get currency pricing for {currency}: {e}")
            return {}

    async def _validate_pricing_structure(
        self, pricing_data: Dict[str, Any], currency: str
    ) -> bool:
        """Validate pricing structure for a currency."""
        try:
            # Validate all plans have pricing
            for plan in self.test_plans:
                if plan not in pricing_data.get("plans", {}):
                    logger.error(f"Missing pricing for plan {plan} in {currency}")
                    return False

                plan_pricing = pricing_data["plans"][plan]
                if not plan_pricing.get("monthly") or not plan_pricing.get("yearly"):
                    logger.error(
                        f"Missing monthly/yearly pricing for {plan} in {currency}"
                    )
                    return False

            # Validate all addons have pricing
            for addon in self.test_addons:
                if addon not in pricing_data.get("addons", {}):
                    logger.error(f"Missing pricing for addon {addon} in {currency}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating pricing structure for {currency}: {e}")
            return False

    async def _validate_pricing_relationships(
        self, pricing_data: Dict[str, Dict[str, Any]]
    ) -> bool:
        """Validate pricing relationships across currencies."""
        try:
            # Validate that all currencies have the same structure
            base_currency = list(pricing_data.keys())[0]
            base_structure = pricing_data[base_currency]

            for currency, currency_data in pricing_data.items():
                if currency == base_currency:
                    continue

                # Check plan structure consistency
                if set(currency_data.get("plans", {}).keys()) != set(
                    base_structure.get("plans", {}).keys()
                ):
                    logger.error(
                        f"Plan structure mismatch between {base_currency} and {currency}"
                    )
                    return False

                # Check addon structure consistency
                if set(currency_data.get("addons", {}).keys()) != set(
                    base_structure.get("addons", {}).keys()
                ):
                    logger.error(
                        f"Addon structure mismatch between {base_currency} and {currency}"
                    )
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating pricing relationships: {e}")
            return False

    async def _create_test_webhook_event(self, region: str) -> Dict[str, Any]:
        """Create a test webhook event for a region."""
        currency = self._get_region_currency(region)

        return {
            "id": f"evt_test_{region.lower()}_{int(time.time())}",
            "type": "customer.subscription.created",
            "data": {
                "object": {
                    "id": f"sub_test_{region.lower()}_{int(time.time())}",
                    "currency": currency.lower(),
                    "customer": f"cus_test_{region.lower()}_{int(time.time())}",
                    "metadata": {"region": region, "test_event": "true"},
                }
            },
            "created": int(time.time()),
            "livemode": False,
        }

    async def _validate_routing_result(
        self, routing_result: Dict[str, Any], expected_region: str
    ) -> bool:
        """Validate webhook routing result."""
        try:
            if not routing_result:
                logger.error("No routing result received")
                return False

            detected_region = routing_result.get("region")
            if detected_region != expected_region:
                logger.error(
                    f"Region routing mismatch: expected {expected_region}, got {detected_region}"
                )
                return False

            # Validate database routing
            database_config = routing_result.get("database_config")
            if not database_config:
                logger.error("No database config in routing result")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating routing result: {e}")
            return False

    async def _test_regional_compliance_routing(
        self, event: Dict[str, Any], region: str
    ) -> bool:
        """Test regional compliance routing."""
        try:
            # Test GDPR compliance for EU/UK
            if region in ["EU", "UK"]:
                # Validate GDPR compliance requirements
                gdpr_valid = await self._validate_gdpr_compliance(event, region)
                if not gdpr_valid:
                    return False

            # Test CCPA compliance for US
            elif region == "US":
                ccpa_valid = await self._validate_ccpa_compliance(event, region)
                if not ccpa_valid:
                    return False

            # Test PIPEDA compliance for CA
            elif region == "CA":
                pipeda_valid = await self._validate_pipeda_compliance(event, region)
                if not pipeda_valid:
                    return False

            return True

        except Exception as e:
            logger.error(f"Error testing regional compliance routing: {e}")
            return False

    async def _validate_gdpr_compliance(
        self, event: Dict[str, Any], region: str
    ) -> bool:
        """Validate GDPR compliance for EU/UK regions."""
        try:
            # Check data residency requirements
            if region == "EU":
                # EU data must stay in EU
                expected_db = "ailex-belgium"
            elif region == "UK":
                # UK data can be in EU (adequacy decision)
                expected_db = "ailex-belgium"

            # Validate other GDPR requirements
            # This would include consent validation, data minimization, etc.

            return True

        except Exception as e:
            logger.error(f"Error validating GDPR compliance: {e}")
            return False

    async def _validate_ccpa_compliance(
        self, event: Dict[str, Any], region: str
    ) -> bool:
        """Validate CCPA compliance for US region."""
        try:
            # Check CCPA requirements
            # This would include consumer rights validation, opt-out mechanisms, etc.

            return True

        except Exception as e:
            logger.error(f"Error validating CCPA compliance: {e}")
            return False

    async def _validate_pipeda_compliance(
        self, event: Dict[str, Any], region: str
    ) -> bool:
        """Validate PIPEDA compliance for CA region."""
        try:
            # Check PIPEDA requirements
            # This would include privacy policy validation, consent mechanisms, etc.

            return True

        except Exception as e:
            logger.error(f"Error validating PIPEDA compliance: {e}")
            return False

    async def _simulate_regional_routing(
        self, event: Dict[str, Any], region: str
    ) -> Dict[str, Any]:
        """Simulate regional webhook routing."""
        try:
            # Simulate routing logic
            await asyncio.sleep(0.1)  # Simulate processing time

            return {
                "region": region,
                "database_config": {
                    "host": f"db-{region.lower()}.example.com",
                    "region": region,
                },
                "compliance_validated": True,
                "routing_successful": True,
            }

        except Exception as e:
            logger.error(f"Error simulating regional routing: {e}")
            return {}

    async def _simulate_currency_processing(
        self, event: Dict[str, Any], currency: str
    ) -> Dict[str, Any]:
        """Simulate currency-specific event processing."""
        try:
            # Simulate currency processing logic
            await asyncio.sleep(0.1)  # Simulate processing time

            return {
                "currency": currency,
                "regional_context": {
                    "currency": currency,
                    "region": self._get_currency_region(currency),
                },
                "processing_successful": True,
                "amount_converted": True,
            }

        except Exception as e:
            logger.error(f"Error simulating currency processing: {e}")
            return {}

    def _get_currency_region(self, currency: str) -> str:
        """Get the primary region for a currency."""
        currency_region_map = {"USD": "US", "EUR": "EU", "GBP": "UK", "CAD": "CA"}
        return currency_region_map.get(currency, "US")

    async def _test_tax_calculation(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Test tax calculation for a scenario."""
        try:
            # Create test tax calculation request
            tax_request = {
                "amount": 10000,  # $100.00
                "currency": self._get_region_currency(scenario["region"]),
                "region": scenario["region"],
                "customer_location": scenario,
            }

            # This would call your tax calculation service
            # For now, return mock result
            return {
                "amount": tax_request["amount"],
                "tax_amount": 800,  # $8.00 tax
                "tax_rate": 0.08,
                "tax_type": scenario["expected_tax_type"],
                "region": scenario["region"],
            }

        except Exception as e:
            logger.error(f"Error testing tax calculation: {e}")
            return {}

    async def _validate_tax_calculation(
        self, tax_result: Dict[str, Any], scenario: Dict[str, Any]
    ) -> bool:
        """Validate tax calculation result."""
        try:
            if not tax_result:
                return False

            # Validate tax type
            if tax_result.get("tax_type") != scenario["expected_tax_type"]:
                logger.error(
                    f"Tax type mismatch: expected {scenario['expected_tax_type']}, got {tax_result.get('tax_type')}"
                )
                return False

            # Validate tax amount is reasonable
            tax_rate = tax_result.get("tax_rate", 0)
            if tax_rate < 0 or tax_rate > 0.3:  # 0-30% tax rate range
                logger.error(f"Unreasonable tax rate: {tax_rate}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating tax calculation: {e}")
            return False

    async def _create_currency_test_events(self, currency: str) -> List[Dict[str, Any]]:
        """Create test events for a specific currency."""
        events = []

        event_types = [
            "customer.subscription.created",
            "customer.subscription.updated",
            "invoice.payment_succeeded",
            "checkout.session.completed",
        ]

        for event_type in event_types:
            event = {
                "id": f"evt_{currency.lower()}_{event_type.replace('.', '_')}_{int(time.time())}",
                "type": event_type,
                "data": {
                    "object": {
                        "id": f"obj_{currency.lower()}_{int(time.time())}",
                        "currency": currency.lower(),
                        "amount": 9900,  # $99.00
                        "metadata": {"currency": currency, "test_event": "true"},
                    }
                },
                "created": int(time.time()),
                "livemode": False,
            }
            events.append(event)

        return events

    async def _validate_currency_processing(
        self, processing_result: Dict[str, Any], currency: str
    ) -> bool:
        """Validate currency processing result."""
        try:
            if not processing_result:
                return False

            # Validate currency was detected correctly
            detected_currency = processing_result.get("currency")
            if detected_currency != currency:
                logger.error(
                    f"Currency detection failed: expected {currency}, got {detected_currency}"
                )
                return False

            # Validate regional context was created
            regional_context = processing_result.get("regional_context")
            if not regional_context:
                logger.error("No regional context in processing result")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating currency processing: {e}")
            return False

    async def _test_compliance_scenario(self, scenario: Dict[str, Any]) -> bool:
        """Test a compliance scenario."""
        try:
            region = scenario["region"]
            compliance_type = scenario["compliance_type"]

            # Create test data for compliance validation
            test_data = {
                "customer_data": {
                    "region": region,
                    "compliance_requirements": [compliance_type],
                },
                "processing_data": {
                    "data_residency": scenario["data_residency"],
                    "consent_status": "granted",
                },
            }

            # This would call your compliance validation service
            # For now, return success if basic requirements are met
            return (
                test_data["customer_data"]["region"] == region
                and compliance_type
                in test_data["customer_data"]["compliance_requirements"]
                and test_data["processing_data"]["data_residency"]
                == scenario["data_residency"]
            )

        except Exception as e:
            logger.error(f"Error testing compliance scenario: {e}")
            return False

    async def _create_regional_test_data(self, region: str) -> Dict[str, Any]:
        """Create test data for a region."""
        return {
            "id": f"test_data_{region.lower()}_{int(time.time())}",
            "region": region,
            "currency": self._get_region_currency(region),
            "customer_data": {
                "email": f"test-{region.lower()}@example.com",
                "region": region,
            },
            "subscription_data": {
                "plan": "Team",
                "currency": self._get_region_currency(region),
                "region": region,
            },
            "created_at": datetime.utcnow().isoformat(),
        }

    async def _test_regional_database_update(
        self, test_data: Dict[str, Any], region: str
    ) -> bool:
        """Test database update for regional data."""
        try:
            # This would test actual database operations
            # For now, simulate successful update
            logger.info(
                f"Simulating database update for {region} with data: {test_data['id']}"
            )

            # Validate data structure
            required_fields = [
                "id",
                "region",
                "currency",
                "customer_data",
                "subscription_data",
            ]
            for field in required_fields:
                if field not in test_data:
                    logger.error(f"Missing required field: {field}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error testing regional database update: {e}")
            return False

    async def _validate_data_residency(
        self, test_data: Dict[str, Any], region: str
    ) -> bool:
        """Validate data residency requirements."""
        try:
            # Check that data is stored in correct region
            data_region = test_data.get("region")
            if data_region != region:
                logger.error(
                    f"Data residency violation: data in {data_region}, expected {region}"
                )
                return False

            # For EU/UK, ensure data stays in EU
            if region in ["EU", "UK"]:
                # Validate EU data residency
                if not self._is_eu_compliant_storage(test_data):
                    logger.error("EU data residency requirements not met")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating data residency: {e}")
            return False

    def _is_eu_compliant_storage(self, test_data: Dict[str, Any]) -> bool:
        """Check if data storage is EU compliant."""
        # This would check actual storage location
        # For now, return True if region is EU/UK
        return test_data.get("region") in ["EU", "UK"]

    async def _cleanup_regional_test_data(self, test_data: Dict[str, Any], region: str):
        """Clean up regional test data."""
        try:
            logger.info(f"Cleaning up test data {test_data['id']} for region {region}")
            # This would delete test data from database
        except Exception as e:
            logger.warning(f"Failed to clean up test data: {e}")

    async def _test_subscription_update(
        self, subscription: Dict[str, Any], region: str
    ) -> bool:
        """Test subscription update for a region."""
        try:
            # Test updating subscription metadata
            update_data = {
                "metadata": {
                    **subscription.get("metadata", {}),
                    "updated_at": datetime.utcnow().isoformat(),
                    "test_update": "true",
                }
            }

            # This would call Stripe API to update subscription
            # For now, simulate successful update
            logger.info(
                f"Simulating subscription update for {subscription['id']} in {region}"
            )

            return True

        except Exception as e:
            logger.error(f"Error testing subscription update: {e}")
            return False

    async def _test_subscription_cancellation(
        self, subscription: Dict[str, Any], region: str
    ) -> bool:
        """Test subscription cancellation for a region."""
        try:
            # Test cancelling subscription
            # This would call Stripe API to cancel subscription
            # For now, simulate successful cancellation
            logger.info(
                f"Simulating subscription cancellation for {subscription['id']} in {region}"
            )

            return True

        except Exception as e:
            logger.error(f"Error testing subscription cancellation: {e}")
            return False

    async def _get_regional_addons(self, region: str) -> List[Dict[str, Any]]:
        """Get available add-ons for a region."""
        try:
            # This would query your database for regional add-ons
            # For now, return mock data
            regional_addons = {
                "US": [
                    {
                        "name": "AI Receptionist",
                        "features": ["english_voice", "business_hours"],
                    },
                    {"name": "Extra Storage", "features": ["1tb_storage"]},
                    {"name": "Extra Users", "features": ["unlimited_users"]},
                    {"name": "Advanced Analytics", "features": ["custom_reports"]},
                    {"name": "Intake Agent", "features": ["lead_qualification"]},
                ],
                "EU": [
                    {
                        "name": "AI Receptionist",
                        "features": ["multilingual_voice", "gdpr_compliant"],
                    },
                    {
                        "name": "Extra Storage",
                        "features": ["1tb_storage", "eu_residency"],
                    },
                    {"name": "Extra Users", "features": ["unlimited_users"]},
                    {
                        "name": "Advanced Analytics",
                        "features": ["custom_reports", "gdpr_compliant"],
                    },
                    {
                        "name": "Intake Agent",
                        "features": ["lead_qualification", "gdpr_compliant"],
                    },
                ],
                "UK": [
                    {
                        "name": "AI Receptionist",
                        "features": ["british_english_voice", "gdpr_compliant"],
                    },
                    {
                        "name": "Extra Storage",
                        "features": ["1tb_storage", "uk_residency"],
                    },
                    {"name": "Extra Users", "features": ["unlimited_users"]},
                    {
                        "name": "Advanced Analytics",
                        "features": ["custom_reports", "gdpr_compliant"],
                    },
                    {
                        "name": "Intake Agent",
                        "features": ["lead_qualification", "gdpr_compliant"],
                    },
                ],
                "CA": [
                    {
                        "name": "AI Receptionist",
                        "features": ["english_french_voice", "pipeda_compliant"],
                    },
                    {
                        "name": "Extra Storage",
                        "features": ["1tb_storage", "ca_residency"],
                    },
                    {"name": "Extra Users", "features": ["unlimited_users"]},
                    {
                        "name": "Advanced Analytics",
                        "features": ["custom_reports", "pipeda_compliant"],
                    },
                    {
                        "name": "Intake Agent",
                        "features": ["lead_qualification", "pipeda_compliant"],
                    },
                ],
            }

            return regional_addons.get(region, [])

        except Exception as e:
            logger.error(f"Error getting regional add-ons for {region}: {e}")
            return []

    async def _get_addon_pricing(self, currency: str) -> Dict[str, Any]:
        """Get add-on pricing for a currency."""
        try:
            # This would query your database for add-on pricing
            # For now, return mock pricing
            base_prices = {
                "AI Receptionist": {"monthly": 49, "yearly": 490},
                "Extra Storage": {"monthly": 19, "yearly": 190},
                "Extra Users": {"monthly": 29, "yearly": 290},
                "Advanced Analytics": {"monthly": 39, "yearly": 390},
                "Intake Agent": {"monthly": 59, "yearly": 590},
            }

            # Apply currency (same numerical value strategy)
            currency_pricing = {}
            for addon, prices in base_prices.items():
                currency_pricing[addon] = {
                    "monthly": f"{prices['monthly']} {currency}",
                    "yearly": f"{prices['yearly']} {currency}",
                }

            return currency_pricing

        except Exception as e:
            logger.error(f"Error getting add-on pricing for {currency}: {e}")
            return {}

    async def _validate_regional_addon_features(
        self, region: str, addons: List[Dict[str, Any]]
    ) -> bool:
        """Validate regional add-on features."""
        try:
            # Check that region-specific features are present
            region_requirements = {
                "US": ["english_voice", "business_hours"],
                "EU": ["multilingual_voice", "gdpr_compliant", "eu_residency"],
                "UK": ["british_english_voice", "gdpr_compliant", "uk_residency"],
                "CA": ["english_french_voice", "pipeda_compliant", "ca_residency"],
            }

            required_features = region_requirements.get(region, [])

            # Check if any addon has the required regional features
            for addon in addons:
                addon_features = addon.get("features", [])
                if any(feature in addon_features for feature in required_features):
                    return True

            logger.error(
                f"No add-ons found with required features for {region}: {required_features}"
            )
            return False

        except Exception as e:
            logger.error(f"Error validating regional add-on features: {e}")
            return False

    async def _test_error_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Test an error scenario."""
        try:
            error_type = scenario["type"]
            error_data = scenario["data"]

            # Simulate different error scenarios
            if error_type == "invalid_currency":
                return {
                    "error": "invalid_currency",
                    "message": f"Currency {error_data['currency']} is not supported",
                    "handled": True,
                }
            elif error_type == "unsupported_region":
                return {
                    "error": "unsupported_region",
                    "message": f"Region {error_data['region']} is not supported",
                    "handled": True,
                }
            elif error_type == "network_timeout":
                return {
                    "error": "network_timeout",
                    "message": "Request timed out",
                    "handled": True,
                    "retry_attempted": True,
                }
            elif error_type == "database_error":
                return {
                    "error": "database_error",
                    "message": "Database connection failed",
                    "handled": True,
                    "fallback_used": True,
                }
            elif error_type == "stripe_api_error":
                return {
                    "error": "stripe_api_error",
                    "message": "Stripe API returned error",
                    "handled": True,
                    "retry_scheduled": True,
                }

            return {"error": "unknown", "handled": False}

        except Exception as e:
            logger.error(f"Error testing error scenario: {e}")
            return {"error": "test_error", "message": str(e), "handled": False}

    async def _validate_error_handling(
        self, error_result: Dict[str, Any], scenario: Dict[str, Any]
    ) -> bool:
        """Validate error handling result."""
        try:
            if not error_result:
                return False

            # Check that error was handled
            if not error_result.get("handled", False):
                logger.error(f"Error not handled for scenario: {scenario['type']}")
                return False

            # Check that appropriate recovery mechanisms were used
            error_type = scenario["type"]

            if error_type == "network_timeout":
                if not error_result.get("retry_attempted", False):
                    logger.error("Network timeout should trigger retry")
                    return False

            elif error_type == "database_error":
                if not error_result.get("fallback_used", False):
                    logger.error("Database error should use fallback")
                    return False

            elif error_type == "stripe_api_error":
                if not error_result.get("retry_scheduled", False):
                    logger.error("Stripe API error should schedule retry")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating error handling: {e}")
            return False

    def _record_test_success(self, test_name: str, message: str):
        """Record a successful test."""
        self.test_results["total_tests"] += 1
        self.test_results["passed_tests"] += 1
        self.test_results["test_details"].append(
            {
                "test": test_name,
                "status": "PASSED",
                "message": message,
                "timestamp": datetime.utcnow().isoformat(),
            }
        )
        logger.info(f"✅ {test_name}: {message}")

    def _record_test_failure(self, test_name: str, error_message: str):
        """Record a failed test."""
        self.test_results["total_tests"] += 1
        self.test_results["failed_tests"] += 1
        self.test_results["test_details"].append(
            {
                "test": test_name,
                "status": "FAILED",
                "error": error_message,
                "timestamp": datetime.utcnow().isoformat(),
            }
        )
        self.test_results["error_summary"].append(f"{test_name}: {error_message}")
        logger.error(f"❌ {test_name}: {error_message}")

    async def _generate_test_report(self):
        """Generate comprehensive test report."""
        success_rate = (
            (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
            if self.test_results["total_tests"] > 0
            else 0
        )

        report = f"""
# Multi-Country Subscription Test Report

## Test Summary
- **Total Tests**: {self.test_results["total_tests"]}
- **Passed**: {self.test_results["passed_tests"]}
- **Failed**: {self.test_results["failed_tests"]}
- **Success Rate**: {success_rate:.1f}%

## Regional Results
{json.dumps(self.test_results["regional_results"], indent=2)}

## Currency Results
{json.dumps(self.test_results["currency_results"], indent=2)}

## Test Details
"""

        for test in self.test_results["test_details"]:
            status_emoji = "✅" if test["status"] == "PASSED" else "❌"
            report += f"- {status_emoji} **{test['test']}**: {test.get('message', test.get('error', 'No details'))}\n"

        if self.test_results["error_summary"]:
            report += "\n## Error Summary\n"
            for error in self.test_results["error_summary"]:
                report += f"- {error}\n"

        # Save report
        with open("multi_country_subscription_test_results.md", "w") as f:
            f.write(report)

        # Save detailed results
        with open("multi_country_subscription_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2, default=str)

        logger.info(f"📄 Test report saved: {success_rate:.1f}% success rate")


async def main():
    """Main test execution function."""
    logger.info("🧪 Starting Multi-Country Subscription Tests...")

    tester = MultiCountrySubscriptionTester()
    results = await tester.run_comprehensive_tests()

    # Print summary
    success_rate = (
        (results["passed_tests"] / results["total_tests"]) * 100
        if results["total_tests"] > 0
        else 0
    )

    print(f"\n{'='*60}")
    print(f"MULTI-COUNTRY SUBSCRIPTION TEST RESULTS")
    print(f"{'='*60}")
    print(f"Total Tests: {results['total_tests']}")
    print(f"Passed: {results['passed_tests']}")
    print(f"Failed: {results['failed_tests']}")
    print(f"Success Rate: {success_rate:.1f}%")

    if results["failed_tests"] > 0:
        print(f"\n❌ FAILED TESTS:")
        for error in results["error_summary"]:
            print(f"  - {error}")
    else:
        print(f"\n✅ ALL TESTS PASSED!")

    print(
        f"\n📄 Detailed results saved to multi_country_subscription_test_results.json"
    )
    print(f"📄 Test report saved to multi_country_subscription_test_results.md")


if __name__ == "__main__":
    asyncio.run(main())
