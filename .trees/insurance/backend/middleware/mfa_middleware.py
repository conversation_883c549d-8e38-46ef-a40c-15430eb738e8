"""
Multi-Factor Authentication (MFA) Middleware

This middleware enforces MFA requirements for superadmin users and validates
MFA sessions for protected endpoints.
"""

import os
import time
from typing import Callable, Awaitable, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import logging

from backend.services.mfa_service import get_mfa_service
from backend.middleware.auth_middleware import UserContext

logger = logging.getLogger(__name__)


class MFAMiddleware:
    """Middleware to enforce MFA for superadmin operations."""

    def __init__(self):
        self.mfa_exempt_paths = {
            "/api/auth/mfa/status",
            "/api/auth/mfa/setup/totp",
            "/api/auth/mfa/setup/verify",
            "/api/auth/mfa/verify",
            "/api/auth/mfa/challenge",
            "/api/auth/mfa/factors",
            "/api/auth/mfa/sessions",
            "/api/auth/mfa/attempts",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/api/health",
        }

        # MFA session timeout in seconds (8 hours default)
        self.mfa_session_timeout = int(os.getenv("MFA_SESSION_TIMEOUT", "28800"))

    async def __call__(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """Process request and enforce MFA if required."""

        # Skip MFA check for exempt paths
        if self._is_exempt_path(request.url.path):
            return await call_next(request)

        # Skip MFA check for non-superadmin endpoints
        if not self._is_superadmin_endpoint(request.url.path):
            return await call_next(request)

        # Get user context from request state (set by auth middleware)
        user_context = getattr(request.state, "user", None)
        if not user_context or not isinstance(user_context, UserContext):
            return await call_next(request)

        # Check if user is superadmin
        if not self._is_superadmin_user(user_context.email):
            return await call_next(request)

        # Check MFA requirements
        try:
            mfa_service = get_mfa_service()
            mfa_status = await mfa_service.get_mfa_status(user_context.user_id)

            # If MFA is required but not configured, require setup
            if mfa_status.mfa_required and not mfa_status.mfa_configured:
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "detail": "MFA setup required",
                        "error_code": "MFA_SETUP_REQUIRED",
                        "setup_url": "/api/auth/mfa/setup/totp",
                    },
                )

            # If MFA is configured, check for valid session
            if mfa_status.mfa_configured:
                mfa_session_token = self._get_mfa_session_token(request)

                if not mfa_session_token:
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={
                            "detail": "MFA verification required",
                            "error_code": "MFA_VERIFICATION_REQUIRED",
                            "verify_url": "/api/auth/mfa/verify",
                        },
                    )

                # Validate MFA session
                session_valid = await mfa_service.validate_mfa_session(
                    user_context.user_id, mfa_session_token
                )

                if not session_valid:
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={
                            "detail": "MFA session expired or invalid",
                            "error_code": "MFA_SESSION_INVALID",
                            "verify_url": "/api/auth/mfa/verify",
                        },
                    )

                # Store MFA session info in request state
                request.state.mfa_verified = True
                request.state.mfa_session_token = mfa_session_token

        except Exception as e:
            logger.error(f"Error checking MFA requirements: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "detail": "MFA verification error",
                    "error_code": "MFA_VERIFICATION_ERROR",
                },
            )

        # Proceed with the request
        return await call_next(request)

    def _is_exempt_path(self, path: str) -> bool:
        """Check if the path is exempt from MFA requirements."""
        # Exact path matches
        if path in self.mfa_exempt_paths:
            return True

        # Pattern matches
        exempt_patterns = [
            "/static/",
            "/assets/",
            "/favicon.ico",
            "/api/webhooks/",  # Webhook endpoints
            "/api/public/",  # Public API endpoints
        ]

        for pattern in exempt_patterns:
            if path.startswith(pattern):
                return True

        return False

    def _is_superadmin_endpoint(self, path: str) -> bool:
        """Check if the path requires superadmin access."""
        superadmin_patterns = [
            "/api/superadmin/",
            "/api/admin/",
            "/superadmin/",
            "/admin/",
        ]

        for pattern in superadmin_patterns:
            if path.startswith(pattern):
                return True

        return False

    def _is_superadmin_user(self, email: str) -> bool:
        """Check if the user is a superadmin."""
        super_admin_emails = os.getenv("SUPER_ADMIN_EMAILS", "").split(",")
        super_admin_emails = [
            email.strip() for email in super_admin_emails if email.strip()
        ]

        return email in super_admin_emails

    def _get_mfa_session_token(self, request: Request) -> Optional[str]:
        """Extract MFA session token from request."""
        # Check for MFA session token in headers
        mfa_token = request.headers.get("X-MFA-Session-Token")
        if mfa_token:
            return mfa_token

        # Check for MFA session token in cookies
        mfa_token = request.cookies.get("mfa_session_token")
        if mfa_token:
            return mfa_token

        # Check for MFA session token in session storage (if available)
        session = getattr(request.state, "session", None)
        if session:
            mfa_token = session.get("mfa_session_token")
            if mfa_token:
                return mfa_token

        return None


async def require_mfa_verification(request: Request) -> bool:
    """
    Dependency function to require MFA verification.

    This can be used as a FastAPI dependency for endpoints that require MFA.
    """
    mfa_verified = getattr(request.state, "mfa_verified", False)

    if not mfa_verified:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="MFA verification required"
        )

    return True


async def get_mfa_session_token(request: Request) -> Optional[str]:
    """
    Dependency function to get the current MFA session token.

    Returns None if no valid MFA session exists.
    """
    return getattr(request.state, "mfa_session_token", None)


def require_super_admin_with_mfa():
    """
    Enhanced superadmin dependency that includes MFA verification.

    This combines the existing superadmin check with MFA requirements.
    """

    async def dependency(
        request: Request,
        user_context: UserContext = None,  # This would be injected by auth middleware
    ) -> UserContext:
        # The MFA middleware will have already validated MFA requirements
        # This dependency just ensures the user context is available
        if not user_context:
            user_context = getattr(request.state, "user", None)

        if not user_context:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
            )

        # Check if MFA was verified (set by MFA middleware)
        mfa_verified = getattr(request.state, "mfa_verified", False)
        if not mfa_verified:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="MFA verification required",
            )

        return user_context

    return dependency


# Global MFA middleware instance
mfa_middleware = MFAMiddleware()
