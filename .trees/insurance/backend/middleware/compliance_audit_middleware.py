"""
Compliance Audit Middleware

This middleware automatically logs compliance events for various operations
including data access, consent changes, retention actions, and security events.
"""

import asyncio
import hashlib
import json
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from backend.services.comprehensive_compliance_audit import (
    comprehensive_audit_service,
    ComplianceEventType,
    ComplianceFramework,
    ComplianceSeverity,
    log_data_retention_event,
    log_consent_management_event,
    log_data_residency_event,
    log_regional_disclaimer_event,
    log_security_event,
)
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class ComplianceAuditMiddleware(BaseHTTPMiddleware):
    """
    Middleware to automatically log compliance events for API requests.

    This middleware intercepts API requests and automatically logs relevant
    compliance events based on the request path, method, and response status.
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.sensitive_paths = {
            # Regional disclaimers paths (must come before /api/regional)
            "/api/regional-disclaimers": ComplianceFramework.REGIONAL_DISCLAIMERS,
            "/api/disclaimers": ComplianceFramework.REGIONAL_DISCLAIMERS,
            # Data retention paths
            "/api/data-retention": ComplianceFramework.DATA_RETENTION,
            "/api/retention": ComplianceFramework.DATA_RETENTION,
            # Consent management paths
            "/api/consent": ComplianceFramework.CONSENT_MANAGEMENT,
            "/api/privacy": ComplianceFramework.CONSENT_MANAGEMENT,
            # Data residency paths
            "/api/data-residency": ComplianceFramework.DATA_RESIDENCY,
            "/api/regional": ComplianceFramework.DATA_RESIDENCY,
            # Authentication and security paths
            "/api/auth": ComplianceFramework.SECURITY,
            "/api/admin": ComplianceFramework.SECURITY,
            "/api/superadmin": ComplianceFramework.SECURITY,
            # Professional responsibility paths
            "/api/cases": ComplianceFramework.PROFESSIONAL_RESPONSIBILITY,
            "/api/clients": ComplianceFramework.PROFESSIONAL_RESPONSIBILITY,
            "/api/documents": ComplianceFramework.PROFESSIONAL_RESPONSIBILITY,
        }

        self.high_risk_operations = {
            "DELETE": ComplianceSeverity.HIGH,
            "PUT": ComplianceSeverity.MEDIUM,
            "PATCH": ComplianceSeverity.MEDIUM,
            "POST": ComplianceSeverity.LOW,
            "GET": ComplianceSeverity.INFO,
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and log compliance events.
        """
        start_time = datetime.now()

        # Extract request information
        path = request.url.path
        method = request.method
        user_id = self._extract_user_id(request)
        tenant_id = self._extract_tenant_id(request)
        region = self._extract_region(request)
        ip_address = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")

        # Hash sensitive information
        ip_hash = self._hash_value(ip_address) if ip_address else None
        user_agent_hash = self._hash_value(user_agent) if user_agent else None

        # Determine if this is a compliance-relevant request
        framework = self._get_compliance_framework(path)

        # Process the request
        response = await call_next(request)

        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()

        # Log compliance event if relevant
        if framework:
            await self._log_request_event(
                framework=framework,
                path=path,
                method=method,
                status_code=response.status_code,
                user_id=user_id,
                tenant_id=tenant_id,
                region=region,
                ip_hash=ip_hash,
                user_agent_hash=user_agent_hash,
                processing_time=processing_time,
            )

        # Log security events for failed requests
        if response.status_code >= 400:
            await self._log_security_event(
                path=path,
                method=method,
                status_code=response.status_code,
                user_id=user_id,
                tenant_id=tenant_id,
                region=region,
                ip_hash=ip_hash,
                user_agent_hash=user_agent_hash,
            )

        return response

    def _extract_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from request context."""
        try:
            # Try to get from request state (set by auth middleware)
            if hasattr(request.state, "user") and request.state.user:
                return str(request.state.user.id)

            # Try to get from headers
            auth_header = request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                # This would need JWT decoding logic
                # For now, return None and let the auth middleware handle it
                pass

            return None
        except Exception:
            return None

    def _extract_tenant_id(self, request: Request) -> Optional[str]:
        """Extract tenant ID from request context."""
        try:
            # Try to get from request state
            if hasattr(request.state, "tenant_id"):
                return str(request.state.tenant_id)

            # Try to get from headers
            return request.headers.get("x-tenant-id")
        except Exception:
            return None

    def _extract_region(self, request: Request) -> Optional[str]:
        """Extract region from request context."""
        try:
            # Try to get from request state
            if hasattr(request.state, "region"):
                return str(request.state.region)

            # Try to get from headers
            return request.headers.get("x-region")
        except Exception:
            return None

    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Get client IP address from request."""
        try:
            # Check for forwarded headers first
            forwarded_for = request.headers.get("x-forwarded-for")
            if forwarded_for:
                return forwarded_for.split(",")[0].strip()

            # Check for real IP header
            real_ip = request.headers.get("x-real-ip")
            if real_ip:
                return real_ip

            # Fall back to client host
            if request.client:
                return request.client.host

            return None
        except Exception:
            return None

    def _hash_value(self, value: str) -> str:
        """Hash a value for privacy protection."""
        return hashlib.sha256(value.encode()).hexdigest()

    def _get_compliance_framework(self, path: str) -> Optional[ComplianceFramework]:
        """Determine the compliance framework for a given path."""
        for path_prefix, framework in self.sensitive_paths.items():
            if path.startswith(path_prefix):
                return framework
        return None

    async def _log_request_event(
        self,
        framework: ComplianceFramework,
        path: str,
        method: str,
        status_code: int,
        user_id: Optional[str],
        tenant_id: Optional[str],
        region: Optional[str],
        ip_hash: Optional[str],
        user_agent_hash: Optional[str],
        processing_time: float,
    ):
        """Log a compliance event for the request."""
        try:
            # Determine event type based on method and path
            event_type = self._get_event_type(framework, method, path)

            # Determine severity based on method and status
            severity = self._get_severity(method, status_code)

            # Determine compliance status
            compliance_status = "COMPLIANT" if status_code < 400 else "VIOLATION"

            # Create metadata
            metadata = {
                "http_method": method,
                "request_path": path,
                "status_code": status_code,
                "processing_time_seconds": processing_time,
                "ip_address_hash": ip_hash,
                "user_agent_hash": user_agent_hash,
            }

            # Log the event
            await comprehensive_audit_service.log_compliance_event(
                event_type=event_type,
                framework=framework,
                severity=severity,
                user_id=user_id,
                tenant_id=tenant_id,
                region=region,
                action=f"{method}_{path.split('/')[-1] if '/' in path else path}",
                outcome="SUCCESS" if status_code < 400 else "FAILED",
                compliance_status=compliance_status,
                metadata=metadata,
            )

        except Exception as e:
            logger.error(f"Error logging compliance event: {e}")

    async def _log_security_event(
        self,
        path: str,
        method: str,
        status_code: int,
        user_id: Optional[str],
        tenant_id: Optional[str],
        region: Optional[str],
        ip_hash: Optional[str],
        user_agent_hash: Optional[str],
    ):
        """Log a security event for failed requests."""
        try:
            # Determine event type based on status code
            if status_code == 401:
                event_type = ComplianceEventType.AUTHENTICATION_EVENT
            elif status_code == 403:
                event_type = ComplianceEventType.ACCESS_DENIED
            elif status_code >= 500:
                event_type = ComplianceEventType.SECURITY_VIOLATION
            else:
                return  # Don't log other 4xx errors as security events

            # Determine severity
            severity = (
                ComplianceSeverity.HIGH
                if status_code in [401, 403]
                else ComplianceSeverity.MEDIUM
            )

            # Create metadata
            metadata = {
                "http_method": method,
                "request_path": path,
                "status_code": status_code,
                "ip_address_hash": ip_hash,
                "user_agent_hash": user_agent_hash,
                "security_event": True,
            }

            # Log the security event
            await log_security_event(
                event_type=event_type,
                user_id=user_id,
                region=region,
                security_level="HIGH" if status_code in [401, 403] else "MEDIUM",
                metadata=metadata,
                action=f"failed_{method.lower()}",
                outcome="FAILED",
                compliance_status="VIOLATION",
                requires_review=status_code in [401, 403],
            )

        except Exception as e:
            logger.error(f"Error logging security event: {e}")

    def _get_event_type(
        self, framework: ComplianceFramework, method: str, path: str
    ) -> ComplianceEventType:
        """Determine the event type based on framework, method, and path."""
        if framework == ComplianceFramework.DATA_RETENTION:
            if "cleanup" in path or method == "DELETE":
                return ComplianceEventType.DATA_CLEANUP_EXECUTED
            elif "policy" in path:
                return ComplianceEventType.RETENTION_POLICY_APPLIED
            else:
                return ComplianceEventType.RETENTION_POLICY_APPLIED

        elif framework == ComplianceFramework.CONSENT_MANAGEMENT:
            if "withdraw" in path or method == "DELETE":
                return ComplianceEventType.CONSENT_WITHDRAWN
            elif method in ["POST", "PUT", "PATCH"]:
                return ComplianceEventType.CONSENT_GRANTED
            else:
                return ComplianceEventType.CONSENT_UPDATED

        elif framework == ComplianceFramework.DATA_RESIDENCY:
            if "transfer" in path:
                return ComplianceEventType.CROSS_BORDER_TRANSFER
            elif "preference" in path:
                return ComplianceEventType.RESIDENCY_PREFERENCE_CHANGED
            else:
                return ComplianceEventType.REGION_ROUTING

        elif framework == ComplianceFramework.REGIONAL_DISCLAIMERS:
            if "acknowledge" in path:
                return ComplianceEventType.DISCLAIMER_ACKNOWLEDGED
            elif method in ["POST", "PUT", "PATCH"]:
                return ComplianceEventType.DISCLAIMER_UPDATED
            else:
                return ComplianceEventType.DISCLAIMER_DISPLAYED

        elif framework == ComplianceFramework.SECURITY:
            if "auth" in path:
                return ComplianceEventType.AUTHENTICATION_EVENT
            elif "admin" in path:
                return ComplianceEventType.AUTHORIZATION_EVENT
            else:
                return ComplianceEventType.SECURITY_VIOLATION

        elif framework == ComplianceFramework.PROFESSIONAL_RESPONSIBILITY:
            if "client" in path:
                return ComplianceEventType.ATTORNEY_CLIENT_PRIVILEGE_ACCESS
            elif "conflict" in path:
                return ComplianceEventType.CONFLICT_CHECK_PERFORMED
            else:
                return ComplianceEventType.PROFESSIONAL_DISCLAIMER_SHOWN

        # Default fallback
        return ComplianceEventType.COMPLIANCE_VIOLATION

    def _get_severity(self, method: str, status_code: int) -> ComplianceSeverity:
        """Determine severity based on method and status code."""
        if status_code >= 500:
            return ComplianceSeverity.CRITICAL
        elif status_code in [401, 403]:
            return ComplianceSeverity.HIGH
        elif status_code >= 400:
            return ComplianceSeverity.MEDIUM
        else:
            return self.high_risk_operations.get(method, ComplianceSeverity.INFO)


# Middleware factory function
def create_compliance_audit_middleware() -> ComplianceAuditMiddleware:
    """Create and configure the compliance audit middleware."""
    return ComplianceAuditMiddleware
