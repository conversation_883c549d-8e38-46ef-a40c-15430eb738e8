"""
Payment Rate Limiting Middleware

Applies production-grade rate limiting to payment endpoints with proper
error handling and monitoring integration.
"""

import asyncio
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from services.payment_rate_limiter import payment_rate_limiter, RateLimitType
from backend.utils.logging import get_logger

logger = get_logger(__name__)


class PaymentRateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware to apply rate limiting to payment endpoints."""

    # Map URL patterns to rate limit types
    ENDPOINT_RATE_LIMITS = {
        "/api/billing/checkout": RateLimitType.CHECKOUT_SESSION,
        "/api/payment-methods": RateLimitType.PAYMENT_METHOD,
        "/api/webhooks/stripe": RateLimitType.WEBHOOK,
        "/api/subscription": RateLimitType.SUBSCRIPTION_CHANGE,
        "/api/billing/refund": RateLimitType.REFUND,
    }

    def __init__(self, app, enabled: bool = True):
        super().__init__(app)
        self.enabled = enabled

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting to payment endpoints."""
        if not self.enabled:
            return await call_next(request)

        # Check if this is a payment endpoint
        rate_limit_type = self._get_rate_limit_type(request.url.path)

        if rate_limit_type is None:
            # Not a payment endpoint, proceed without rate limiting
            return await call_next(request)

        try:
            # Extract identifiers for rate limiting
            identifier = await self._extract_identifier(request)
            tenant_id = await self._extract_tenant_id(request)
            user_id = await self._extract_user_id(request)
            ip_address = self._get_client_ip(request)

            # Check rate limit
            result = await payment_rate_limiter.check_rate_limit(
                limit_type=rate_limit_type,
                identifier=identifier,
                tenant_id=tenant_id,
                user_id=user_id,
                ip_address=ip_address,
            )

            if not result.allowed:
                # Rate limit exceeded
                logger.warning(
                    f"Rate limit exceeded for {rate_limit_type.value}: "
                    f"identifier={identifier}, tenant_id={tenant_id}, "
                    f"user_id={user_id}, ip={ip_address}"
                )

                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "message": f"Too many {rate_limit_type.value} requests. Please try again later.",
                        "retry_after": result.retry_after,
                        "limit_type": result.limit_type,
                    },
                    headers=result.to_headers(),
                )

            # Rate limit passed, proceed with request
            response = await call_next(request)

            # Add rate limit headers to response
            for header_name, header_value in result.to_headers().items():
                response.headers[header_name] = header_value

            return response

        except Exception as e:
            logger.error(f"Rate limiting middleware error: {e}", exc_info=True)
            # Fail open - proceed with request if rate limiting fails
            return await call_next(request)

    def _get_rate_limit_type(self, path: str) -> Optional[RateLimitType]:
        """Determine the rate limit type for a given path."""
        for endpoint_pattern, limit_type in self.ENDPOINT_RATE_LIMITS.items():
            if path.startswith(endpoint_pattern):
                return limit_type
        return None

    async def _extract_identifier(self, request: Request) -> str:
        """Extract primary identifier for rate limiting."""
        # Try to get user ID from session/auth
        user_id = await self._extract_user_id(request)
        if user_id:
            return f"user:{user_id}"

        # Fall back to IP address
        ip_address = self._get_client_ip(request)
        return f"ip:{ip_address}"

    async def _extract_tenant_id(self, request: Request) -> Optional[str]:
        """Extract tenant ID from request."""
        try:
            # Try to get from headers first
            tenant_id = request.headers.get("X-Tenant-ID")
            if tenant_id:
                return tenant_id

            # Try to get from auth session
            # This would need to be implemented based on your auth system
            # For now, return None
            return None

        except Exception as e:
            logger.debug(f"Could not extract tenant ID: {e}")
            return None

    async def _extract_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from request."""
        try:
            # Try to get from headers first
            user_id = request.headers.get("X-User-ID")
            if user_id:
                return user_id

            # Try to get from auth session
            # This would need to be implemented based on your auth system
            # For now, return None
            return None

        except Exception as e:
            logger.debug(f"Could not extract user ID: {e}")
            return None

    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (common in production behind proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"


def create_payment_rate_limit_middleware(enabled: bool = True):
    """Factory function to create payment rate limit middleware."""

    def middleware_factory(app):
        return PaymentRateLimitMiddleware(app, enabled=enabled)

    return middleware_factory


# Decorator for applying rate limiting to specific endpoints
def rate_limit(limit_type: RateLimitType):
    """Decorator to apply rate limiting to specific endpoint functions."""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            # This would need to be implemented based on your framework
            # For FastAPI, you'd typically use dependencies instead
            return await func(*args, **kwargs)

        return wrapper

    return decorator


# Utility functions for manual rate limit checks
async def check_payment_rate_limit(
    request: Request, limit_type: RateLimitType, identifier: Optional[str] = None
) -> bool:
    """
    Manually check rate limit for a payment operation.

    Returns True if request is allowed, False if rate limited.
    """
    try:
        if identifier is None:
            # Extract identifier from request
            user_id = request.headers.get("X-User-ID")
            if user_id:
                identifier = f"user:{user_id}"
            else:
                ip_address = (
                    request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
                )
                if not ip_address and hasattr(request, "client"):
                    ip_address = request.client.host
                identifier = f"ip:{ip_address}"

        tenant_id = request.headers.get("X-Tenant-ID")
        user_id = request.headers.get("X-User-ID")
        ip_address = request.headers.get("X-Forwarded-For", "").split(",")[0].strip()

        result = await payment_rate_limiter.check_rate_limit(
            limit_type=limit_type,
            identifier=identifier,
            tenant_id=tenant_id,
            user_id=user_id,
            ip_address=ip_address,
        )

        return result.allowed

    except Exception as e:
        logger.error(f"Manual rate limit check failed: {e}", exc_info=True)
        # Fail open
        return True


async def get_rate_limit_headers(
    request: Request, limit_type: RateLimitType, identifier: Optional[str] = None
) -> dict:
    """Get rate limit headers for a request without incrementing counters."""
    try:
        if identifier is None:
            user_id = request.headers.get("X-User-ID")
            if user_id:
                identifier = f"user:{user_id}"
            else:
                ip_address = (
                    request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
                )
                if not ip_address and hasattr(request, "client"):
                    ip_address = request.client.host
                identifier = f"ip:{ip_address}"

        status = await payment_rate_limiter.get_rate_limit_status(
            limit_type=limit_type, identifier=identifier
        )

        if "error" in status:
            return {}

        # Convert status to headers
        normal = status.get("normal", {})
        return {
            "X-RateLimit-Limit": str(normal.get("limit", 0)),
            "X-RateLimit-Remaining": str(normal.get("remaining", 0)),
            "X-RateLimit-Reset": str(normal.get("reset_time", 0)),
            "X-RateLimit-Type": status.get("limit_type", ""),
        }

    except Exception as e:
        logger.error(f"Failed to get rate limit headers: {e}", exc_info=True)
        return {}
