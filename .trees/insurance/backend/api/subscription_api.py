"""
Subscription API router.

This module provides API endpoints for subscription and feature management.
"""

import logging
from typing import Set
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from backend.api.dependencies.auth import get_current_tenant
from backend.api.dependencies.authorization import require_permission, Permission
from backend.api.schemas.subscription import ErrorResponse, FeatureListResponse
from backend.db.session import get_db_session
from backend.models.subscription import (
    SubscriptionAddon,
    SubscriptionPlan,
    TenantAddon,
    TenantSubscription,
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/tenant", tags=["subscription"])


async def get_tenant_features(tenant_id: UUID, db: AsyncSession) -> Set[str]:
    """
    Get the list of enabled features for a tenant.

    This function queries the database to find the tenant's active subscription
    and any active addons, then combines their features into a single set.

    Args:
        tenant_id: The tenant UUID
        db: Database session

    Returns:
        Set[str]: Set of enabled feature codes

    Raises:
        HTTPException: If no active subscription is found
    """
    # Get active subscription for tenant
    subscription_query = select(TenantSubscription).where(
        TenantSubscription.tenant_id == tenant_id, TenantSubscription.status == "active"
    )
    subscription_result = await db.execute(subscription_query)
    subscription = subscription_result.scalar_one_or_none()

    if not subscription:
        logger.warning(f"No active subscription found for tenant {tenant_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found for tenant",
        )

    # Get the subscription plan
    plan_query = select(SubscriptionPlan).where(
        SubscriptionPlan.id == subscription.plan_id, SubscriptionPlan.is_active == True
    )
    plan_result = await db.execute(plan_query)
    plan = plan_result.scalar_one_or_none()

    if not plan:
        logger.error(f"Subscription plan {subscription.plan_id} not found or inactive")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription plan not found or inactive",
        )

    # Start with base plan features
    features = set()

    # Extract features from plan
    if plan.features and isinstance(plan.features, dict):
        # Look for feature flags in the plan features
        for key, value in plan.features.items():
            if key.startswith("feature_") and value:
                # Remove "feature_" prefix to get the feature code
                feature_code = key.replace("feature_", "")
                features.add(feature_code)
            elif key == "features" and isinstance(value, list):
                # Handle case where features are stored as a list
                features.update(value)

    # Get active addons for the tenant
    addons_query = select(TenantAddon).where(
        TenantAddon.tenant_id == tenant_id,
        TenantAddon.subscription_id == subscription.id,
        TenantAddon.status == "active",
    )
    addons_result = await db.execute(addons_query)
    tenant_addons = addons_result.scalars().all()

    # Get addon details and add their features
    if tenant_addons:
        addon_ids = [addon.addon_id for addon in tenant_addons]
        addon_details_query = select(SubscriptionAddon).where(
            SubscriptionAddon.id.in_(addon_ids), SubscriptionAddon.is_active == True
        )
        addon_details_result = await db.execute(addon_details_query)
        addon_details = addon_details_result.scalars().all()

        for addon in addon_details:
            if addon.features and isinstance(addon.features, dict):
                for key, value in addon.features.items():
                    if key.startswith("feature_") and value:
                        feature_code = key.replace("feature_", "")
                        features.add(feature_code)
                    elif key == "features" and isinstance(value, list):
                        features.update(value)

    # Add some default features based on common subscription patterns
    # This ensures we always return some features even if the database
    # doesn't have the exact feature flag structure we expect
    if not features:
        # Fallback to basic features based on plan code
        plan_code = plan.code.lower()
        if "basic" in plan_code or "starter" in plan_code:
            features.update(["document_upload", "basic_search"])
        elif "professional" in plan_code or "pro" in plan_code:
            features.update(
                [
                    "document_upload",
                    "basic_search",
                    "ai_research",
                    "case_management",
                    "calendar_booking",
                ]
            )
        elif "enterprise" in plan_code:
            features.update(
                [
                    "document_upload",
                    "basic_search",
                    "ai_research",
                    "case_management",
                    "calendar_booking",
                    "voice_intake",
                    "advanced_analytics",
                    "custom_integrations",
                ]
            )
        else:
            # Default minimal features
            features.add("document_upload")

    logger.info(
        f"Retrieved {len(features)} features for tenant {tenant_id}: {features}"
    )
    return features


@router.get(
    "/{tenant_id}/features",
    response_model=FeatureListResponse,
    responses={
        404: {
            "model": ErrorResponse,
            "description": "Tenant not found or no active subscription",
        },
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        400: {"model": ErrorResponse, "description": "Invalid tenant ID"},
    },
    summary="Get tenant features",
    description="""
    Get the list of enabled features for a specific tenant based on their active subscription plan and addons.
    
    This endpoint returns all feature codes that the tenant has access to, combining:
    - Features from their active subscription plan
    - Features from any active addons
    
    The response includes the tenant ID and a list of feature codes that can be used
    to control access to various parts of the application.
    """,
)
async def get_tenant_features_endpoint(
    tenant_id: UUID,
    current_tenant: UUID = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db_session),
) -> FeatureListResponse:
    """
    Get enabled features for a tenant.

    Args:
        tenant_id: The tenant ID from the URL path
        current_tenant: The authenticated tenant ID from headers
        db: Database session

    Returns:
        FeatureListResponse: The tenant ID and list of enabled features

    Raises:
        HTTPException: If tenant not found, unauthorized, or no active subscription
    """
    # Verify that the requested tenant matches the authenticated tenant
    # This ensures users can only access their own tenant's features
    if tenant_id != current_tenant:
        logger.warning(
            f"Tenant mismatch: requested {tenant_id}, authenticated {current_tenant}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: cannot access features for different tenant",
        )

    try:
        # Get features for the tenant
        features = await get_tenant_features(tenant_id, db)

        # Convert set to sorted list for consistent response
        feature_list = sorted(list(features))

        return FeatureListResponse(tenant_id=tenant_id, features=feature_list)

    except HTTPException:
        # Re-raise HTTP exceptions (like 404 for no subscription)
        raise
    except Exception as e:
        logger.error(f"Error retrieving features for tenant {tenant_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving features",
        )
