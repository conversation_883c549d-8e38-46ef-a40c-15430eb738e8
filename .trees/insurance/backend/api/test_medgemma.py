"""
Test API endpoint for MedGemma functionality

This endpoint demonstrates the MedGemma integration and allows testing
of the superadmin toggle functionality.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any
import logging

from backend.agents.interactive.research.multimodal_processor import (
    MultimodalDocumentProcessor,
)
from backend.agents.interactive.research.medgemma_utils import (
    get_medgemma_status,
    is_medgemma_enabled,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/test", tags=["testing"])


class MedGemmaStatusResponse(BaseModel):
    """Response model for MedGemma status."""

    status: Dict[str, Any]
    message: str


class TestMedicalAnalysisRequest(BaseModel):
    """Request model for testing medical analysis."""

    text: str
    use_medgemma: bool = True


class TestMedicalAnalysisResponse(BaseModel):
    """Response model for medical analysis test."""

    analysis: Dict[str, Any]
    processing_model: str
    medgemma_enabled: bool


@router.get("/medgemma/status", response_model=MedGemmaStatusResponse)
async def get_medgemma_status_endpoint():
    """
    Get MedGemma status and configuration.

    Returns comprehensive status information about MedGemma availability,
    configuration, and current settings.
    """
    try:
        status = get_medgemma_status()

        if status["ready"]:
            message = "MedGemma is enabled and ready for medical document processing"
        elif status["enabled"]:
            message = "MedGemma is enabled but missing dependencies (check Vertex AI configuration)"
        else:
            message = (
                "MedGemma is disabled - using Gemini fallback for medical analysis"
            )

        return MedGemmaStatusResponse(status=status, message=message)

    except Exception as e:
        logger.error(f"Error getting MedGemma status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get MedGemma status: {str(e)}"
        )


@router.post("/medgemma/analyze", response_model=TestMedicalAnalysisResponse)
async def analyze_medical_text(request: TestMedicalAnalysisRequest):
    """
    Test medical text analysis with MedGemma integration.

    This endpoint allows testing the medical analysis functionality
    and demonstrates the MedGemma vs Gemini fallback behavior.
    """
    try:
        # Initialize the processor
        processor = MultimodalDocumentProcessor()

        # Check if MedGemma is enabled
        medgemma_enabled = await is_medgemma_enabled()

        # Create a mock element for testing
        class MockElement:
            def __str__(self):
                return request.text

        mock_element = MockElement()

        # Perform medical analysis
        analysis = await processor._analyze_medical_element(mock_element)

        # Determine which model was actually used
        processing_model = analysis.get("processing_model", "unknown")

        return TestMedicalAnalysisResponse(
            analysis=analysis,
            processing_model=processing_model,
            medgemma_enabled=medgemma_enabled,
        )

    except Exception as e:
        logger.error(f"Error in medical analysis test: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Medical analysis failed: {str(e)}"
        )


@router.get("/medgemma/demo")
async def medgemma_demo():
    """
    Demo endpoint showing MedGemma capabilities.

    Returns example medical text analysis results demonstrating
    the difference between MedGemma and Gemini processing.
    """
    try:
        # Sample medical texts for demonstration
        sample_texts = [
            "Patient presents with acute chest pain, EKG shows ST elevation in leads II, III, aVF",
            "MRI brain shows multiple hyperintense lesions in white matter consistent with demyelination",
            "Pathology report: Invasive ductal carcinoma, grade 2, ER positive, PR positive, HER2 negative",
        ]

        processor = MultimodalDocumentProcessor()
        medgemma_enabled = await is_medgemma_enabled()

        results = []

        for text in sample_texts:

            class MockElement:
                def __str__(self):
                    return text

            mock_element = MockElement()
            analysis = await processor._analyze_medical_element(mock_element)

            results.append(
                {
                    "input_text": text,
                    "analysis": analysis,
                    "processing_model": analysis.get("processing_model", "unknown"),
                }
            )

        return {
            "medgemma_enabled": medgemma_enabled,
            "status": get_medgemma_status(),
            "sample_analyses": results,
            "message": f"Demo completed using {'MedGemma' if medgemma_enabled else 'Gemini fallback'}",
        }

    except Exception as e:
        logger.error(f"Error in MedGemma demo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Demo failed: {str(e)}")
