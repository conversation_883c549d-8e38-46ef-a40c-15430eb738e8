"""
API endpoints for the Multi-Practice Intake Agent

This module provides REST API endpoints for interacting with the
consolidated intake agent, supporting both client and staff interfaces.
"""

from typing import Dict, Any, Optional
from datetime import datetime
import logging

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from backend.agents.interactive.intake import (
    IntakeAgent,
    intake_router,
    intake_client_router,
    intake_staff_router,
    IntakeState,
    PracticeArea,
    WorkType,
    CaseUrgency,
)
from backend.api.dependencies.auth import get_current_user, get_tenant_context
from backend.db.session import get_db_session

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agents/intake", tags=["intake"])


class IntakeRequest(BaseModel):
    """Request model for intake operations."""

    mode: str = Field(..., description="Intake mode: 'client' or 'staff'")
    message: Optional[str] = Field(None, description="User message")
    state: Optional[Dict[str, Any]] = Field(None, description="Current intake state")
    practice_area: Optional[str] = Field(None, description="Pre-selected practice area")


class IntakeResponse(BaseModel):
    """Response model for intake operations."""

    success: bool
    state: Dict[str, Any]
    message: Optional[str] = None
    next_step: Optional[str] = None
    display_label: Optional[str] = None
    urgency: Optional[str] = None
    errors: Optional[list] = None


class ClassificationRequest(BaseModel):
    """Request model for case classification."""

    description: str = Field(..., description="Case/matter description")
    additional_context: Optional[Dict[str, Any]] = Field(
        None, description="Additional context"
    )


class ClassificationResponse(BaseModel):
    """Response model for intelligent matter classification."""

    practice_area: str
    work_type: str
    case_type: str
    urgency: str
    confidence: float
    reasoning: str
    keywords_matched: list
    display_label: str
    # Enhanced LLM insights
    key_factors: list
    timeline_factors: list
    parties_involved: list
    potential_damages: list
    complexity_indicators: list
    red_flags: list
    llm_reasoning: str
    fallback_used: bool


@router.post("/start", response_model=IntakeResponse)
async def start_intake(
    request: IntakeRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    tenant_context=Depends(get_tenant_context),
    db_session=Depends(get_db_session),
):
    """
    Start a new intake session.

    Args:
        request: Intake request with mode and optional parameters
        background_tasks: Background task queue
        current_user: Current authenticated user
        tenant_context: Tenant context
        db_session: Database session

    Returns:
        IntakeResponse with initial state and next steps
    """
    try:
        logger.info(
            f"Starting intake session for user {current_user.id} in {request.mode} mode"
        )

        # Initialize state
        initial_state = {
            "messages": [],
            "intake_mode": request.mode,
            "tenant_id": tenant_context.tenant_id,
            "user_id": current_user.id,
        }

        # Add initial message if provided
        if request.message:
            initial_state["messages"].append(
                {"type": "human", "content": request.message}
            )

        # Route based on mode
        config = {
            "configurable": {
                "tenant_id": tenant_context.tenant_id,
                "user_id": current_user.id,
                "user_role": getattr(current_user, "role", "client"),
            }
        }

        if request.mode == "client":
            state = await intake_client_router(initial_state, config)
        elif request.mode == "staff":
            state = await intake_staff_router(initial_state, config)
        else:
            state = await intake_router(initial_state, config)

        # Pre-select practice area if provided
        if request.practice_area:
            if "matter" not in state:
                state["matter"] = {}
            state["matter"]["practice_area"] = request.practice_area

        return IntakeResponse(
            success=True,
            state=state,
            next_step=state.get("next"),
            display_label=state.get("matter", {}).get("display_label"),
            urgency=state.get("matter", {}).get("urgency"),
        )

    except Exception as e:
        logger.error(f"Error starting intake: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start intake: {str(e)}")


@router.post("/continue", response_model=IntakeResponse)
async def continue_intake(
    request: IntakeRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    tenant_context=Depends(get_tenant_context),
    db_session=Depends(get_db_session),
):
    """
    Continue an existing intake session.

    Args:
        request: Intake request with current state and new message
        background_tasks: Background task queue
        current_user: Current authenticated user
        tenant_context: Tenant context
        db_session: Database session

    Returns:
        IntakeResponse with updated state
    """
    try:
        if not request.state:
            raise HTTPException(
                status_code=400, detail="State is required to continue intake"
            )

        logger.info(f"Continuing intake for user {current_user.id}")

        # Add new message to state
        if request.message:
            if "messages" not in request.state:
                request.state["messages"] = []
            request.state["messages"].append(
                {"type": "human", "content": request.message}
            )

        # Initialize and execute agent
        agent = IntakeAgent()

        config = {
            "configurable": {
                "tenant_id": tenant_context.tenant_id,
                "user_id": current_user.id,
                "user_role": getattr(current_user, "role", "client"),
            }
        }

        # Execute agent
        updated_state = await agent.execute(request.state, config)

        # Handle completion
        if updated_state.get("current_step") == "completed":
            background_tasks.add_task(
                _handle_intake_completion,
                updated_state,
                tenant_context.tenant_id,
                current_user.id,
            )

        return IntakeResponse(
            success=True,
            state=updated_state,
            next_step=updated_state.get("next"),
            display_label=updated_state.get("matter", {}).get("display_label"),
            urgency=updated_state.get("matter", {}).get("urgency"),
            errors=updated_state.get("validation_errors"),
        )

    except Exception as e:
        logger.error(f"Error continuing intake: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to continue intake: {str(e)}"
        )


@router.post("/classify", response_model=ClassificationResponse)
async def classify_matter(
    request: ClassificationRequest,
    current_user=Depends(get_current_user),
    tenant_context=Depends(get_tenant_context),
):
    """
    Classify a matter description into practice area and case type.

    Args:
        request: Classification request with description
        current_user: Current authenticated user
        tenant_context: Tenant context

    Returns:
        ClassificationResponse with classification results
    """
    try:
        logger.info(f"Classifying matter for user {current_user.id}")

        # Initialize agent and classify using intelligent classifier
        agent = IntakeAgent()
        result = await agent.classify_matter_from_description(
            request.description, request.additional_context
        )

        return ClassificationResponse(
            practice_area=result["practice_area"],
            work_type=result["work_type"],
            case_type=result["case_type"],
            urgency=result["urgency"],
            confidence=result["confidence"],
            reasoning=result["reasoning"],
            keywords_matched=result["keywords_matched"],
            display_label=result["display_label"],
            key_factors=result.get("key_factors", []),
            timeline_factors=result.get("timeline_factors", []),
            parties_involved=result.get("parties_involved", []),
            potential_damages=result.get("potential_damages", []),
            complexity_indicators=result.get("complexity_indicators", []),
            red_flags=result.get("red_flags", []),
            llm_reasoning=result.get("llm_reasoning", ""),
            fallback_used=result.get("fallback_used", False),
        )

    except Exception as e:
        logger.error(f"Error classifying matter: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to classify matter: {str(e)}"
        )


@router.get("/practice-areas")
async def get_practice_areas(current_user=Depends(get_current_user)):
    """
    Get supported practice areas.

    Returns:
        List of supported practice areas with metadata
    """
    try:
        agent = IntakeAgent()
        practice_areas = agent.get_supported_practice_areas()

        # Add metadata for each practice area
        areas_with_metadata = []
        for area in practice_areas:
            practice_area_enum = PracticeArea(area)
            work_type = agent.practice_area_configs.get(practice_area_enum, {}).get(
                "work_type", "litigation"
            )

            areas_with_metadata.append(
                {
                    "value": area,
                    "label": area.replace("_", " ").title(),
                    "work_type": work_type,
                    "display_label": "Case" if work_type == "litigation" else "Matter",
                    "description": _get_practice_area_description(area),
                }
            )

        return {"success": True, "practice_areas": areas_with_metadata}

    except Exception as e:
        logger.error(f"Error getting practice areas: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get practice areas: {str(e)}"
        )


@router.get("/workflow-steps")
async def get_workflow_steps(current_user=Depends(get_current_user)):
    """
    Get intake workflow steps.

    Returns:
        List of workflow steps with descriptions
    """
    try:
        agent = IntakeAgent()
        steps = agent.get_workflow_steps()

        steps_with_descriptions = []
        for step in steps:
            steps_with_descriptions.append(
                {
                    "value": step,
                    "label": step.replace("_", " ").title(),
                    "description": _get_step_description(step),
                }
            )

        return {"success": True, "workflow_steps": steps_with_descriptions}

    except Exception as e:
        logger.error(f"Error getting workflow steps: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get workflow steps: {str(e)}"
        )


async def _handle_intake_completion(
    state: Dict[str, Any], tenant_id: str, user_id: str
):
    """Handle intake completion in background."""
    try:
        logger.info(f"Processing completed intake for user {user_id}")

        # TODO: Implement actual client and matter creation
        # This would involve:
        # 1. Creating client record
        # 2. Creating matter record
        # 3. Sending confirmation emails
        # 4. Creating initial tasks
        # 5. Notifying relevant staff

        logger.info("Intake completion processing finished")

    except Exception as e:
        logger.error(f"Error processing intake completion: {str(e)}")


def _get_practice_area_description(area: str) -> str:
    """Get description for practice area."""
    descriptions = {
        "personal_injury": "Auto accidents, slip/fall, medical malpractice, product liability",
        "family_law": "Divorce, child custody, child support, adoption, domestic violence",
        "criminal_defense": "DUI/DWI, misdemeanors, felonies, traffic violations",
    }
    return descriptions.get(area, "")


def _get_step_description(step: str) -> str:
    """Get description for workflow step."""
    descriptions = {
        "initial_contact": "Welcome and gather basic client name",
        "collect_personal_info": "Collect email, phone, and contact information",
        "collect_case_details": "Gather case description and initial classification",
        "practice_area_details": "Collect practice-specific information",
        "check_conflicts": "Perform conflict of interest checking",
        "summarize_and_confirm": "Review collected information with client",
        "save_client_info": "Create client and matter records",
    }
    return descriptions.get(step, "")
