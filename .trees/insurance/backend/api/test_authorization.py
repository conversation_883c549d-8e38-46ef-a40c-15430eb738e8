#!/usr/bin/env python3
"""
Test script for authorization dependencies.

This script tests the role-based access control (RBAC) system to ensure
proper authorization checks are in place.
"""

import os
import sys
import uuid
from unittest.mock import Mock

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", ".."))

from backend.api.dependencies.authorization import (
    UserRole,
    Permission,
    ROLE_PERMISSIONS,
    has_permission,
    require_permission,
    require_role,
)
from backend.api.dependencies.auth import UserContext


def create_mock_user(role: str, email: str = "<EMAIL>") -> UserContext:
    """Create a mock user context for testing."""
    return UserContext(
        id=uuid.uuid4(),
        email=email,
        name="Test User",
        tenant_id=uuid.uuid4(),
        role=role,
    )


def test_role_permissions():
    """Test role-based permission matrix."""
    print("Testing role-based permission matrix...")

    # Test superadmin has all permissions
    superadmin = create_mock_user("superadmin")
    for permission in Permission:
        if not has_permission(superadmin, permission):
            print(f"❌ Superadmin should have {permission.value}")
            return False
    print("✅ Superadmin has all permissions")

    # Test partner permissions
    partner = create_mock_user("partner")
    expected_partner_perms = [
        Permission.MANAGE_TENANT,
        Permission.CREATE_USER,
        Permission.ADMIN_ACCESS,
        Permission.READ_METRICS,
    ]

    for perm in expected_partner_perms:
        if not has_permission(partner, perm):
            print(f"❌ Partner should have {perm.value}")
            return False

    # Partner should NOT have super admin access
    if has_permission(partner, Permission.SUPER_ADMIN_ACCESS):
        print("❌ Partner should NOT have super admin access")
        return False

    print("✅ Partner permissions correct")

    # Test client permissions (most restrictive)
    client = create_mock_user("client")
    allowed_client_perms = [Permission.READ_MATTER, Permission.READ_DOCUMENT]

    for perm in allowed_client_perms:
        if not has_permission(client, perm):
            print(f"❌ Client should have {perm.value}")
            return False

    # Client should NOT have admin access
    restricted_perms = [
        Permission.CREATE_TENANT,
        Permission.MANAGE_USERS,
        Permission.ADMIN_ACCESS,
        Permission.SUPER_ADMIN_ACCESS,
    ]

    for perm in restricted_perms:
        if has_permission(client, perm):
            print(f"❌ Client should NOT have {perm.value}")
            return False

    print("✅ Client permissions correct")

    assert True


def test_permission_checks():
    """Test individual permission checking functions."""
    print("\nTesting permission checking functions...")

    # Test valid role
    attorney = create_mock_user("attorney")

    # Attorney should have read matter permission
    if not has_permission(attorney, Permission.READ_MATTER):
        print("❌ Attorney should have READ_MATTER permission")
        return False

    # Attorney should NOT have tenant management permission
    if has_permission(attorney, Permission.MANAGE_TENANT):
        print("❌ Attorney should NOT have MANAGE_TENANT permission")
        return False

    print("✅ Attorney permission checks correct")

    # Test invalid role
    invalid_user = create_mock_user("invalid_role")

    # Invalid role should not have any permissions
    if has_permission(invalid_user, Permission.READ_MATTER):
        print("❌ Invalid role should not have any permissions")
        return False

    print("✅ Invalid role correctly denied permissions")

    assert True


def test_role_hierarchy():
    """Test that role hierarchy works correctly."""
    print("\nTesting role hierarchy...")

    roles_by_privilege = [
        ("client", UserRole.CLIENT),
        ("staff", UserRole.STAFF),
        ("paralegal", UserRole.PARALEGAL),
        ("attorney", UserRole.ATTORNEY),
        ("partner", UserRole.PARTNER),
        ("superadmin", UserRole.SUPERADMIN),
    ]

    # Higher privilege roles should have more or equal permissions
    for i in range(len(roles_by_privilege) - 1):
        lower_role_str, lower_role = roles_by_privilege[i]
        higher_role_str, higher_role = roles_by_privilege[i + 1]

        lower_perms = ROLE_PERMISSIONS.get(lower_role, set())
        higher_perms = ROLE_PERMISSIONS.get(higher_role, set())

        # Check that higher role has at least as many permissions
        if len(higher_perms) < len(lower_perms):
            print(
                f"❌ {higher_role_str} should have at least as many permissions as {lower_role_str}"
            )
            return False

    print("✅ Role hierarchy correct")
    assert True


def test_tenant_isolation():
    """Test that tenant isolation is maintained."""
    print("\nTesting tenant isolation...")

    tenant1_id = uuid.uuid4()
    tenant2_id = uuid.uuid4()

    user1 = UserContext(
        id=uuid.uuid4(),
        email="<EMAIL>",
        name="User 1",
        tenant_id=tenant1_id,
        role="attorney",
    )

    user2 = UserContext(
        id=uuid.uuid4(),
        email="<EMAIL>",
        name="User 2",
        tenant_id=tenant2_id,
        role="attorney",
    )

    # Both users should have same permissions within their tenants
    if not has_permission(user1, Permission.READ_MATTER):
        print("❌ User1 should have READ_MATTER permission")
        return False

    if not has_permission(user2, Permission.READ_MATTER):
        print("❌ User2 should have READ_MATTER permission")
        return False

    # Verify they have different tenant IDs
    if user1.tenant_id == user2.tenant_id:
        print("❌ Users should have different tenant IDs")
        return False

    print("✅ Tenant isolation maintained")
    assert True


def main():
    """Run all authorization tests."""
    print("🔐 Testing Authorization System")
    print("=" * 50)

    tests = [
        test_role_permissions,
        test_permission_checks,
        test_role_hierarchy,
        test_tenant_isolation,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")

    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All authorization tests passed!")
        return 0
    else:
        print("💥 Some authorization tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
