"""
Tenant Briefing Configuration API Routes

This module provides REST API endpoints for managing tenant-specific
briefing schedules and preferences.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import BaseModel, Field

from backend.api.dependencies.auth import get_current_user, get_current_tenant
from backend.api.dependencies.authorization import require_permission, Permission
from backend.db.supabase_client import get_supabase_client

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/tenant/briefing-config", tags=["tenant-briefing"])


# Pydantic models
class BriefingSchedule(BaseModel):
    """Model for briefing schedule configuration."""

    enabled: bool = Field(
        default=True, description="Whether morning briefings are enabled"
    )
    time: str = Field(default="08:00", description="Time in HH:MM format (24-hour)")
    timezone: str = Field(
        default="UTC", description="Timezone identifier (e.g., 'America/New_York')"
    )
    days_of_week: list[int] = Field(
        default=[1, 2, 3, 4, 5], description="Days of week (1=Monday, 7=Sunday)"
    )
    include_weekends: bool = Field(
        default=False, description="Include weekends in briefings"
    )


class TenantBriefingConfig(BaseModel):
    """Model for tenant briefing configuration."""

    tenant_id: str
    morning_briefing: BriefingSchedule
    insight_frequency_hours: int = Field(
        default=4, ge=1, le=24, description="Hours between insight generation"
    )
    auto_insights_enabled: bool = Field(
        default=True, description="Enable automatic insight generation"
    )
    user_return_threshold_hours: int = Field(
        default=1,
        ge=1,
        le=48,
        description="Hours away before triggering return insights",
    )
    created_at: str
    updated_at: str


@router.get("/", response_model=TenantBriefingConfig)
async def get_briefing_config(
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
    _: None = Depends(require_permission(Permission.READ_TENANT_CONFIG)),
):
    """
    Get the current tenant's briefing configuration.
    """
    try:
        tenant_id = current_tenant["id"]
        logger.info(f"Fetching briefing config for tenant {tenant_id}")

        supabase = get_supabase_client(use_service_role=True)

        # Try to get existing config
        config_response = (
            supabase.schema("tenants")
            .from_("briefing_config")
            .select("*")
            .eq("tenant_id", tenant_id)
            .single()
            .execute()
        )

        if config_response.data:
            config_data = config_response.data
            return TenantBriefingConfig(
                tenant_id=tenant_id,
                morning_briefing=BriefingSchedule(
                    **config_data.get("morning_briefing", {})
                ),
                insight_frequency_hours=config_data.get("insight_frequency_hours", 4),
                auto_insights_enabled=config_data.get("auto_insights_enabled", True),
                user_return_threshold_hours=config_data.get(
                    "user_return_threshold_hours", 1
                ),
                created_at=config_data["created_at"],
                updated_at=config_data["updated_at"],
            )
        else:
            # Return default configuration
            now = datetime.now(timezone.utc).isoformat()
            return TenantBriefingConfig(
                tenant_id=tenant_id,
                morning_briefing=BriefingSchedule(),
                insight_frequency_hours=4,
                auto_insights_enabled=True,
                user_return_threshold_hours=1,
                created_at=now,
                updated_at=now,
            )

    except Exception as e:
        logger.error(f"Error fetching briefing config: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch briefing configuration"
        )


@router.put("/")
async def update_briefing_config(
    config: TenantBriefingConfig,
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
    _: None = Depends(require_permission(Permission.WRITE_TENANT_CONFIG)),
):
    """
    Update the tenant's briefing configuration.
    """
    try:
        tenant_id = current_tenant["id"]
        user_id = current_user["id"]

        # Validate tenant_id matches
        if config.tenant_id != tenant_id:
            raise HTTPException(status_code=400, detail="Tenant ID mismatch")

        logger.info(f"Updating briefing config for tenant {tenant_id}")

        supabase = get_supabase_client(use_service_role=True)

        # Prepare config data
        config_data = {
            "tenant_id": tenant_id,
            "morning_briefing": config.morning_briefing.dict(),
            "insight_frequency_hours": config.insight_frequency_hours,
            "auto_insights_enabled": config.auto_insights_enabled,
            "user_return_threshold_hours": config.user_return_threshold_hours,
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "updated_by": user_id,
        }

        # Upsert configuration
        upsert_response = (
            supabase.schema("tenants")
            .from_("briefing_config")
            .upsert(config_data, on_conflict="tenant_id")
            .execute()
        )

        if not upsert_response.data:
            raise HTTPException(
                status_code=500, detail="Failed to update configuration"
            )

        # Log the configuration change
        supabase.schema("security").from_("events").insert(
            {
                "event_type": "tenant.briefing_config_updated",
                "event_category": "admin_action",
                "user_id": user_id,
                "details": {
                    "tenant_id": tenant_id,
                    "changes": config_data,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
                "created_at": datetime.now(timezone.utc).isoformat(),
            }
        ).execute()

        # TODO: Update Celery periodic tasks based on new configuration
        # This would require implementing dynamic task scheduling

        return JSONResponse(
            content={
                "success": True,
                "message": "Briefing configuration updated successfully",
                "config": config_data,
            }
        )

    except Exception as e:
        logger.error(f"Error updating briefing config: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to update briefing configuration"
        )


@router.get("/timezones")
async def get_supported_timezones(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Get list of supported timezones for briefing configuration.
    """
    try:
        # Common business timezones in the US
        timezones = [
            {
                "value": "America/New_York",
                "label": "Eastern Time (ET)",
                "offset": "UTC-5/-4",
            },
            {
                "value": "America/Chicago",
                "label": "Central Time (CT)",
                "offset": "UTC-6/-5",
            },
            {
                "value": "America/Denver",
                "label": "Mountain Time (MT)",
                "offset": "UTC-7/-6",
            },
            {
                "value": "America/Los_Angeles",
                "label": "Pacific Time (PT)",
                "offset": "UTC-8/-7",
            },
            {
                "value": "America/Anchorage",
                "label": "Alaska Time (AKT)",
                "offset": "UTC-9/-8",
            },
            {
                "value": "Pacific/Honolulu",
                "label": "Hawaii Time (HST)",
                "offset": "UTC-10",
            },
            {
                "value": "UTC",
                "label": "Coordinated Universal Time (UTC)",
                "offset": "UTC+0",
            },
        ]

        return JSONResponse(content={"timezones": timezones})

    except Exception as e:
        logger.error(f"Error fetching timezones: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch supported timezones"
        )


@router.post("/test-schedule")
async def test_briefing_schedule(
    schedule: BriefingSchedule,
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
    _: None = Depends(require_permission(Permission.WRITE_TENANT_CONFIG)),
):
    """
    Test a briefing schedule configuration to see when it would run.
    """
    try:
        import pytz
        from datetime import datetime, timedelta

        tenant_id = current_tenant["id"]
        logger.info(f"Testing briefing schedule for tenant {tenant_id}")

        # Parse timezone
        try:
            tz = pytz.timezone(schedule.timezone)
        except pytz.exceptions.UnknownTimeZoneError:
            raise HTTPException(
                status_code=400, detail=f"Unknown timezone: {schedule.timezone}"
            )

        # Parse time
        try:
            hour, minute = map(int, schedule.time.split(":"))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError("Invalid time format")
        except ValueError:
            raise HTTPException(
                status_code=400, detail="Invalid time format. Use HH:MM (24-hour)"
            )

        # Calculate next 7 occurrences
        now = datetime.now(tz)
        occurrences = []

        for i in range(14):  # Check next 14 days to get at least 7 occurrences
            check_date = now + timedelta(days=i)
            weekday = check_date.isoweekday()  # 1=Monday, 7=Sunday

            if weekday in schedule.days_of_week:
                scheduled_time = check_date.replace(
                    hour=hour, minute=minute, second=0, microsecond=0
                )

                # Only include future times
                if scheduled_time > now:
                    occurrences.append(
                        {
                            "local_time": scheduled_time.strftime("%Y-%m-%d %H:%M %Z"),
                            "utc_time": scheduled_time.astimezone(pytz.UTC).strftime(
                                "%Y-%m-%d %H:%M UTC"
                            ),
                            "day_of_week": scheduled_time.strftime("%A"),
                            "relative": (
                                f"in {(scheduled_time - now).days} days"
                                if (scheduled_time - now).days > 0
                                else "today"
                            ),
                        }
                    )

                if len(occurrences) >= 7:
                    break

        return JSONResponse(
            content={
                "success": True,
                "schedule": schedule.dict(),
                "next_occurrences": occurrences,
                "timezone_info": {
                    "name": schedule.timezone,
                    "current_time": now.strftime("%Y-%m-%d %H:%M %Z"),
                    "utc_offset": now.strftime("%z"),
                },
            }
        )

    except Exception as e:
        logger.error(f"Error testing briefing schedule: {e}")
        raise HTTPException(status_code=500, detail="Failed to test briefing schedule")
