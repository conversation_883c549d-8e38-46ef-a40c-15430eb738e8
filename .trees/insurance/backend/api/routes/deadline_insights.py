"""
Deadline Insights API Routes

This module provides REST API endpoints for deadline insights functionality,
including fetching insights data, triggering analysis, and managing deadline alerts.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from backend.api.dependencies.auth import get_current_user, get_current_tenant
from backend.api.dependencies.authorization import require_permission, Permission
from backend.db.supabase_client import get_supabase_client

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/deadline-insights", tags=["deadline-insights"])


# Pydantic models for request/response
class DeadlineInsightResponse(BaseModel):
    """Response model for deadline insights."""

    tenant_id: str
    analysis_type: str
    insights: Dict[str, Any]
    generated_at: str
    expires_at: str
    cache_hit: bool = False


class DeadlineInsightsSummary(BaseModel):
    """Summary model for deadline insights."""

    critical_deadlines: int = Field(description="Number of critical deadlines")
    high_risk_deadlines: int = Field(description="Number of high-risk deadlines")
    conflicts_detected: int = Field(description="Number of conflicts detected")
    recommendations: int = Field(description="Number of recommendations")
    last_updated: str = Field(description="Last update timestamp")


class TriggerInsightsRequest(BaseModel):
    """Request model for triggering insights generation."""

    analysis_type: str = Field(
        default="comprehensive", description="Type of analysis to perform"
    )
    matter_ids: Optional[List[str]] = Field(
        default=None, description="Specific matter IDs to analyze"
    )
    force_refresh: bool = Field(
        default=False, description="Force refresh even if cached data exists"
    )


@router.get("/summary", response_model=DeadlineInsightsSummary)
async def get_deadline_insights_summary(
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
):
    """
    Get a summary of deadline insights for the dashboard.

    Returns high-level metrics about deadlines, conflicts, and recommendations.
    """
    try:
        tenant_id = current_tenant["id"]
        logger.info(f"Fetching deadline insights summary for tenant {tenant_id}")

        # Get Supabase client
        supabase = get_supabase_client(use_service_role=True)

        # Fetch latest insights from database
        insights_response = (
            supabase.schema("tenants")
            .from_("deadline_insights")
            .select("insights, generated_at")
            .eq("tenant_id", tenant_id)
            .order("generated_at", desc=True)
            .limit(1)
            .execute()
        )

        if not insights_response.data:
            # No insights available, return empty summary
            return DeadlineInsightsSummary(
                critical_deadlines=0,
                high_risk_deadlines=0,
                conflicts_detected=0,
                recommendations=0,
                last_updated=datetime.now(timezone.utc).isoformat(),
            )

        insights_data = insights_response.data[0]["insights"]

        # Extract summary metrics
        recommendations = insights_data.get("recommendations", [])
        conflicts = insights_data.get("conflicts", [])

        critical_count = len(
            [r for r in recommendations if r.get("priority") == "CRITICAL"]
        )
        high_risk_count = len(
            [r for r in recommendations if r.get("priority") == "HIGH"]
        )

        summary = DeadlineInsightsSummary(
            critical_deadlines=critical_count,
            high_risk_deadlines=high_risk_count,
            conflicts_detected=len(conflicts),
            recommendations=len(recommendations),
            last_updated=insights_response.data[0]["generated_at"],
        )

        logger.info(
            f"Returning deadline insights summary: {critical_count} critical, {high_risk_count} high-risk"
        )
        return summary

    except Exception as e:
        logger.error(f"Error fetching deadline insights summary: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch deadline insights summary"
        )


@router.get("/detailed", response_model=DeadlineInsightResponse)
async def get_detailed_deadline_insights(
    analysis_type: str = Query(
        default="comprehensive", description="Type of analysis to fetch"
    ),
    include_expired: bool = Query(
        default=False, description="Include expired insights"
    ),
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
):
    """
    Get detailed deadline insights for the tenant.

    Returns comprehensive deadline analysis including conflicts, risks, and recommendations.
    """
    try:
        tenant_id = current_tenant["id"]
        logger.info(
            f"Fetching detailed deadline insights for tenant {tenant_id}, type: {analysis_type}"
        )

        # Get Supabase client
        supabase = get_supabase_client(use_service_role=True)

        # Build query
        query = (
            supabase.schema("tenants")
            .from_("deadline_insights")
            .select("*")
            .eq("tenant_id", tenant_id)
        )

        if analysis_type != "all":
            query = query.eq("analysis_type", analysis_type)

        if not include_expired:
            query = query.gte("expires_at", datetime.now(timezone.utc).isoformat())

        insights_response = query.order("generated_at", desc=True).limit(1).execute()

        if not insights_response.data:
            # No insights available, trigger generation
            logger.info(
                f"No insights found for tenant {tenant_id}, triggering generation"
            )
            await trigger_insights_generation(tenant_id, analysis_type)

            # Return empty response with indication that generation is in progress
            return DeadlineInsightResponse(
                tenant_id=tenant_id,
                analysis_type=analysis_type,
                insights={
                    "status": "generating",
                    "message": "Insights are being generated",
                },
                generated_at=datetime.now(timezone.utc).isoformat(),
                expires_at=(
                    datetime.now(timezone.utc) + timedelta(hours=4)
                ).isoformat(),
                cache_hit=False,
            )

        insights_record = insights_response.data[0]

        response = DeadlineInsightResponse(
            tenant_id=tenant_id,
            analysis_type=insights_record["analysis_type"],
            insights=insights_record["insights"],
            generated_at=insights_record["generated_at"],
            expires_at=insights_record["expires_at"],
            cache_hit=True,
        )

        logger.info(f"Returning detailed deadline insights for tenant {tenant_id}")
        return response

    except Exception as e:
        logger.error(f"Error fetching detailed deadline insights: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch detailed deadline insights"
        )


@router.post("/trigger")
async def trigger_deadline_insights(
    request: TriggerInsightsRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
    _: None = Depends(require_permission(Permission.WRITE_DEADLINE_INSIGHTS)),
):
    """
    Trigger deadline insights generation for the tenant.

    This endpoint allows manual triggering of deadline analysis.
    """
    try:
        tenant_id = current_tenant["id"]
        user_id = current_user["id"]

        logger.info(
            f"Triggering deadline insights for tenant {tenant_id} by user {user_id}"
        )

        # Check if we should skip due to recent generation (unless force_refresh is True)
        if not request.force_refresh:
            supabase = get_supabase_client(use_service_role=True)
            recent_insights = (
                supabase.schema("tenants")
                .from_("deadline_insights")
                .select("generated_at")
                .eq("tenant_id", tenant_id)
                .gte(
                    "generated_at",
                    (datetime.now(timezone.utc) - timedelta(minutes=30)).isoformat(),
                )
                .execute()
            )

            if recent_insights.data:
                return JSONResponse(
                    content={
                        "message": "Insights were recently generated. Use force_refresh=true to regenerate.",
                        "last_generated": recent_insights.data[0]["generated_at"],
                    },
                    status_code=202,
                )

        # Submit background job for insights generation
        from jobs.helpers import submit_deadline_insights_job

        job_id = submit_deadline_insights_job([tenant_id])

        # Log the trigger event
        supabase = get_supabase_client(use_service_role=True)
        supabase.schema("security").from_("events").insert(
            {
                "event_type": "deadline_insights.triggered",
                "event_category": "user_action",
                "user_id": user_id,
                "details": {
                    "tenant_id": tenant_id,
                    "analysis_type": request.analysis_type,
                    "matter_ids": request.matter_ids,
                    "force_refresh": request.force_refresh,
                    "job_id": job_id,
                },
                "created_at": datetime.now(timezone.utc).isoformat(),
            }
        ).execute()

        return JSONResponse(
            content={
                "message": "Deadline insights generation triggered successfully",
                "job_id": job_id,
                "estimated_completion": (
                    datetime.now(timezone.utc) + timedelta(minutes=5)
                ).isoformat(),
            },
            status_code=202,
        )

    except Exception as e:
        logger.error(f"Error triggering deadline insights: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to trigger deadline insights generation"
        )


@router.get("/critical")
async def get_critical_deadlines(
    limit: int = Query(
        default=10, le=50, description="Maximum number of critical deadlines to return"
    ),
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
):
    """
    Get critical deadlines that require immediate attention.

    Returns a list of the most critical deadlines based on recent insights analysis.
    """
    try:
        tenant_id = current_tenant["id"]
        logger.info(f"Fetching critical deadlines for tenant {tenant_id}")

        # Get latest insights
        supabase = get_supabase_client(use_service_role=True)
        insights_response = (
            supabase.schema("tenants")
            .from_("deadline_insights")
            .select("insights")
            .eq("tenant_id", tenant_id)
            .order("generated_at", desc=True)
            .limit(1)
            .execute()
        )

        if not insights_response.data:
            return JSONResponse(
                content={"critical_deadlines": [], "message": "No insights available"}
            )

        insights = insights_response.data[0]["insights"]
        recommendations = insights.get("recommendations", [])

        # Filter for critical deadlines
        critical_deadlines = [
            rec
            for rec in recommendations
            if rec.get("priority") in ["CRITICAL", "HIGH"]
        ]

        # Sort by priority and due date
        critical_deadlines.sort(
            key=lambda x: (
                0 if x.get("priority") == "CRITICAL" else 1,
                x.get("due_date", "9999-12-31"),
            )
        )

        # Limit results
        critical_deadlines = critical_deadlines[:limit]

        return JSONResponse(
            content={
                "critical_deadlines": critical_deadlines,
                "total_count": len(critical_deadlines),
                "last_updated": insights_response.data[0].get("generated_at"),
            }
        )

    except Exception as e:
        logger.error(f"Error fetching critical deadlines: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch critical deadlines"
        )


@router.get("/conflicts")
async def get_deadline_conflicts(
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
):
    """
    Get detected deadline conflicts.

    Returns conflicts between deadlines that may require attention or rescheduling.
    """
    try:
        tenant_id = current_tenant["id"]
        logger.info(f"Fetching deadline conflicts for tenant {tenant_id}")

        # Get latest insights
        supabase = get_supabase_client(use_service_role=True)
        insights_response = (
            supabase.schema("tenants")
            .from_("deadline_insights")
            .select("insights")
            .eq("tenant_id", tenant_id)
            .order("generated_at", desc=True)
            .limit(1)
            .execute()
        )

        if not insights_response.data:
            return JSONResponse(
                content={"conflicts": [], "message": "No insights available"}
            )

        insights = insights_response.data[0]["insights"]
        conflicts = insights.get("conflicts", [])

        return JSONResponse(
            content={
                "conflicts": conflicts,
                "total_count": len(conflicts),
                "last_updated": insights_response.data[0].get("generated_at"),
            }
        )

    except Exception as e:
        logger.error(f"Error fetching deadline conflicts: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch deadline conflicts"
        )


@router.get("/morning-briefing")
async def get_morning_briefing(
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
):
    """
    Get morning briefing with today's priorities and insights.

    Returns personalized morning briefing with critical deadlines,
    priority actions, and AI-generated insights for the day.
    """
    try:
        tenant_id = current_tenant["id"]
        user_id = current_user["id"]
        user_name = current_user.get("first_name", "there")

        logger.info(
            f"Generating morning briefing for user {user_id} in tenant {tenant_id}"
        )

        # Get current time info
        now = datetime.now(timezone.utc)
        hour = now.hour

        # Create personalized greeting
        if hour < 12:
            greeting = f"Good morning, {user_name}! ☀️"
        elif hour < 17:
            greeting = f"Good afternoon, {user_name}! 🌤️"
        else:
            greeting = f"Good evening, {user_name}! 🌙"

        # Get Supabase client
        supabase = get_supabase_client(use_service_role=True)

        # Get today's date range
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Fetch today's deadlines
        deadlines_response = (
            supabase.schema("tenants")
            .from_("deadlines")
            .select("id, title, description, due_date, priority, status, matter_id")
            .eq("tenant_id", tenant_id)
            .gte("due_date", today_start.isoformat())
            .lte("due_date", today_end.isoformat())
            .eq("status", "pending")
            .execute()
        )

        today_deadlines = deadlines_response.data or []

        # Fetch today's tasks
        tasks_response = (
            supabase.schema("tenants")
            .from_("tasks")
            .select("id, title, description, due_date, priority, status")
            .eq("tenant_id", tenant_id)
            .gte("due_date", today_start.isoformat())
            .lte("due_date", today_end.isoformat())
            .neq("status", "done")
            .execute()
        )

        today_tasks = tasks_response.data or []

        # Get latest insights
        insights_response = (
            supabase.schema("tenants")
            .from_("deadline_insights")
            .select("insights")
            .eq("tenant_id", tenant_id)
            .order("generated_at", desc=True)
            .limit(1)
            .execute()
        )

        insights_data = (
            insights_response.data[0]["insights"] if insights_response.data else {}
        )
        recommendations = insights_data.get("recommendations", [])

        # Build today's summary
        critical_deadlines = len(
            [d for d in today_deadlines if d.get("priority") == "critical"]
        )
        scheduled_tasks = len(today_tasks)

        # Get priority matters (matters with critical deadlines or high priority)
        priority_matter_ids = set()
        for deadline in today_deadlines:
            if deadline.get("priority") in ["critical", "high"] and deadline.get(
                "matter_id"
            ):
                priority_matter_ids.add(deadline["matter_id"])

        # Build priority actions from deadlines and recommendations
        priority_actions = []

        # Add critical deadlines as priority actions
        for deadline in today_deadlines:
            if deadline.get("priority") == "critical":
                priority_actions.append(
                    {
                        "id": f"deadline-{deadline['id']}",
                        "title": deadline["title"],
                        "description": deadline.get(
                            "description", "Critical deadline due today"
                        ),
                        "priority": "CRITICAL",
                        "due_time": datetime.fromisoformat(
                            deadline["due_date"].replace("Z", "+00:00")
                        ).strftime("%I:%M %p"),
                        "estimated_duration": "30 minutes",
                        "matter_title": None,  # Could be enhanced with matter lookup
                    }
                )

        # Add high-priority recommendations
        for rec in recommendations:
            if (
                rec.get("priority") in ["CRITICAL", "HIGH"]
                and len(priority_actions) < 5
            ):
                priority_actions.append(
                    {
                        "id": f"rec-{rec.get('id', len(priority_actions))}",
                        "title": rec.get("title", "Priority Action"),
                        "description": rec.get("description", "Recommended action"),
                        "priority": rec.get("priority", "HIGH"),
                        "due_time": None,
                        "estimated_duration": rec.get("estimated_duration"),
                        "matter_title": rec.get("matter_title"),
                    }
                )

        # Generate AI insights
        ai_insights = []

        if critical_deadlines > 0:
            ai_insights.append(
                {
                    "id": "critical-alert",
                    "type": "deadline_risk",
                    "title": "Critical Deadlines Today",
                    "description": f"You have {critical_deadlines} critical deadline(s) due today. Consider prioritizing these first.",
                    "action_suggested": "Review and tackle critical deadlines before 2 PM",
                }
            )

        if len(today_tasks) > 8:
            ai_insights.append(
                {
                    "id": "workload-alert",
                    "type": "workload_alert",
                    "title": "Heavy Workload Detected",
                    "description": f"You have {len(today_tasks)} tasks scheduled today. Consider delegating or rescheduling non-critical items.",
                    "action_suggested": "Identify 2-3 tasks that can be moved to tomorrow",
                }
            )

        if len(priority_actions) == 0:
            ai_insights.append(
                {
                    "id": "opportunity",
                    "type": "opportunity",
                    "title": "Light Schedule Today",
                    "description": "Your schedule looks manageable today. Great opportunity to work on strategic initiatives.",
                    "action_suggested": "Consider tackling that important project you've been putting off",
                }
            )

        # Weather check (mock data - could integrate with weather API)
        weather_check = {
            "message": "🌤️ Partly cloudy, 72°F. Perfect weather for client meetings!",
            "impact_on_schedule": "No weather-related schedule impacts expected today.",
        }

        briefing_data = {
            "greeting": greeting,
            "today_summary": {
                "critical_deadlines": critical_deadlines,
                "scheduled_tasks": scheduled_tasks,
                "upcoming_meetings": 0,  # Could be enhanced with calendar integration
                "priority_matters": len(priority_matter_ids),
            },
            "priority_actions": priority_actions[:5],  # Limit to top 5
            "insights": ai_insights,
            "weather_check": weather_check,
        }

        logger.info(
            f"Generated morning briefing for user {user_id}: {critical_deadlines} critical deadlines, {len(priority_actions)} priority actions"
        )

        return JSONResponse(content=briefing_data)

    except Exception as e:
        logger.error(f"Error generating morning briefing: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to generate morning briefing"
        )


async def trigger_insights_generation(tenant_id: str, analysis_type: str) -> str:
    """Helper function to trigger insights generation."""
    try:
        from jobs.helpers import submit_deadline_insights_job

        job_id = submit_deadline_insights_job([tenant_id])
        logger.info(
            f"Triggered insights generation for tenant {tenant_id}, job_id: {job_id}"
        )
        return job_id
    except Exception as e:
        logger.error(f"Error triggering insights generation: {e}")
        raise
