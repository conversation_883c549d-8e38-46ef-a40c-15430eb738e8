"""
Redis Proxy API for Frontend

Provides secure Redis operations for the frontend through the backend,
maintaining SSL security while avoiding Node.js SSL compatibility issues.
"""

import json
import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from backend.utils.redis_client import get_redis_client
from backend.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/redis", tags=["redis-proxy"])


# Request/Response Models
class RedisSetRequest(BaseModel):
    key: str
    value: str
    ex: Optional[int] = None  # Expiration in seconds
    px: Optional[int] = None  # Expiration in milliseconds
    nx: bool = False  # Only set if key doesn't exist
    xx: bool = False  # Only set if key exists


class RedisGetRequest(BaseModel):
    key: str


class RedisDelRequest(BaseModel):
    keys: List[str]


class RedisExistsRequest(BaseModel):
    keys: List[str]


class RedisHashSetRequest(BaseModel):
    key: str
    field: str
    value: str


class RedisHashGetRequest(BaseModel):
    key: str
    field: str


class RedisZAddRequest(BaseModel):
    key: str
    score: float
    member: str


class RedisZRangeRequest(BaseModel):
    key: str
    start: int
    stop: int
    withscores: bool = False


class RedisResponse(BaseModel):
    success: bool
    data: Any = None
    error: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())


@router.post("/ping", response_model=RedisResponse)
async def redis_ping():
    """Test Redis connection"""
    try:
        redis = await get_redis_client()
        result = await redis.ping()

        return RedisResponse(
            success=True,
            data={
                "ping": result,
                "message": "Redis connection successful via backend SSL",
            },
        )
    except Exception as e:
        logger.error(f"Redis ping failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/set", response_model=RedisResponse)
async def redis_set(request: RedisSetRequest):
    """Set a Redis key-value pair"""
    try:
        redis = await get_redis_client()

        # Build set arguments for async Redis client
        if request.ex:
            result = await redis.setex(request.key, request.ex, request.value)
        else:
            result = await redis.set(request.key, request.value)

        return RedisResponse(
            success=True, data={"result": bool(result), "key": request.key}
        )
    except Exception as e:
        logger.error(f"Redis set failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/get", response_model=RedisResponse)
async def redis_get(request: RedisGetRequest):
    """Get a Redis key value"""
    try:
        redis = await get_redis_client()
        result = await redis.get(request.key)

        return RedisResponse(success=True, data={"value": result, "key": request.key})
    except Exception as e:
        logger.error(f"Redis get failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/delete", response_model=RedisResponse)
async def redis_delete(request: RedisDelRequest):
    """Delete Redis keys"""
    try:
        redis = await get_redis_client()
        result = await redis.delete(*request.keys)

        return RedisResponse(
            success=True, data={"deleted_count": result, "keys": request.keys}
        )
    except Exception as e:
        logger.error(f"Redis delete failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/exists", response_model=RedisResponse)
async def redis_exists(request: RedisExistsRequest):
    """Check if Redis keys exist"""
    try:
        redis = await get_redis_client()
        result = await redis.exists(*request.keys)

        return RedisResponse(
            success=True, data={"exists_count": result, "keys": request.keys}
        )
    except Exception as e:
        logger.error(f"Redis exists failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/hset", response_model=RedisResponse)
async def redis_hset(request: RedisHashSetRequest):
    """Set a Redis hash field"""
    try:
        redis = await get_redis_client()
        result = await redis.hset(request.key, request.field, request.value)

        return RedisResponse(
            success=True,
            data={"result": result, "key": request.key, "field": request.field},
        )
    except Exception as e:
        logger.error(f"Redis hset failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/hget", response_model=RedisResponse)
async def redis_hget(request: RedisHashGetRequest):
    """Get a Redis hash field value"""
    try:
        redis = await get_redis_client()
        result = await redis.hget(request.key, request.field)

        return RedisResponse(
            success=True,
            data={"value": result, "key": request.key, "field": request.field},
        )
    except Exception as e:
        logger.error(f"Redis hget failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/zadd", response_model=RedisResponse)
async def redis_zadd(request: RedisZAddRequest):
    """Add member to Redis sorted set"""
    try:
        redis = await get_redis_client()
        result = await redis.zadd(request.key, {request.member: request.score})

        return RedisResponse(
            success=True,
            data={
                "result": result,
                "key": request.key,
                "member": request.member,
                "score": request.score,
            },
        )
    except Exception as e:
        logger.error(f"Redis zadd failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.post("/zrange", response_model=RedisResponse)
async def redis_zrange(request: RedisZRangeRequest):
    """Get range from Redis sorted set"""
    try:
        redis = await get_redis_client()
        result = await redis.zrange(
            request.key, request.start, request.stop, withscores=request.withscores
        )

        return RedisResponse(success=True, data={"result": result, "key": request.key})
    except Exception as e:
        logger.error(f"Redis zrange failed: {e}")
        return RedisResponse(success=False, error=str(e))


@router.get("/health", response_model=RedisResponse)
async def redis_health():
    """Get Redis health status"""
    try:
        redis = await get_redis_client()

        # Test basic operations
        ping_result = await redis.ping()

        # Test set/get
        test_key = f"health_check:{datetime.utcnow().timestamp()}"
        await redis.setex(test_key, 60, "test")
        get_result = await redis.get(test_key)
        await redis.delete(test_key)

        return RedisResponse(
            success=True,
            data={
                "ping": ping_result,
                "set_get_test": get_result == "test",
                "ssl_enabled": True,  # Backend uses SSL
                "status": "healthy",
                "method": "backend_proxy_ssl",
            },
        )
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        return RedisResponse(success=False, error=str(e))
