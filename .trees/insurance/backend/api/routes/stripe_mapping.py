"""
Stripe Mapping API Routes

Provides endpoints for managing automatic mapping between Supabase plans and Stripe products.
"""

import logging
from typing import Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from backend.services.stripe_mapping import stripe_mapper, SyncResult, PlanMapping
from backend.core.auth import get_current_user, require_admin
from backend.core.database import get_async_session

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/stripe/mapping", tags=["stripe-mapping"])

# Response Models


class SyncResultResponse(BaseModel):
    """Response model for sync operations."""

    status: str
    products_synced: int
    prices_created: int
    prices_updated: int
    errors: list[str]


class PlanMappingResponse(BaseModel):
    """Response model for plan mapping resolution."""

    stripe_price_id: str
    internal_type: str
    internal_id: str
    plan_code: str
    country_code: str
    currency: str
    billing_cycle: str
    tax_behavior: str


class SyncStatusResponse(BaseModel):
    """Response model for sync status."""

    last_sync: Optional[str]
    total_plans: int
    total_addons: int
    synced_plans: int
    synced_addons: int
    total_prices: int
    synced_prices: int
    issues: list[str]


# Admin Endpoints (require admin authentication)


@router.post("/sync/all", response_model=SyncResultResponse)
async def trigger_full_sync(current_user=Depends(require_admin)):
    """
    Trigger a full sync of all plans and add-ons to Stripe.

    This endpoint:
    1. Syncs all active subscription plans to Stripe products
    2. Creates multi-currency prices for each plan
    3. Syncs all active add-ons to Stripe products
    4. Updates database with Stripe IDs

    Requires admin authentication.
    """
    try:
        logger.info(f"Admin {current_user.email} triggered full Stripe sync")

        result = await stripe_mapper.sync_all_products()

        return SyncResultResponse(
            status="completed" if not result.errors else "completed_with_errors",
            products_synced=result.products_synced,
            prices_created=result.prices_created,
            prices_updated=result.prices_updated,
            errors=result.errors,
        )

    except Exception as e:
        logger.error(f"Error during full sync: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Sync failed: {str(e)}",
        )


@router.post("/sync/plan/{plan_id}", response_model=SyncResultResponse)
async def sync_specific_plan(plan_id: UUID, current_user=Depends(require_admin)):
    """
    Sync a specific plan to Stripe.

    This endpoint syncs a single plan and all its pricing variants to Stripe.
    Useful for testing or updating a specific plan after changes.

    Requires admin authentication.
    """
    try:
        logger.info(f"Admin {current_user.email} triggered sync for plan {plan_id}")

        async with get_async_session() as session:
            # Get the plan
            from backend.models.subscription import SubscriptionPlan
            from sqlalchemy import select

            query = select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
            result = await session.execute(query)
            plan = result.scalar_one_or_none()

            if not plan:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Plan {plan_id} not found",
                )

            # Sync the plan
            sync_result = await stripe_mapper.sync_plan_to_stripe(plan, session)

            return SyncResultResponse(
                status=(
                    "completed" if not sync_result.errors else "completed_with_errors"
                ),
                products_synced=1,
                prices_created=sync_result.prices_created,
                prices_updated=sync_result.prices_updated,
                errors=sync_result.errors,
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing plan {plan_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Plan sync failed: {str(e)}",
        )


@router.get("/status", response_model=SyncStatusResponse)
async def get_sync_status(current_user=Depends(require_admin)):
    """
    Get current sync status between database and Stripe.

    Returns information about:
    - Total plans and add-ons in database
    - How many are synced to Stripe
    - Total prices and sync status
    - Any issues or missing mappings

    Requires admin authentication.
    """
    try:
        async with get_async_session() as session:
            # Get plan statistics
            plan_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) as total_plans,
                    COUNT(stripe_product_id) as synced_plans
                FROM tenants.subscription_plans 
                WHERE is_active = true AND is_public = true
            """
            )
            plan_row = plan_stats.fetchone()

            # Get add-on statistics
            addon_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) as total_addons,
                    COUNT(stripe_product_id) as synced_addons
                FROM tenants.subscription_addons 
                WHERE is_active = true
            """
            )
            addon_row = addon_stats.fetchone()

            # Get pricing statistics
            price_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) as total_prices,
                    COUNT(stripe_price_id) as synced_prices
                FROM tenants.plan_pricing 
                WHERE is_active = true
            """
            )
            price_row = price_stats.fetchone()

            # Get last sync time
            last_sync_result = await session.execute(
                """
                SELECT MAX(last_synced_at) as last_sync
                FROM (
                    SELECT last_synced_at FROM tenants.subscription_plans
                    UNION ALL
                    SELECT last_synced_at FROM tenants.subscription_addons
                    UNION ALL
                    SELECT last_synced_at FROM tenants.plan_pricing
                ) combined
            """
            )
            last_sync_row = last_sync_result.fetchone()

            # Identify issues
            issues = []

            # Check for unsynced plans
            if plan_row.synced_plans < plan_row.total_plans:
                issues.append(
                    f"{plan_row.total_plans - plan_row.synced_plans} plans not synced to Stripe"
                )

            # Check for unsynced add-ons
            if addon_row.synced_addons < addon_row.total_addons:
                issues.append(
                    f"{addon_row.total_addons - addon_row.synced_addons} add-ons not synced to Stripe"
                )

            # Check for unsynced prices
            if price_row.synced_prices < price_row.total_prices:
                issues.append(
                    f"{price_row.total_prices - price_row.synced_prices} prices not synced to Stripe"
                )

            return SyncStatusResponse(
                last_sync=(
                    last_sync_row.last_sync.isoformat()
                    if last_sync_row.last_sync
                    else None
                ),
                total_plans=plan_row.total_plans,
                total_addons=addon_row.total_addons,
                synced_plans=plan_row.synced_plans,
                synced_addons=addon_row.synced_addons,
                total_prices=price_row.total_prices,
                synced_prices=price_row.synced_prices,
                issues=issues,
            )

    except Exception as e:
        logger.error(f"Error getting sync status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sync status: {str(e)}",
        )


# Internal Endpoints (for webhook and internal use)


@router.get("/resolve/{stripe_price_id}", response_model=PlanMappingResponse)
async def resolve_stripe_price(
    stripe_price_id: str, current_user=Depends(get_current_user)
):
    """
    Resolve a Stripe price ID to internal plan mapping.

    This endpoint is used by webhooks and internal services to map
    Stripe price IDs back to internal plan/add-on information.

    Returns detailed mapping information including:
    - Internal plan/add-on ID and code
    - Country and currency information
    - Billing cycle and tax behavior
    """
    try:
        mapping = await stripe_mapper.resolve_stripe_price(stripe_price_id)

        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No mapping found for Stripe price ID: {stripe_price_id}",
            )

        return PlanMappingResponse(
            stripe_price_id=mapping.stripe_price_id,
            internal_type=mapping.internal_type,
            internal_id=str(mapping.internal_id),
            plan_code=mapping.plan_code,
            country_code=mapping.country_code,
            currency=mapping.currency,
            billing_cycle=mapping.billing_cycle,
            tax_behavior=mapping.tax_behavior,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving Stripe price {stripe_price_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resolve price: {str(e)}",
        )


@router.get("/price/{plan_code}/{country_code}/{billing_cycle}")
async def get_stripe_price_for_plan(
    plan_code: str,
    country_code: str,
    billing_cycle: str,
    current_user=Depends(get_current_user),
):
    """
    Get Stripe price ID for a specific plan/country/cycle combination.

    This endpoint is used by the frontend and checkout processes to get
    the correct Stripe price ID for a given plan configuration.

    Parameters:
    - plan_code: Internal plan code (solo, team, scale, ai_receptionist, extra_user)
    - country_code: ISO country code (US, BE)
    - billing_cycle: Billing cycle (monthly, yearly)
    """
    try:
        # Validate inputs
        if billing_cycle not in ["monthly", "yearly"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="billing_cycle must be 'monthly' or 'yearly'",
            )

        country_code = country_code.upper()
        if country_code not in ["US", "BE"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="country_code must be 'US' or 'BE'",
            )

        stripe_price_id = await stripe_mapper.get_stripe_price_for_plan(
            plan_code, country_code, billing_cycle
        )

        if not stripe_price_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No Stripe price found for {plan_code}/{country_code}/{billing_cycle}",
            )

        return {
            "stripe_price_id": stripe_price_id,
            "plan_code": plan_code,
            "country_code": country_code,
            "billing_cycle": billing_cycle,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error getting price for {plan_code}/{country_code}/{billing_cycle}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get price: {str(e)}",
        )


# Health check endpoint


@router.get("/health")
async def mapping_health_check():
    """
    Health check endpoint for the mapping service.

    Returns basic status information about the mapping service.
    """
    try:
        # Basic connectivity test
        async with get_async_session() as session:
            result = await session.execute("SELECT 1")
            db_ok = result.scalar() == 1

        return {
            "status": "healthy" if db_ok else "unhealthy",
            "database": "connected" if db_ok else "disconnected",
            "service": "stripe_mapping",
            "version": "1.0",
        }

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "service": "stripe_mapping",
                "error": str(e),
            },
        )
