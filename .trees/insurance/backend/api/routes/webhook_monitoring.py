#!/usr/bin/env python3
"""
Webhook Monitoring API for PI Lawyer AI
API endpoints for comprehensive webhook monitoring, health checks, and performance metrics.

This provides real-time monitoring capabilities for regional subscription webhooks.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from typing import Dict, Any, List, Optional
from datetime import datetime

from backend.services.webhook_monitoring_service import (
    webhook_monitoring_service,
    HealthStatus,
)
from backend.middleware.auth_middleware import require_role
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/admin/webhooks/monitoring", tags=["Webhook Monitoring"])


@router.get("/health")
async def get_webhook_health_status(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get comprehensive webhook system health status.

    Returns:
        Complete health status including all components
    """
    try:
        health_status = await webhook_monitoring_service.get_health_status()

        return {
            "status": "success",
            "data": health_status,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting webhook health status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get health status: {str(e)}"
        )


@router.get("/health/simple")
async def get_simple_health_check() -> Dict[str, Any]:
    """
    Simple health check endpoint for load balancers and monitoring systems.
    No authentication required.

    Returns:
        Simple health status
    """
    try:
        health_status = await webhook_monitoring_service.get_health_status()
        overall_status = health_status.get("overall_status", "unknown")

        if overall_status == HealthStatus.HEALTHY.value:
            return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
        elif overall_status == HealthStatus.WARNING.value:
            return {
                "status": "warning",
                "message": "Some components degraded",
                "timestamp": datetime.utcnow().isoformat(),
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "unhealthy",
                    "message": "Critical issues detected",
                    "timestamp": datetime.utcnow().isoformat(),
                },
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in simple health check: {e}", exc_info=True)
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "message": "Health check failed",
                "timestamp": datetime.utcnow().isoformat(),
            },
        )


@router.get("/metrics")
async def get_performance_metrics(
    region: Optional[str] = Query(
        None, description="Specific region to get metrics for"
    ),
    hours: int = Query(
        1, ge=1, le=24, description="Number of hours of historical data"
    ),
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get webhook performance metrics.

    Args:
        region: Optional specific region filter
        hours: Hours of historical data to include

    Returns:
        Performance metrics data
    """
    try:
        metrics = await webhook_monitoring_service.get_performance_metrics(
            region, hours
        )

        return {
            "status": "success",
            "data": metrics,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.get("/metrics/regional")
async def get_regional_metrics_breakdown(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get detailed breakdown of metrics by region.

    Returns:
        Regional metrics breakdown
    """
    try:
        # Get metrics for all regions
        all_metrics = await webhook_monitoring_service.get_performance_metrics()

        # Get additional regional data
        regional_data = {}
        for region in ["US", "EU", "UK", "CA"]:
            regional_metrics = await webhook_monitoring_service.get_performance_metrics(
                region, 24
            )
            regional_data[region] = regional_metrics

        return {
            "status": "success",
            "data": {"aggregated": all_metrics, "regional_breakdown": regional_data},
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting regional metrics: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get regional metrics: {str(e)}"
        )


@router.get("/dead-letter-queue")
async def get_dead_letter_queue_status(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get dead letter queue monitoring status.

    Returns:
        Dead letter queue status and analysis
    """
    try:
        dlq_status = await webhook_monitoring_service.get_dead_letter_queue_status()

        return {
            "status": "success",
            "data": dlq_status,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting DLQ status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get DLQ status: {str(e)}"
        )


@router.get("/compliance")
async def get_compliance_report(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get regional compliance monitoring report.

    Returns:
        Comprehensive compliance report
    """
    try:
        compliance_report = await webhook_monitoring_service.get_compliance_report()

        return {
            "status": "success",
            "data": compliance_report,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting compliance report: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get compliance report: {str(e)}"
        )


@router.get("/dashboard")
async def get_monitoring_dashboard_data(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get comprehensive dashboard data for real-time monitoring.

    Returns:
        Complete dashboard data including health, metrics, and alerts
    """
    try:
        dashboard_data = await webhook_monitoring_service.get_real_time_dashboard_data()

        return {
            "status": "success",
            "data": dashboard_data,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get dashboard data: {str(e)}"
        )


@router.post("/record-event")
async def record_webhook_event(
    event_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Record a webhook processing event for monitoring.

    Args:
        event_data: Webhook event processing data

    Returns:
        Recording confirmation
    """
    try:
        # Validate required fields
        required_fields = ["event_id", "region", "processing_time_ms", "success"]
        missing_fields = [field for field in required_fields if field not in event_data]

        if missing_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required fields: {', '.join(missing_fields)}",
            )

        # Record the event in background
        background_tasks.add_task(
            webhook_monitoring_service.record_webhook_processing,
            event_data["event_id"],
            event_data["region"],
            event_data["processing_time_ms"],
            event_data["success"],
            event_data.get("retry_count", 0),
            event_data.get("moved_to_dlq", False),
        )

        return {
            "status": "success",
            "message": "Event recorded for monitoring",
            "event_id": event_data["event_id"],
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error recording webhook event: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to record event: {str(e)}")


@router.get("/alerts")
async def get_active_alerts(
    severity: Optional[str] = Query(None, description="Filter by alert severity"),
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get active monitoring alerts.

    Args:
        severity: Optional severity filter

    Returns:
        List of active alerts
    """
    try:
        # Get current health status to determine alerts
        health_status = await webhook_monitoring_service.get_health_status()

        alerts = []

        # Generate alerts based on health status
        if health_status.get("overall_status") == HealthStatus.CRITICAL.value:
            alerts.append(
                {
                    "id": "critical_health",
                    "severity": "critical",
                    "message": "Webhook system health is critical",
                    "timestamp": datetime.utcnow().isoformat(),
                    "details": health_status,
                }
            )
        elif health_status.get("overall_status") == HealthStatus.WARNING.value:
            alerts.append(
                {
                    "id": "warning_health",
                    "severity": "warning",
                    "message": "Webhook system health is degraded",
                    "timestamp": datetime.utcnow().isoformat(),
                    "details": health_status,
                }
            )

        # Filter by severity if specified
        if severity:
            alerts = [
                alert for alert in alerts if alert["severity"] == severity.lower()
            ]

        return {
            "status": "success",
            "data": {"alerts": alerts, "total_count": len(alerts)},
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting alerts: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get alerts: {str(e)}")


@router.post("/start-monitoring")
async def start_monitoring_service(
    background_tasks: BackgroundTasks, _: dict = Depends(require_role(["superadmin"]))
) -> Dict[str, Any]:
    """
    Start the webhook monitoring service.
    Superadmin only endpoint.

    Returns:
        Service start confirmation
    """
    try:
        # Start monitoring service in background
        background_tasks.add_task(webhook_monitoring_service.start_monitoring)

        logger.info("Webhook monitoring service start requested")

        return {
            "status": "success",
            "message": "Webhook monitoring service starting",
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error starting monitoring service: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to start monitoring: {str(e)}"
        )


@router.get("/status")
async def get_monitoring_service_status(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get monitoring service operational status.

    Returns:
        Service status information
    """
    try:
        # Check if monitoring service is running
        health_status = await webhook_monitoring_service.get_health_status()

        return {
            "status": "success",
            "data": {
                "service_status": "running",
                "health_check_interval": 30,
                "metrics_retention_hours": 24,
                "last_health_check": health_status.get("timestamp"),
                "monitored_regions": ["US", "EU", "UK", "CA"],
                "monitoring_components": [
                    "webhook_retry_service",
                    "regional_routing",
                    "processing_performance",
                    "compliance_status",
                ],
            },
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get monitoring status: {str(e)}"
        )


@router.get("/export")
async def export_monitoring_data(
    format: str = Query("json", description="Export format (json, csv)"),
    hours: int = Query(24, ge=1, le=168, description="Hours of data to export"),
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Export monitoring data for analysis.

    Args:
        format: Export format (json or csv)
        hours: Hours of historical data to export

    Returns:
        Exported monitoring data
    """
    try:
        # Get comprehensive monitoring data
        dashboard_data = await webhook_monitoring_service.get_real_time_dashboard_data()

        # Get historical metrics for all regions
        export_data = {
            "export_timestamp": datetime.utcnow().isoformat(),
            "export_format": format,
            "data_period_hours": hours,
            "dashboard_data": dashboard_data,
        }

        # Add regional historical data
        regional_history = {}
        for region in ["US", "EU", "UK", "CA"]:
            regional_metrics = await webhook_monitoring_service.get_performance_metrics(
                region, hours
            )
            regional_history[region] = regional_metrics

        export_data["regional_history"] = regional_history

        return {
            "status": "success",
            "data": export_data,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error exporting monitoring data: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to export data: {str(e)}")
