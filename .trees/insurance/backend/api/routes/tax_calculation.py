"""
Tax Calculation API Routes for PI Lawyer AI

Provides endpoints for real-time tax calculation, VAT validation,
and tax rate lookups for subscription billing.
"""

import logging
import time
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, status, Request
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, update, and_, or_

try:
    from backend.db.session import get_db
    from backend.api.dependencies.auth import get_current_user
    from backend.api.dependencies.authorization import require_role, UserRole
    from backend.services.tax_calculation_service import (
        tax_calculation_service,
        TaxCalculationResult,
        VATValidationResult,
    )
except ImportError:
    # Fallback for when running from backend directory
    import sys
    import os

    sys.path.append(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from core.database import get_db
    from api.dependencies.auth import get_current_user
    from api.dependencies.authorization import require_role, UserRole
    from services.tax_calculation_service import (
        tax_calculation_service,
        TaxCalculationResult,
        VATValidationResult,
    )

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/tax", tags=["tax-calculation"])


# Security and audit functions


async def check_rate_limit(
    tenant_id: str,
    endpoint: str,
    action: str,
    user_id: str,
    db: AsyncSession,
    limit: int = 100,
    window_minutes: int = 60,
) -> int:
    """
    Check and enforce rate limiting per tenant.

    Args:
        tenant_id: Tenant UUID
        endpoint: API endpoint being accessed
        action: Action being performed
        user_id: User UUID
        db: Database session
        limit: Request limit per window (default: 100)
        window_minutes: Time window in minutes (default: 60)

    Returns:
        Remaining requests in current window

    Raises:
        HTTPException: If rate limit exceeded
    """
    now = datetime.utcnow()
    window_start = now.replace(minute=0, second=0, microsecond=0)
    window_end = window_start + timedelta(minutes=window_minutes)

    # Check existing rate limit record
    result = await db.execute(
        select("*")
        .select_from("tenants.tax_rate_limits")
        .where(
            and_(
                "tenant_id" == tenant_id,
                "endpoint" == endpoint,
                "action" == action,
                "window_start" == window_start,
            )
        )
    )
    rate_limit_record = result.fetchone()

    if rate_limit_record:
        # Check if blocked
        if rate_limit_record.is_blocked and rate_limit_record.blocked_until > now:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Try again after {rate_limit_record.blocked_until}",
                headers={
                    "Retry-After": str(
                        int((rate_limit_record.blocked_until - now).total_seconds())
                    )
                },
            )

        # Update request count
        new_count = rate_limit_record.request_count + 1
        is_blocked = new_count >= limit
        blocked_until = now + timedelta(minutes=window_minutes) if is_blocked else None

        await db.execute(
            update("tenants.tax_rate_limits")
            .where("id" == rate_limit_record.id)
            .values(
                request_count=new_count,
                is_blocked=is_blocked,
                blocked_until=blocked_until,
                last_request_at=now,
            )
        )

        if is_blocked:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Maximum {limit} requests per {window_minutes} minutes.",
                headers={"Retry-After": str(window_minutes * 60)},
            )

        return limit - new_count
    else:
        # Create new rate limit record
        await db.execute(
            insert("tenants.tax_rate_limits").values(
                tenant_id=tenant_id,
                user_id=user_id,
                endpoint=endpoint,
                action=action,
                window_start=window_start,
                window_end=window_end,
                window_duration_minutes=window_minutes,
                request_count=1,
                limit_threshold=limit,
                first_request_at=now,
                last_request_at=now,
            )
        )

        return limit - 1


async def log_tax_audit(
    user_context: Dict[str, Any],
    request: Request,
    action: str,
    endpoint: str,
    request_data: Dict[str, Any],
    response_status: int,
    response_data: Optional[Dict[str, Any]] = None,
    error_message: Optional[str] = None,
    error_code: Optional[str] = None,
    request_duration_ms: Optional[int] = None,
    rate_limit_remaining: Optional[int] = None,
    db: Optional[AsyncSession] = None,
):
    """
    Log comprehensive audit information for tax operations.

    Args:
        user_context: Current user context
        request: FastAPI request object
        action: Action being performed
        endpoint: API endpoint
        request_data: Request payload
        response_status: HTTP response status
        response_data: Response payload
        error_message: Error message if any
        error_code: Error code if any
        request_duration_ms: Request duration in milliseconds
        rate_limit_remaining: Remaining rate limit
        db: Database session
    """
    try:
        if not db:
            return  # Skip logging if no database session

        # Extract request metadata
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent", "")

        # Extract tax-specific data from request
        customer_country = request_data.get("customer_country")
        customer_type = request_data.get("customer_type")
        amount = request_data.get("amount")
        currency = request_data.get("currency")
        vat_number = request_data.get("vat_number")

        # Extract tax-specific data from response
        tax_amount = None
        stripe_calculation_id = None
        calculation_source = None

        if response_data:
            tax_amount = response_data.get("tax_amount")
            stripe_calculation_id = response_data.get("stripe_calculation_id")
            if stripe_calculation_id:
                calculation_source = "stripe_tax"
            elif response_data.get("vat_validation_status") == "fallback":
                calculation_source = "fallback"
            else:
                calculation_source = "cache"

        # Insert audit record
        await db.execute(
            insert("tenants.tax_calculation_audit").values(
                user_id=user_context.get("user_id"),
                tenant_id=user_context.get("tenant_id"),
                user_email=user_context.get("email"),
                user_role=user_context.get("role"),
                action=action,
                endpoint=endpoint,
                http_method=request.method,
                request_data=request_data,
                customer_country=customer_country,
                customer_type=customer_type,
                amount=amount,
                currency=currency,
                vat_number=vat_number,
                response_status=response_status,
                response_data=response_data,
                tax_amount=tax_amount,
                stripe_calculation_id=stripe_calculation_id,
                calculation_source=calculation_source,
                ip_address=client_ip,
                user_agent=user_agent,
                request_duration_ms=request_duration_ms,
                rate_limit_remaining=rate_limit_remaining,
                error_message=error_message,
                error_code=error_code,
            )
        )
        await db.commit()

    except Exception as e:
        logger.error(f"Failed to log tax audit: {e}", exc_info=True)


# Request/Response Models


class TaxCalculationRequest(BaseModel):
    """Request model for tax calculation."""

    amount: Decimal = Field(..., description="Amount to calculate tax for", gt=0)
    currency: str = Field(
        ..., description="Currency code (USD, EUR, etc.)", min_length=3, max_length=3
    )
    customer_country: str = Field(
        ..., description="Customer country code", min_length=2, max_length=2
    )
    customer_type: str = Field(default="B2C", description="Customer type (B2C or B2B)")
    vat_number: Optional[str] = Field(None, description="VAT number for B2B customers")
    customer_address: Dict[str, str] = Field(
        ..., description="Customer billing address"
    )

    @validator("currency")
    def validate_currency(cls, v):
        return v.upper()

    @validator("customer_country")
    def validate_country(cls, v):
        return v.upper()

    @validator("customer_type")
    def validate_customer_type(cls, v):
        if v not in ["B2C", "B2B"]:
            raise ValueError("customer_type must be B2C or B2B")
        return v.upper()


class TaxCalculationResponse(BaseModel):
    """Response model for tax calculation."""

    subtotal_amount: float
    tax_amount: float
    total_amount: float
    tax_rate: float
    tax_type: str
    currency: str
    customer_country: str
    customer_type: str
    vat_number: Optional[str] = None
    vat_validation_status: Optional[str] = None
    reverse_charge_applied: bool = False
    tax_jurisdiction: Optional[str] = None
    stripe_calculation_id: Optional[str] = None


class VATValidationRequest(BaseModel):
    """Request model for VAT validation."""

    vat_number: str = Field(..., description="VAT number to validate")
    country_code: str = Field(
        ..., description="Country code", min_length=2, max_length=2
    )

    @validator("country_code")
    def validate_country(cls, v):
        return v.upper()


class VATValidationResponse(BaseModel):
    """Response model for VAT validation."""

    vat_number: str
    country_code: str
    is_valid: bool
    company_name: Optional[str] = None
    company_address: Optional[str] = None
    validation_source: str


class TaxRateRequest(BaseModel):
    """Request model for tax rate lookup."""

    country_code: str = Field(
        ..., description="Country code", min_length=2, max_length=2
    )
    state: Optional[str] = Field(None, description="State/region code")

    @validator("country_code")
    def validate_country(cls, v):
        return v.upper()


class TaxRateResponse(BaseModel):
    """Response model for tax rates."""

    country_code: str
    region_code: Optional[str]
    tax_type: str
    tax_rate: float
    applies_to: str
    requires_vat_validation: bool
    reverse_charge_eligible: bool
    description: str


# API Endpoints


@router.post("/calculate", response_model=TaxCalculationResponse)
async def calculate_tax(
    tax_request: TaxCalculationRequest,
    fastapi_request: Request,
    current_user: Dict[str, Any] = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
    db: AsyncSession = Depends(get_db),
):
    """
    Calculate tax for a subscription amount.

    Performs real-time tax calculation using Stripe Tax API with fallback
    to local tax configuration. Includes VAT validation for B2B customers.
    Requires admin or partner role and enforces rate limiting.
    """
    start_time = time.time()
    tenant_id = current_user.get("tenant_id")
    user_id = current_user.get("user_id")

    # Prepare request data for audit logging
    request_data = {
        "amount": float(tax_request.amount),
        "currency": tax_request.currency,
        "customer_country": tax_request.customer_country,
        "customer_type": tax_request.customer_type,
        "vat_number": tax_request.vat_number,
        "customer_address": tax_request.customer_address,
    }

    try:
        logger.info(
            f"Tax calculation request from user {user_id} (tenant {tenant_id}): {tax_request.amount} {tax_request.currency}"
        )

        # Check rate limiting
        rate_limit_remaining = await check_rate_limit(
            tenant_id=tenant_id,
            endpoint="/api/tax/calculate",
            action="calculate_tax",
            user_id=user_id,
            db=db,
            limit=100,
            window_minutes=60,
        )

        # Validate required address fields
        required_fields = ["line1", "city", "country"]
        missing_fields = [
            field
            for field in required_fields
            if not tax_request.customer_address.get(field)
        ]
        if missing_fields:
            error_msg = f"Missing required address fields: {', '.join(missing_fields)}"
            await log_tax_audit(
                user_context=current_user,
                request=fastapi_request,
                action="calculate_tax",
                endpoint="/api/tax/calculate",
                request_data=request_data,
                response_status=400,
                error_message=error_msg,
                error_code="MISSING_ADDRESS_FIELDS",
                request_duration_ms=int((time.time() - start_time) * 1000),
                rate_limit_remaining=rate_limit_remaining,
                db=db,
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=error_msg
            )

        # Ensure country matches
        if (
            tax_request.customer_address.get("country", "").upper()
            != tax_request.customer_country
        ):
            tax_request.customer_address["country"] = tax_request.customer_country

        # Calculate tax with tenant context
        result = await tax_calculation_service.calculate_tax(
            amount=tax_request.amount,
            currency=tax_request.currency,
            customer_country=tax_request.customer_country,
            customer_address=tax_request.customer_address,
            customer_type=tax_request.customer_type,
            vat_number=tax_request.vat_number,
            tenant_id=tenant_id,  # Add tenant context
        )

        # Prepare response data
        response_data = result.to_dict()
        request_duration_ms = int((time.time() - start_time) * 1000)

        # Log successful audit
        await log_tax_audit(
            user_context=current_user,
            request=fastapi_request,
            action="calculate_tax",
            endpoint="/api/tax/calculate",
            request_data=request_data,
            response_status=200,
            response_data=response_data,
            request_duration_ms=request_duration_ms,
            rate_limit_remaining=rate_limit_remaining,
            db=db,
        )

        logger.info(
            f"Tax calculation successful: {result.tax_amount} {result.currency} (duration: {request_duration_ms}ms)"
        )

        return TaxCalculationResponse(**response_data)

    except HTTPException as e:
        # Log HTTP exceptions
        await log_tax_audit(
            user_context=current_user,
            request=fastapi_request,
            action="calculate_tax",
            endpoint="/api/tax/calculate",
            request_data=request_data,
            response_status=e.status_code,
            error_message=str(e.detail),
            error_code="HTTP_EXCEPTION",
            request_duration_ms=int((time.time() - start_time) * 1000),
            db=db,
        )
        raise
    except Exception as e:
        # Log unexpected exceptions
        error_msg = "Tax calculation failed"
        await log_tax_audit(
            user_context=current_user,
            request=fastapi_request,
            action="calculate_tax",
            endpoint="/api/tax/calculate",
            request_data=request_data,
            response_status=500,
            error_message=str(e),
            error_code="INTERNAL_ERROR",
            request_duration_ms=int((time.time() - start_time) * 1000),
            db=db,
        )
        logger.error(f"Tax calculation error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=error_msg
        )


@router.post("/validate-vat", response_model=VATValidationResponse)
async def validate_vat_number(
    vat_request: VATValidationRequest,
    fastapi_request: Request,
    current_user: Dict[str, Any] = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
    db: AsyncSession = Depends(get_db),
):
    """
    Validate a VAT number using Stripe Tax API.

    Checks VAT number validity and returns company information if available.
    Results are cached for 24 hours to improve performance with tenant isolation.
    Requires admin or partner role and enforces rate limiting.
    """
    start_time = time.time()
    tenant_id = current_user.get("tenant_id")
    user_id = current_user.get("user_id")

    # Prepare request data for audit logging
    request_data = {
        "vat_number": vat_request.vat_number,
        "country_code": vat_request.country_code,
    }

    try:
        logger.info(
            f"VAT validation request from user {user_id} (tenant {tenant_id}): {vat_request.vat_number}"
        )

        # Check rate limiting
        rate_limit_remaining = await check_rate_limit(
            tenant_id=tenant_id,
            endpoint="/api/tax/validate-vat",
            action="validate_vat",
            user_id=user_id,
            db=db,
            limit=50,  # Lower limit for VAT validation
            window_minutes=60,
        )

        # Validate VAT number with tenant context
        result = await tax_calculation_service.validate_vat_number(
            vat_number=vat_request.vat_number,
            country_code=vat_request.country_code,
            tenant_id=tenant_id,  # Add tenant context
        )

        if not result:
            error_msg = "VAT validation failed"
            await log_tax_audit(
                user_context=current_user,
                request=fastapi_request,
                action="validate_vat",
                endpoint="/api/tax/validate-vat",
                request_data=request_data,
                response_status=400,
                error_message=error_msg,
                error_code="VAT_VALIDATION_FAILED",
                request_duration_ms=int((time.time() - start_time) * 1000),
                rate_limit_remaining=rate_limit_remaining,
                db=db,
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=error_msg
            )

        # Prepare response data
        response_data = result.to_dict()
        request_duration_ms = int((time.time() - start_time) * 1000)

        # Log successful audit
        await log_tax_audit(
            user_context=current_user,
            request=fastapi_request,
            action="validate_vat",
            endpoint="/api/tax/validate-vat",
            request_data=request_data,
            response_status=200,
            response_data=response_data,
            request_duration_ms=request_duration_ms,
            rate_limit_remaining=rate_limit_remaining,
            db=db,
        )

        logger.info(
            f"VAT validation result: {result.is_valid} (duration: {request_duration_ms}ms)"
        )

        return VATValidationResponse(**response_data)

    except HTTPException as e:
        # Log HTTP exceptions
        await log_tax_audit(
            user_context=current_user,
            request=fastapi_request,
            action="validate_vat",
            endpoint="/api/tax/validate-vat",
            request_data=request_data,
            response_status=e.status_code,
            error_message=str(e.detail),
            error_code="HTTP_EXCEPTION",
            request_duration_ms=int((time.time() - start_time) * 1000),
            db=db,
        )
        raise
    except Exception as e:
        # Log unexpected exceptions
        error_msg = "VAT validation failed"
        await log_tax_audit(
            user_context=current_user,
            request=fastapi_request,
            action="validate_vat",
            endpoint="/api/tax/validate-vat",
            request_data=request_data,
            response_status=500,
            error_message=str(e),
            error_code="INTERNAL_ERROR",
            request_duration_ms=int((time.time() - start_time) * 1000),
            db=db,
        )
        logger.error(f"VAT validation error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=error_msg
        )


@router.post("/rates", response_model=List[TaxRateResponse])
async def get_tax_rates(
    rates_request: TaxRateRequest,
    fastapi_request: Request,
    current_user: Dict[str, Any] = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
    db: AsyncSession = Depends(get_db),
):
    """
    Get tax rates for a specific country/region.

    Returns all applicable tax rates including VAT, sales tax, GST, etc.
    based on the configured tax rules for the specified location.
    """
    try:
        logger.info(
            f"Tax rates request from user {current_user.get('user_id')}: {request.country_code}"
        )

        # Get tax rates
        rates = await tax_calculation_service.get_tax_rates(
            country_code=request.country_code, state=request.state
        )

        logger.info(f"Found {len(rates)} tax rates for {request.country_code}")

        return [TaxRateResponse(**rate) for rate in rates]

    except Exception as e:
        logger.error(f"Tax rates lookup error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Tax rates lookup failed",
        )


@router.get("/behavior/{country_code}")
async def get_tax_behavior(
    country_code: str,
    customer_type: str = "B2C",
    vat_number: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Determine tax behavior (inclusive/exclusive) for a country and customer type.

    Returns whether pricing should be displayed as tax-inclusive or tax-exclusive
    based on regional conventions and customer type.
    """
    try:
        country_code = country_code.upper()
        customer_type = customer_type.upper()

        logger.info(f"Tax behavior request: {country_code} {customer_type}")

        # Validate VAT number if provided
        vat_validation = None
        if vat_number and customer_type == "B2B":
            vat_validation = await tax_calculation_service.validate_vat_number(
                vat_number, country_code
            )

        # Determine tax behavior
        behavior = await tax_calculation_service.determine_tax_behavior(
            customer_country=country_code,
            customer_type=customer_type,
            vat_validation=vat_validation,
        )

        return {
            "country_code": country_code,
            "customer_type": customer_type,
            "tax_behavior": behavior,
            "vat_validated": vat_validation.is_valid if vat_validation else None,
        }

    except Exception as e:
        logger.error(f"Tax behavior lookup error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Tax behavior lookup failed",
        )


@router.get("/health")
async def tax_service_health():
    """
    Health check endpoint for tax calculation service.

    Verifies that the tax calculation service is operational and
    can connect to required external services.
    """
    try:
        # Test basic service functionality
        test_rates = await tax_calculation_service.get_tax_rates("US")

        return {
            "status": "healthy",
            "service": "tax_calculation",
            "timestamp": "2025-01-30T00:00:00Z",
            "test_rates_count": len(test_rates),
        }

    except Exception as e:
        logger.error(f"Tax service health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Tax calculation service unhealthy",
        )
