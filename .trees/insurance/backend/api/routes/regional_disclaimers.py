"""
Regional Legal Disclaimers API Routes

This module provides API endpoints for managing and retrieving regional legal disclaimers
that integrate with the data residency system for jurisdiction-specific compliance.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, Request, HTTPException, Query
from pydantic import BaseModel

from backend.api.dependencies.auth import get_current_user, UserContext
from backend.models.legal_disclaimer import (
    LegalDisclaimer,
    LegalDisclaimerCreate,
    DisclaimerType,
    DisclaimerRegion,
    DisclaimerPlacement,
    DisclaimerDisplayContext,
    DisclaimerResponse,
    RegionalDisclaimerSet,
)
from backend.services.regional_disclaimer_service import regional_disclaimer_service
from backend.services.data_residency import DataRegion
from backend.middleware.data_residency_middleware import get_user_region_from_request

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/regional-disclaimers", tags=["Regional Legal Disclaimers"])


# =====================================================
# Pydantic Models
# =====================================================


class DisclaimerContextRequest(BaseModel):
    """Request model for disclaimer context."""

    placement: DisclaimerPlacement = DisclaimerPlacement.FOOTER
    practice_area: Optional[str] = None
    jurisdiction: Optional[str] = None
    page_type: Optional[str] = None
    language: str = "en"


class DisclaimerListResponse(BaseModel):
    """Response model for disclaimer lists."""

    disclaimers: List[LegalDisclaimer]
    total_count: int
    region: str
    placement: str
    cache_info: Optional[dict] = None


class RegionalDisclaimerSummary(BaseModel):
    """Summary of disclaimers by region."""

    region: str
    disclaimer_count: int
    required_count: int
    last_updated: str
    compliance_frameworks: List[str]


# =====================================================
# API Endpoints
# =====================================================


@router.get("/", response_model=DisclaimerResponse)
async def get_regional_disclaimers(
    request: Request,
    placement: DisclaimerPlacement = Query(
        DisclaimerPlacement.FOOTER, description="Where disclaimers will be displayed"
    ),
    practice_area: Optional[str] = Query(None, description="Practice area context"),
    jurisdiction: Optional[str] = Query(None, description="Jurisdiction context"),
    page_type: Optional[str] = Query(None, description="Type of page"),
    language: str = Query("en", description="Language preference"),
    current_user: Optional[UserContext] = Depends(get_current_user),
):
    """
    Get regional legal disclaimers based on user's location and context.

    This endpoint automatically detects the user's region using the data residency
    system and returns appropriate disclaimers for their jurisdiction.
    """
    try:
        # Get user's region from data residency middleware
        data_region = get_user_region_from_request(request)
        disclaimer_region = (
            regional_disclaimer_service.map_data_region_to_disclaimer_region(
                data_region
            )
        )

        # Build display context
        context = DisclaimerDisplayContext(
            user_region=disclaimer_region,
            placement=placement,
            practice_area=practice_area,
            jurisdiction=jurisdiction,
            user_role=getattr(current_user, "role", None) if current_user else None,
            page_type=page_type,
            language=language,
        )

        # Get disclaimers
        response = await regional_disclaimer_service.get_disclaimers_for_region(
            context=context, user_id=current_user.id if current_user else None
        )

        logger.info(
            f"Retrieved {len(response.disclaimers)} disclaimers for region {disclaimer_region}",
            extra={
                "user_id": str(current_user.id) if current_user else None,
                "region": disclaimer_region.value,
                "placement": placement.value,
                "disclaimer_count": len(response.disclaimers),
            },
        )

        return response

    except Exception as e:
        logger.error(f"Failed to get regional disclaimers: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve disclaimers")


@router.get("/region/{region}", response_model=RegionalDisclaimerSet)
async def get_disclaimers_by_region(
    region: DisclaimerRegion, current_user: UserContext = Depends(get_current_user)
):
    """
    Get complete disclaimer set for a specific region.

    This endpoint is useful for administrative purposes and compliance reporting.
    """
    try:
        disclaimer_set = await regional_disclaimer_service.get_regional_disclaimer_set(
            region
        )

        logger.info(
            f"Retrieved disclaimer set for region {region}",
            extra={
                "user_id": str(current_user.id),
                "region": region.value,
                "disclaimer_count": len(disclaimer_set.disclaimers),
            },
        )

        return disclaimer_set

    except Exception as e:
        logger.error(
            f"Failed to get disclaimer set for region {region}: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve disclaimer set for region {region}",
        )


@router.get("/summary", response_model=List[RegionalDisclaimerSummary])
async def get_regional_disclaimer_summary(
    current_user: UserContext = Depends(get_current_user),
):
    """
    Get summary of disclaimers across all regions.

    Useful for administrative dashboards and compliance overview.
    """
    try:
        summaries = []

        for region in DisclaimerRegion:
            try:
                disclaimer_set = (
                    await regional_disclaimer_service.get_regional_disclaimer_set(
                        region
                    )
                )

                required_count = sum(
                    1 for d in disclaimer_set.disclaimers if d.is_required
                )

                summary = RegionalDisclaimerSummary(
                    region=region.value,
                    disclaimer_count=len(disclaimer_set.disclaimers),
                    required_count=required_count,
                    last_updated=disclaimer_set.last_updated.isoformat(),
                    compliance_frameworks=disclaimer_set.compliance_frameworks,
                )

                summaries.append(summary)

            except Exception as e:
                logger.warning(f"Failed to get summary for region {region}: {str(e)}")
                continue

        logger.info(
            f"Retrieved disclaimer summary for {len(summaries)} regions",
            extra={"user_id": str(current_user.id), "region_count": len(summaries)},
        )

        return summaries

    except Exception as e:
        logger.error(f"Failed to get disclaimer summary: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve disclaimer summary"
        )


@router.get("/types", response_model=List[dict])
async def get_disclaimer_types():
    """
    Get available disclaimer types and their descriptions.

    Useful for frontend components to understand available disclaimer categories.
    """
    try:
        types = []

        type_descriptions = {
            DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP: "Disclaims attorney-client relationship formation",
            DisclaimerType.ATTORNEY_ADVERTISING: "Attorney advertising compliance notices",
            DisclaimerType.PROFESSIONAL_RESPONSIBILITY: "Professional regulation and licensing information",
            DisclaimerType.JURISDICTION_LIMITATION: "Limitations on jurisdictional practice",
            DisclaimerType.NO_LEGAL_ADVICE: "General disclaimer that content is not legal advice",
            DisclaimerType.CONFIDENTIALITY_WARNING: "Warnings about confidentiality of communications",
            DisclaimerType.WEBSITE_TERMS: "Website terms of use and conditions",
            DisclaimerType.DATA_PROCESSING: "Data processing and privacy notices",
            DisclaimerType.THIRD_PARTY_LINKS: "Disclaimers for third-party links and content",
            DisclaimerType.LIABILITY_LIMITATION: "Limitations on liability and warranties",
        }

        for disclaimer_type in DisclaimerType:
            types.append(
                {
                    "type": disclaimer_type.value,
                    "description": type_descriptions.get(disclaimer_type, ""),
                    "required_regions": self._get_required_regions_for_type(
                        disclaimer_type
                    ),
                }
            )

        return types

    except Exception as e:
        logger.error(f"Failed to get disclaimer types: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve disclaimer types"
        )


@router.get("/placements", response_model=List[dict])
async def get_disclaimer_placements():
    """
    Get available disclaimer placement options.

    Useful for frontend components to understand where disclaimers can be displayed.
    """
    try:
        placements = []

        placement_descriptions = {
            DisclaimerPlacement.FOOTER: "Website footer - always visible",
            DisclaimerPlacement.HEADER: "Website header - prominent placement",
            DisclaimerPlacement.SIDEBAR: "Sidebar - contextual placement",
            DisclaimerPlacement.MODAL: "Modal dialog - requires user interaction",
            DisclaimerPlacement.BANNER: "Banner notification - temporary display",
            DisclaimerPlacement.PAGE_CONTENT: "Within page content - contextual",
            DisclaimerPlacement.TERMS_PAGE: "Terms of service page - comprehensive",
            DisclaimerPlacement.PRIVACY_PAGE: "Privacy policy page - data-related",
        }

        for placement in DisclaimerPlacement:
            placements.append(
                {
                    "placement": placement.value,
                    "description": placement_descriptions.get(placement, ""),
                    "recommended_types": self._get_recommended_types_for_placement(
                        placement
                    ),
                }
            )

        return placements

    except Exception as e:
        logger.error(f"Failed to get disclaimer placements: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve disclaimer placements"
        )


@router.post("/", response_model=LegalDisclaimer)
async def create_disclaimer(
    disclaimer_data: LegalDisclaimerCreate,
    current_user: UserContext = Depends(get_current_user),
):
    """
    Create a new legal disclaimer.

    Requires appropriate permissions to create disclaimers.
    """
    try:
        # Check if user has permission to create disclaimers
        if not self._can_manage_disclaimers(current_user):
            raise HTTPException(
                status_code=403, detail="Insufficient permissions to create disclaimers"
            )

        disclaimer = await regional_disclaimer_service.create_disclaimer(
            disclaimer_data=disclaimer_data, created_by=current_user.id
        )

        logger.info(
            f"Created disclaimer: {disclaimer.title}",
            extra={
                "disclaimer_id": str(disclaimer.id),
                "created_by": str(current_user.id),
                "region": disclaimer.region.value,
                "type": disclaimer.disclaimer_type.value,
            },
        )

        return disclaimer

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create disclaimer: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create disclaimer")


# =====================================================
# Helper Functions
# =====================================================


def _get_required_regions_for_type(disclaimer_type: DisclaimerType) -> List[str]:
    """Get regions where a disclaimer type is required."""
    requirements = {
        DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP: ["US"],
        DisclaimerType.ATTORNEY_ADVERTISING: ["US"],
        DisclaimerType.DATA_PROCESSING: ["EU"],
        DisclaimerType.NO_LEGAL_ADVICE: ["US", "EU"],
        DisclaimerType.PROFESSIONAL_RESPONSIBILITY: ["US", "EU"],
    }
    return requirements.get(disclaimer_type, [])


def _get_recommended_types_for_placement(placement: DisclaimerPlacement) -> List[str]:
    """Get recommended disclaimer types for a placement."""
    recommendations = {
        DisclaimerPlacement.FOOTER: [
            DisclaimerType.ATTORNEY_CLIENT_RELATIONSHIP.value,
            DisclaimerType.ATTORNEY_ADVERTISING.value,
        ],
        DisclaimerPlacement.TERMS_PAGE: [
            DisclaimerType.JURISDICTION_LIMITATION.value,
            DisclaimerType.LIABILITY_LIMITATION.value,
            DisclaimerType.WEBSITE_TERMS.value,
        ],
        DisclaimerPlacement.PRIVACY_PAGE: [
            DisclaimerType.DATA_PROCESSING.value,
            DisclaimerType.CONFIDENTIALITY_WARNING.value,
        ],
    }
    return recommendations.get(placement, [])


def _can_manage_disclaimers(user: UserContext) -> bool:
    """Check if user can manage disclaimers."""
    # In a real implementation, this would check user roles/permissions
    # For now, allow partners and admins
    allowed_roles = ["partner", "admin", "super_admin"]
    return getattr(user, "role", None) in allowed_roles
