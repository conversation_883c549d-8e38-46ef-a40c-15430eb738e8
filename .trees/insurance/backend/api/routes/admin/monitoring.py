"""
Superadmin Monitoring & Security API

Provides comprehensive monitoring, security, and system health endpoints for superadmins.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, Field

from backend.api.dependencies.auth import require_super_admin as require_superadmin
from backend.db.session import get_db_session as get_async_session
from backend.services.stripe_mapping import stripe_mapper

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin/monitoring", tags=["admin", "monitoring"])

# Response Models


class SystemHealthResponse(BaseModel):
    """System health overview."""

    status: str = Field(..., description="Overall system status")
    uptime: str = Field(..., description="System uptime")
    database_status: str = Field(..., description="Database connection status")
    stripe_status: str = Field(..., description="Stripe integration status")
    webhook_status: str = Field(..., description="Webhook endpoint status")
    last_updated: datetime = Field(..., description="Last health check time")
    issues: List[str] = Field(..., description="Current system issues")


class SecurityMetricsResponse(BaseModel):
    """Security metrics and alerts."""

    failed_login_attempts: int = Field(..., description="Failed login attempts (24h)")
    suspicious_activities: int = Field(
        ..., description="Suspicious activities detected"
    )
    webhook_signature_failures: int = Field(
        ..., description="Webhook signature failures"
    )
    unauthorized_access_attempts: int = Field(
        ..., description="Unauthorized access attempts"
    )
    active_sessions: int = Field(..., description="Currently active user sessions")
    security_alerts: List[Dict[str, Any]] = Field(
        ..., description="Active security alerts"
    )


class SubscriptionMetricsResponse(BaseModel):
    """Subscription business metrics."""

    total_active_subscriptions: int
    new_subscriptions_today: int
    new_subscriptions_this_week: int
    new_subscriptions_this_month: int
    churned_subscriptions_this_month: int
    total_mrr: float = Field(..., description="Monthly Recurring Revenue")
    mrr_growth_rate: float = Field(..., description="MRR growth rate percentage")
    subscription_by_plan: Dict[str, int]
    subscription_by_country: Dict[str, int]
    payment_failures_this_week: int
    average_subscription_value: float


class WebhookMetricsResponse(BaseModel):
    """Webhook processing metrics."""

    total_webhooks_processed: int = Field(
        ..., description="Total webhooks processed (24h)"
    )
    successful_webhooks: int = Field(..., description="Successfully processed webhooks")
    failed_webhooks: int = Field(..., description="Failed webhook processing")
    average_processing_time: float = Field(
        ..., description="Average processing time (ms)"
    )
    webhook_types: Dict[str, int] = Field(..., description="Webhook events by type")
    recent_failures: List[Dict[str, Any]] = Field(
        ..., description="Recent webhook failures"
    )


class UserActivityResponse(BaseModel):
    """User activity and engagement metrics."""

    total_active_users: int = Field(..., description="Total active users")
    daily_active_users: int = Field(..., description="Daily active users")
    weekly_active_users: int = Field(..., description="Weekly active users")
    monthly_active_users: int = Field(..., description="Monthly active users")
    new_users_this_week: int = Field(..., description="New users this week")
    user_retention_rate: float = Field(
        ..., description="User retention rate percentage"
    )
    top_features_used: Dict[str, int] = Field(..., description="Most used features")


# Monitoring Endpoints


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(current_user=Depends(require_superadmin)):
    """
    Get comprehensive system health status.

    Provides real-time system health including:
    - Database connectivity
    - Stripe integration status
    - Webhook endpoint health
    - Recent error rates
    """
    try:
        issues = []

        # Test database connectivity
        try:
            async with get_async_session() as session:
                result = await session.execute("SELECT 1")
                db_status = "healthy" if result.scalar() == 1 else "unhealthy"
        except Exception as e:
            db_status = "unhealthy"
            issues.append(f"Database connection failed: {str(e)}")

        # Test Stripe connectivity
        try:
            # Simple Stripe API test
            import stripe
            from backend.config import Settings

            settings = Settings()
            stripe.api_key = settings.STRIPE_SECRET_KEY
            stripe.Account.retrieve()
            stripe_status = "healthy"
        except Exception as e:
            stripe_status = "unhealthy"
            issues.append(f"Stripe connection failed: {str(e)}")

        # Check webhook health (recent processing)
        try:
            async with get_async_session() as session:
                # Check for recent webhook activity (this would need webhook logging table)
                webhook_status = "healthy"  # Placeholder
        except Exception:
            webhook_status = "unknown"

        # Determine overall status
        if db_status == "healthy" and stripe_status == "healthy":
            overall_status = "healthy"
        elif db_status == "healthy" or stripe_status == "healthy":
            overall_status = "degraded"
        else:
            overall_status = "unhealthy"

        return SystemHealthResponse(
            status=overall_status,
            uptime="N/A",  # Would need application start time tracking
            database_status=db_status,
            stripe_status=stripe_status,
            webhook_status=webhook_status,
            last_updated=datetime.utcnow(),
            issues=issues,
        )

    except Exception as e:
        logger.error(f"Error getting system health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system health: {str(e)}",
        )


@router.get("/security", response_model=SecurityMetricsResponse)
async def get_security_metrics(current_user=Depends(require_superadmin)):
    """
    Get security metrics and alerts.

    Monitors:
    - Failed authentication attempts
    - Suspicious activities
    - Webhook security issues
    - Active sessions
    """
    try:
        async with get_async_session() as session:
            # These queries would need proper logging tables
            # For now, returning placeholder data

            security_alerts = []

            # Check for recent failed logins (would need auth_logs table)
            failed_logins = 0

            # Check for webhook signature failures (would need webhook_logs table)
            webhook_failures = 0

            # Check for unauthorized access attempts
            unauthorized_attempts = 0

            # Active sessions (would need sessions table)
            active_sessions = 0

            return SecurityMetricsResponse(
                failed_login_attempts=failed_logins,
                suspicious_activities=0,
                webhook_signature_failures=webhook_failures,
                unauthorized_access_attempts=unauthorized_attempts,
                active_sessions=active_sessions,
                security_alerts=security_alerts,
            )

    except Exception as e:
        logger.error(f"Error getting security metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get security metrics: {str(e)}",
        )


@router.get("/subscriptions", response_model=SubscriptionMetricsResponse)
async def get_subscription_metrics(current_user=Depends(require_superadmin)):
    """
    Get subscription business metrics.

    Provides insights into:
    - Subscription growth
    - Revenue metrics (MRR)
    - Plan distribution
    - Geographic distribution
    - Churn analysis
    """
    try:
        async with get_async_session() as session:
            # Get subscription counts
            subscription_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) FILTER (WHERE status = 'active') as active_subscriptions,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE) as new_today,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as new_this_week,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_this_month,
                    COUNT(*) FILTER (WHERE status = 'cancelled' AND updated_at >= CURRENT_DATE - INTERVAL '30 days') as churned_this_month
                FROM tenants.tenant_subscriptions
            """
            )
            sub_row = subscription_stats.fetchone()

            # Get plan distribution
            plan_distribution = await session.execute(
                """
                SELECT plan_code, COUNT(*) as count
                FROM tenants.tenant_subscriptions
                WHERE status = 'active'
                GROUP BY plan_code
            """
            )
            plan_dist = {
                row.plan_code: row.count for row in plan_distribution.fetchall()
            }

            # Get country distribution
            country_distribution = await session.execute(
                """
                SELECT billing_country, COUNT(*) as count
                FROM tenants.tenant_subscriptions
                WHERE status = 'active' AND billing_country IS NOT NULL
                GROUP BY billing_country
            """
            )
            country_dist = {
                row.billing_country: row.count
                for row in country_distribution.fetchall()
            }

            # Calculate MRR (simplified - would need more complex calculation)
            mrr_stats = await session.execute(
                """
                SELECT 
                    SUM(
                        CASE 
                            WHEN billing_cycle = 'monthly' THEN last_payment_amount
                            WHEN billing_cycle = 'yearly' THEN last_payment_amount / 12
                            ELSE 0
                        END
                    ) as total_mrr,
                    AVG(last_payment_amount) as avg_subscription_value
                FROM tenants.tenant_subscriptions
                WHERE status = 'active' AND last_payment_amount IS NOT NULL
            """
            )
            mrr_row = mrr_stats.fetchone()

            # Payment failures
            payment_failures = await session.execute(
                """
                SELECT COUNT(*) as failures
                FROM tenants.tenant_subscriptions
                WHERE status = 'payment_failed' 
                  AND updated_at >= CURRENT_DATE - INTERVAL '7 days'
            """
            )
            failures_row = payment_failures.fetchone()

            return SubscriptionMetricsResponse(
                total_active_subscriptions=sub_row.active_subscriptions or 0,
                new_subscriptions_today=sub_row.new_today or 0,
                new_subscriptions_this_week=sub_row.new_this_week or 0,
                new_subscriptions_this_month=sub_row.new_this_month or 0,
                churned_subscriptions_this_month=sub_row.churned_this_month or 0,
                total_mrr=float(mrr_row.total_mrr or 0),
                mrr_growth_rate=0.0,  # Would need historical data
                subscription_by_plan=plan_dist,
                subscription_by_country=country_dist,
                payment_failures_this_week=failures_row.failures or 0,
                average_subscription_value=float(mrr_row.avg_subscription_value or 0),
            )

    except Exception as e:
        logger.error(f"Error getting subscription metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get subscription metrics: {str(e)}",
        )


@router.get("/webhooks", response_model=WebhookMetricsResponse)
async def get_webhook_metrics(current_user=Depends(require_superadmin)):
    """
    Get webhook processing metrics.

    Monitors:
    - Webhook processing success rates
    - Processing times
    - Event type distribution
    - Recent failures
    """
    try:
        # This would need a webhook_logs table to track webhook events
        # For now, returning placeholder data

        return WebhookMetricsResponse(
            total_webhooks_processed=0,
            successful_webhooks=0,
            failed_webhooks=0,
            average_processing_time=0.0,
            webhook_types={},
            recent_failures=[],
        )

    except Exception as e:
        logger.error(f"Error getting webhook metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get webhook metrics: {str(e)}",
        )


@router.get("/users", response_model=UserActivityResponse)
async def get_user_activity(current_user=Depends(require_superadmin)):
    """
    Get user activity and engagement metrics.

    Provides insights into:
    - Active user counts
    - User growth
    - Feature usage
    - Retention rates
    """
    try:
        async with get_async_session() as session:
            # Get user activity stats
            user_stats = await session.execute(
                """
                SELECT 
                    COUNT(DISTINCT f.id) as total_users,
                    COUNT(DISTINCT f.id) FILTER (WHERE f.created_at >= CURRENT_DATE - INTERVAL '7 days') as new_users_week
                FROM tenants.firms f
                WHERE f.is_active = true
            """
            )
            user_row = user_stats.fetchone()

            # Active users would need activity tracking
            # For now, using subscription data as proxy
            active_users_stats = await session.execute(
                """
                SELECT COUNT(DISTINCT firm_id) as active_users
                FROM tenants.tenant_subscriptions
                WHERE status = 'active'
            """
            )
            active_row = active_users_stats.fetchone()

            return UserActivityResponse(
                total_active_users=active_row.active_users or 0,
                daily_active_users=0,  # Would need activity tracking
                weekly_active_users=0,  # Would need activity tracking
                monthly_active_users=active_row.active_users or 0,
                new_users_this_week=user_row.new_users_week or 0,
                user_retention_rate=0.0,  # Would need cohort analysis
                top_features_used={},  # Would need feature usage tracking
            )

    except Exception as e:
        logger.error(f"Error getting user activity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user activity: {str(e)}",
        )


# Stripe Sync Monitoring


@router.get("/stripe/sync-status")
async def get_stripe_sync_status(current_user=Depends(require_superadmin)):
    """
    Get detailed Stripe synchronization status.

    Shows:
    - Products synced vs total
    - Prices synced vs total
    - Last sync times
    - Sync errors
    """
    try:
        result = await stripe_mapper.check_sync_status()
        return result

    except Exception as e:
        logger.error(f"Error getting Stripe sync status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sync status: {str(e)}",
        )


@router.post("/stripe/force-sync")
async def force_stripe_sync(current_user=Depends(require_superadmin)):
    """
    Force a complete Stripe synchronization.

    This endpoint triggers a full sync of all plans and add-ons to Stripe.
    Use with caution as it may create duplicate products if not properly managed.
    """
    try:
        logger.info(f"Superadmin {current_user['email']} triggered force Stripe sync")

        result = await stripe_mapper.sync_all_products()

        return {
            "status": "completed" if not result.errors else "completed_with_errors",
            "products_synced": result.products_synced,
            "prices_created": result.prices_created,
            "prices_updated": result.prices_updated,
            "errors": result.errors,
            "triggered_by": current_user["email"],
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error during force Stripe sync: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Force sync failed: {str(e)}",
        )


# System Control Endpoints


@router.post("/system/clear-cache")
async def clear_system_cache(current_user=Depends(require_superadmin)):
    """
    Clear system caches.

    Clears various system caches for troubleshooting.
    """
    try:
        logger.info(f"Superadmin {current_user['email']} cleared system cache")

        # Clear any application caches here
        # This would depend on your caching implementation

        return {
            "status": "success",
            "message": "System cache cleared",
            "cleared_by": current_user["email"],
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error clearing cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}",
        )
