"""
Superadmin Subscription Management API

Provides comprehensive subscription package management for superadmins including:
- Creating and managing subscription plans
- Setting up multi-country pricing
- Managing features and availability
- Monitoring subscription health
"""

import logging
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, Field

from backend.api.dependencies.auth import require_super_admin as require_superadmin
from backend.core.database import get_async_session
from backend.services.stripe_mapping import stripe_mapper

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin/subscriptions", tags=["admin", "subscriptions"])

# Request/Response Models


class CountryAvailability(BaseModel):
    """Country availability configuration."""

    country_code: str = Field(..., description="ISO country code (US, BE)")
    currency: str = Field(..., description="Currency code (USD, EUR)")
    price_monthly: float = Field(..., description="Monthly price in local currency")
    price_yearly: float = Field(..., description="Yearly price in local currency")
    tax_inclusive: bool = Field(..., description="Whether prices include tax")
    is_available: bool = Field(
        default=True, description="Whether available in this country"
    )


class FeatureConfiguration(BaseModel):
    """Feature configuration for a plan."""

    max_users: int = Field(..., description="Maximum number of users")
    max_storage: int = Field(..., description="Maximum storage in GB")
    max_documents: int = Field(..., description="Maximum documents")
    max_concurrent_processing: int = Field(
        ..., description="Max concurrent AI processing"
    )
    practice_areas: List[str] = Field(..., description="Included practice areas")
    ai_features: Dict[str, bool] = Field(..., description="AI feature flags")
    compliance_level: str = Field(
        ..., description="Compliance level (standard, enterprise)"
    )


class CreatePlanRequest(BaseModel):
    """Request to create a new subscription plan."""

    name: str = Field(..., description="Plan display name")
    code: str = Field(..., description="Internal plan code")
    description: str = Field(..., description="Plan description")
    features: FeatureConfiguration = Field(..., description="Plan features")
    country_pricing: List[CountryAvailability] = Field(
        ..., description="Multi-country pricing"
    )
    is_public: bool = Field(
        default=True, description="Whether plan is publicly available"
    )
    is_active: bool = Field(default=True, description="Whether plan is active")


class UpdatePlanRequest(BaseModel):
    """Request to update an existing subscription plan."""

    name: Optional[str] = Field(None, description="Plan display name")
    description: Optional[str] = Field(None, description="Plan description")
    features: Optional[FeatureConfiguration] = Field(None, description="Plan features")
    country_pricing: Optional[List[CountryAvailability]] = Field(
        None, description="Multi-country pricing"
    )
    is_public: Optional[bool] = Field(
        None, description="Whether plan is publicly available"
    )
    is_active: Optional[bool] = Field(None, description="Whether plan is active")


class PlanResponse(BaseModel):
    """Response model for subscription plan."""

    id: str
    name: str
    code: str
    description: str
    features: Dict[str, Any]
    country_pricing: List[Dict[str, Any]]
    is_public: bool
    is_active: bool
    stripe_product_id: Optional[str]
    last_synced_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class SubscriptionHealthResponse(BaseModel):
    """Response model for subscription system health."""

    total_plans: int
    active_plans: int
    total_addons: int
    active_addons: int
    countries_supported: int
    currencies_supported: int
    stripe_sync_status: Dict[str, Any]
    recent_subscriptions: int
    failed_payments: int
    health_score: float
    issues: List[str]


# Plan Management Endpoints


@router.post("/plans", response_model=PlanResponse)
async def create_subscription_plan(
    plan_request: CreatePlanRequest, current_user=Depends(require_superadmin)
):
    """
    Create a new subscription plan with multi-country pricing.

    This endpoint allows superadmins to create new subscription plans with:
    - Feature configuration
    - Multi-country pricing
    - Automatic Stripe product creation
    """
    try:
        async with get_async_session() as session:
            # Create the plan record
            plan_data = {
                "name": plan_request.name,
                "code": plan_request.code,
                "description": plan_request.description,
                "is_public": plan_request.is_public,
                "is_active": plan_request.is_active,
                "features": plan_request.features.dict(),
                "available_countries": [
                    cp.country_code for cp in plan_request.country_pricing
                ],
                "base_currency": "USD",  # Default base currency
                "compliance_level": plan_request.features.compliance_level,
            }

            # Insert plan
            plan_query = """
            INSERT INTO tenants.subscription_plans 
            (name, code, description, is_public, is_active, features, available_countries, base_currency, compliance_level)
            VALUES (:name, :code, :description, :is_public, :is_active, :features, :available_countries, :base_currency, :compliance_level)
            RETURNING id, created_at, updated_at
            """

            result = await session.execute(plan_query, plan_data)
            plan_row = result.fetchone()
            plan_id = plan_row.id

            # Create pricing records for each country
            for country_pricing in plan_request.country_pricing:
                pricing_data = {
                    "plan_id": plan_id,
                    "country_code": country_pricing.country_code,
                    "currency": country_pricing.currency,
                    "price_monthly": country_pricing.price_monthly,
                    "price_yearly": country_pricing.price_yearly,
                    "tax_inclusive": country_pricing.tax_inclusive,
                    "is_active": country_pricing.is_available,
                }

                pricing_query = """
                INSERT INTO tenants.plan_pricing 
                (plan_id, country_code, currency, price_monthly, price_yearly, tax_inclusive, is_active)
                VALUES (:plan_id, :country_code, :currency, :price_monthly, :price_yearly, :tax_inclusive, :is_active)
                """

                await session.execute(pricing_query, pricing_data)

            await session.commit()

            # Trigger Stripe sync
            try:
                await stripe_mapper.sync_plan_to_stripe(plan_id, session)
                logger.info(
                    f"Successfully synced new plan {plan_request.code} to Stripe"
                )
            except Exception as e:
                logger.error(
                    f"Failed to sync plan {plan_request.code} to Stripe: {str(e)}"
                )

            # Return the created plan
            return await _get_plan_response(plan_id, session)

    except Exception as e:
        logger.error(f"Error creating subscription plan: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create plan: {str(e)}",
        )


@router.get("/plans", response_model=List[PlanResponse])
async def list_subscription_plans(
    include_inactive: bool = Query(False, description="Include inactive plans"),
    country_code: Optional[str] = Query(
        None, description="Filter by country availability"
    ),
    current_user=Depends(require_superadmin),
):
    """
    List all subscription plans with their multi-country pricing.

    Provides comprehensive view of all plans for superadmin management.
    """
    try:
        async with get_async_session() as session:
            # Build query conditions
            conditions = []
            params = {}

            if not include_inactive:
                conditions.append("sp.is_active = true")

            if country_code:
                conditions.append(":country_code = ANY(sp.available_countries)")
                params["country_code"] = country_code.upper()

            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
            SELECT 
                sp.id,
                sp.name,
                sp.code,
                sp.description,
                sp.features,
                sp.available_countries,
                sp.is_public,
                sp.is_active,
                sp.stripe_product_id,
                sp.last_synced_at,
                sp.created_at,
                sp.updated_at,
                COALESCE(
                    json_agg(
                        json_build_object(
                            'country_code', pp.country_code,
                            'currency', pp.currency,
                            'price_monthly', pp.price_monthly,
                            'price_yearly', pp.price_yearly,
                            'tax_inclusive', pp.tax_inclusive,
                            'is_active', pp.is_active,
                            'stripe_price_id', pp.stripe_price_id
                        )
                    ) FILTER (WHERE pp.id IS NOT NULL),
                    '[]'::json
                ) as country_pricing
            FROM tenants.subscription_plans sp
            LEFT JOIN tenants.plan_pricing pp ON sp.id = pp.plan_id
            {where_clause}
            GROUP BY sp.id, sp.name, sp.code, sp.description, sp.features, 
                     sp.available_countries, sp.is_public, sp.is_active, 
                     sp.stripe_product_id, sp.last_synced_at, sp.created_at, sp.updated_at
            ORDER BY sp.created_at DESC
            """

            result = await session.execute(query, params)
            plans = result.fetchall()

            return [
                PlanResponse(
                    id=str(plan.id),
                    name=plan.name,
                    code=plan.code,
                    description=plan.description,
                    features=plan.features,
                    country_pricing=plan.country_pricing,
                    is_public=plan.is_public,
                    is_active=plan.is_active,
                    stripe_product_id=plan.stripe_product_id,
                    last_synced_at=plan.last_synced_at,
                    created_at=plan.created_at,
                    updated_at=plan.updated_at,
                )
                for plan in plans
            ]

    except Exception as e:
        logger.error(f"Error listing subscription plans: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list plans: {str(e)}",
        )


@router.get("/plans/{plan_id}", response_model=PlanResponse)
async def get_subscription_plan(
    plan_id: UUID, current_user=Depends(require_superadmin)
):
    """Get detailed information about a specific subscription plan."""
    try:
        async with get_async_session() as session:
            return await _get_plan_response(plan_id, session)
    except Exception as e:
        logger.error(f"Error getting subscription plan {plan_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"Plan not found: {str(e)}"
        )


@router.put("/plans/{plan_id}", response_model=PlanResponse)
async def update_subscription_plan(
    plan_id: UUID,
    plan_request: UpdatePlanRequest,
    current_user=Depends(require_superadmin),
):
    """
    Update an existing subscription plan.

    Allows updating plan details, features, and pricing.
    Automatically syncs changes to Stripe.
    """
    try:
        async with get_async_session() as session:
            # Build update query
            update_fields = []
            params = {"plan_id": str(plan_id)}

            if plan_request.name is not None:
                update_fields.append("name = :name")
                params["name"] = plan_request.name

            if plan_request.description is not None:
                update_fields.append("description = :description")
                params["description"] = plan_request.description

            if plan_request.features is not None:
                update_fields.append("features = :features")
                params["features"] = plan_request.features.dict()

            if plan_request.is_public is not None:
                update_fields.append("is_public = :is_public")
                params["is_public"] = plan_request.is_public

            if plan_request.is_active is not None:
                update_fields.append("is_active = :is_active")
                params["is_active"] = plan_request.is_active

            if update_fields:
                update_fields.append("updated_at = NOW()")

                update_query = f"""
                UPDATE tenants.subscription_plans 
                SET {', '.join(update_fields)}
                WHERE id = :plan_id
                """

                await session.execute(update_query, params)

            # Update pricing if provided
            if plan_request.country_pricing is not None:
                # Delete existing pricing
                await session.execute(
                    "DELETE FROM tenants.plan_pricing WHERE plan_id = :plan_id",
                    {"plan_id": str(plan_id)},
                )

                # Insert new pricing
                for country_pricing in plan_request.country_pricing:
                    pricing_data = {
                        "plan_id": str(plan_id),
                        "country_code": country_pricing.country_code,
                        "currency": country_pricing.currency,
                        "price_monthly": country_pricing.price_monthly,
                        "price_yearly": country_pricing.price_yearly,
                        "tax_inclusive": country_pricing.tax_inclusive,
                        "is_active": country_pricing.is_available,
                    }

                    pricing_query = """
                    INSERT INTO tenants.plan_pricing 
                    (plan_id, country_code, currency, price_monthly, price_yearly, tax_inclusive, is_active)
                    VALUES (:plan_id, :country_code, :currency, :price_monthly, :price_yearly, :tax_inclusive, :is_active)
                    """

                    await session.execute(pricing_query, pricing_data)

            await session.commit()

            # Trigger Stripe sync
            try:
                await stripe_mapper.sync_plan_to_stripe(plan_id, session)
                logger.info(f"Successfully synced updated plan {plan_id} to Stripe")
            except Exception as e:
                logger.error(
                    f"Failed to sync updated plan {plan_id} to Stripe: {str(e)}"
                )

            return await _get_plan_response(plan_id, session)

    except Exception as e:
        logger.error(f"Error updating subscription plan {plan_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update plan: {str(e)}",
        )


# Monitoring Endpoints


@router.get("/health", response_model=SubscriptionHealthResponse)
async def get_subscription_health(current_user=Depends(require_superadmin)):
    """
    Get comprehensive subscription system health metrics.

    Provides overview of:
    - Plan and add-on counts
    - Multi-country coverage
    - Stripe sync status
    - Recent subscription activity
    - System issues
    """
    try:
        async with get_async_session() as session:
            # Get plan statistics
            plan_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) as total_plans,
                    COUNT(*) FILTER (WHERE is_active = true) as active_plans
                FROM tenants.subscription_plans
            """
            )
            plan_row = plan_stats.fetchone()

            # Get add-on statistics
            addon_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) as total_addons,
                    COUNT(*) FILTER (WHERE is_active = true) as active_addons
                FROM tenants.subscription_addons
            """
            )
            addon_row = addon_stats.fetchone()

            # Get country/currency coverage
            coverage_stats = await session.execute(
                """
                SELECT 
                    COUNT(DISTINCT country_code) as countries_supported,
                    COUNT(DISTINCT currency) as currencies_supported
                FROM tenants.plan_pricing
                WHERE is_active = true
            """
            )
            coverage_row = coverage_stats.fetchone()

            # Get Stripe sync status
            sync_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) as total_items,
                    COUNT(*) FILTER (WHERE stripe_product_id IS NOT NULL) as synced_products,
                    COUNT(*) FILTER (WHERE last_synced_at > NOW() - INTERVAL '24 hours') as recently_synced
                FROM (
                    SELECT stripe_product_id, last_synced_at FROM tenants.subscription_plans WHERE is_active = true
                    UNION ALL
                    SELECT stripe_product_id, last_synced_at FROM tenants.subscription_addons WHERE is_active = true
                ) combined
            """
            )
            sync_row = sync_stats.fetchone()

            # Get recent subscription activity (last 7 days)
            activity_stats = await session.execute(
                """
                SELECT 
                    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '7 days') as recent_subscriptions,
                    COUNT(*) FILTER (WHERE status = 'payment_failed' AND updated_at > NOW() - INTERVAL '7 days') as failed_payments
                FROM tenants.tenant_subscriptions
            """
            )
            activity_row = activity_stats.fetchone()

            # Calculate health score and identify issues
            issues = []
            health_factors = []

            # Check plan coverage
            if plan_row.active_plans < 3:
                issues.append("Less than 3 active subscription plans")
                health_factors.append(0.7)
            else:
                health_factors.append(1.0)

            # Check Stripe sync
            sync_percentage = sync_row.synced_products / max(sync_row.total_items, 1)
            if sync_percentage < 1.0:
                issues.append(
                    f"Only {sync_percentage:.1%} of products synced to Stripe"
                )
                health_factors.append(sync_percentage)
            else:
                health_factors.append(1.0)

            # Check multi-country coverage
            if coverage_row.countries_supported < 2:
                issues.append("Less than 2 countries supported")
                health_factors.append(0.5)
            else:
                health_factors.append(1.0)

            # Check recent sync activity
            recent_sync_percentage = sync_row.recently_synced / max(
                sync_row.total_items, 1
            )
            if recent_sync_percentage < 0.8:
                issues.append("Some products not recently synced to Stripe")
                health_factors.append(0.9)
            else:
                health_factors.append(1.0)

            # Calculate overall health score
            health_score = (
                sum(health_factors) / len(health_factors) if health_factors else 0.0
            )

            return SubscriptionHealthResponse(
                total_plans=plan_row.total_plans,
                active_plans=plan_row.active_plans,
                total_addons=addon_row.total_addons,
                active_addons=addon_row.active_addons,
                countries_supported=coverage_row.countries_supported,
                currencies_supported=coverage_row.currencies_supported,
                stripe_sync_status={
                    "total_items": sync_row.total_items,
                    "synced_products": sync_row.synced_products,
                    "recently_synced": sync_row.recently_synced,
                    "sync_percentage": sync_percentage,
                },
                recent_subscriptions=activity_row.recent_subscriptions or 0,
                failed_payments=activity_row.failed_payments or 0,
                health_score=health_score,
                issues=issues,
            )

    except Exception as e:
        logger.error(f"Error getting subscription health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get health status: {str(e)}",
        )


# Helper Functions


async def _get_plan_response(plan_id: UUID, session) -> PlanResponse:
    """Get a complete plan response with pricing information."""
    query = """
    SELECT 
        sp.id,
        sp.name,
        sp.code,
        sp.description,
        sp.features,
        sp.is_public,
        sp.is_active,
        sp.stripe_product_id,
        sp.last_synced_at,
        sp.created_at,
        sp.updated_at,
        COALESCE(
            json_agg(
                json_build_object(
                    'country_code', pp.country_code,
                    'currency', pp.currency,
                    'price_monthly', pp.price_monthly,
                    'price_yearly', pp.price_yearly,
                    'tax_inclusive', pp.tax_inclusive,
                    'is_active', pp.is_active,
                    'stripe_price_id', pp.stripe_price_id
                )
            ) FILTER (WHERE pp.id IS NOT NULL),
            '[]'::json
        ) as country_pricing
    FROM tenants.subscription_plans sp
    LEFT JOIN tenants.plan_pricing pp ON sp.id = pp.plan_id
    WHERE sp.id = :plan_id
    GROUP BY sp.id, sp.name, sp.code, sp.description, sp.features, 
             sp.is_public, sp.is_active, sp.stripe_product_id, 
             sp.last_synced_at, sp.created_at, sp.updated_at
    """

    result = await session.execute(query, {"plan_id": str(plan_id)})
    plan = result.fetchone()

    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"Plan {plan_id} not found"
        )

    return PlanResponse(
        id=str(plan.id),
        name=plan.name,
        code=plan.code,
        description=plan.description,
        features=plan.features,
        country_pricing=plan.country_pricing,
        is_public=plan.is_public,
        is_active=plan.is_active,
        stripe_product_id=plan.stripe_product_id,
        last_synced_at=plan.last_synced_at,
        created_at=plan.created_at,
        updated_at=plan.updated_at,
    )
