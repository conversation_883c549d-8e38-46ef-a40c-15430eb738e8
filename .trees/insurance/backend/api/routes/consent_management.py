"""
Consent Management API Routes

Provides GDPR and CCPA compliant consent management endpoints for:
- Recording user consent
- Retrieving consent status
- Updating consent preferences
- Withdrawing consent
- Exporting consent data
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from backend.db.supabase_client import get_supabase_client
from backend.api.dependencies.auth import get_current_user, UserContext
from backend.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/consent", tags=["consent-management"])


# Pydantic Models
class ConsentRequest(BaseModel):
    """Request model for recording consent"""

    consent_type: str = Field(
        ..., description="Type of consent (marketing, analytics, etc.)"
    )
    consent_given: bool = Field(
        ..., description="Whether consent is given or withdrawn"
    )
    consent_method: str = Field(..., description="Method of consent collection")
    legal_basis: str = Field(..., description="Legal basis for processing")
    purpose_description: str = Field(
        ..., description="Clear description of processing purpose"
    )
    data_categories: Optional[List[str]] = Field(
        default=[], description="Categories of personal data"
    )
    retention_period_days: Optional[int] = Field(
        default=None, description="Data retention period"
    )
    third_party_sharing: bool = Field(
        default=False, description="Whether data is shared with third parties"
    )
    automated_decision_making: bool = Field(
        default=False, description="Whether automated decisions are made"
    )

    @validator("consent_type")
    def validate_consent_type(cls, v):
        valid_types = [
            "marketing",
            "analytics",
            "cookies",
            "essential",
            "personalization",
            "communication",
            "profiling",
            "third_party_sharing",
            "automated_decisions",
        ]
        if v not in valid_types:
            raise ValueError(f"consent_type must be one of: {valid_types}")
        return v

    @validator("consent_method")
    def validate_consent_method(cls, v):
        valid_methods = [
            "signup",
            "banner",
            "preferences",
            "api",
            "import",
            "migration",
            "settings",
            "onboarding",
        ]
        if v not in valid_methods:
            raise ValueError(f"consent_method must be one of: {valid_methods}")
        return v

    @validator("legal_basis")
    def validate_legal_basis(cls, v):
        valid_bases = [
            "consent",
            "contract",
            "legitimate_interest",
            "legal_obligation",
            "vital_interests",
            "public_task",
        ]
        if v not in valid_bases:
            raise ValueError(f"legal_basis must be one of: {valid_bases}")
        return v


class ConsentResponse(BaseModel):
    """Response model for consent operations"""

    id: UUID
    consent_type: str
    consent_given: bool
    consent_date: datetime
    withdrawn_date: Optional[datetime]
    legal_basis: str
    purpose_description: str
    created_at: datetime
    updated_at: datetime


class ConsentSummaryResponse(BaseModel):
    """Response model for consent summary"""

    consent_type: str
    consent_given: bool
    consent_date: datetime
    withdrawn_date: Optional[datetime]
    legal_basis: str
    purpose_description: str


class ConsentPreferencesRequest(BaseModel):
    """Request model for updating consent preferences"""

    email_marketing: Optional[bool] = None
    email_product_updates: Optional[bool] = None
    email_security_alerts: Optional[bool] = None
    email_legal_notices: Optional[bool] = None
    analytics_tracking: Optional[bool] = None
    personalization: Optional[bool] = None
    third_party_sharing: Optional[bool] = None
    automated_decisions: Optional[bool] = None
    data_residency_preference: Optional[str] = None
    ccpa_opt_out_sale: Optional[bool] = None
    ccpa_opt_out_targeted_ads: Optional[bool] = None

    @validator("data_residency_preference")
    def validate_data_residency(cls, v):
        if v is not None and v not in ["US", "EU", "AUTO"]:
            raise ValueError("data_residency_preference must be one of: US, EU, AUTO")
        return v


class ConsentPreferencesResponse(BaseModel):
    """Response model for consent preferences"""

    id: UUID
    privacy_policy_version: str
    terms_of_service_version: str
    cookie_policy_version: str
    email_marketing: bool
    email_product_updates: bool
    email_security_alerts: bool
    email_legal_notices: bool
    analytics_tracking: bool
    personalization: bool
    third_party_sharing: bool
    automated_decisions: bool
    data_residency_preference: Optional[str]
    language_preference: str
    timezone: Optional[str]
    ccpa_opt_out_sale: bool
    ccpa_opt_out_targeted_ads: bool
    created_at: datetime
    updated_at: datetime


# Helper Functions
def get_client_info(request: Request) -> Dict[str, Any]:
    """Extract client information from request"""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "referrer_url": request.headers.get("referer"),
    }


async def get_user_identifiers(current_user: UserContext) -> Dict[str, Optional[UUID]]:
    """Get user identifiers for both auth and tenant users"""
    # UserContext has id (tenant user ID) and tenant_id
    tenant_user_id = current_user.id
    tenant_id = current_user.tenant_id

    # For now, we don't have a separate auth_user_id in UserContext
    # In a real implementation, this might be stored separately
    auth_user_id = None

    return {
        "auth_user_id": auth_user_id,
        "tenant_user_id": tenant_user_id,
        "tenant_id": tenant_id,
    }


# API Endpoints
@router.post("/record", response_model=ConsentResponse)
async def record_consent(
    consent_request: ConsentRequest,
    request: Request,
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Record user consent for a specific type of data processing.

    This endpoint allows users to give or withdraw consent for various
    types of data processing in compliance with GDPR and CCPA requirements.
    """
    try:
        logger.info(
            "Recording user consent",
            extra={
                "user_id": str(current_user.id),
                "consent_type": consent_request.consent_type,
                "consent_given": consent_request.consent_given,
                "consent_method": consent_request.consent_method,
            },
        )

        # Get user identifiers
        user_ids = await get_user_identifiers(current_user)

        # Get client information
        client_info = get_client_info(request)

        # Call the database function to record consent
        result = supabase.rpc(
            "record_user_consent",
            {
                "p_auth_user_id": user_ids["auth_user_id"],
                "p_tenant_user_id": user_ids["tenant_user_id"],
                "p_tenant_id": user_ids["tenant_id"],
                "p_consent_type": consent_request.consent_type,
                "p_consent_given": consent_request.consent_given,
                "p_consent_method": consent_request.consent_method,
                "p_legal_basis": consent_request.legal_basis,
                "p_purpose_description": consent_request.purpose_description,
                "p_ip_address": client_info["ip_address"],
                "p_user_agent": client_info["user_agent"],
            },
        ).execute()

        if result.data is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to record consent",
            )

        consent_id = result.data

        # Fetch the created consent record
        consent_record = (
            supabase.table("user_consent").select("*").eq("id", consent_id).execute()
        )

        if not consent_record.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Consent record not found after creation",
            )

        record = consent_record.data[0]

        logger.info(
            "Consent recorded successfully",
            extra={
                "user_id": str(current_user.id),
                "consent_id": str(consent_id),
                "consent_type": consent_request.consent_type,
            },
        )

        return ConsentResponse(**record)

    except Exception as e:
        logger.error(
            "Failed to record consent",
            extra={
                "user_id": str(current_user.id),
                "consent_type": consent_request.consent_type,
                "error": str(e),
                "error_type": type(e).__name__,
            },
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record consent",
        )


@router.get("/summary", response_model=List[ConsentSummaryResponse])
async def get_consent_summary(
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Get a summary of all consent records for the current user.

    Returns the latest consent status for each consent type,
    useful for privacy dashboards and consent management interfaces.
    """
    try:
        logger.info("Fetching consent summary", extra={"user_id": str(current_user.id)})

        # Get user identifiers
        user_ids = await get_user_identifiers(current_user)

        # Call the database function to get consent summary
        result = supabase.rpc(
            "get_user_consent_summary",
            {
                "p_auth_user_id": user_ids["auth_user_id"],
                "p_tenant_user_id": user_ids["tenant_user_id"],
            },
        ).execute()

        if result.data is None:
            return []

        consent_summary = [ConsentSummaryResponse(**record) for record in result.data]

        logger.info(
            "Consent summary retrieved successfully",
            extra={
                "user_id": str(current_user.id),
                "consent_count": len(consent_summary),
            },
        )

        return consent_summary

    except Exception as e:
        logger.error(
            "Failed to fetch consent summary",
            extra={"user_id": str(current_user.id), "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch consent summary",
        )


@router.get("/check/{consent_type}")
async def check_consent(
    consent_type: str,
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Check if user has given consent for a specific type of data processing.

    Returns a boolean indicating whether the user has active consent
    for the specified consent type.
    """
    try:
        logger.info(
            "Checking user consent",
            extra={"user_id": str(current_user.id), "consent_type": consent_type},
        )

        # Get user identifiers
        user_ids = await get_user_identifiers(current_user)

        # Call the database function to check consent
        result = supabase.rpc(
            "check_user_consent",
            {
                "p_auth_user_id": user_ids["auth_user_id"],
                "p_tenant_user_id": user_ids["tenant_user_id"],
                "p_consent_type": consent_type,
            },
        ).execute()

        has_consent = result.data if result.data is not None else False

        logger.info(
            "Consent check completed",
            extra={
                "user_id": str(current_user.id),
                "consent_type": consent_type,
                "has_consent": has_consent,
            },
        )

        return {"consent_type": consent_type, "has_consent": has_consent}

    except Exception as e:
        logger.error(
            "Failed to check consent",
            extra={
                "user_id": str(current_user.id),
                "consent_type": consent_type,
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check consent",
        )


@router.post("/withdraw/{consent_type}")
async def withdraw_consent(
    consent_type: str,
    request: Request,
    withdrawal_reason: Optional[str] = None,
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Withdraw consent for a specific type of data processing.

    This endpoint allows users to withdraw previously given consent
    in compliance with GDPR Article 7(3) - right to withdraw consent.
    """
    try:
        logger.info(
            "Withdrawing user consent",
            extra={
                "user_id": str(current_user.id),
                "consent_type": consent_type,
                "withdrawal_reason": withdrawal_reason,
            },
        )

        # Create withdrawal request
        withdrawal_request = ConsentRequest(
            consent_type=consent_type,
            consent_given=False,
            consent_method="api",
            legal_basis="consent",
            purpose_description=f"Withdrawal of {consent_type} consent",
        )

        # Record the withdrawal
        return await record_consent(withdrawal_request, request, current_user, supabase)

    except Exception as e:
        logger.error(
            "Failed to withdraw consent",
            extra={
                "user_id": str(current_user.id),
                "consent_type": consent_type,
                "error": str(e),
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to withdraw consent",
        )


@router.get("/preferences", response_model=ConsentPreferencesResponse)
async def get_consent_preferences(
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Get user's consent preferences and privacy settings.

    Returns the user's current privacy preferences including
    communication settings, data processing preferences, and regional settings.
    """
    try:
        logger.info(
            "Fetching consent preferences", extra={"user_id": str(current_user.id)}
        )

        # Get user identifiers
        user_ids = await get_user_identifiers(current_user)

        # Query user consent preferences
        query = supabase.table("user_consent_preferences").select("*")

        if user_ids["auth_user_id"]:
            query = query.eq("auth_user_id", user_ids["auth_user_id"])
        elif user_ids["tenant_user_id"]:
            query = query.eq("tenant_user_id", user_ids["tenant_user_id"])
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unable to identify user",
            )

        result = query.execute()

        if not result.data:
            # Create default preferences if none exist
            default_preferences = {
                "auth_user_id": user_ids["auth_user_id"],
                "tenant_user_id": user_ids["tenant_user_id"],
                "tenant_id": user_ids["tenant_id"],
                "email_marketing": False,
                "email_product_updates": True,
                "email_security_alerts": True,
                "email_legal_notices": True,
                "analytics_tracking": False,
                "personalization": False,
                "third_party_sharing": False,
                "automated_decisions": False,
                "data_residency_preference": "AUTO",
                "language_preference": "en",
                "ccpa_opt_out_sale": False,
                "ccpa_opt_out_targeted_ads": False,
                "last_updated_by": "system",
            }

            create_result = (
                supabase.table("user_consent_preferences")
                .insert(default_preferences)
                .execute()
            )

            if not create_result.data:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create default preferences",
                )

            preferences = create_result.data[0]
        else:
            preferences = result.data[0]

        logger.info(
            "Consent preferences retrieved successfully",
            extra={"user_id": str(current_user.id)},
        )

        return ConsentPreferencesResponse(**preferences)

    except Exception as e:
        logger.error(
            "Failed to fetch consent preferences",
            extra={"user_id": str(current_user.id), "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch consent preferences",
        )


@router.put("/preferences", response_model=ConsentPreferencesResponse)
async def update_consent_preferences(
    preferences_request: ConsentPreferencesRequest,
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Update user's consent preferences and privacy settings.

    Allows users to modify their privacy preferences including
    communication settings, data processing preferences, and regional settings.
    """
    try:
        logger.info(
            "Updating consent preferences", extra={"user_id": str(current_user.id)}
        )

        # Get user identifiers
        user_ids = await get_user_identifiers(current_user)

        # Prepare update data (only include non-None values)
        update_data = {
            "last_updated_by": "user",
            "updated_at": datetime.utcnow().isoformat(),
        }

        for field, value in preferences_request.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value

        # Update preferences
        query = supabase.table("user_consent_preferences").update(update_data)

        if user_ids["auth_user_id"]:
            query = query.eq("auth_user_id", user_ids["auth_user_id"])
        elif user_ids["tenant_user_id"]:
            query = query.eq("tenant_user_id", user_ids["tenant_user_id"])
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unable to identify user",
            )

        result = query.execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Consent preferences not found",
            )

        updated_preferences = result.data[0]

        logger.info(
            "Consent preferences updated successfully",
            extra={"user_id": str(current_user.id)},
        )

        return ConsentPreferencesResponse(**updated_preferences)

    except Exception as e:
        logger.error(
            "Failed to update consent preferences",
            extra={"user_id": str(current_user.id), "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update consent preferences",
        )


@router.get("/export")
async def export_consent_data(
    current_user: UserContext = Depends(get_current_user),
    supabase=Depends(get_supabase_client),
):
    """
    Export all consent data for the current user.

    Provides a complete export of all consent records and preferences
    in compliance with GDPR Article 20 (Right to data portability) and
    CCPA Right to Know requirements.
    """
    try:
        logger.info("Exporting consent data", extra={"user_id": str(current_user.id)})

        # Get user identifiers
        user_ids = await get_user_identifiers(current_user)

        # Export consent records
        consent_query = supabase.table("user_consent").select("*")
        if user_ids["auth_user_id"]:
            consent_query = consent_query.eq("auth_user_id", user_ids["auth_user_id"])
        elif user_ids["tenant_user_id"]:
            consent_query = consent_query.eq(
                "tenant_user_id", user_ids["tenant_user_id"]
            )

        consent_result = consent_query.execute()

        # Export preferences
        prefs_query = supabase.table("user_consent_preferences").select("*")
        if user_ids["auth_user_id"]:
            prefs_query = prefs_query.eq("auth_user_id", user_ids["auth_user_id"])
        elif user_ids["tenant_user_id"]:
            prefs_query = prefs_query.eq("tenant_user_id", user_ids["tenant_user_id"])

        prefs_result = prefs_query.execute()

        # Prepare export data
        export_data = {
            "export_metadata": {
                "export_date": datetime.utcnow().isoformat(),
                "user_id": str(current_user.id),
                "export_format": "JSON",
                "data_controller": "PI Lawyer AI",
                "export_purpose": "GDPR Article 20 / CCPA Right to Know",
            },
            "consent_records": consent_result.data or [],
            "consent_preferences": prefs_result.data[0] if prefs_result.data else None,
            "data_categories": [
                "Identity Data (user IDs, email)",
                "Contact Data (IP address)",
                "Technical Data (user agent, browser info)",
                "Preference Data (consent choices, communication preferences)",
                "Usage Data (consent timestamps, withdrawal history)",
            ],
            "legal_bases": [
                "Consent (GDPR Article 6(1)(a))",
                "Contract (GDPR Article 6(1)(b))",
                "Legitimate Interest (GDPR Article 6(1)(f))",
            ],
            "retention_information": {
                "consent_records": "Retained for 7 years after withdrawal for legal compliance",
                "preferences": "Retained while account is active plus 1 year",
                "technical_data": "Retained for 2 years for security purposes",
            },
            "your_rights": {
                "access": "You can request access to your personal data",
                "rectification": "You can request correction of inaccurate data",
                "erasure": "You can request deletion of your data (right to be forgotten)",
                "restrict_processing": "You can request restriction of processing",
                "data_portability": "You can request your data in a portable format",
                "object": "You can object to processing based on legitimate interests",
                "withdraw_consent": "You can withdraw consent at any time",
            },
        }

        logger.info(
            "Consent data exported successfully",
            extra={
                "user_id": str(current_user.id),
                "consent_records_count": len(consent_result.data or []),
                "has_preferences": bool(prefs_result.data),
            },
        )

        return export_data

    except Exception as e:
        logger.error(
            "Failed to export consent data",
            extra={"user_id": str(current_user.id), "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export consent data",
        )
