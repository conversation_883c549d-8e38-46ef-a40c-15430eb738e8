#!/usr/bin/env python3
"""
Webhook Retry Management API for PI Lawyer AI
API endpoints for monitoring and managing webhook retry operations.

This provides superadmin interfaces for webhook retry monitoring and manual intervention.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Dict, Any, List, Optional
from datetime import datetime

from backend.services.webhook_retry_service import (
    webhook_retry_service,
    WebhookRetryStatus,
)
from backend.middleware.auth import require_role
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)

# Create router
router = APIRouter(
    prefix="/api/admin/webhooks/retry", tags=["Webhook Retry Management"]
)


@router.get("/status")
async def get_retry_queue_status(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get current status of webhook retry queue.

    Returns:
        Current retry queue statistics
    """
    try:
        status = await webhook_retry_service.get_retry_queue_status()

        return {
            "status": "success",
            "data": status,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting retry queue status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get retry queue status: {str(e)}"
        )


@router.get("/queue")
async def get_retry_queue(
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(
        50, ge=1, le=500, description="Maximum number of entries to return"
    ),
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get webhook retry queue entries.

    Args:
        status: Optional status filter
        limit: Maximum number of entries to return

    Returns:
        List of retry queue entries
    """
    try:
        # Get retry queue entries
        retry_entries = []

        for event_id, entry in webhook_retry_service.retry_queue.items():
            if status and entry["status"].value != status:
                continue

            # Sanitize entry for API response
            sanitized_entry = {
                "event_id": entry["event_id"],
                "event_type": entry["event_type"],
                "status": entry["status"].value,
                "attempt_count": entry["attempt_count"],
                "max_attempts": entry["max_attempts"],
                "created_at": entry["created_at"].isoformat(),
                "last_attempt_at": (
                    entry["last_attempt_at"].isoformat()
                    if entry["last_attempt_at"]
                    else None
                ),
                "next_retry_at": (
                    entry["next_retry_at"].isoformat()
                    if entry["next_retry_at"]
                    else None
                ),
                "total_processing_time": entry["total_processing_time"],
                "failure_count": len(entry["failure_history"]),
                "region": entry.get("regional_context", {}).get("region", "US"),
                "currency": entry.get("regional_context", {}).get("currency", "USD"),
            }

            retry_entries.append(sanitized_entry)

            if len(retry_entries) >= limit:
                break

        # Sort by creation time (newest first)
        retry_entries.sort(key=lambda x: x["created_at"], reverse=True)

        return {
            "status": "success",
            "data": {
                "entries": retry_entries,
                "total_count": len(webhook_retry_service.retry_queue),
                "returned_count": len(retry_entries),
            },
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting retry queue: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get retry queue: {str(e)}"
        )


@router.get("/dead-letter")
async def get_dead_letter_queue(
    limit: int = Query(
        50, ge=1, le=500, description="Maximum number of entries to return"
    ),
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get dead letter queue entries.

    Args:
        limit: Maximum number of entries to return

    Returns:
        List of dead letter queue entries
    """
    try:
        dead_letter_entries = []

        for event_id, entry in webhook_retry_service.dead_letter_queue.items():
            # Sanitize entry for API response
            sanitized_entry = {
                "event_id": entry["event_id"],
                "event_type": entry["event_type"],
                "status": entry["status"].value,
                "attempt_count": entry["attempt_count"],
                "max_attempts": entry["max_attempts"],
                "created_at": entry["created_at"].isoformat(),
                "moved_to_dlq_at": entry["moved_to_dlq_at"].isoformat(),
                "dlq_retention_until": entry["dlq_retention_until"].isoformat(),
                "total_processing_time": entry["total_processing_time"],
                "failure_history": entry["failure_history"],
                "region": entry.get("regional_context", {}).get("region", "US"),
                "currency": entry.get("regional_context", {}).get("currency", "USD"),
            }

            dead_letter_entries.append(sanitized_entry)

            if len(dead_letter_entries) >= limit:
                break

        # Sort by moved to DLQ time (newest first)
        dead_letter_entries.sort(key=lambda x: x["moved_to_dlq_at"], reverse=True)

        return {
            "status": "success",
            "data": {
                "entries": dead_letter_entries,
                "total_count": len(webhook_retry_service.dead_letter_queue),
                "returned_count": len(dead_letter_entries),
            },
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting dead letter queue: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get dead letter queue: {str(e)}"
        )


@router.get("/entry/{event_id}")
async def get_retry_entry_details(
    event_id: str, _: dict = Depends(require_role(["admin", "superadmin"]))
) -> Dict[str, Any]:
    """
    Get detailed information about a specific retry entry.

    Args:
        event_id: Webhook event ID

    Returns:
        Detailed retry entry information
    """
    try:
        # Check retry queue first
        if event_id in webhook_retry_service.retry_queue:
            entry = webhook_retry_service.retry_queue[event_id]
            queue_type = "retry_queue"
        elif event_id in webhook_retry_service.dead_letter_queue:
            entry = webhook_retry_service.dead_letter_queue[event_id]
            queue_type = "dead_letter_queue"
        else:
            raise HTTPException(
                status_code=404, detail=f"Webhook entry {event_id} not found"
            )

        # Create detailed response
        detailed_entry = {
            "event_id": entry["event_id"],
            "event_type": entry["event_type"],
            "queue_type": queue_type,
            "status": entry["status"].value,
            "attempt_count": entry["attempt_count"],
            "max_attempts": entry["max_attempts"],
            "created_at": entry["created_at"].isoformat(),
            "last_attempt_at": (
                entry["last_attempt_at"].isoformat()
                if entry["last_attempt_at"]
                else None
            ),
            "next_retry_at": (
                entry["next_retry_at"].isoformat() if entry["next_retry_at"] else None
            ),
            "total_processing_time": entry["total_processing_time"],
            "failure_history": entry["failure_history"],
            "regional_context": entry.get("regional_context", {}),
            "retry_config": entry.get("retry_config", {}),
            "reprocessed_from_dlq": entry.get("reprocessed_from_dlq", False),
        }

        # Add DLQ-specific fields if applicable
        if queue_type == "dead_letter_queue":
            detailed_entry.update(
                {
                    "moved_to_dlq_at": entry["moved_to_dlq_at"].isoformat(),
                    "dlq_retention_until": entry["dlq_retention_until"].isoformat(),
                }
            )

        return {
            "status": "success",
            "data": detailed_entry,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting retry entry details: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get retry entry details: {str(e)}"
        )


@router.post("/reprocess/{event_id}")
async def reprocess_dead_letter_webhook(
    event_id: str, _: dict = Depends(require_role(["admin", "superadmin"]))
) -> Dict[str, Any]:
    """
    Manually reprocess a webhook from dead letter queue.

    Args:
        event_id: Webhook event ID to reprocess

    Returns:
        Reprocessing result
    """
    try:
        logger.info(f"Manual reprocessing requested for webhook {event_id}")

        result = await webhook_retry_service.reprocess_dead_letter_webhook(event_id)

        if result["status"] == "not_found":
            raise HTTPException(status_code=404, detail=result["message"])

        logger.info(f"Webhook {event_id} reprocessing completed: {result['status']}")

        return {
            "status": "success",
            "data": result,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing webhook {event_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to reprocess webhook: {str(e)}"
        )


@router.delete("/cleanup-expired")
async def cleanup_expired_dead_letters(
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Clean up expired entries from dead letter queue.

    Returns:
        Cleanup result with count of removed entries
    """
    try:
        logger.info("Manual cleanup of expired dead letter entries requested")

        removed_count = await webhook_retry_service.cleanup_expired_dead_letters()

        logger.info(f"Cleaned up {removed_count} expired dead letter entries")

        return {
            "status": "success",
            "data": {
                "removed_count": removed_count,
                "message": f"Removed {removed_count} expired entries from dead letter queue",
            },
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error cleaning up expired dead letters: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to cleanup expired entries: {str(e)}"
        )


@router.get("/metrics")
async def get_webhook_retry_metrics(
    hours: int = Query(
        24, ge=1, le=168, description="Number of hours to include in metrics"
    ),
    _: dict = Depends(require_role(["admin", "superadmin"])),
) -> Dict[str, Any]:
    """
    Get webhook retry metrics and statistics.

    Args:
        hours: Number of hours to include in metrics

    Returns:
        Webhook retry metrics
    """
    try:
        # Calculate metrics from current queues
        retry_queue = webhook_retry_service.retry_queue
        dead_letter_queue = webhook_retry_service.dead_letter_queue

        # Status distribution
        status_distribution = {}
        for entry in retry_queue.values():
            status = entry["status"].value
            status_distribution[status] = status_distribution.get(status, 0) + 1

        # Regional distribution
        regional_distribution = {}
        for entry in list(retry_queue.values()) + list(dead_letter_queue.values()):
            region = entry.get("regional_context", {}).get("region", "US")
            regional_distribution[region] = regional_distribution.get(region, 0) + 1

        # Event type distribution
        event_type_distribution = {}
        for entry in list(retry_queue.values()) + list(dead_letter_queue.values()):
            event_type = entry["event_type"]
            event_type_distribution[event_type] = (
                event_type_distribution.get(event_type, 0) + 1
            )

        # Failure category analysis
        failure_categories = {}
        for entry in list(retry_queue.values()) + list(dead_letter_queue.values()):
            for failure in entry["failure_history"]:
                category = failure["category"]
                failure_categories[category] = failure_categories.get(category, 0) + 1

        metrics = {
            "queue_status": await webhook_retry_service.get_retry_queue_status(),
            "status_distribution": status_distribution,
            "regional_distribution": regional_distribution,
            "event_type_distribution": event_type_distribution,
            "failure_categories": failure_categories,
            "total_failures": sum(
                len(entry["failure_history"])
                for entry in list(retry_queue.values())
                + list(dead_letter_queue.values())
            ),
            "average_attempts": sum(
                entry["attempt_count"]
                for entry in list(retry_queue.values())
                + list(dead_letter_queue.values())
            )
            / max(len(retry_queue) + len(dead_letter_queue), 1),
        }

        return {
            "status": "success",
            "data": metrics,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting webhook retry metrics: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get retry metrics: {str(e)}"
        )


@router.get("/health")
async def get_webhook_retry_health() -> Dict[str, Any]:
    """
    Get webhook retry service health status.

    Returns:
        Health status of webhook retry service
    """
    try:
        status = await webhook_retry_service.get_retry_queue_status()

        # Determine health based on queue sizes
        health_status = "healthy"
        warnings = []

        if status["dead_letter_queue_size"] > 10:
            health_status = "warning"
            warnings.append(
                f"High dead letter queue size: {status['dead_letter_queue_size']}"
            )

        if status["pending_webhooks"] > 50:
            health_status = "warning"
            warnings.append(f"High pending webhook count: {status['pending_webhooks']}")

        if status["dead_letter_queue_size"] > 50:
            health_status = "critical"
            warnings.append(
                f"Critical dead letter queue size: {status['dead_letter_queue_size']}"
            )

        return {
            "status": "success",
            "data": {
                "health_status": health_status,
                "warnings": warnings,
                "queue_status": status,
            },
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting webhook retry health: {e}", exc_info=True)
        return {
            "status": "error",
            "data": {
                "health_status": "critical",
                "warnings": [f"Health check failed: {str(e)}"],
            },
            "timestamp": datetime.utcnow().isoformat(),
        }
