"""
Stripe webhook handler.

This module provides a webhook handler for Stripe events,
specifically for handling subscription and payment events.
"""

import json
import stripe
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import Response
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from backend.config import settings
from backend.db.session import get_db_session
from backend.models.firm import Firm
from backend.models.subscription import TenantSubscription, TenantAddon
from backend.utils.logging import get_logger
from backend.services.webhook_idempotency import (
    is_webhook_processed,
    mark_webhook_processed,
)
from backend.services.multi_currency_webhook_handler import multi_currency_handler
from backend.services.tax_webhook_handler import tax_webhook_handler
from backend.services.regional_webhook_router import regional_webhook_router
from backend.services.webhook_retry_service import webhook_retry_service

# Configure logging
logger = get_logger(__name__)

# Configure Stripe
stripe.api_key = settings.stripe_secret_key

# Create router
router = APIRouter(prefix="/api/webhooks", tags=["webhooks", "stripe"])


@router.post("/stripe")
async def stripe_webhook(
    request: Request, db: AsyncSession = Depends(get_db_session)
) -> Response:
    """
    Handle Stripe webhook events.

    This endpoint receives webhook events from Stripe and processes them
    to keep the local subscription data in sync.

    Args:
        request: The HTTP request containing the webhook payload
        db: Database session

    Returns:
        Response: HTTP 200 response to acknowledge receipt

    Raises:
        HTTPException: If signature verification fails or processing errors occur
    """
    # Get the raw payload and signature
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")

    if not sig_header:
        logger.error("Missing stripe-signature header")
        raise HTTPException(status_code=400, detail="Missing stripe-signature header")

    try:
        # Verify the webhook signature
        event = stripe.Webhook.construct_event(
            payload=payload,
            sig_header=sig_header,
            secret=settings.stripe_webhook_secret,
        )
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Webhook signature verification failed: {e}")
        raise HTTPException(
            status_code=400, detail=f"Webhook signature error: {e}"
        ) from e
    except Exception as e:
        logger.error(f"Webhook parse error: {e}")
        raise HTTPException(status_code=400, detail=f"Webhook parse error: {e}") from e

    # Log the event
    event_type = event["type"]
    event_id = event["id"]
    logger.info(f"Processing Stripe webhook event: {event_type} (ID: {event_id})")

    # Check idempotency - prevent duplicate processing
    if await is_webhook_processed(event):
        logger.info(f"Webhook event already processed: {event_type} (ID: {event_id})")
        return Response(status_code=200)

    try:
        # Route webhook event to appropriate regional handler
        routing_result = await regional_webhook_router.route_webhook_event(event)

        if routing_result["status"] != "success":
            logger.error(f"Webhook routing failed: {routing_result}")
            if routing_result["status"] == "compliance_failed":
                raise HTTPException(
                    status_code=400,
                    detail=f"Data residency compliance failed: {routing_result['violations']}",
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=f"Webhook routing failed: {routing_result.get('error')}",
                )

        # Extract routing information
        region = routing_result["region"]
        currency = routing_result["currency"]
        regional_context = routing_result["regional_context"]
        db_config = routing_result["database_config"]

        # Log routing context
        logger.info(
            f"Processing {event_type} for region {region} with currency {currency} (routed to {db_config['supabase_project']})"
        )

        # Log routing metrics
        await regional_webhook_router.log_routing_metrics(event, routing_result)

        # Process webhook with retry logic
        processing_start = datetime.utcnow()

        # Create webhook handler function
        async def webhook_handler(webhook_event, regional_context=None):
            return await _process_webhook_event(webhook_event, db, regional_context)

        # Process with retry service
        retry_result = await webhook_retry_service.process_webhook_with_retry(
            event, webhook_handler, regional_context
        )

        if retry_result["status"] not in ["success", "already_processed"]:
            logger.error(f"Webhook processing failed after retries: {retry_result}")
            raise HTTPException(
                status_code=500,
                detail=f"Webhook processing failed: {retry_result.get('message', 'Unknown error')}",
            )

        # Calculate processing time and log metrics
        processing_end = datetime.utcnow()
        processing_time_ms = int(
            (processing_end - processing_start).total_seconds() * 1000
        )

        result = {
            "status": "success",
            "processing_time_ms": processing_time_ms,
            "region": region,
            "currency": currency,
            "database_project": db_config["supabase_project"],
            "handler_class": routing_result["handler_class"],
        }

        await multi_currency_handler.log_multi_currency_event(
            event, regional_context, result
        )

        # Commit any database changes
        await db.commit()

        # Mark event as processed for idempotency
        await mark_webhook_processed(
            event, {"status": "success", "processed_at": datetime.utcnow().isoformat()}
        )

        logger.info(
            f"Successfully processed Stripe webhook event: {event_type} (ID: {event_id})"
        )

    except Exception as e:
        logger.error(
            f"Error processing Stripe webhook event {event_type} (ID: {event_id}): {e}",
            exc_info=True,
        )
        await db.rollback()
        # Don't raise the exception - return 200 to prevent Stripe retries for processing errors
        # The error is logged for investigation

    # Always return 200 to acknowledge receipt
    return Response(status_code=200)


async def _process_webhook_event(
    event: Dict[str, Any], db: AsyncSession, regional_context: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Process webhook event with proper error handling and regional context.

    Args:
        event: Stripe webhook event
        db: Database session
        regional_context: Regional processing context

    Returns:
        Processing result
    """
    event_type = event.get("type")
    event_data = event.get("data", {}).get("object", {})

    try:
        if event_type == "checkout.session.completed":
            await _handle_checkout_completed(event_data, db, regional_context)

        elif event_type in (
            "customer.subscription.created",
            "customer.subscription.updated",
        ):
            await _handle_subscription_upsert(event_data, db, regional_context)

        elif event_type == "customer.subscription.deleted":
            await _handle_subscription_deleted(event_data, db, regional_context)

        elif event_type in ("invoice.payment_succeeded", "invoice.payment_failed"):
            await _handle_invoice_event(
                event_data,
                success=(event_type == "invoice.payment_succeeded"),
                db=db,
                regional_context=regional_context,
            )

        elif event_type in ("tax.rate.created", "tax.rate.updated"):
            await _handle_tax_rate_event(event_data, db, regional_context)

        elif event_type in (
            "invoice.tax_calculation_succeeded",
            "invoice.tax_calculation_failed",
        ):
            await _handle_tax_calculation_event(
                event_data,
                success=(event_type == "invoice.tax_calculation_succeeded"),
                db=db,
                regional_context=regional_context,
            )

        else:
            logger.info(f"Unhandled Stripe event type: {event_type}")
            return {"status": "unhandled", "event_type": event_type}

        return {"status": "success", "event_type": event_type}

    except Exception as e:
        logger.error(f"Error processing webhook event {event_type}: {e}", exc_info=True)
        raise


async def _handle_checkout_completed(
    session: Dict[str, Any], db: AsyncSession, regional_context: Dict[str, Any] = None
) -> None:
    """
    Handle checkout.session.completed event.

    This links the Stripe customer to a tenant and sets up the initial subscription.

    Args:
        session: The Stripe checkout session object
        db: Database session
    """
    stripe_customer_id = session.get("customer")
    subscription_id = session.get("subscription")
    customer_email = (session.get("customer_details") or {}).get("email")
    metadata = session.get("metadata") or {}

    # Log regional context if available
    if regional_context:
        region = regional_context.get("region", "US")
        currency = regional_context.get("currency", "USD")
        logger.info(
            f"Processing checkout completion for customer {stripe_customer_id}, email: {customer_email} in region {region} with currency {currency}"
        )
    else:
        logger.info(
            f"Processing checkout completion for customer {stripe_customer_id}, email: {customer_email}"
        )

    if not customer_email:
        logger.warning("No customer email found in checkout session")
        return

    # Find the firm by email
    result = await db.execute(select(Firm).where(Firm.primary_email == customer_email))
    firm = result.scalar_one_or_none()

    if not firm:
        logger.warning(f"No firm found with email {customer_email}")
        return

    # Update the firm with Stripe customer ID
    if stripe_customer_id:
        await db.execute(
            update(Firm)
            .where(Firm.id == firm.id)
            .values(stripe_customer_id=stripe_customer_id)
        )
        logger.info(f"Linked firm {firm.id} to Stripe customer {stripe_customer_id}")

    logger.info(f"Checkout completed for firm {firm.name} (ID: {firm.id})")


async def _handle_subscription_upsert(
    subscription: Dict[str, Any],
    db: AsyncSession,
    regional_context: Dict[str, Any] = None,
) -> None:
    """
    Handle subscription created/updated events.

    Args:
        subscription: The Stripe subscription object
        db: Database session
    """
    stripe_subscription_id = subscription["id"]
    stripe_customer_id = subscription["customer"]
    status = subscription["status"]
    current_period_end = datetime.fromtimestamp(subscription["current_period_end"])
    current_period_start = datetime.fromtimestamp(subscription["current_period_start"])

    # Get trial information if present
    trial_start = None
    trial_end = None
    if subscription.get("trial_start"):
        trial_start = datetime.fromtimestamp(subscription["trial_start"])
    if subscription.get("trial_end"):
        trial_end = datetime.fromtimestamp(subscription["trial_end"])

    logger.info(
        f"Processing subscription {stripe_subscription_id} for customer {stripe_customer_id}"
    )

    # Find the firm by Stripe customer ID
    result = await db.execute(
        select(Firm).where(Firm.stripe_customer_id == stripe_customer_id)
    )
    firm = result.scalar_one_or_none()

    if not firm:
        logger.warning(f"No firm found with Stripe customer ID {stripe_customer_id}")
        return

    # Find existing subscription
    result = await db.execute(
        select(TenantSubscription).where(
            TenantSubscription.payment_provider_subscription_id
            == stripe_subscription_id
        )
    )
    existing_subscription = result.scalar_one_or_none()

    if existing_subscription:
        # Update existing subscription
        await db.execute(
            update(TenantSubscription)
            .where(TenantSubscription.id == existing_subscription.id)
            .values(
                status=status,
                current_period_start=current_period_start,
                current_period_end=current_period_end,
                trial_start=trial_start,
                trial_end=trial_end,
                updated_at=datetime.utcnow(),
            )
        )
        logger.info(
            f"Updated subscription {existing_subscription.id} for firm {firm.id}"
        )
    else:
        logger.info(
            f"Subscription {stripe_subscription_id} not found in database - may need manual linking"
        )

    logger.info(f"Processed subscription update for firm {firm.name} (ID: {firm.id})")


async def _handle_subscription_deleted(
    subscription: Dict[str, Any],
    db: AsyncSession,
    regional_context: Dict[str, Any] = None,
) -> None:
    """
    Handle subscription deleted events.

    Args:
        subscription: The Stripe subscription object
        db: Database session
    """
    stripe_subscription_id = subscription["id"]

    logger.info(f"Processing subscription deletion: {stripe_subscription_id}")

    # Find and cancel the subscription
    result = await db.execute(
        select(TenantSubscription).where(
            TenantSubscription.payment_provider_subscription_id
            == stripe_subscription_id
        )
    )
    tenant_subscription = result.scalar_one_or_none()

    if tenant_subscription:
        await db.execute(
            update(TenantSubscription)
            .where(TenantSubscription.id == tenant_subscription.id)
            .values(
                status="canceled",
                canceled_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
        )
        logger.info(f"Canceled subscription {tenant_subscription.id}")
    else:
        logger.warning(
            f"Subscription {stripe_subscription_id} not found for cancellation"
        )


async def _handle_invoice_event(
    invoice: Dict[str, Any],
    success: bool,
    db: AsyncSession,
    regional_context: Dict[str, Any] = None,
) -> None:
    """
    Handle invoice payment events.

    Args:
        invoice: The Stripe invoice object
        success: Whether the payment was successful
        db: Database session
    """
    invoice_id = invoice["id"]
    stripe_subscription_id = invoice.get("subscription")

    status_text = "succeeded" if success else "failed"
    logger.info(f"Processing invoice payment {status_text}: {invoice_id}")

    if stripe_subscription_id:
        # Find the subscription and update status if needed
        result = await db.execute(
            select(TenantSubscription).where(
                TenantSubscription.payment_provider_subscription_id
                == stripe_subscription_id
            )
        )
        tenant_subscription = result.scalar_one_or_none()

        if tenant_subscription:
            # Update subscription status based on payment result
            new_status = "active" if success else "past_due"
            await db.execute(
                update(TenantSubscription)
                .where(TenantSubscription.id == tenant_subscription.id)
                .values(status=new_status, updated_at=datetime.utcnow())
            )
            logger.info(
                f"Updated subscription {tenant_subscription.id} status to {new_status}"
            )

    logger.info(f"Processed invoice {invoice_id} payment {status_text}")


async def _handle_tax_rate_event(
    tax_rate_data: Dict[str, Any],
    db: AsyncSession,
    regional_context: Dict[str, Any] = None,
):
    """Handle tax rate created/updated events from Stripe."""
    try:
        tax_rate_id = tax_rate_data.get("id")

        # Use enhanced tax webhook handler with regional context
        if regional_context:
            result = await tax_webhook_handler.handle_tax_rate_event(
                tax_rate_data, regional_context
            )

            if result["status"] == "success":
                logger.info(
                    f"Tax rate {tax_rate_id} processed successfully for region {result['region']}"
                )
            else:
                logger.error(
                    f"Tax rate processing failed: {result.get('errors', result.get('error'))}"
                )
        else:
            # Fallback to basic processing
            country = tax_rate_data.get("country")
            percentage = tax_rate_data.get("percentage", 0)
            tax_type = tax_rate_data.get("tax_type", "vat")
            logger.info(
                f"Processing tax rate event: {tax_rate_id} for {country} (no regional context)"
            )
            logger.info(
                f"Tax rate {tax_rate_id}: {percentage}% {tax_type} for {country}"
            )

    except Exception as e:
        logger.error(f"Error processing tax rate event: {e}", exc_info=True)


async def _handle_tax_calculation_event(
    invoice_data: Dict[str, Any],
    success: bool,
    db: AsyncSession,
    regional_context: Dict[str, Any] = None,
):
    """Handle tax calculation succeeded/failed events from Stripe with multi-country support."""
    try:
        invoice_id = invoice_data.get("id")
        subscription_id = invoice_data.get("subscription")
        tax_calculation_id = invoice_data.get("tax_calculation_id")

        status_text = "succeeded" if success else "failed"
        logger.info(
            f"Processing tax calculation {status_text} for invoice {invoice_id}"
        )

        # Use enhanced tax webhook handler with regional context
        if regional_context:
            tax_result = await tax_webhook_handler.handle_tax_calculation_event(
                invoice_data, success, regional_context
            )

            if tax_result["status"] == "success":
                logger.info(
                    f"Enhanced tax calculation processed for invoice {invoice_id} in region {tax_result['region']}"
                )
            else:
                logger.error(
                    f"Enhanced tax calculation processing failed: {tax_result.get('error')}"
                )
        else:
            logger.info(f"Processing tax calculation without regional context")

        if not subscription_id:
            logger.warning(
                f"No subscription ID in tax calculation event for invoice {invoice_id}"
            )
            return

        # Find the subscription in our database
        result = await db.execute(
            select(TenantSubscription).where(
                TenantSubscription.payment_provider_subscription_id == subscription_id
            )
        )
        tenant_subscription = result.scalar_one_or_none()

        if tenant_subscription:
            # Update subscription with tax calculation status
            tax_status = "calculated" if success else "failed"
            await db.execute(
                update(TenantSubscription)
                .where(TenantSubscription.id == tenant_subscription.id)
                .values(
                    tax_status=tax_status,
                    tax_calculation_id=tax_calculation_id,
                    updated_at=datetime.utcnow(),
                )
            )

            # If successful, extract and save tax information
            if success and invoice_data.get("tax"):
                tax_amount = invoice_data["tax"] / 100  # Convert from cents
                total_amount = invoice_data.get("amount_paid", 0) / 100
                subtotal_amount = total_amount - tax_amount

                # Update subscription with tax amounts
                await db.execute(
                    update(TenantSubscription)
                    .where(TenantSubscription.id == tenant_subscription.id)
                    .values(tax_amount=tax_amount, updated_at=datetime.utcnow())
                )

                # Save detailed tax calculation if available
                if tax_calculation_id:
                    from backend.services.tax_calculation_service import (
                        tax_calculation_service,
                    )

                    # Create tax calculation record
                    customer_country = invoice_data.get(
                        "customer_tax_location", {}
                    ).get("country", "US")
                    currency = invoice_data.get("currency", "usd").upper()

                    # Note: This is a simplified version. In a real implementation,
                    # you would fetch the full tax calculation details from Stripe
                    await db.execute(
                        """
                        INSERT INTO tenants.tax_calculations
                        (subscription_id, stripe_calculation_id, customer_country, customer_type,
                         subtotal_amount, tax_amount, total_amount, tax_rate, tax_type, currency,
                         calculation_source)
                        VALUES (:subscription_id, :calculation_id, :country, 'B2C',
                                :subtotal, :tax, :total, :rate, 'stripe_webhook', :currency,
                                'stripe_tax')
                        """,
                        {
                            "subscription_id": tenant_subscription.id,
                            "calculation_id": tax_calculation_id,
                            "country": customer_country,
                            "subtotal": subtotal_amount,
                            "tax": tax_amount,
                            "total": total_amount,
                            "rate": (
                                tax_amount / subtotal_amount
                                if subtotal_amount > 0
                                else 0
                            ),
                            "currency": currency,
                        },
                    )

            logger.info(
                f"Updated subscription {tenant_subscription.id} tax status to {tax_status}"
            )
        else:
            logger.warning(
                f"Subscription {subscription_id} not found for tax calculation event"
            )

    except Exception as e:
        logger.error(f"Error processing tax calculation event: {e}", exc_info=True)
