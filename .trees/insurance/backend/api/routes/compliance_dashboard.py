"""
Compliance Dashboard API Routes

Provides comprehensive compliance monitoring and reporting endpoints
integrating all compliance frameworks (GDPR, CCPA, Professional Responsibility,
Data Retention, Consent Management, Data Residency, Regional Disclaimers)
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from backend.api.dependencies.auth import get_current_user, UserContext, require_role
from backend.services.comprehensive_compliance_audit import (
    comprehensive_compliance_audit_service,
)
from backend.services.data_retention_service import data_retention_service
from backend.services.regional_disclaimer_service import regional_disclaimer_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/compliance", tags=["Compliance Dashboard"])


# =====================================================
# Pydantic Models
# =====================================================


class ComplianceOverview(BaseModel):
    """Compliance overview response model."""

    total_frameworks: int
    active_policies: int
    compliance_rate: float
    pending_reviews: int
    last_updated: str
    critical_violations: int
    upcoming_audits: int


class FrameworkStatus(BaseModel):
    """Framework status response model."""

    framework: str
    status: str  # compliant, warning, violation, pending
    compliance_rate: float
    last_audit: str
    next_review: str
    active_policies: int
    pending_actions: int
    critical_issues: int
    description: str
    regulatory_body: str


class ComplianceMetric(BaseModel):
    """Compliance metric response model."""

    date: str
    framework: str
    events: int
    violations: int
    compliance_rate: float
    pending_reviews: int
    resolved_issues: int


class RegionalCompliance(BaseModel):
    """Regional compliance response model."""

    region: str
    region_name: str
    frameworks: List[str]
    compliance_rate: float
    data_residency_status: str
    last_updated: str
    active_users: int
    data_volume_gb: float
    regulatory_requirements: List[str]


class ComplianceAlert(BaseModel):
    """Compliance alert response model."""

    id: str
    framework: str
    severity: str
    title: str
    description: str
    created_at: str
    due_date: str
    status: str
    assigned_to: Optional[str] = None
    region: Optional[str] = None


class AuditEvent(BaseModel):
    """Audit event response model."""

    id: str
    event_type: str
    framework: str
    severity: str
    timestamp: str
    user_id: Optional[str] = None
    region: str
    description: str
    metadata: Dict[str, Any]
    compliance_status: str


class ComplianceReport(BaseModel):
    """Compliance report response model."""

    id: str
    report_type: str
    framework: str
    period_start: str
    period_end: str
    generated_at: str
    status: str
    file_url: Optional[str] = None
    summary: Dict[str, Any]


class ReportGenerationRequest(BaseModel):
    """Report generation request model."""

    report_type: str
    framework: str
    period_start: str
    period_end: str


class AlertUpdateRequest(BaseModel):
    """Alert update request model."""

    status: str
    notes: Optional[str] = None


# =====================================================
# API Endpoints
# =====================================================


@router.get("/overview", response_model=ComplianceOverview)
async def get_compliance_overview(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"])),
):
    """
    Get comprehensive compliance overview across all frameworks.
    """
    try:
        # Get statistics from comprehensive compliance audit service
        stats = await comprehensive_compliance_audit_service.get_compliance_statistics()

        # Calculate overview metrics
        overview = ComplianceOverview(
            total_frameworks=8,  # GDPR, CCPA, Professional, Data Retention, etc.
            active_policies=stats.get("active_policies", 47),
            compliance_rate=stats.get("overall_compliance_rate", 96.8),
            pending_reviews=stats.get("pending_reviews", 3),
            last_updated=datetime.utcnow().isoformat(),
            critical_violations=stats.get("critical_violations", 0),
            upcoming_audits=stats.get("upcoming_audits", 2),
        )

        logger.info(
            f"Retrieved compliance overview for user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "compliance_rate": overview.compliance_rate,
                "pending_reviews": overview.pending_reviews,
            },
        )

        return overview

    except Exception as e:
        logger.error(
            f"Failed to get compliance overview",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve compliance overview"
        )


@router.get("/frameworks/status", response_model=List[FrameworkStatus])
async def get_framework_statuses(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"])),
):
    """
    Get status for all compliance frameworks.
    """
    try:
        # Mock framework statuses - in production this would query actual data
        frameworks = [
            FrameworkStatus(
                framework="GDPR",
                status="compliant",
                compliance_rate=98.5,
                last_audit="2024-11-15",
                next_review="2024-12-15",
                active_policies=12,
                pending_actions=0,
                critical_issues=0,
                description="General Data Protection Regulation compliance",
                regulatory_body="European Data Protection Board",
            ),
            FrameworkStatus(
                framework="CCPA",
                status="compliant",
                compliance_rate=97.2,
                last_audit="2024-11-10",
                next_review="2024-12-10",
                active_policies=8,
                pending_actions=1,
                critical_issues=0,
                description="California Consumer Privacy Act compliance",
                regulatory_body="California Attorney General",
            ),
            FrameworkStatus(
                framework="Professional Responsibility",
                status="warning",
                compliance_rate=94.1,
                last_audit="2024-11-01",
                next_review="2024-12-01",
                active_policies=15,
                pending_actions=2,
                critical_issues=1,
                description="Legal professional conduct compliance",
                regulatory_body="State Bar Associations",
            ),
            FrameworkStatus(
                framework="Data Retention",
                status="compliant",
                compliance_rate=99.1,
                last_audit="2024-11-20",
                next_review="2024-12-20",
                active_policies=6,
                pending_actions=0,
                critical_issues=0,
                description="Automated data lifecycle management",
                regulatory_body="Various Data Protection Authorities",
            ),
            FrameworkStatus(
                framework="Consent Management",
                status="compliant",
                compliance_rate=96.8,
                last_audit="2024-11-12",
                next_review="2024-12-12",
                active_policies=4,
                pending_actions=0,
                critical_issues=0,
                description="User consent tracking and management",
                regulatory_body="Data Protection Authorities",
            ),
            FrameworkStatus(
                framework="Data Residency",
                status="compliant",
                compliance_rate=100.0,
                last_audit="2024-11-25",
                next_review="2024-12-25",
                active_policies=2,
                pending_actions=0,
                critical_issues=0,
                description="Regional data placement compliance",
                regulatory_body="Regional Data Protection Authorities",
            ),
            FrameworkStatus(
                framework="Regional Disclaimers",
                status="compliant",
                compliance_rate=98.9,
                last_audit="2024-11-28",
                next_review="2024-12-28",
                active_policies=5,
                pending_actions=0,
                critical_issues=0,
                description="Jurisdiction-specific legal notices",
                regulatory_body="State Bar Associations",
            ),
            FrameworkStatus(
                framework="Security",
                status="compliant",
                compliance_rate=97.5,
                last_audit="2024-11-22",
                next_review="2024-12-22",
                active_policies=8,
                pending_actions=1,
                critical_issues=0,
                description="Authentication and authorization compliance",
                regulatory_body="Security Standards Organizations",
            ),
        ]

        logger.info(
            f"Retrieved framework statuses for user {current_user.id}",
            extra={"user_id": str(current_user.id), "framework_count": len(frameworks)},
        )

        return frameworks

    except Exception as e:
        logger.error(
            f"Failed to get framework statuses",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve framework statuses"
        )


@router.get("/metrics", response_model=List[ComplianceMetric])
async def get_compliance_metrics(
    timeframe: str = Query("30d", description="Time period for metrics"),
    framework: Optional[str] = Query(None, description="Specific framework filter"),
    current_user: UserContext = Depends(require_role(["superadmin", "admin"])),
):
    """
    Get compliance metrics over time.
    """
    try:
        # Parse timeframe
        days = 30
        if timeframe.endswith("d"):
            days = int(timeframe[:-1])
        elif timeframe.endswith("w"):
            days = int(timeframe[:-1]) * 7
        elif timeframe.endswith("m"):
            days = int(timeframe[:-1]) * 30

        # Generate mock metrics - in production this would query actual data
        metrics = []
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=days - 1 - i)).date().isoformat()
            metrics.append(
                ComplianceMetric(
                    date=date,
                    framework=framework or "Overall",
                    events=50 + (i % 20),
                    violations=max(0, (i % 7) - 5),
                    compliance_rate=95.0 + (i % 10) * 0.5,
                    pending_reviews=max(0, (i % 5) - 2),
                    resolved_issues=i % 3,
                )
            )

        logger.info(
            f"Retrieved compliance metrics for user {current_user.id}",
            extra={
                "user_id": str(current_user.id),
                "timeframe": timeframe,
                "framework": framework,
                "metric_count": len(metrics),
            },
        )

        return metrics

    except Exception as e:
        logger.error(
            f"Failed to get compliance metrics",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve compliance metrics"
        )


@router.get("/regional", response_model=List[RegionalCompliance])
async def get_regional_compliance(
    current_user: UserContext = Depends(require_role(["superadmin", "admin"])),
):
    """
    Get regional compliance status.
    """
    try:
        # Mock regional compliance data - in production this would query actual data
        regional_data = [
            RegionalCompliance(
                region="US",
                region_name="United States",
                frameworks=["CCPA", "Professional Responsibility", "Data Retention"],
                compliance_rate=97.2,
                data_residency_status="compliant",
                last_updated=datetime.utcnow().isoformat(),
                active_users=1250,
                data_volume_gb=45.7,
                regulatory_requirements=[
                    "CCPA",
                    "State Privacy Laws",
                    "ABA Model Rules",
                ],
            ),
            RegionalCompliance(
                region="EU",
                region_name="European Union",
                frameworks=["GDPR", "Professional Responsibility", "Data Retention"],
                compliance_rate=98.5,
                data_residency_status="compliant",
                last_updated=datetime.utcnow().isoformat(),
                active_users=890,
                data_volume_gb=32.1,
                regulatory_requirements=[
                    "GDPR",
                    "ePrivacy Directive",
                    "EU Legal Services Directive",
                ],
            ),
            RegionalCompliance(
                region="UK",
                region_name="United Kingdom",
                frameworks=["UK GDPR", "Professional Responsibility", "Data Retention"],
                compliance_rate=96.8,
                data_residency_status="compliant",
                last_updated=datetime.utcnow().isoformat(),
                active_users=340,
                data_volume_gb=12.8,
                regulatory_requirements=[
                    "UK GDPR",
                    "Data Protection Act 2018",
                    "SRA Regulations",
                ],
            ),
            RegionalCompliance(
                region="CA",
                region_name="Canada",
                frameworks=["PIPEDA", "Professional Responsibility", "Data Retention"],
                compliance_rate=95.4,
                data_residency_status="compliant",
                last_updated=datetime.utcnow().isoformat(),
                active_users=180,
                data_volume_gb=6.9,
                regulatory_requirements=[
                    "PIPEDA",
                    "Provincial Privacy Laws",
                    "Law Society Rules",
                ],
            ),
        ]

        logger.info(
            f"Retrieved regional compliance for user {current_user.id}",
            extra={"user_id": str(current_user.id), "region_count": len(regional_data)},
        )

        return regional_data

    except Exception as e:
        logger.error(
            f"Failed to get regional compliance",
            extra={"error": str(e), "user_id": str(current_user.id)},
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve regional compliance"
        )


@router.get("/health")
async def compliance_dashboard_health():
    """
    Health check endpoint for compliance dashboard.
    """
    return {
        "status": "healthy",
        "service": "compliance_dashboard",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
    }
