"""
Compliance Audit Trail API Endpoints

This module provides API endpoints for managing and querying the comprehensive
compliance audit trail system.
"""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pydantic import BaseModel, Field
from uuid import UUID

from backend.services.comprehensive_compliance_audit import (
    comprehensive_audit_service,
    ComplianceEventType,
    ComplianceFramework,
    ComplianceSeverity,
    log_data_retention_event,
    log_consent_management_event,
    log_data_residency_event,
    log_regional_disclaimer_event,
    log_professional_responsibility_event,
    log_security_event,
)
from backend.db.supabase_client import get_supabase_client
from backend.models.data_retention import DataRegion
from backend.api.dependencies.auth import get_current_user, require_superadmin
from backend.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/compliance/audit", tags=["compliance-audit"])


# Pydantic models
class ComplianceAuditEventRequest(BaseModel):
    """Request model for creating compliance audit events."""

    event_type: ComplianceEventType
    framework: ComplianceFramework
    severity: ComplianceSeverity = ComplianceSeverity.INFO
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None
    region: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: Optional[str] = None
    outcome: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    compliance_status: str = "COMPLIANT"
    requires_review: bool = False
    legal_basis: Optional[str] = None
    retention_period: Optional[int] = None


class ComplianceAuditEventResponse(BaseModel):
    """Response model for compliance audit events."""

    event_id: str
    event_type: str
    framework: str
    severity: str
    event_timestamp: datetime
    user_id: Optional[str]
    tenant_id: Optional[str]
    region: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    action: Optional[str]
    outcome: Optional[str]
    compliance_status: str
    requires_review: bool
    metadata: Optional[Dict[str, Any]]


class ComplianceReviewRequest(BaseModel):
    """Request model for reviewing compliance events."""

    compliance_status: Optional[str] = None
    requires_review: Optional[bool] = None
    review_notes: Optional[str] = None


class ComplianceStatistics(BaseModel):
    """Model for compliance statistics."""

    framework: str
    total_events: int
    compliant_events: int
    violations: int
    pending_review: int
    compliance_rate: float


class ComplianceMetrics(BaseModel):
    """Model for compliance metrics."""

    framework: str
    metric_date: datetime
    total_events: int
    high_severity_events: int
    violations: int
    review_required: int
    reviewed: int
    compliance_rate: float


# API Endpoints
@router.post("/events", response_model=Dict[str, str])
async def create_audit_event(
    event_request: ComplianceAuditEventRequest,
    request: Request,
    current_user=Depends(get_current_user),
):
    """
    Create a new compliance audit event.

    This endpoint allows authorized users to log compliance events
    for audit trail purposes.
    """
    try:
        logger.info(
            "Creating compliance audit event",
            extra={
                "event_type": event_request.event_type,
                "framework": event_request.framework,
                "user_id": str(current_user.id) if current_user else None,
            },
        )

        # Use current user if not specified
        user_id = event_request.user_id or (
            str(current_user.id) if current_user else None
        )

        # Log the compliance event
        event_id = await comprehensive_audit_service.log_compliance_event(
            event_type=event_request.event_type,
            framework=event_request.framework,
            severity=event_request.severity,
            user_id=user_id,
            tenant_id=event_request.tenant_id,
            region=event_request.region,
            resource_type=event_request.resource_type,
            resource_id=event_request.resource_id,
            action=event_request.action,
            outcome=event_request.outcome,
            metadata=event_request.metadata,
            compliance_status=event_request.compliance_status,
            requires_review=event_request.requires_review,
            legal_basis=event_request.legal_basis,
            retention_period=event_request.retention_period,
        )

        return {"event_id": event_id, "status": "created"}

    except Exception as e:
        logger.error(f"Error creating compliance audit event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events", response_model=List[ComplianceAuditEventResponse])
async def get_audit_events(
    framework: Optional[ComplianceFramework] = Query(
        None, description="Filter by compliance framework"
    ),
    severity: Optional[ComplianceSeverity] = Query(
        None, description="Filter by severity level"
    ),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    tenant_id: Optional[str] = Query(None, description="Filter by tenant ID"),
    region: Optional[str] = Query(None, description="Filter by region"),
    compliance_status: Optional[str] = Query(
        None, description="Filter by compliance status"
    ),
    requires_review: Optional[bool] = Query(
        None, description="Filter by review requirement"
    ),
    days_back: int = Query(30, description="Number of days to look back", ge=1, le=365),
    limit: int = Query(
        100, description="Maximum number of events to return", ge=1, le=1000
    ),
    offset: int = Query(0, description="Number of events to skip", ge=0),
    current_user=Depends(require_superadmin),
):
    """
    Get compliance audit events with filtering options.

    This endpoint allows superadmins to query the compliance audit trail
    with various filtering and pagination options.
    """
    try:
        logger.info(
            "Querying compliance audit events",
            extra={
                "framework": framework,
                "severity": severity,
                "days_back": days_back,
                "limit": limit,
            },
        )

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Build query
        query = supabase.table("compliance_audit_log").select("*")

        # Apply filters
        cutoff_date = datetime.now() - timedelta(days=days_back)
        query = query.gte("event_timestamp", cutoff_date.isoformat())

        if framework:
            query = query.eq("framework", framework.value)
        if severity:
            query = query.eq("severity", severity.value)
        if user_id:
            query = query.eq("user_id", user_id)
        if tenant_id:
            query = query.eq("tenant_id", tenant_id)
        if region:
            query = query.eq("region", region)
        if compliance_status:
            query = query.eq("compliance_status", compliance_status)
        if requires_review is not None:
            query = query.eq("requires_review", requires_review)

        # Apply pagination and ordering
        query = query.order("event_timestamp", desc=True)
        query = query.range(offset, offset + limit - 1)

        # Execute query
        result = query.execute()

        if not result.data:
            return []

        # Convert to response models
        events = []
        for event_data in result.data:
            events.append(
                ComplianceAuditEventResponse(
                    event_id=event_data["event_id"],
                    event_type=event_data["event_type"],
                    framework=event_data["framework"],
                    severity=event_data["severity"],
                    event_timestamp=datetime.fromisoformat(
                        event_data["event_timestamp"].replace("Z", "+00:00")
                    ),
                    user_id=event_data.get("user_id"),
                    tenant_id=event_data.get("tenant_id"),
                    region=event_data.get("region"),
                    resource_type=event_data.get("resource_type"),
                    resource_id=event_data.get("resource_id"),
                    action=event_data.get("action"),
                    outcome=event_data.get("outcome"),
                    compliance_status=event_data["compliance_status"],
                    requires_review=event_data["requires_review"],
                    metadata=event_data.get("metadata", {}),
                )
            )

        return events

    except Exception as e:
        logger.error(f"Error querying compliance audit events: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events/{event_id}", response_model=ComplianceAuditEventResponse)
async def get_audit_event(event_id: UUID, current_user=Depends(require_superadmin)):
    """
    Get a specific compliance audit event by ID.
    """
    try:
        logger.info(f"Getting compliance audit event: {event_id}")

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Query for the specific event
        result = (
            supabase.table("compliance_audit_log")
            .select("*")
            .eq("event_id", str(event_id))
            .execute()
        )

        if not result.data:
            raise HTTPException(status_code=404, detail="Audit event not found")

        event_data = result.data[0]

        return ComplianceAuditEventResponse(
            event_id=event_data["event_id"],
            event_type=event_data["event_type"],
            framework=event_data["framework"],
            severity=event_data["severity"],
            event_timestamp=datetime.fromisoformat(
                event_data["event_timestamp"].replace("Z", "+00:00")
            ),
            user_id=event_data.get("user_id"),
            tenant_id=event_data.get("tenant_id"),
            region=event_data.get("region"),
            resource_type=event_data.get("resource_type"),
            resource_id=event_data.get("resource_id"),
            action=event_data.get("action"),
            outcome=event_data.get("outcome"),
            compliance_status=event_data["compliance_status"],
            requires_review=event_data["requires_review"],
            metadata=event_data.get("metadata", {}),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting compliance audit event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/events/{event_id}/review", response_model=Dict[str, str])
async def review_audit_event(
    event_id: UUID,
    review_request: ComplianceReviewRequest,
    current_user=Depends(require_superadmin),
):
    """
    Review and update a compliance audit event.

    This endpoint allows superadmins to review compliance events,
    update their status, and add review notes.
    """
    try:
        logger.info(
            f"Reviewing compliance audit event: {event_id}",
            extra={
                "reviewer_id": str(current_user.id),
                "compliance_status": review_request.compliance_status,
            },
        )

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Call the database function to update the record
        result = supabase.rpc(
            "update_compliance_audit_record",
            {
                "p_event_id": str(event_id),
                "p_compliance_status": review_request.compliance_status,
                "p_requires_review": review_request.requires_review,
                "p_reviewed_by": str(current_user.id),
                "p_review_notes": review_request.review_notes,
            },
        ).execute()

        if not result.data:
            raise HTTPException(status_code=404, detail="Audit event not found")

        # Log the review action
        await comprehensive_audit_service.log_compliance_event(
            event_type=ComplianceEventType.AUDIT_REPORT_GENERATED,
            framework=ComplianceFramework.SECURITY,
            severity=ComplianceSeverity.INFO,
            user_id=str(current_user.id),
            action="event_reviewed",
            resource_type="compliance_audit_event",
            resource_id=str(event_id),
            metadata={
                "reviewed_event_id": str(event_id),
                "new_status": review_request.compliance_status,
                "review_notes": review_request.review_notes,
            },
        )

        return {"event_id": str(event_id), "status": "reviewed"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reviewing compliance audit event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=List[ComplianceStatistics])
async def get_compliance_statistics(
    framework: Optional[ComplianceFramework] = Query(
        None, description="Filter by compliance framework"
    ),
    days_back: int = Query(30, description="Number of days to analyze", ge=1, le=365),
    current_user=Depends(require_superadmin),
):
    """
    Get compliance statistics by framework.

    This endpoint provides aggregated compliance statistics including
    total events, violations, compliance rates, and pending reviews.
    """
    try:
        logger.info(
            "Getting compliance statistics",
            extra={"framework": framework, "days_back": days_back},
        )

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Call the database function
        result = supabase.rpc(
            "get_compliance_statistics",
            {
                "p_framework": framework.value if framework else None,
                "p_days_back": days_back,
            },
        ).execute()

        if not result.data:
            return []

        # Convert to response models
        statistics = []
        for stat_data in result.data:
            statistics.append(
                ComplianceStatistics(
                    framework=stat_data["framework"],
                    total_events=stat_data["total_events"],
                    compliant_events=stat_data["compliant_events"],
                    violations=stat_data["violations"],
                    pending_review=stat_data["pending_review"],
                    compliance_rate=float(stat_data["compliance_rate"] or 0),
                )
            )

        return statistics

    except Exception as e:
        logger.error(f"Error getting compliance statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics", response_model=List[ComplianceMetrics])
async def get_compliance_metrics(
    framework: Optional[ComplianceFramework] = Query(
        None, description="Filter by compliance framework"
    ),
    days_back: int = Query(30, description="Number of days to analyze", ge=1, le=90),
    current_user=Depends(require_superadmin),
):
    """
    Get daily compliance metrics by framework.

    This endpoint provides daily compliance metrics including
    event counts, severity breakdown, and compliance rates.
    """
    try:
        logger.info(
            "Getting compliance metrics",
            extra={"framework": framework, "days_back": days_back},
        )

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Build query for compliance metrics view
        query = supabase.table("compliance_metrics").select("*")

        # Apply filters
        cutoff_date = datetime.now() - timedelta(days=days_back)
        query = query.gte("metric_date", cutoff_date.date().isoformat())

        if framework:
            query = query.eq("framework", framework.value)

        query = query.order("metric_date", desc=True)

        # Execute query
        result = query.execute()

        if not result.data:
            return []

        # Convert to response models
        metrics = []
        for metric_data in result.data:
            metrics.append(
                ComplianceMetrics(
                    framework=metric_data["framework"],
                    metric_date=datetime.fromisoformat(metric_data["metric_date"]),
                    total_events=metric_data["total_events"],
                    high_severity_events=metric_data["high_severity_events"],
                    violations=metric_data["violations"],
                    review_required=metric_data["review_required"],
                    reviewed=metric_data["reviewed"],
                    compliance_rate=float(metric_data["compliance_rate"] or 0),
                )
            )

        return metrics

    except Exception as e:
        logger.error(f"Error getting compliance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/review-queue", response_model=List[ComplianceAuditEventResponse])
async def get_review_queue(
    framework: Optional[ComplianceFramework] = Query(
        None, description="Filter by compliance framework"
    ),
    severity: Optional[ComplianceSeverity] = Query(
        None, description="Filter by severity level"
    ),
    limit: int = Query(
        50, description="Maximum number of events to return", ge=1, le=200
    ),
    current_user=Depends(require_superadmin),
):
    """
    Get compliance events requiring review.

    This endpoint returns compliance events that require manual review,
    ordered by severity and age.
    """
    try:
        logger.info(
            "Getting compliance review queue",
            extra={"framework": framework, "severity": severity, "limit": limit},
        )

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Build query for review queue view
        query = supabase.table("compliance_review_queue").select("*")

        # Apply filters
        if framework:
            query = query.eq("framework", framework.value)
        if severity:
            query = query.eq("severity", severity.value)

        query = query.limit(limit)

        # Execute query
        result = query.execute()

        if not result.data:
            return []

        # Convert to response models
        events = []
        for event_data in result.data:
            events.append(
                ComplianceAuditEventResponse(
                    event_id=event_data["event_id"],
                    event_type=event_data["event_type"],
                    framework=event_data["framework"],
                    severity=event_data["severity"],
                    event_timestamp=datetime.fromisoformat(
                        event_data["event_timestamp"].replace("Z", "+00:00")
                    ),
                    user_id=event_data.get("user_id"),
                    tenant_id=event_data.get("tenant_id"),
                    region=event_data.get("region"),
                    resource_type=event_data.get("resource_type"),
                    resource_id=event_data.get("resource_id"),
                    action=event_data.get("action"),
                    outcome=event_data.get("outcome"),
                    compliance_status=event_data["compliance_status"],
                    requires_review=event_data["requires_review"],
                    metadata=event_data.get("metadata", {}),
                )
            )

        return events

    except Exception as e:
        logger.error(f"Error getting compliance review queue: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/archive", response_model=Dict[str, Any])
async def archive_old_records(
    retention_days: int = Query(
        2555, description="Retention period in days (default: 7 years)", ge=365
    ),
    current_user=Depends(require_superadmin),
):
    """
    Archive old compliance audit records.

    This endpoint moves old compliance audit records to an archive table
    based on the specified retention period.
    """
    try:
        logger.info(
            "Archiving old compliance audit records",
            extra={
                "retention_days": retention_days,
                "admin_user": str(current_user.id),
            },
        )

        # Get Supabase client
        supabase = get_supabase_client(DataRegion.US)

        # Call the database function to archive records
        result = supabase.rpc(
            "archive_old_compliance_audit_records", {"p_retention_days": retention_days}
        ).execute()

        archived_count = result.data if result.data is not None else 0

        # Log the archival action
        await comprehensive_audit_service.log_compliance_event(
            event_type=ComplianceEventType.AUDIT_REPORT_GENERATED,
            framework=ComplianceFramework.DATA_RETENTION,
            severity=ComplianceSeverity.INFO,
            user_id=str(current_user.id),
            action="records_archived",
            metadata={
                "archived_count": archived_count,
                "retention_days": retention_days,
            },
        )

        return {
            "status": "completed",
            "archived_count": archived_count,
            "retention_days": retention_days,
        }

    except Exception as e:
        logger.error(f"Error archiving compliance audit records: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Health check endpoint
@router.get("/health")
async def audit_health_check():
    """
    Health check for the compliance audit system.
    """
    try:
        # Check if audit service is running
        await comprehensive_audit_service.start_background_tasks()

        # Test database connectivity
        supabase = get_supabase_client(DataRegion.US)
        result = (
            supabase.table("compliance_audit_log")
            .select("count", count="exact")
            .limit(1)
            .execute()
        )

        return {
            "status": "healthy",
            "audit_service": "running",
            "database": "connected",
            "total_events": result.count if result.count is not None else 0,
        }

    except Exception as e:
        logger.error(f"Audit health check failed: {e}")
        return {"status": "unhealthy", "error": str(e)}
