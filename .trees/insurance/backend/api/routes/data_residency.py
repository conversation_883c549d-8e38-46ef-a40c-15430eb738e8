"""
Data Residency Management API Routes

This module provides API endpoints for managing user data residency preferences,
compliance monitoring, and regional data controls.
"""

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, Request, HTTPException
from pydantic import BaseModel, Field

from backend.api.dependencies.auth import get_current_user, UserContext
from backend.services.data_residency import DataRegion, validate_regional_configuration
from backend.services.location_detection import (
    detect_user_region,
    preference_manager,
    get_region_for_country,
)
from backend.services.compliance_audit import log_data_residency_event
from backend.middleware.data_residency_middleware import (
    get_user_region_from_request,
    get_region_metadata_from_request,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data-residency", tags=["Data Residency"])


# =====================================================
# Pydantic Models
# =====================================================


class UserRegionResponse(BaseModel):
    """Response model for user region detection."""

    detected_region: str = Field(..., description="Detected data region")
    current_preference: Optional[str] = Field(
        None, description="User's current preference"
    )
    detection_metadata: Dict[str, Any] = Field(..., description="Detection metadata")
    available_regions: Dict[str, str] = Field(..., description="Available regions")


class UpdateRegionPreferenceRequest(BaseModel):
    """Request model for updating user region preference."""

    preferred_region: str = Field(..., description="Preferred data region (US or EU)")
    reason: Optional[str] = Field("user_request", description="Reason for change")


class RegionPreferenceResponse(BaseModel):
    """Response model for region preference updates."""

    success: bool = Field(..., description="Whether update was successful")
    old_region: Optional[str] = Field(None, description="Previous region")
    new_region: str = Field(..., description="New region")
    requires_data_migration: bool = Field(
        ..., description="Whether data migration is needed"
    )


class ComplianceStatusResponse(BaseModel):
    """Response model for compliance status."""

    regional_config_valid: Dict[str, bool] = Field(
        ..., description="Regional configuration status"
    )
    dpa_status: Dict[str, bool] = Field(..., description="DPA compliance status")
    current_region: str = Field(..., description="Current user region")
    compliance_score: float = Field(..., description="Overall compliance score")


class RegionInfoResponse(BaseModel):
    """Response model for region information."""

    region: str = Field(..., description="Region code")
    display_name: str = Field(..., description="Human-readable region name")
    data_location: str = Field(..., description="Physical data location")
    compliance_frameworks: List[str] = Field(
        ..., description="Applicable compliance frameworks"
    )
    supabase_url: str = Field(..., description="Regional Supabase URL")


# =====================================================
# API Endpoints
# =====================================================


@router.get("/detect", response_model=UserRegionResponse)
async def detect_user_region_endpoint(
    request: Request, current_user: UserContext = Depends(get_current_user)
):
    """
    Detect user's data residency region based on location and preferences.
    """
    try:
        # Get user's current preference
        user_preference = getattr(current_user, "data_residency_preference", None)

        # Detect region
        detected_region, metadata = await detect_user_region(request, user_preference)

        # Log the detection event
        await log_data_residency_event(
            event_type="region_detection",
            user_id=str(current_user.id),
            region=detected_region.value,
            metadata=metadata,
        )

        return UserRegionResponse(
            detected_region=detected_region.value,
            current_preference=user_preference,
            detection_metadata=metadata,
            available_regions=preference_manager.get_available_regions(),
        )

    except Exception as e:
        logger.error(f"Failed to detect user region: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to detect user region")


@router.post("/preference", response_model=RegionPreferenceResponse)
async def update_region_preference(
    request_data: UpdateRegionPreferenceRequest,
    request: Request,
    current_user: UserContext = Depends(get_current_user),
):
    """
    Update user's data residency preference.
    """
    try:
        # Validate the requested region
        new_region = preference_manager.validate_region_preference(
            request_data.preferred_region
        )
        if not new_region:
            raise HTTPException(status_code=400, detail="Invalid region preference")

        # Get current region
        current_region = get_user_region_from_request(request)

        # Update user preference (this would typically update the database)
        # For now, we'll just log the change
        requires_migration = current_region != new_region

        # Log the preference change
        await log_data_residency_event(
            event_type="user_preference_change",
            user_id=str(current_user.id),
            region=new_region.value,
            metadata={
                "old_region": current_region.value,
                "new_region": new_region.value,
                "reason": request_data.reason,
                "requires_data_migration": requires_migration,
            },
        )

        return RegionPreferenceResponse(
            success=True,
            old_region=current_region.value,
            new_region=new_region.value,
            requires_data_migration=requires_migration,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update region preference: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to update region preference"
        )


@router.get("/status", response_model=ComplianceStatusResponse)
async def get_compliance_status(
    request: Request, current_user: UserContext = Depends(get_current_user)
):
    """
    Get current compliance status and configuration.
    """
    try:
        # Check regional configuration
        regional_config = validate_regional_configuration()

        # Mock DPA status (in production, this would check actual DPA records)
        dpa_status = {"openai": True, "pinecone": True, "neo4j": True, "supabase": True}

        # Get current region
        current_region = get_user_region_from_request(request)

        # Calculate compliance score
        config_score = sum(regional_config.values()) / len(regional_config)
        dpa_score = sum(dpa_status.values()) / len(dpa_status)
        compliance_score = (config_score + dpa_score) / 2

        return ComplianceStatusResponse(
            regional_config_valid=regional_config,
            dpa_status=dpa_status,
            current_region=current_region.value,
            compliance_score=compliance_score,
        )

    except Exception as e:
        logger.error(f"Failed to get compliance status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get compliance status")


@router.get("/regions", response_model=List[RegionInfoResponse])
async def get_available_regions():
    """
    Get information about available data residency regions.
    """
    try:
        regions_info = []

        # US Region Info
        regions_info.append(
            RegionInfoResponse(
                region="US",
                display_name="United States",
                data_location="US East (Ohio) - us-east-2",
                compliance_frameworks=["CCPA", "COPPA", "HIPAA"],
                supabase_url="https://anwefmklplkjxkmzpnva.supabase.co",
            )
        )

        # EU Region Info
        regions_info.append(
            RegionInfoResponse(
                region="EU",
                display_name="European Union",
                data_location="EU West (Paris) - eu-west-3",
                compliance_frameworks=["GDPR", "ePrivacy Directive"],
                supabase_url="https://lsixcrtzawcxyfkxhyxd.supabase.co",
            )
        )

        return regions_info

    except Exception as e:
        logger.error(f"Failed to get region information: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get region information")


@router.get("/country/{country_code}")
async def get_region_for_country_endpoint(country_code: str):
    """
    Get the appropriate data region for a country code.
    """
    try:
        if len(country_code) != 2:
            raise HTTPException(
                status_code=400, detail="Country code must be 2 characters"
            )

        region = get_region_for_country(country_code.upper())

        return {
            "country_code": country_code.upper(),
            "region": region.value,
            "display_name": preference_manager.get_region_display_name(region),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get region for country: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get region for country")


@router.get("/current")
async def get_current_region_info(
    request: Request, current_user: UserContext = Depends(get_current_user)
):
    """
    Get current user's region information and detection metadata.
    """
    try:
        current_region = get_user_region_from_request(request)
        region_metadata = get_region_metadata_from_request(request)

        return {
            "user_id": str(current_user.id),
            "current_region": current_region.value,
            "region_display_name": preference_manager.get_region_display_name(
                current_region
            ),
            "detection_metadata": region_metadata,
            "timestamp": region_metadata.get("timestamp"),
        }

    except Exception as e:
        logger.error(f"Failed to get current region info: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get current region info")
