"""
Data Retention Dashboard API Routes

This module provides API endpoints for the data retention management dashboard,
including retention policies, legal holds, cleanup jobs, and compliance metrics.
"""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pydantic import BaseModel, Field
from uuid import UUID

from backend.services.data_retention_service import DataRetentionService
from backend.models.data_retention import (
    DataRegion,
    RetentionBasis,
    DataSensitivity,
    LegalHoldType,
    CleanupStatus,
    RetentionPolicyCreate,
    RetentionPolicyResponse,
    LegalHoldCreate,
    LegalHoldResponse,
    CleanupCriteria,
    CleanupResult,
)
from backend.db.supabase_client import get_supabase_client
from backend.api.dependencies.auth import require_super_admin
from backend.middleware.auth_middleware import get_current_user
from backend.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/data-retention", tags=["data-retention-dashboard"])


# Pydantic Models for Dashboard
class RetentionPolicyDashboard(BaseModel):
    """Dashboard representation of retention policy."""

    id: str
    data_type: str
    region: str
    retention_days: int
    legal_basis: str
    sensitivity: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    metadata: Optional[Dict[str, Any]] = None


class LegalHoldDashboard(BaseModel):
    """Dashboard representation of legal hold."""

    id: str
    hold_name: str
    hold_type: str
    reason: str
    data_types: List[str]
    region: str
    status: str
    created_at: datetime
    expires_at: Optional[datetime] = None
    created_by: str
    metadata: Optional[Dict[str, Any]] = None


class CleanupJobDashboard(BaseModel):
    """Dashboard representation of cleanup job."""

    id: str
    data_type: str
    region: str
    status: str
    scheduled_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    records_processed: int = 0
    records_deleted: int = 0
    error_message: Optional[str] = None
    dry_run: bool = False
    batch_size: int = 100


class ComplianceMetricsDashboard(BaseModel):
    """Dashboard compliance metrics."""

    total_policies: int
    active_policies: int
    active_legal_holds: int
    pending_cleanup_jobs: int
    compliance_rate: float
    last_cleanup: str
    policies_by_region: Dict[str, int]
    cleanup_success_rate: float


class CreateRetentionPolicyRequest(BaseModel):
    """Request to create retention policy."""

    data_type: str
    region: str
    retention_days: int = Field(gt=0, le=36500)
    legal_basis: str
    sensitivity: str
    metadata: Optional[Dict[str, Any]] = None


class CreateLegalHoldRequest(BaseModel):
    """Request to create legal hold."""

    hold_name: str
    hold_type: str
    reason: str
    data_types: List[str]
    region: str
    expires_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class ScheduleCleanupRequest(BaseModel):
    """Request to schedule cleanup job."""

    data_type: str
    region: str
    dry_run: bool = True
    batch_size: int = Field(default=100, gt=0, le=10000)
    scheduled_at: Optional[datetime] = None


# API Endpoints


@router.get("/policies", response_model=List[RetentionPolicyDashboard])
async def get_retention_policies(
    region: Optional[str] = Query(None, description="Filter by region"),
    data_type: Optional[str] = Query(None, description="Filter by data type"),
    active_only: bool = Query(False, description="Show only active policies"),
    current_user=Depends(require_super_admin),
):
    """
    Get retention policies for dashboard display.
    """
    try:
        logger.info(
            "Getting retention policies for dashboard",
            extra={
                "region": region,
                "data_type": data_type,
                "active_only": active_only,
                "user_id": str(current_user["user_id"]),
            },
        )

        # Get Supabase client for the appropriate region
        supabase = get_supabase_client(
            DataRegion.US if not region or region == "US" else DataRegion.EU
        )

        # Build query
        query = supabase.table("data_retention.retention_policies").select("*")

        if region and region != "all":
            query = query.eq("region", region)
        if data_type and data_type != "all":
            query = query.eq("data_type", data_type)
        if active_only:
            query = query.eq("is_active", True)

        query = query.order("created_at", desc=True)

        result = query.execute()

        if not result.data:
            return []

        # Convert to dashboard format
        policies = []
        for policy_data in result.data:
            policies.append(
                RetentionPolicyDashboard(
                    id=policy_data["id"],
                    data_type=policy_data["data_type"],
                    region=policy_data["region"],
                    retention_days=policy_data["retention_days"],
                    legal_basis=policy_data["legal_basis"],
                    sensitivity=policy_data["sensitivity"],
                    is_active=policy_data["is_active"],
                    created_at=datetime.fromisoformat(
                        policy_data["created_at"].replace("Z", "+00:00")
                    ),
                    updated_at=datetime.fromisoformat(
                        policy_data["updated_at"].replace("Z", "+00:00")
                    ),
                    metadata=policy_data.get("metadata", {}),
                )
            )

        return policies

    except Exception as e:
        logger.error(f"Error getting retention policies: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/legal-holds", response_model=List[LegalHoldDashboard])
async def get_legal_holds(
    region: Optional[str] = Query(None, description="Filter by region"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user=Depends(require_super_admin),
):
    """
    Get legal holds for dashboard display.
    """
    try:
        logger.info(
            "Getting legal holds for dashboard",
            extra={
                "region": region,
                "status": status,
                "user_id": str(current_user["user_id"]),
            },
        )

        # Get Supabase client
        supabase = get_supabase_client(
            DataRegion.US if not region or region == "US" else DataRegion.EU
        )

        # Build query
        query = supabase.table("data_retention.legal_holds").select("*")

        if region and region != "all":
            query = query.eq("region", region)
        if status and status != "all":
            query = query.eq("status", status)

        query = query.order("created_at", desc=True)

        result = query.execute()

        if not result.data:
            return []

        # Convert to dashboard format
        holds = []
        for hold_data in result.data:
            holds.append(
                LegalHoldDashboard(
                    id=hold_data["id"],
                    hold_name=hold_data["hold_name"],
                    hold_type=hold_data["hold_type"],
                    reason=hold_data["reason"],
                    data_types=hold_data["data_types"],
                    region=hold_data["region"],
                    status=hold_data["status"],
                    created_at=datetime.fromisoformat(
                        hold_data["created_at"].replace("Z", "+00:00")
                    ),
                    expires_at=(
                        datetime.fromisoformat(
                            hold_data["expires_at"].replace("Z", "+00:00")
                        )
                        if hold_data.get("expires_at")
                        else None
                    ),
                    created_by=hold_data["created_by"],
                    metadata=hold_data.get("metadata", {}),
                )
            )

        return holds

    except Exception as e:
        logger.error(f"Error getting legal holds: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cleanup-jobs", response_model=List[CleanupJobDashboard])
async def get_cleanup_jobs(
    region: Optional[str] = Query(None, description="Filter by region"),
    data_type: Optional[str] = Query(None, description="Filter by data type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(
        50, description="Maximum number of jobs to return", ge=1, le=500
    ),
    current_user=Depends(require_super_admin),
):
    """
    Get cleanup jobs for dashboard display.
    """
    try:
        logger.info(
            "Getting cleanup jobs for dashboard",
            extra={
                "region": region,
                "data_type": data_type,
                "status": status,
                "limit": limit,
                "user_id": str(current_user["user_id"]),
            },
        )

        # Get Supabase client
        supabase = get_supabase_client(
            DataRegion.US if not region or region == "US" else DataRegion.EU
        )

        # Build query
        query = supabase.table("data_retention.cleanup_jobs").select("*")

        if region and region != "all":
            query = query.eq("region", region)
        if data_type and data_type != "all":
            query = query.eq("data_type", data_type)
        if status and status != "all":
            query = query.eq("status", status)

        query = query.order("scheduled_at", desc=True).limit(limit)

        result = query.execute()

        if not result.data:
            return []

        # Convert to dashboard format
        jobs = []
        for job_data in result.data:
            jobs.append(
                CleanupJobDashboard(
                    id=job_data["id"],
                    data_type=job_data["data_type"],
                    region=job_data["region"],
                    status=job_data["status"],
                    scheduled_at=datetime.fromisoformat(
                        job_data["scheduled_at"].replace("Z", "+00:00")
                    ),
                    started_at=(
                        datetime.fromisoformat(
                            job_data["started_at"].replace("Z", "+00:00")
                        )
                        if job_data.get("started_at")
                        else None
                    ),
                    completed_at=(
                        datetime.fromisoformat(
                            job_data["completed_at"].replace("Z", "+00:00")
                        )
                        if job_data.get("completed_at")
                        else None
                    ),
                    records_processed=job_data.get("records_processed", 0),
                    records_deleted=job_data.get("records_deleted", 0),
                    error_message=job_data.get("error_message"),
                    dry_run=job_data.get("dry_run", False),
                    batch_size=job_data.get("batch_size", 100),
                )
            )

        return jobs

    except Exception as e:
        logger.error(f"Error getting cleanup jobs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics", response_model=ComplianceMetricsDashboard)
async def get_compliance_metrics(
    region: Optional[str] = Query(None, description="Filter by region"),
    current_user=Depends(require_super_admin),
):
    """
    Get compliance metrics for dashboard display.
    """
    try:
        logger.info(
            "Getting compliance metrics for dashboard",
            extra={"region": region, "user_id": str(current_user["user_id"])},
        )

        # Get Supabase client
        supabase = get_supabase_client(
            DataRegion.US if not region or region == "US" else DataRegion.EU
        )

        # Get policy metrics
        policies_query = supabase.table("data_retention.retention_policies").select(
            "region, is_active"
        )
        if region and region != "all":
            policies_query = policies_query.eq("region", region)

        policies_result = policies_query.execute()
        policies_data = policies_result.data or []

        total_policies = len(policies_data)
        active_policies = len([p for p in policies_data if p["is_active"]])

        # Get policies by region
        policies_by_region = {}
        for policy in policies_data:
            region_key = policy["region"]
            policies_by_region[region_key] = policies_by_region.get(region_key, 0) + 1

        # Get legal holds metrics
        holds_query = supabase.table("data_retention.legal_holds").select("status")
        if region and region != "all":
            holds_query = holds_query.eq("region", region)

        holds_result = holds_query.execute()
        holds_data = holds_result.data or []

        active_legal_holds = len([h for h in holds_data if h["status"] == "active"])

        # Get cleanup jobs metrics
        jobs_query = supabase.table("data_retention.cleanup_jobs").select(
            "status, completed_at"
        )
        if region and region != "all":
            jobs_query = jobs_query.eq("region", region)

        jobs_result = jobs_query.execute()
        jobs_data = jobs_result.data or []

        pending_cleanup_jobs = len(
            [j for j in jobs_data if j["status"] in ["pending", "running"]]
        )

        # Calculate cleanup success rate
        completed_jobs = [
            j for j in jobs_data if j["status"] in ["completed", "failed"]
        ]
        successful_jobs = [j for j in completed_jobs if j["status"] == "completed"]
        cleanup_success_rate = (
            (len(successful_jobs) / len(completed_jobs) * 100)
            if completed_jobs
            else 100.0
        )

        # Calculate compliance rate (simplified)
        compliance_rate = (
            (active_policies / total_policies * 100) if total_policies > 0 else 100.0
        )

        # Get last cleanup date
        last_cleanup = "Never"
        if jobs_data:
            completed_jobs_with_dates = [j for j in jobs_data if j.get("completed_at")]
            if completed_jobs_with_dates:
                latest_job = max(
                    completed_jobs_with_dates, key=lambda x: x["completed_at"]
                )
                last_cleanup = latest_job["completed_at"]

        return ComplianceMetricsDashboard(
            total_policies=total_policies,
            active_policies=active_policies,
            active_legal_holds=active_legal_holds,
            pending_cleanup_jobs=pending_cleanup_jobs,
            compliance_rate=round(compliance_rate, 1),
            last_cleanup=last_cleanup,
            policies_by_region=policies_by_region,
            cleanup_success_rate=round(cleanup_success_rate, 1),
        )

    except Exception as e:
        logger.error(f"Error getting compliance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/policies", response_model=Dict[str, str])
async def create_retention_policy(
    policy_request: CreateRetentionPolicyRequest,
    current_user=Depends(require_super_admin),
):
    """
    Create a new retention policy.
    """
    try:
        logger.info(
            "Creating retention policy",
            extra={
                "data_type": policy_request.data_type,
                "region": policy_request.region,
                "user_id": str(current_user["user_id"]),
            },
        )

        # Use data retention service
        service = DataRetentionService()

        policy_create = RetentionPolicyCreate(
            data_type=policy_request.data_type,
            region=DataRegion(policy_request.region),
            retention_days=policy_request.retention_days,
            legal_basis=RetentionBasis(policy_request.legal_basis),
            sensitivity=DataSensitivity(policy_request.sensitivity),
            metadata=policy_request.metadata or {},
        )

        policy_id = await service.create_retention_policy(policy_create)

        return {"id": policy_id, "status": "created"}

    except Exception as e:
        logger.error(f"Error creating retention policy: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/legal-holds", response_model=Dict[str, str])
async def create_legal_hold(
    hold_request: CreateLegalHoldRequest, current_user=Depends(require_super_admin)
):
    """
    Create a new legal hold.
    """
    try:
        logger.info(
            "Creating legal hold",
            extra={
                "hold_name": hold_request.hold_name,
                "region": hold_request.region,
                "user_id": str(current_user["user_id"]),
            },
        )

        # Use data retention service
        service = DataRetentionService()

        hold_create = LegalHoldCreate(
            hold_name=hold_request.hold_name,
            hold_type=LegalHoldType(hold_request.hold_type),
            reason=hold_request.reason,
            data_types=hold_request.data_types,
            region=DataRegion(hold_request.region),
            expires_at=hold_request.expires_at,
            created_by=str(current_user["user_id"]),
            metadata=hold_request.metadata or {},
        )

        hold_id = await service.create_legal_hold(hold_create)

        return {"id": hold_id, "status": "created"}

    except Exception as e:
        logger.error(f"Error creating legal hold: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup-jobs", response_model=Dict[str, str])
async def schedule_cleanup(
    cleanup_request: ScheduleCleanupRequest, current_user=Depends(require_super_admin)
):
    """
    Schedule a new cleanup job.
    """
    try:
        logger.info(
            "Scheduling cleanup job",
            extra={
                "data_type": cleanup_request.data_type,
                "region": cleanup_request.region,
                "dry_run": cleanup_request.dry_run,
                "user_id": str(current_user["user_id"]),
            },
        )

        # Use data retention service
        service = DataRetentionService()

        cleanup_criteria = CleanupCriteria(
            data_type=cleanup_request.data_type,
            region=DataRegion(cleanup_request.region),
            dry_run=cleanup_request.dry_run,
            batch_size=cleanup_request.batch_size,
        )

        job_id = await service.schedule_cleanup(cleanup_criteria)

        return {"job_id": job_id, "status": "scheduled"}

    except Exception as e:
        logger.error(f"Error scheduling cleanup job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data-types", response_model=Dict[str, Any])
async def get_data_type_registry(current_user=Depends(require_super_admin)):
    """
    Get the data type registry for dashboard configuration.
    """
    try:
        from backend.models.data_retention import DATA_TYPE_REGISTRY

        return DATA_TYPE_REGISTRY

    except Exception as e:
        logger.error(f"Error getting data type registry: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """
    Health check for the data retention dashboard API.
    """
    try:
        # Test database connectivity
        supabase_us = get_supabase_client(DataRegion.US)
        supabase_eu = get_supabase_client(DataRegion.EU)

        # Simple connectivity test
        us_result = (
            supabase_us.table("data_retention.retention_policies")
            .select("count", count="exact")
            .limit(1)
            .execute()
        )
        eu_result = (
            supabase_eu.table("data_retention.retention_policies")
            .select("count", count="exact")
            .limit(1)
            .execute()
        )

        return {
            "status": "healthy",
            "database_us": "connected",
            "database_eu": "connected",
            "policies_us": us_result.count if us_result.count is not None else 0,
            "policies_eu": eu_result.count if eu_result.count is not None else 0,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Data retention dashboard health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
        }
