"""
Multi-Factor Authentication (MFA) API Routes

This module provides API endpoints for MFA management including TOTP setup,
verification, session management, and recovery processes.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import JSONResponse
from typing import Optional
from datetime import datetime, timedelta
import logging
import os

from backend.models.mfa import (
    MFASetupRequest,
    MFAVerificationRequest,
    MFAStatusResponse,
    TOTPSetupResponse,
    MFAChallengeRequest,
    MFAChallengeResponse,
    MFAValidationRequest,
    MFAValidationResponse,
    RecoveryCodesResponse,
    MFAMethod,
    SecurityEventCategory,
    SecurityEventSeverity,
)
from backend.services.mfa_service import get_mfa_service
from backend.api.dependencies.auth import require_super_admin
from backend.middleware.auth_middleware import UserContext

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth/mfa", tags=["MFA"])


@router.get("/status", response_model=MFAStatusResponse)
async def get_mfa_status(
    request: Request, user_context: UserContext = Depends(require_super_admin)
):
    """Get MFA status for the current superadmin user."""
    try:
        status = await mfa_service.get_mfa_status(user_context.user_id)
        return status
    except Exception as e:
        logger.error(f"Error getting MFA status for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MFA status",
        )


@router.post("/setup/totp", response_model=TOTPSetupResponse)
async def setup_totp(
    request: Request, user_context: UserContext = Depends(require_super_admin)
):
    """Set up TOTP authentication for the current superadmin user."""
    try:
        # Ensure user has MFA config
        config = await mfa_service.get_mfa_config(user_context.user_id)
        if not config:
            config = await mfa_service.create_mfa_config(
                user_context.user_id, user_context.email
            )

        setup_response = await mfa_service.setup_totp(
            user_context.user_id, user_context.email
        )

        return setup_response
    except Exception as e:
        logger.error(f"Error setting up TOTP for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set up TOTP authentication",
        )


@router.post("/setup/verify")
async def verify_totp_setup(
    request: Request,
    verification: MFAVerificationRequest,
    user_context: UserContext = Depends(require_super_admin),
):
    """Verify TOTP setup with the provided token."""
    try:
        if verification.method != MFAMethod.TOTP:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid method for TOTP verification",
            )

        if not verification.factor_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Factor ID is required for TOTP verification",
            )

        success = await mfa_service.verify_totp_setup(
            user_context.user_id, verification.token, str(verification.factor_id)
        )

        if success:
            return JSONResponse(
                content={"message": "TOTP setup verified successfully"},
                status_code=status.HTTP_200_OK,
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification token",
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying TOTP setup for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify TOTP setup",
        )


@router.post("/verify", response_model=MFAValidationResponse)
async def verify_mfa(
    request: Request,
    verification: MFAVerificationRequest,
    user_context: UserContext = Depends(require_super_admin),
):
    """Verify MFA token and create session if successful."""
    try:
        success = False

        if verification.method == MFAMethod.TOTP:
            success = await mfa_service.verify_totp(
                user_context.user_id, verification.token
            )
        elif verification.method in [MFAMethod.PHONE, MFAMethod.BACKUP_EMAIL]:
            if not verification.factor_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Challenge ID is required for backup authentication",
                )

            success = await mfa_service.verify_backup_token(
                user_context.user_id, str(verification.factor_id), verification.token
            )
        elif verification.method == MFAMethod.RECOVERY_CODE:
            success = await mfa_service.verify_recovery_code(
                user_context.user_id, verification.token
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"MFA method {verification.method} not supported",
            )

        if success:
            # Create MFA session
            ip_address = request.client.host if request.client else "unknown"
            user_agent = request.headers.get("user-agent")

            session = await mfa_service.create_mfa_session(
                user_id=user_context.user_id,
                mfa_method=verification.method,
                ip_address=ip_address,
                user_agent=user_agent,
                factor_id=(
                    str(verification.factor_id) if verification.factor_id else None
                ),
            )

            return MFAValidationResponse(
                success=True,
                session_token=session.session_token,
                expires_at=session.expires_at,
                message="MFA verification successful",
            )
        else:
            return MFAValidationResponse(success=False, message="Invalid MFA token")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying MFA for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify MFA",
        )


@router.post("/session/validate")
async def validate_session(
    request: Request,
    session_token: str,
    user_context: UserContext = Depends(require_super_admin),
):
    """Validate an existing MFA session."""
    try:
        valid = await mfa_service.validate_mfa_session(
            user_context.user_id, session_token
        )

        return JSONResponse(content={"valid": valid}, status_code=status.HTTP_200_OK)
    except Exception as e:
        logger.error(
            f"Error validating MFA session for user {user_context.user_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate MFA session",
        )


@router.post("/recovery/generate", response_model=RecoveryCodesResponse)
async def generate_recovery_codes(
    request: Request, user_context: UserContext = Depends(require_super_admin)
):
    """Generate new recovery codes for the user."""
    try:
        # Generate new recovery codes
        new_codes = mfa_service._generate_recovery_codes(10)

        # Get or create MFA config
        config = await mfa_service.get_mfa_config(user_context.user_id)
        if not config:
            config = await mfa_service.create_mfa_config(
                user_context.user_id, user_context.email
            )

        # Encrypt and store new recovery codes
        encrypted_codes = [mfa_service._encrypt_secret(code) for code in new_codes]

        mfa_service.supabase.table("mfa_enhancements.superadmin_mfa_config").update(
            {
                "recovery_codes": encrypted_codes,
                "recovery_codes_generated_at": datetime.utcnow().isoformat(),
                "recovery_codes_used": 0,
                "updated_at": datetime.utcnow().isoformat(),
            }
        ).eq("user_id", user_context.user_id).execute()

        # Log security event
        await mfa_service._log_security_event(
            user_id=user_context.user_id,
            event_type="recovery_codes_generated",
            event_category=SecurityEventCategory.MFA,
            severity=SecurityEventSeverity.WARNING,
            description="New recovery codes generated",
            metadata={"codes_count": len(new_codes)},
            ip_address=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent"),
        )

        return RecoveryCodesResponse(
            recovery_codes=new_codes,
            generated_at=datetime.utcnow(),
            warning_message="Store these codes securely. Each code can only be used once. Previous recovery codes are now invalid.",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error generating recovery codes for user {user_context.user_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate recovery codes",
        )


@router.post("/recovery/use")
async def use_recovery_code(
    request: Request,
    recovery_code: str,
    user_context: UserContext = Depends(require_super_admin),
):
    """Use a recovery code for MFA verification."""
    try:
        success = await mfa_service.verify_recovery_code(
            user_context.user_id, recovery_code.upper().strip()
        )

        if success:
            # Create MFA session
            ip_address = request.client.host if request.client else "unknown"
            user_agent = request.headers.get("user-agent")

            session = await mfa_service.create_mfa_session(
                user_id=user_context.user_id,
                mfa_method=MFAMethod.RECOVERY_CODE,
                ip_address=ip_address,
                user_agent=user_agent,
            )

            return MFAValidationResponse(
                success=True,
                session_token=session.session_token,
                expires_at=session.expires_at,
                message="Recovery code accepted. MFA session created.",
            )
        else:
            return MFAValidationResponse(
                success=False, message="Invalid or already used recovery code"
            )
    except Exception as e:
        logger.error(f"Error using recovery code for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process recovery code",
        )


@router.post("/recovery/admin-override")
async def admin_override_mfa(
    request: Request,
    target_user_email: str,
    justification: str,
    user_context: UserContext = Depends(require_super_admin),
):
    """Administrative override for MFA (emergency access)."""
    try:
        # Verify the requesting user is a superadmin
        super_admin_emails = os.getenv("SUPER_ADMIN_EMAILS", "").split(",")
        super_admin_emails = [
            email.strip() for email in super_admin_emails if email.strip()
        ]

        if user_context.email not in super_admin_emails:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Administrative override requires superadmin privileges",
            )

        # Log the override attempt
        await mfa_service._log_security_event(
            user_id=user_context.user_id,
            event_type="mfa_admin_override",
            event_category=SecurityEventCategory.SECURITY,
            severity=SecurityEventSeverity.CRITICAL,
            description=f"Administrative MFA override for {target_user_email}",
            metadata={
                "target_user": target_user_email,
                "justification": justification,
                "admin_user": user_context.email,
            },
            ip_address=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent"),
        )

        # Generate temporary session for target user
        # This would require additional verification in production
        return JSONResponse(
            content={
                "message": "Administrative override logged. Manual intervention required.",
                "override_id": "admin-override-"
                + datetime.utcnow().strftime("%Y%m%d-%H%M%S"),
                "warning": "This action has been logged for security audit.",
            },
            status_code=status.HTTP_200_OK,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing admin override: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process administrative override",
        )


@router.post("/challenge", response_model=MFAChallengeResponse)
async def initiate_mfa_challenge(
    request: Request,
    challenge_request: MFAChallengeRequest,
    user_context: UserContext = Depends(require_super_admin),
):
    """Initiate an MFA challenge (for SMS/email backup methods)."""
    try:
        ip_address = request.client.host if request.client else "unknown"

        if challenge_request.method == MFAMethod.PHONE:
            if not challenge_request.destination:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Phone number is required for SMS challenge",
                )

            token_id = await mfa_service.send_sms_token(
                user_context.user_id, challenge_request.destination, ip_address
            )

            return MFAChallengeResponse(
                challenge_id=token_id,
                method=MFAMethod.PHONE,
                expires_at=datetime.utcnow() + timedelta(minutes=10),
                message=f"SMS verification code sent to {challenge_request.destination[-4:]}",
            )

        elif challenge_request.method == MFAMethod.BACKUP_EMAIL:
            if not challenge_request.destination:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email address is required for email challenge",
                )

            token_id = await mfa_service.send_email_token(
                user_context.user_id, challenge_request.destination, ip_address
            )

            return MFAChallengeResponse(
                challenge_id=token_id,
                method=MFAMethod.BACKUP_EMAIL,
                expires_at=datetime.utcnow() + timedelta(minutes=15),
                message=f"Email verification code sent to {challenge_request.destination}",
            )

        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid MFA method for challenge",
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error initiating MFA challenge for user {user_context.user_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate MFA challenge",
        )


@router.delete("/disable")
async def disable_mfa(
    request: Request, user_context: UserContext = Depends(require_super_admin)
):
    """Disable MFA for the user (admin only)."""
    try:
        # This would require additional admin verification
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="MFA disable functionality not yet implemented",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disabling MFA for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disable MFA",
        )


@router.get("/factors")
async def get_mfa_factors(
    request: Request, user_context: UserContext = Depends(require_super_admin)
):
    """Get all MFA factors for the user."""
    try:
        # Get factors from Supabase auth.mfa_factors
        factors_result = (
            mfa_service.supabase.table("auth.mfa_factors")
            .select(
                "id, factor_type, status, friendly_name, created_at, last_challenged_at"
            )
            .eq("user_id", user_context.user_id)
            .execute()
        )

        return JSONResponse(
            content={"factors": factors_result.data}, status_code=status.HTTP_200_OK
        )
    except Exception as e:
        logger.error(f"Error getting MFA factors for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MFA factors",
        )


@router.get("/sessions")
async def get_mfa_sessions(
    request: Request, user_context: UserContext = Depends(require_super_admin)
):
    """Get active MFA sessions for the user."""
    try:
        sessions_result = (
            mfa_service.supabase.table("mfa_enhancements.superadmin_mfa_sessions")
            .select(
                "id, verified_at, expires_at, last_activity_at, mfa_method, is_active, ip_address"
            )
            .eq("user_id", user_context.user_id)
            .eq("is_active", True)
            .order("created_at", desc=True)
            .execute()
        )

        return JSONResponse(
            content={"sessions": sessions_result.data}, status_code=status.HTTP_200_OK
        )
    except Exception as e:
        logger.error(f"Error getting MFA sessions for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MFA sessions",
        )


@router.get("/attempts")
async def get_mfa_attempts(
    request: Request,
    limit: int = 50,
    user_context: UserContext = Depends(require_super_admin),
):
    """Get recent MFA attempts for the user."""
    try:
        attempts_result = (
            mfa_service.supabase.table("mfa_enhancements.superadmin_mfa_attempts")
            .select("method, success, failure_reason, attempted_at, ip_address")
            .eq("user_id", user_context.user_id)
            .order("attempted_at", desc=True)
            .limit(limit)
            .execute()
        )

        return JSONResponse(
            content={"attempts": attempts_result.data}, status_code=status.HTTP_200_OK
        )
    except Exception as e:
        logger.error(f"Error getting MFA attempts for user {user_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MFA attempts",
        )
