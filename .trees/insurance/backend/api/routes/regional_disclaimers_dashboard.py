"""
Regional Disclaimers Dashboard API Routes

Provides comprehensive dashboard endpoints for managing and monitoring
regional legal disclaimers compliance system.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from uuid import UUID

from backend.api.dependencies.auth import get_current_user, UserContext
from backend.api.dependencies.authorization import require_role, UserRole
from backend.services.regional_disclaimer_service import regional_disclaimer_service
from backend.db.supabase_client import get_supabase_client, DataRegion

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/regional-disclaimers-dashboard", tags=["Regional Disclaimers Dashboard"]
)


# Response Models
class DisclaimerStats(BaseModel):
    """Statistics for a single disclaimer."""

    disclaimer_id: str
    title: str
    disclaimer_type: str
    region: str
    placement: List[str]
    is_required: bool
    total_views: int
    total_acknowledgments: int
    acknowledgment_rate: float
    last_viewed: Optional[datetime]
    last_acknowledged: Optional[datetime]


class RegionalSummary(BaseModel):
    """Summary statistics for a region."""

    region: str
    region_name: str
    total_disclaimers: int
    required_disclaimers: int
    optional_disclaimers: int
    total_views: int
    total_acknowledgments: int
    overall_acknowledgment_rate: float
    compliance_frameworks: List[str]
    last_updated: datetime


class AuditLogEntry(BaseModel):
    """Audit log entry for disclaimer events."""

    id: str
    event_type: str
    disclaimer_id: Optional[str]
    disclaimer_title: Optional[str]
    user_id: Optional[str]
    region: str
    placement: Optional[str]
    practice_area: Optional[str]
    jurisdiction: Optional[str]
    event_timestamp: datetime
    ip_address_hash: Optional[str]
    request_path: Optional[str]
    metadata: Dict[str, Any]


class ComplianceReport(BaseModel):
    """Comprehensive compliance report."""

    report_id: str
    generated_at: datetime
    period_start: datetime
    period_end: datetime
    regional_summaries: List[RegionalSummary]
    disclaimer_stats: List[DisclaimerStats]
    total_events: int
    compliance_score: float
    recommendations: List[str]


class DashboardMetrics(BaseModel):
    """Dashboard overview metrics."""

    total_disclaimers: int
    total_regions: int
    total_views_today: int
    total_acknowledgments_today: int
    overall_compliance_rate: float
    critical_issues: int
    pending_reviews: int
    last_updated: datetime


# API Endpoints
@router.get("/metrics", response_model=DashboardMetrics)
async def get_dashboard_metrics(
    current_user: UserContext = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
):
    """
    Get overview metrics for the disclaimers dashboard.
    """
    try:
        # Get current date for today's metrics
        today = datetime.utcnow().date()

        # Mock data - in production this would query the database
        metrics = DashboardMetrics(
            total_disclaimers=12,
            total_regions=2,
            total_views_today=156,
            total_acknowledgments_today=142,
            overall_compliance_rate=91.0,
            critical_issues=0,
            pending_reviews=2,
            last_updated=datetime.utcnow(),
        )

        logger.info(
            f"Retrieved dashboard metrics",
            extra={
                "user_id": str(current_user.id),
                "total_disclaimers": metrics.total_disclaimers,
                "compliance_rate": metrics.overall_compliance_rate,
            },
        )

        return metrics

    except Exception as e:
        logger.error(f"Failed to get dashboard metrics: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve dashboard metrics"
        )


@router.get("/regional-summary", response_model=List[RegionalSummary])
async def get_regional_summary(
    current_user: UserContext = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
):
    """
    Get summary statistics for all regions.
    """
    try:
        # Mock data - in production this would query the database
        summaries = [
            RegionalSummary(
                region="US",
                region_name="United States",
                total_disclaimers=7,
                required_disclaimers=5,
                optional_disclaimers=2,
                total_views=1250,
                total_acknowledgments=1142,
                overall_acknowledgment_rate=91.4,
                compliance_frameworks=[
                    "ABA Model Rules",
                    "State Bar Rules",
                    "Attorney Advertising",
                ],
                last_updated=datetime.utcnow(),
            ),
            RegionalSummary(
                region="EU",
                region_name="European Union",
                total_disclaimers=5,
                required_disclaimers=4,
                optional_disclaimers=1,
                total_views=890,
                total_acknowledgments=812,
                overall_acknowledgment_rate=91.2,
                compliance_frameworks=[
                    "EU Legal Services Directive",
                    "GDPR",
                    "National Bar Rules",
                ],
                last_updated=datetime.utcnow(),
            ),
        ]

        logger.info(
            f"Retrieved regional summary for {len(summaries)} regions",
            extra={
                "user_id": str(current_user.id),
                "regions": [s.region for s in summaries],
            },
        )

        return summaries

    except Exception as e:
        logger.error(f"Failed to get regional summary: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve regional summary"
        )


@router.get("/disclaimer-stats", response_model=List[DisclaimerStats])
async def get_disclaimer_stats(
    region: Optional[str] = Query(None, description="Filter by region"),
    disclaimer_type: Optional[str] = Query(
        None, description="Filter by disclaimer type"
    ),
    current_user: UserContext = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
):
    """
    Get detailed statistics for individual disclaimers.
    """
    try:
        # Mock data - in production this would query the database
        all_stats = [
            DisclaimerStats(
                disclaimer_id="1",
                title="AI Assistant Disclaimer",
                disclaimer_type="no_legal_advice",
                region="US",
                placement=["modal", "footer"],
                is_required=True,
                total_views=450,
                total_acknowledgments=412,
                acknowledgment_rate=91.6,
                last_viewed=datetime.utcnow() - timedelta(minutes=15),
                last_acknowledged=datetime.utcnow() - timedelta(minutes=20),
            ),
            DisclaimerStats(
                disclaimer_id="2",
                title="Attorney-Client Relationship",
                disclaimer_type="attorney_client_relationship",
                region="US",
                placement=["modal"],
                is_required=True,
                total_views=380,
                total_acknowledgments=350,
                acknowledgment_rate=92.1,
                last_viewed=datetime.utcnow() - timedelta(hours=1),
                last_acknowledged=datetime.utcnow() - timedelta(hours=2),
            ),
            DisclaimerStats(
                disclaimer_id="3",
                title="Professional Responsibility",
                disclaimer_type="professional_responsibility",
                region="EU",
                placement=["footer", "terms"],
                is_required=True,
                total_views=320,
                total_acknowledgments=290,
                acknowledgment_rate=90.6,
                last_viewed=datetime.utcnow() - timedelta(hours=3),
                last_acknowledged=datetime.utcnow() - timedelta(hours=4),
            ),
        ]

        # Apply filters
        filtered_stats = all_stats
        if region:
            filtered_stats = [s for s in filtered_stats if s.region == region]
        if disclaimer_type:
            filtered_stats = [
                s for s in filtered_stats if s.disclaimer_type == disclaimer_type
            ]

        logger.info(
            f"Retrieved disclaimer stats: {len(filtered_stats)} disclaimers",
            extra={
                "user_id": str(current_user.id),
                "region_filter": region,
                "type_filter": disclaimer_type,
                "result_count": len(filtered_stats),
            },
        )

        return filtered_stats

    except Exception as e:
        logger.error(f"Failed to get disclaimer stats: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve disclaimer statistics"
        )


@router.get("/audit-log", response_model=List[AuditLogEntry])
async def get_audit_log(
    limit: int = Query(50, ge=1, le=1000, description="Number of entries to return"),
    offset: int = Query(0, ge=0, description="Number of entries to skip"),
    region: Optional[str] = Query(None, description="Filter by region"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    start_date: Optional[datetime] = Query(
        None, description="Start date for filtering"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    current_user: UserContext = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
):
    """
    Get audit log entries for disclaimer events.
    """
    try:
        # Mock data - in production this would query the database
        all_entries = [
            AuditLogEntry(
                id="log_1",
                event_type="disclaimer_viewed",
                disclaimer_id="1",
                disclaimer_title="AI Assistant Disclaimer",
                user_id="user_123",
                region="US",
                placement="modal",
                practice_area="personal_injury",
                jurisdiction="california",
                event_timestamp=datetime.utcnow() - timedelta(minutes=15),
                ip_address_hash="hash_abc123",
                request_path="/dashboard",
                metadata={"user_agent": "Chrome", "session_id": "sess_456"},
            ),
            AuditLogEntry(
                id="log_2",
                event_type="disclaimer_acknowledged",
                disclaimer_id="1",
                disclaimer_title="AI Assistant Disclaimer",
                user_id="user_123",
                region="US",
                placement="modal",
                practice_area="personal_injury",
                jurisdiction="california",
                event_timestamp=datetime.utcnow() - timedelta(minutes=20),
                ip_address_hash="hash_abc123",
                request_path="/dashboard",
                metadata={"acknowledgment_method": "click", "session_id": "sess_456"},
            ),
        ]

        # Apply filters
        filtered_entries = all_entries
        if region:
            filtered_entries = [e for e in filtered_entries if e.region == region]
        if event_type:
            filtered_entries = [
                e for e in filtered_entries if e.event_type == event_type
            ]
        if start_date:
            filtered_entries = [
                e for e in filtered_entries if e.event_timestamp >= start_date
            ]
        if end_date:
            filtered_entries = [
                e for e in filtered_entries if e.event_timestamp <= end_date
            ]

        # Apply pagination
        paginated_entries = filtered_entries[offset : offset + limit]

        logger.info(
            f"Retrieved audit log: {len(paginated_entries)} entries",
            extra={
                "user_id": str(current_user.id),
                "total_entries": len(filtered_entries),
                "returned_entries": len(paginated_entries),
                "filters": {
                    "region": region,
                    "event_type": event_type,
                    "start_date": start_date,
                    "end_date": end_date,
                },
            },
        )

        return paginated_entries

    except Exception as e:
        logger.error(f"Failed to get audit log: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve audit log")


@router.post("/generate-report", response_model=ComplianceReport)
async def generate_compliance_report(
    period_days: int = Query(30, ge=1, le=365, description="Report period in days"),
    regions: Optional[List[str]] = Query(
        None, description="Regions to include in report"
    ),
    current_user: UserContext = Depends(
        require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
    ),
):
    """
    Generate a comprehensive compliance report.
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=period_days)

        # Get regional summaries and disclaimer stats
        regional_summaries = await get_regional_summary(current_user)
        disclaimer_stats = await get_disclaimer_stats(current_user=current_user)

        # Filter by regions if specified
        if regions:
            regional_summaries = [r for r in regional_summaries if r.region in regions]
            disclaimer_stats = [d for d in disclaimer_stats if d.region in regions]

        # Calculate compliance score
        total_views = sum(r.total_views for r in regional_summaries)
        total_acknowledgments = sum(r.total_acknowledgments for r in regional_summaries)
        compliance_score = (
            (total_acknowledgments / total_views * 100) if total_views > 0 else 0
        )

        # Generate recommendations
        recommendations = []
        if compliance_score < 90:
            recommendations.append(
                "Consider improving disclaimer visibility and clarity"
            )
        if any(r.overall_acknowledgment_rate < 85 for r in regional_summaries):
            recommendations.append(
                "Review low-performing disclaimers in specific regions"
            )
        if not recommendations:
            recommendations.append(
                "Compliance rates are excellent - maintain current practices"
            )

        report = ComplianceReport(
            report_id=f"report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            generated_at=datetime.utcnow(),
            period_start=start_date,
            period_end=end_date,
            regional_summaries=regional_summaries,
            disclaimer_stats=disclaimer_stats,
            total_events=total_views,
            compliance_score=compliance_score,
            recommendations=recommendations,
        )

        logger.info(
            f"Generated compliance report",
            extra={
                "user_id": str(current_user.id),
                "report_id": report.report_id,
                "period_days": period_days,
                "compliance_score": compliance_score,
                "regions": regions or "all",
            },
        )

        return report

    except Exception as e:
        logger.error(f"Failed to generate compliance report: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to generate compliance report"
        )


@router.get("/health")
async def health_check():
    """
    Health check for the regional disclaimers dashboard.
    """
    try:
        # Test service connectivity
        test_context = {"user_region": "US", "placement": "footer"}

        return {
            "status": "healthy",
            "service": "regional_disclaimers_dashboard",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat(),
        }
