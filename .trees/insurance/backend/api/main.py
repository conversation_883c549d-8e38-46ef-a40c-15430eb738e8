"""
Main FastAPI application entry point.

This module initializes the FastAPI application and registers all routers.
"""

# Ensure .env is loaded before importing any route or service modules
from dotenv import load_dotenv

# Load .env and override any existing vars to pick up updated values
load_dotenv(override=True)

import logging
import os
import sys

# Add the project root to Python path to fix import issues
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response

# from backend.metrics import get_metrics_output

# Import middleware components
from backend.middleware.metrics_middleware import CalendarMetricsMiddleware
from backend.middleware.deprecation_middleware import DeprecationMiddleware
from backend.middleware.data_residency_middleware import DataResidencyMiddleware
from backend.middleware.mfa_middleware import mfa_middleware
from backend.middleware.compliance_audit_middleware import ComplianceAuditMiddleware

# Import webhook retry task
from backend.tasks.webhook_retry import schedule_webhook_retry_task

# Import all route modules
from .copilotkit_route import router as copilotkit_router
from .routes import scheduling_router, webhook_router
from .routes.admin import llm_router
from .routes.avricons_intake import router as avricons_intake_router
from .routes.calendar import router as calendar_router
from .routes.deadline_insights import router as deadline_insights_router
from .routes.deprecation_monitoring import router as deprecation_monitoring_router
from .routes.jobs import router as jobs_router
from .routes.mcp_metrics import router as mcp_metrics_router
from .routes.stripe_webhook import router as stripe_webhook_router
from .routes.tenant_briefing_config import router as tenant_briefing_config_router
from .routes.tenants import router as tenants_router
from .routes.compliance_audit import router as compliance_audit_router
from .routes.regional_disclaimers_dashboard import (
    router as regional_disclaimers_dashboard_router,
)
from .routes.research_websocket import router as research_websocket_router
from .routes.redis_proxy import router as redis_proxy_router
from .routes.consent_management import router as consent_management_router
from .routes.data_residency import router as data_residency_router
from .routes.regional_disclaimers import router as regional_disclaimers_router
from .routes.mfa import router as mfa_router
from .routes.webhook_monitoring import router as webhook_monitoring_router
from .subscription_api import router as subscription_router

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Initialization function to create the FastAPI app
def create_app():
    # Create FastAPI app
    app = FastAPI(
        title="AiLex API",
        description="API for AiLex Legal Assistant",
        version="1.0.0",
    )

    # Configure CORS
    origins = os.getenv("CORS_ORIGINS", "*").split(",")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add metrics middleware for calendar routes
    app.add_middleware(CalendarMetricsMiddleware)

    # Add deprecation middleware for deprecated API endpoints
    app.add_middleware(DeprecationMiddleware)

    # Add data residency middleware for GDPR/CCPA compliance
    enable_strict_mode = (
        os.getenv("ENABLE_STRICT_DATA_RESIDENCY", "false").lower() == "true"
    )
    app.add_middleware(DataResidencyMiddleware, enable_strict_mode=enable_strict_mode)

    # Add MFA middleware for superadmin authentication
    app.middleware("http")(mfa_middleware)

    # Add compliance audit middleware for automatic event logging
    app.add_middleware(ComplianceAuditMiddleware)

    # Register routers
    app.include_router(copilotkit_router)
    app.include_router(webhook_router)
    app.include_router(scheduling_router)
    app.include_router(llm_router)
    app.include_router(avricons_intake_router)
    app.include_router(calendar_router)
    app.include_router(deadline_insights_router)
    app.include_router(deprecation_monitoring_router)
    app.include_router(jobs_router)
    app.include_router(mcp_metrics_router)
    app.include_router(stripe_webhook_router)
    app.include_router(tenant_briefing_config_router)
    app.include_router(tenants_router)
    app.include_router(research_websocket_router)
    app.include_router(redis_proxy_router)
    app.include_router(consent_management_router)
    app.include_router(data_residency_router)
    app.include_router(regional_disclaimers_router)
    app.include_router(mfa_router)
    app.include_router(webhook_monitoring_router)
    app.include_router(subscription_router)

    # Import and register tax calculation router
    from backend.api.routes.tax_calculation import router as tax_calculation_router

    app.include_router(tax_calculation_router)

    # Register compliance audit router
    app.include_router(
        compliance_audit_router,
        prefix="/api/compliance-audit",
        tags=["Compliance Audit"],
    )

    # Register regional disclaimers dashboard router
    app.include_router(
        regional_disclaimers_dashboard_router,
        prefix="/api",
        tags=["Regional Disclaimers Dashboard"],
    )

    # Add startup and shutdown events
    @app.on_event("startup")
    async def startup_event():
        import time

        app.state.start_time = time.time()
        logger.info("Starting AiLex API")

        # Initialize compliance audit service
        try:
            from backend.services.comprehensive_compliance_audit import (
                comprehensive_audit_service,
            )

            await comprehensive_audit_service.start_background_tasks()
            logger.info("✅ Compliance audit service started")
        except Exception as e:
            logger.error(f"❌ Failed to start compliance audit service: {e}")

        # Validate security configuration at startup
        try:
            # Import here to avoid circular imports
            import sys
            import os

            sys.path.append(os.path.join(os.path.dirname(__file__), "..", "..", "src"))
            from pi_lawyer.security import validate_startup_security

            validate_startup_security()
            logger.info("✅ Security validation passed")
        except Exception as e:
            logger.error(f"❌ Security validation failed: {e}")
            logger.error("Consider fixing security configuration issues")
            # Don't block startup for backend API, but log the issues

    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("Shutting down AiLex API")

        # Shutdown compliance audit service
        try:
            from backend.services.comprehensive_compliance_audit import (
                comprehensive_audit_service,
            )

            await comprehensive_audit_service.stop_background_tasks()
            logger.info("✅ Compliance audit service stopped")
        except Exception as e:
            logger.error(f"❌ Failed to stop compliance audit service: {e}")

    # Add root endpoint
    @app.get("/")
    async def root():
        """Root endpoint that returns a simple message."""
        return {"message": "PI Lawyer AI API is running"}

    # Add health endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for monitoring and container health checks."""
        from datetime import datetime
        import time
        import platform

        # Check Redis connection (simplified for now)
        redis_status = "configured" if os.getenv("REDIS_URL") else "not_configured"

        return {
            "status": "ok",
            "version": "1.0.0",
            "environment": os.environ.get("APP_ENV", "development"),
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": (
                int(time.time() - app.state.start_time)
                if hasattr(app.state, "start_time")
                else 0
            ),
            "system_info": {
                "python_version": platform.python_version(),
                "system": platform.system(),
                "platform": platform.platform(),
            },
            "redis_status": redis_status,
        }

    # Redis testing endpoints
    @app.post("/api/redis/ping")
    async def redis_ping():
        """Test Redis connection with ping."""
        try:
            from backend.utils.redis_health import get_redis_status

            redis_health = await get_redis_status()

            if redis_health.get("status") == "connected":
                return {
                    "status": "success",
                    "ping": redis_health.get("ping", False),
                    "redis_info": redis_health.get("info", {}),
                }
            else:
                return {
                    "status": "error",
                    "message": redis_health.get("error", "Redis connection failed"),
                    "ping": False,
                }
        except Exception as e:
            return {"status": "error", "message": str(e)}

    @app.get("/api/metrics/cache")
    async def cache_metrics():
        """Get semantic cache performance metrics."""
        try:
            from backend.agents.interactive.research.cache.semantic_cache_lite import (
                SemanticCacheLite,
            )

            cache = SemanticCacheLite()
            await cache._initialize_clients()

            if not cache._redis_client:
                return {"error": "Redis client not initialized"}

            # Get cache statistics
            info = await cache._redis_client.info()

            # Get cache-specific metrics (these would be tracked by the cache implementation)
            # For now, return basic Redis metrics
            return {
                "hit_rate": 0,  # Would be calculated from actual cache hits/misses
                "hits": 0,
                "misses": 0,
                "total_requests": 0,
                "cache_size": (
                    info.get("db1", {}).get("keys", 0) if "db1" in info else 0
                ),
                "avg_response_time": 0,
                "redis_memory_usage": info.get("used_memory_human", "unknown"),
                "redis_connected_clients": info.get("connected_clients", 0),
            }
        except Exception as e:
            return {"error": str(e)}

    @app.post("/api/research/query")
    async def research_query(request: dict):
        """Test research query with semantic caching."""
        try:
            query = request.get("query", "")
            tenant_id = request.get("tenant_id", "default")

            if not query:
                return {"error": "Query is required"}

            # Simulate a research response with cache testing
            import time

            start_time = time.time()

            # For testing, return a mock response
            response_data = {
                "query": query,
                "tenant_id": tenant_id,
                "results": [
                    {
                        "title": "Sample Legal Research Result",
                        "content": f"This is a mock response for query: {query}",
                        "source": "Texas Legal Database",
                        "relevance_score": 0.95,
                    }
                ],
                "cache_hit": False,  # Would be determined by actual cache lookup
                "response_time_ms": int((time.time() - start_time) * 1000),
                "timestamp": time.time(),
            }

            return response_data

        except Exception as e:
            return {"error": str(e)}

    # Add metrics endpoint
    # @app.get("/metrics")
    # async def metrics():
    #     """Prometheus metrics endpoint."""
    #     metrics_output = get_metrics_output()
    #     return Response(
    #         content=metrics_output,
    #         media_type="text/plain; version=0.0.4"
    #     )

    # Register webhook retry task
    schedule_webhook_retry_task(app)

    return app


# Create the app instance to be used by ASGI servers (like Uvicorn)
app = create_app()

# For direct execution (development)
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
