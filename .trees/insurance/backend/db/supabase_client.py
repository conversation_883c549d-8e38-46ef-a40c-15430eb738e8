"""
Supabase client for the backend with regional data residency support.

This module provides a Supabase client for the backend with intelligent
regional routing for GDPR/CCPA compliance.
"""

import os
from typing import Any, Dict, List, Optional
from backend.services.data_residency import (
    DataRegion,
    get_regional_supabase_client,
    get_supabase_client_for_user,
)


class MockResult:
    """Mock result object that mimics Supabase query results."""

    def __init__(
        self, data: List[Dict[str, Any]], error: Optional[str] = None, count: int = 0
    ):
        self.data = data
        self.error = error
        self.count = count


class SupabaseClient:
    """Mock Supabase client for testing."""

    def __init__(self):
        """Initialize the Supabase client."""
        self.current_table = None
        self.current_query = {}
        self.current_schema = None

    def schema(self, schema_name: str):
        """
        Set the schema for the query.

        Args:
            schema_name: The name of the schema

        Returns:
            self: The Supabase client
        """
        self.current_schema = schema_name
        return self

    def from_(self, table: str):
        """Set the table for the query (alternative to table method)."""
        self.current_table = table
        return self

    def table(self, table_name: str):
        """
        Select a table to query.

        Args:
            table_name: The name of the table

        Returns:
            self: The Supabase client
        """
        self.current_table = table_name
        self.current_query = {}
        return self

    def select(self, columns: str = "*"):
        """
        Select columns from the table.

        Args:
            columns: The columns to select

        Returns:
            self: The Supabase client
        """
        self.current_query["select"] = columns
        return self

    def eq(self, column: str, value: Any):
        """
        Add an equality filter to the query.

        Args:
            column: The column to filter on
            value: The value to filter for

        Returns:
            self: The Supabase client
        """
        if "filters" not in self.current_query:
            self.current_query["filters"] = []

        self.current_query["filters"].append(
            {"type": "eq", "column": column, "value": value}
        )
        return self

    def gte(self, column: str, value: Any):
        """Add a greater than or equal filter."""
        if "filters" not in self.current_query:
            self.current_query["filters"] = []
        self.current_query["filters"].append(
            {"type": "gte", "column": column, "value": value}
        )
        return self

    def lte(self, column: str, value: Any):
        """Add a less than or equal filter."""
        if "filters" not in self.current_query:
            self.current_query["filters"] = []
        self.current_query["filters"].append(
            {"type": "lte", "column": column, "value": value}
        )
        return self

    def neq(self, column: str, value: Any):
        """Add a not equal filter."""
        if "filters" not in self.current_query:
            self.current_query["filters"] = []
        self.current_query["filters"].append(
            {"type": "neq", "column": column, "value": value}
        )
        return self

    def or_(self, conditions: str):
        """Add an OR filter."""
        if "filters" not in self.current_query:
            self.current_query["filters"] = []
        self.current_query["filters"].append({"type": "or", "conditions": conditions})
        return self

    def is_(self, column: str, value: Any):
        """
        Add an IS filter to the query.

        Args:
            column: The column to filter on
            value: The value to filter for

        Returns:
            self: The Supabase client
        """
        if "filters" not in self.current_query:
            self.current_query["filters"] = []

        self.current_query["filters"].append(
            {"type": "is", "column": column, "value": value}
        )
        return self

    def lte(self, column: str, value: Any):
        """
        Add a less than or equal filter to the query.

        Args:
            column: The column to filter on
            value: The value to filter for

        Returns:
            self: The Supabase client
        """
        if "filters" not in self.current_query:
            self.current_query["filters"] = []

        self.current_query["filters"].append(
            {"type": "lte", "column": column, "value": value}
        )
        return self

    def order(self, column: str, desc: bool = False):
        """
        Add an order by clause to the query.

        Args:
            column: The column to order by
            desc: Whether to order in descending order

        Returns:
            self: The Supabase client
        """
        self.current_query["order"] = {"column": column, "desc": desc}
        return self

    def limit(self, limit: int):
        """
        Add a limit clause to the query.

        Args:
            limit: The maximum number of rows to return

        Returns:
            self: The Supabase client
        """
        self.current_query["limit"] = limit
        return self

    def update(self, data: Dict[str, Any]):
        """
        Update rows in the table.

        Args:
            data: The data to update

        Returns:
            self: The Supabase client
        """
        self.current_query["update"] = data
        return self

    def insert(self, data: Dict[str, Any]):
        """Insert data into the table."""
        self.current_query["insert"] = data
        return self

    def upsert(self, data: Dict[str, Any], on_conflict: str = None):
        """Insert or update data in the table."""
        self.current_query["upsert"] = data
        if on_conflict:
            self.current_query["on_conflict"] = on_conflict
        return self

    def execute(self):
        """
        Execute the query.

        Returns:
            MockResult: The query result
        """
        # This is a mock implementation that returns empty data
        return MockResult(data=[], error=None, count=0)


def get_supabase_client(use_service_role: bool = False) -> SupabaseClient:
    """
    Get a Supabase client (sync version).

    Args:
        use_service_role: Whether to use service role credentials (ignored in mock)

    Returns:
        SupabaseClient: The Supabase client
    """
    return SupabaseClient()


async def get_supabase_client_async(use_service_role: bool = False) -> SupabaseClient:
    """
    Get a Supabase client (async version).

    Args:
        use_service_role: Whether to use service role credentials (ignored in mock)

    Returns:
        SupabaseClient: The Supabase client
    """
    return SupabaseClient()


def get_regional_supabase_client_wrapper(
    region: Optional[str] = None, user_id: Optional[str] = None
):
    """
    Get a regional Supabase client with fallback to mock for testing.

    Args:
        region: Target region ('US' or 'EU')
        user_id: User ID for logging

    Returns:
        Regional Supabase client or mock client for testing
    """
    # Check if we're in a testing environment
    if os.getenv("TESTING", "false").lower() == "true":
        return SupabaseClient()  # Return mock client for tests

    try:
        # Try to use real regional client
        if region:
            data_region = DataRegion(region.upper())
            return get_regional_supabase_client(data_region)
        else:
            # Use user-based routing
            user_region = DataRegion(region.upper()) if region else None
            return get_supabase_client_for_user(user_region, user_id)
    except Exception:
        # Fallback to mock client if regional routing fails
        return SupabaseClient()
