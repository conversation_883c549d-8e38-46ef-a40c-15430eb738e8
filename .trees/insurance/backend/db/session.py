"""
Database session utilities for the PI Lawyer AI system.

This module provides functions for creating and managing database sessions
with comprehensive connection pooling for production scalability.
"""

import logging
import os
import time
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.engine.events import event
from sqlalchemy import text

# Set up logging
logger = logging.getLogger(__name__)

# Connection pool configuration from environment variables
POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "20"))
MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "30"))
POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))
POOL_RECYCLE = int(os.getenv("DB_POOL_RECYCLE", "3600"))  # 1 hour
POOL_PRE_PING = os.getenv("DB_POOL_PRE_PING", "true").lower() == "true"
CONNECT_TIMEOUT = int(os.getenv("DB_CONNECT_TIMEOUT", "10"))
COMMAND_TIMEOUT = int(os.getenv("DB_COMMAND_TIMEOUT", "60"))

# Environment detection
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
IS_PRODUCTION = ENVIRONMENT == "production"

# Get database connection string from environment variables
# Try Supabase first, then fall back to local PostgreSQL
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")

if SUPABASE_URL and SUPABASE_SERVICE_KEY:
    # Use Supabase connection
    # Convert Supabase URL to PostgreSQL connection string
    # Supabase URL format: https://xxx.supabase.co
    # PostgreSQL URL format: postgresql+asyncpg://postgres:[password]@db.xxx.supabase.co:5432/postgres
    supabase_host = SUPABASE_URL.replace("https://", "").replace("http://", "")
    DATABASE_URL = f"postgresql+asyncpg://postgres:{SUPABASE_SERVICE_KEY}@db.{supabase_host}:5432/postgres"
    logger.info("Using Supabase database connection")
else:
    # Fall back to local PostgreSQL
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "postgres")
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_NAME = os.getenv("DB_NAME", "ailex")
    DATABASE_URL = (
        f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    logger.info("Using local PostgreSQL database connection")

# Connection pool configuration for async engine
# Note: async engines use their own pool management, so we configure via engine parameters
pool_config = {
    "pool_size": POOL_SIZE,
    "max_overflow": MAX_OVERFLOW,
    "pool_timeout": POOL_TIMEOUT,
    "pool_recycle": POOL_RECYCLE,
    "pool_pre_ping": POOL_PRE_PING,
}

# For testing environments, use minimal pool settings
if ENVIRONMENT == "testing":
    pool_config.update(
        {"pool_size": 1, "max_overflow": 0, "pool_timeout": 5, "pool_recycle": 300}
    )

# Connection arguments for asyncpg
connect_args = {
    "command_timeout": COMMAND_TIMEOUT,
    "server_settings": {
        "jit": "off",  # Disable JIT for better connection performance
        "application_name": f"ailex_backend_{ENVIRONMENT}",
    },
}

# Create async engine with connection pooling
engine = create_async_engine(
    DATABASE_URL,
    echo=True if ENVIRONMENT == "development" else False,
    future=True,
    connect_args=connect_args,
    **pool_config,
)


# Connection pool monitoring
class ConnectionPoolMonitor:
    """Monitor connection pool statistics and health."""

    def __init__(self, engine):
        self.engine = engine
        self._connection_count = 0
        self._total_connections = 0
        self._failed_connections = 0
        self._connection_times = []

    def get_pool_status(self) -> dict:
        """Get current connection pool status."""
        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
            "total_connections": self._total_connections,
            "failed_connections": self._failed_connections,
            "success_rate": (
                (self._total_connections - self._failed_connections)
                / max(self._total_connections, 1)
                * 100
            ),
            "avg_connection_time": (
                sum(self._connection_times) / len(self._connection_times)
                if self._connection_times
                else 0
            ),
        }

    def record_connection_attempt(self, success: bool, duration: float):
        """Record a connection attempt for monitoring."""
        self._total_connections += 1
        if not success:
            self._failed_connections += 1
        if success:
            self._connection_times.append(duration)
            # Keep only last 100 connection times
            if len(self._connection_times) > 100:
                self._connection_times = self._connection_times[-100:]


# Initialize pool monitor
pool_monitor = ConnectionPoolMonitor(engine)


# Event listeners for connection monitoring
@event.listens_for(engine.sync_engine, "connect")
def on_connect(dbapi_connection, connection_record):
    """Handle new database connections."""
    logger.debug("New database connection established")


@event.listens_for(engine.sync_engine, "checkout")
def on_checkout(dbapi_connection, connection_record, connection_proxy):
    """Handle connection checkout from pool."""
    start_time = time.time()
    connection_record.info["checkout_time"] = start_time


@event.listens_for(engine.sync_engine, "checkin")
def on_checkin(dbapi_connection, connection_record):
    """Handle connection checkin to pool."""
    if "checkout_time" in connection_record.info:
        duration = time.time() - connection_record.info["checkout_time"]
        pool_monitor.record_connection_attempt(True, duration)
        del connection_record.info["checkout_time"]


# Create async session factory
async_session_factory = async_sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get a database session with enhanced error handling and monitoring.

    This function is used as a FastAPI dependency to provide database sessions
    to route handlers with connection pool monitoring.

    Yields:
        AsyncSession: A database session
    """
    start_time = time.time()
    session = None

    try:
        session = async_session_factory()
        yield session
        await session.commit()

        # Record successful connection
        duration = time.time() - start_time
        pool_monitor.record_connection_attempt(True, duration)

    except Exception as e:
        logger.error(f"Database session error: {e}")

        # Record failed connection
        duration = time.time() - start_time
        pool_monitor.record_connection_attempt(False, duration)

        if session:
            try:
                await session.rollback()
            except Exception as rollback_error:
                logger.error(f"Error during rollback: {rollback_error}")
        raise
    finally:
        if session:
            try:
                await session.close()
            except Exception as close_error:
                logger.error(f"Error closing session: {close_error}")


@asynccontextmanager
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Get a database session as an async context manager.

    This function is used for database operations outside of FastAPI routes.

    Yields:
        AsyncSession: A database session
    """
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            await session.close()


async def check_database_health() -> dict:
    """
    Check database connection health and pool status.

    Returns:
        dict: Database health status including connection pool metrics
    """
    try:
        start_time = time.time()

        # Test database connection
        async with async_session_factory() as session:
            result = await session.execute(text("SELECT 1"))
            await result.fetchone()

        connection_time = time.time() - start_time
        pool_status = pool_monitor.get_pool_status()

        return {
            "status": "healthy",
            "connection_time_ms": round(connection_time * 1000, 2),
            "database_url": DATABASE_URL.split("@")[0] + "@***",  # Hide credentials
            "pool_status": pool_status,
            "environment": ENVIRONMENT,
            "timestamp": time.time(),
        }

    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "pool_status": pool_monitor.get_pool_status(),
            "environment": ENVIRONMENT,
            "timestamp": time.time(),
        }


async def get_connection_pool_stats() -> dict:
    """
    Get detailed connection pool statistics.

    Returns:
        dict: Detailed pool statistics and configuration
    """
    pool_status = pool_monitor.get_pool_status()

    return {
        "configuration": {
            "pool_size": POOL_SIZE,
            "max_overflow": MAX_OVERFLOW,
            "pool_timeout": POOL_TIMEOUT,
            "pool_recycle": POOL_RECYCLE,
            "pool_pre_ping": POOL_PRE_PING,
            "connect_timeout": CONNECT_TIMEOUT,
            "command_timeout": COMMAND_TIMEOUT,
        },
        "current_status": pool_status,
        "health_indicators": {
            "pool_utilization": (
                pool_status["checked_out"] / max(POOL_SIZE + MAX_OVERFLOW, 1) * 100
            ),
            "connection_success_rate": pool_status["success_rate"],
            "avg_connection_time_ms": round(
                pool_status["avg_connection_time"] * 1000, 2
            ),
        },
        "recommendations": _get_pool_recommendations(pool_status),
    }


def _get_pool_recommendations(pool_status: dict) -> list:
    """Generate recommendations based on pool status."""
    recommendations = []

    utilization = pool_status["checked_out"] / max(POOL_SIZE + MAX_OVERFLOW, 1) * 100

    if utilization > 80:
        recommendations.append(
            "High pool utilization detected. Consider increasing pool_size or max_overflow."
        )

    if pool_status["success_rate"] < 95:
        recommendations.append(
            "Low connection success rate. Check database connectivity and network stability."
        )

    if pool_status["avg_connection_time"] > 1.0:
        recommendations.append(
            "High average connection time. Check database performance and network latency."
        )

    if pool_status["invalid"] > 0:
        recommendations.append(
            "Invalid connections detected. Check pool_recycle and pool_pre_ping settings."
        )

    return recommendations


async def cleanup_database_connections():
    """
    Clean up database connections and dispose of the engine.

    This should be called during application shutdown.
    """
    try:
        logger.info("Cleaning up database connections...")
        await engine.dispose()
        logger.info("Database connections cleaned up successfully")
    except Exception as e:
        logger.error(f"Error during database cleanup: {e}")


# Backward compatibility
async_session = async_session_factory
