#!/usr/bin/env python3
"""
Simple standalone test for LLM integration components.

This test validates the basic functionality without complex imports.
"""

import sys
import os
import asyncio
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../"))
sys.path.insert(0, project_root)


def test_basic_imports():
    """Test that we can import the basic components."""
    print("🧪 Testing Basic Imports...")

    try:
        from backend.agents.interactive.intake.conflict_types import (
            ConflictSeverity,
            ConflictType,
        )

        print("   ✅ Conflict types imported successfully")

        # Test enum values
        assert ConflictSeverity.LOW == "low"
        assert ConflictType.DIRECT_CLIENT == "direct_client"
        print("   ✅ Enum values validated")

    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False

    return True


def test_conflict_checker_basic():
    """Test basic conflict checker functionality."""
    print("🔍 Testing Conflict Checker...")

    try:
        from backend.agents.interactive.intake.conflict_checker import (
            MultiPractice<PERSON>onf<PERSON><PERSON><PERSON><PERSON><PERSON>,
            EntityMatcher,
        )

        # Test EntityMatcher
        matcher = EntityMatcher()
        similarity = matcher.calculate_similarity("<PERSON>", "Jon Smith")
        assert 0.0 <= similarity <= 1.0
        print(f"   ✅ Entity matcher works: similarity = {similarity:.2f}")

        # Test ConflictChecker initialization
        checker = MultiPracticeConflictChecker(use_llm=False)
        assert checker.use_llm is False
        print("   ✅ Conflict checker initialized without LLM")

        # Test with LLM (should handle gracefully)
        checker_llm = MultiPracticeConflictChecker(use_llm=True)
        print(f"   ✅ Conflict checker with LLM: use_llm = {checker_llm.use_llm}")

    except Exception as e:
        print(f"   ❌ Conflict checker test failed: {e}")
        return False

    return True


def test_risk_assessment_basic():
    """Test basic risk assessment functionality."""
    print("📊 Testing Risk Assessment...")

    try:
        from backend.agents.interactive.intake.risk_assessment import (
            RiskAssessmentEngine,
            RiskLevel,
        )

        # Test RiskLevel enum
        assert RiskLevel.LOW == "low"
        assert RiskLevel.VERY_HIGH == "very_high"
        print("   ✅ Risk level enum validated")

        # Test RiskAssessmentEngine initialization
        assessor = RiskAssessmentEngine(use_llm=False)
        assert assessor.use_llm is False
        print("   ✅ Risk assessor initialized without LLM")

        # Test with LLM (should handle gracefully)
        assessor_llm = RiskAssessmentEngine(use_llm=True)
        print(f"   ✅ Risk assessor with LLM: use_llm = {assessor_llm.use_llm}")

    except Exception as e:
        print(f"   ❌ Risk assessment test failed: {e}")
        return False

    return True


def test_llm_components_exist():
    """Test that LLM components exist and can be imported."""
    print("🤖 Testing LLM Components...")

    llm_components = [
        "llm_entity_extractor",
        "llm_conflict_analyzer",
        "llm_risk_intelligence",
        "llm_success_predictor",
    ]

    for component in llm_components:
        try:
            module_path = f"backend.agents.interactive.intake.{component}"
            __import__(module_path)
            print(f"   ✅ {component} imported successfully")
        except Exception as e:
            print(f"   ❌ {component} import failed: {e}")
            return False

    return True


def test_entity_patterns():
    """Test entity pattern functionality."""
    print("🔤 Testing Entity Patterns...")

    try:
        from backend.agents.interactive.intake.conflict_checker import (
            MultiPracticeConflictChecker,
        )

        checker = MultiPracticeConflictChecker(use_llm=False)
        patterns = checker.entity_patterns

        # Validate pattern structure
        expected_keys = [
            "person_names",
            "companies",
            "legal_entities",
            "dates",
            "case_numbers",
        ]
        for key in expected_keys:
            assert key in patterns, f"Missing pattern key: {key}"
            assert isinstance(patterns[key], list), f"Pattern {key} should be a list"
            assert len(patterns[key]) > 0, f"Pattern {key} should not be empty"

        print(f"   ✅ Entity patterns validated: {len(patterns)} pattern types")

    except Exception as e:
        print(f"   ❌ Entity patterns test failed: {e}")
        return False

    return True


async def test_async_functionality():
    """Test async functionality without actual LLM calls."""
    print("⚡ Testing Async Functionality...")

    try:
        from backend.agents.interactive.intake.conflict_checker import (
            MultiPracticeConflictChecker,
        )
        from backend.agents.interactive.intake.risk_assessment import (
            RiskAssessmentEngine,
        )

        # Create minimal test objects
        checker = MultiPracticeConflictChecker(use_llm=False)
        assessor = RiskAssessmentEngine(use_llm=False)

        # Test that async methods exist and can be called
        # (We won't call them with real data to avoid dependencies)
        assert hasattr(checker, "check_conflicts")
        assert asyncio.iscoroutinefunction(checker.check_conflicts)
        print("   ✅ Conflict checker has async check_conflicts method")

        assert hasattr(assessor, "assess_risk")
        assert asyncio.iscoroutinefunction(assessor.assess_risk)
        print("   ✅ Risk assessor has async assess_risk method")

    except Exception as e:
        print(f"   ❌ Async functionality test failed: {e}")
        return False

    return True


def main():
    """Run all tests."""
    print("🚀 Starting LLM Integration Tests")
    print("=" * 50)

    tests = [
        test_basic_imports,
        test_conflict_checker_basic,
        test_risk_assessment_basic,
        test_llm_components_exist,
        test_entity_patterns,
    ]

    async_tests = [test_async_functionality]

    # Run synchronous tests
    passed = 0
    total = len(tests) + len(async_tests)

    for test in tests:
        if test():
            passed += 1
        print()

    # Run async tests
    for test in async_tests:
        try:
            if asyncio.run(test()):
                passed += 1
        except Exception as e:
            print(f"   ❌ Async test failed: {e}")
        print()

    # Summary
    print("📋 Test Summary")
    print("-" * 20)
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")

    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
