#!/usr/bin/env python3
"""
Test Current System Without LLM Dependencies

This script tests what we can validate in the current environment
and provides a clear assessment of testing status.
"""

import sys
import os
import time
from typing import Dict, Any

# Add the backend directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../.."))


def test_basic_classification():
    """Test the basic keyword classification that we know works."""
    print("🧪 Testing Basic Keyword Classification...")

    try:
        # Test the enums and basic structures
        from agents.interactive.intake.state import PracticeArea, CaseUrgency, WorkType

        print("✅ Successfully imported enums")

        # Test practice areas
        areas = [pa.value for pa in PracticeArea]
        print(f"✅ Practice Areas: {areas}")

        # Test urgency levels
        urgencies = [u.value for u in CaseUrgency]
        print(f"✅ Urgency Levels: {urgencies}")

        # Test work types
        work_types = [wt.value for wt in WorkType]
        print(f"✅ Work Types: {work_types}")

        return True

    except Exception as e:
        print(f"❌ Basic structure test failed: {e}")
        return False


def test_terminology_updates():
    """Test that terminology has been properly updated."""
    print("\n🔄 Testing Terminology Updates...")

    try:
        # Check that we have MatterInformation instead of CaseInformation
        from agents.interactive.intake.state import MatterInformation, ClientInformation

        print("✅ MatterInformation class exists")

        # Test creating a matter
        matter = MatterInformation()
        print(f"✅ Can create MatterInformation: {type(matter)}")

        # Check for work_type field
        if hasattr(matter, "work_type"):
            print("✅ MatterInformation has work_type field")
        else:
            print("❌ MatterInformation missing work_type field")
            return False

        # Check for practice_area field
        if hasattr(matter, "practice_area"):
            print("✅ MatterInformation has practice_area field")
        else:
            print("❌ MatterInformation missing practice_area field")
            return False

        return True

    except Exception as e:
        print(f"❌ Terminology test failed: {e}")
        return False


def test_api_structure():
    """Test that API structure is properly defined."""
    print("\n🔗 Testing API Structure...")

    try:
        # Check if API file exists and has proper structure
        api_file = "backend/api/agents/intake.py"
        if os.path.exists(api_file):
            print("✅ API file exists")

            # Read the file and check for key components
            with open(api_file, "r") as f:
                content = f.read()

            required_components = [
                "ClassificationResponse",
                "classify_matter",
                "key_factors",
                "timeline_factors",
                "red_flags",
                "llm_reasoning",
            ]

            missing_components = []
            for component in required_components:
                if component in content:
                    print(f"✅ Found {component}")
                else:
                    print(f"❌ Missing {component}")
                    missing_components.append(component)

            return len(missing_components) == 0
        else:
            print("❌ API file does not exist")
            return False

    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        return False


def test_frontend_integration():
    """Test that frontend components are properly structured."""
    print("\n🎨 Testing Frontend Integration...")

    try:
        # Check if frontend files exist
        frontend_files = [
            "frontend/src/hooks/use-intake-agent.ts",
            "frontend/src/components/intake/multi-practice-intake.tsx",
            "frontend/src/components/intake/intake-test.tsx",
        ]

        existing_files = []
        for file_path in frontend_files:
            if os.path.exists(file_path):
                existing_files.append(file_path)
                print(f"✅ {file_path} exists")
            else:
                print(f"❌ {file_path} missing")

        # Check hook structure
        hook_file = "frontend/src/hooks/use-intake-agent.ts"
        if os.path.exists(hook_file):
            with open(hook_file, "r") as f:
                content = f.read()

            required_features = [
                "ClassificationResult",
                "key_factors",
                "timeline_factors",
                "red_flags",
                "llm_reasoning",
                "fallback_used",
            ]

            for feature in required_features:
                if feature in content:
                    print(f"✅ Hook has {feature}")
                else:
                    print(f"❌ Hook missing {feature}")

        return len(existing_files) >= 2  # At least 2 files should exist

    except Exception as e:
        print(f"❌ Frontend integration test failed: {e}")
        return False


def assess_llm_readiness():
    """Assess readiness for LLM integration."""
    print("\n🧠 Assessing LLM Integration Readiness...")

    readiness_checklist = {
        "Pydantic models defined": False,
        "LangChain imports structured": False,
        "Fallback mechanism designed": False,
        "API endpoints ready": False,
        "Frontend prepared for enhanced data": False,
    }

    try:
        # Check if intelligent classifier file exists
        classifier_file = (
            "backend/agents/interactive/intake/intelligent_matter_classifier.py"
        )
        if os.path.exists(classifier_file):
            print("✅ Intelligent classifier file exists")

            with open(classifier_file, "r") as f:
                content = f.read()

            # Check for key components
            if "MatterClassificationOutput" in content:
                readiness_checklist["Pydantic models defined"] = True
                print("✅ Pydantic models defined")

            if "langchain" in content:
                readiness_checklist["LangChain imports structured"] = True
                print("✅ LangChain imports structured")

            if "fallback_classifier" in content:
                readiness_checklist["Fallback mechanism designed"] = True
                print("✅ Fallback mechanism designed")

        # Check API readiness
        api_file = "backend/api/agents/intake.py"
        if os.path.exists(api_file):
            with open(api_file, "r") as f:
                api_content = f.read()

            if "classify_matter_from_description" in api_content:
                readiness_checklist["API endpoints ready"] = True
                print("✅ API endpoints ready")

        # Check frontend readiness
        hook_file = "frontend/src/hooks/use-intake-agent.ts"
        if os.path.exists(hook_file):
            with open(hook_file, "r") as f:
                hook_content = f.read()

            if "key_factors" in hook_content and "red_flags" in hook_content:
                readiness_checklist["Frontend prepared for enhanced data"] = True
                print("✅ Frontend prepared for enhanced data")

        ready_count = sum(readiness_checklist.values())
        total_count = len(readiness_checklist)

        print(f"\n📊 LLM Readiness: {ready_count}/{total_count} components ready")

        return ready_count >= 4  # Most components should be ready

    except Exception as e:
        print(f"❌ LLM readiness assessment failed: {e}")
        return False


def main():
    """Run all available tests."""
    print("🔍 TESTING CURRENT SYSTEM STATUS")
    print("=" * 50)

    test_results = []

    # Test 1: Basic Classification
    print("\n1️⃣ BASIC STRUCTURE TEST")
    result1 = test_basic_classification()
    test_results.append(("Basic Structure", result1))

    # Test 2: Terminology Updates
    print("\n2️⃣ TERMINOLOGY UPDATE TEST")
    result2 = test_terminology_updates()
    test_results.append(("Terminology Updates", result2))

    # Test 3: API Structure
    print("\n3️⃣ API STRUCTURE TEST")
    result3 = test_api_structure()
    test_results.append(("API Structure", result3))

    # Test 4: Frontend Integration
    print("\n4️⃣ FRONTEND INTEGRATION TEST")
    result4 = test_frontend_integration()
    test_results.append(("Frontend Integration", result4))

    # Test 5: LLM Readiness
    print("\n5️⃣ LLM READINESS ASSESSMENT")
    result5 = assess_llm_readiness()
    test_results.append(("LLM Readiness", result5))

    # Final Summary
    print("\n" + "=" * 50)
    print("📋 CURRENT SYSTEM STATUS")
    print("=" * 50)

    passed_tests = 0
    total_tests = len(test_results)

    for test_name, result in test_results:
        status = "✅ READY" if result else "❌ NEEDS WORK"
        print(f"{status}: {test_name}")
        if result:
            passed_tests += 1

    print(f"\nSystem Readiness: {passed_tests}/{total_tests} components ready")

    print("\n🎯 TESTING STATUS SUMMARY:")
    print("=" * 50)

    if passed_tests >= 4:
        print("✅ SYSTEM IS WELL-PREPARED")
        print("• Core structure is solid")
        print("• Terminology properly updated")
        print("• Ready for LLM integration")
        print("• Frontend components in place")
    elif passed_tests >= 2:
        print("⚠️  SYSTEM IS PARTIALLY READY")
        print("• Some components need attention")
        print("• Core functionality may work")
        print("• Requires fixes before LLM testing")
    else:
        print("❌ SYSTEM NEEDS SIGNIFICANT WORK")
        print("• Major components missing or broken")
        print("• Not ready for LLM integration")
        print("• Requires substantial fixes")

    print("\n🚀 NEXT STEPS FOR FULL TESTING:")
    print("1. Install LangChain dependencies:")
    print("   pip install langchain langchain-openai")
    print("2. Set up OpenAI API key:")
    print("   export OPENAI_API_KEY='your-key-here'")
    print("3. Run LLM integration tests")
    print("4. Test with real matter descriptions")
    print("5. Validate performance and accuracy")

    return passed_tests >= 3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
