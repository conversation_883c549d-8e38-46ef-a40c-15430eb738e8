"""
Enhanced State Management for Multi-Practice Intake Agent

This module provides comprehensive state management for the intake agent,
supporting multiple practice areas with specific workflows and requirements.

Key Features:
- Multi-practice area support (Personal Injury, Family Law, Criminal Defense)
- Practice-specific case types and urgency levels
- Enhanced client and case information tracking
- Conflict checking state management
- Validation and error handling
"""

from typing import Dict, Any, List, Optional, Union
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field, validator

from backend.agents.shared.core.state import BaseLangGraphState, AgentExecutionContext


class PracticeArea(str, Enum):
    """Supported practice areas for intake - all 7 core practice areas."""

    # Core 7 practice areas
    PERSONAL_INJURY = "personal_injury"  # Includes medical malpractice
    FAMILY_LAW = "family_law"
    CRIMINAL_DEFENSE = "criminal_defense"
    ESTATE_PLANNING = "estate_planning"  # Includes probate
    IMMIGRATION = "immigration"
    REAL_ESTATE = "real_estate"  # Residential/Landlord-Tenant
    BANKRUPTCY = "bankruptcy"

    # Additional practice areas for expanded support
    CIVIL_LITIGATION = "civil_litigation"
    EMPLOYMENT_LAW = "employment_law"


class WorkType(str, Enum):
    """Work types that determine Case vs Matter terminology."""

    LITIGATION = "litigation"
    TRANSACTIONAL = "transactional"
    ADVISORY = "advisory"
    ADR = "adr"


# Mapping from practice areas to work types (determines Case vs Matter display)
PRACTICE_AREA_TO_WORK_TYPE = {
    # Core 7 practice areas
    PracticeArea.PERSONAL_INJURY: WorkType.LITIGATION,  # Cases
    PracticeArea.FAMILY_LAW: WorkType.LITIGATION,  # Cases
    PracticeArea.CRIMINAL_DEFENSE: WorkType.LITIGATION,  # Cases
    PracticeArea.ESTATE_PLANNING: WorkType.ADVISORY,  # Matters
    PracticeArea.IMMIGRATION: WorkType.ADVISORY,  # Matters
    PracticeArea.REAL_ESTATE: WorkType.TRANSACTIONAL,  # Matters
    PracticeArea.BANKRUPTCY: WorkType.LITIGATION,  # Cases
    # Additional practice areas
    PracticeArea.CIVIL_LITIGATION: WorkType.LITIGATION,
    PracticeArea.EMPLOYMENT_LAW: WorkType.LITIGATION,
}


class CaseUrgency(str, Enum):
    """Case urgency levels with practice-specific considerations."""

    LOW = "low"  # Standard processing
    MEDIUM = "medium"  # Expedited processing
    HIGH = "high"  # Priority processing
    CRITICAL = "critical"  # Immediate attention (e.g., criminal with court date)


class PersonalInjuryCaseType(str, Enum):
    """Personal injury case types."""

    AUTO_ACCIDENT = "auto_accident"
    SLIP_AND_FALL = "slip_and_fall"
    MEDICAL_MALPRACTICE = "medical_malpractice"
    PRODUCT_LIABILITY = "product_liability"
    WORKPLACE_INJURY = "workplace_injury"
    WRONGFUL_DEATH = "wrongful_death"
    OTHER = "other"


class FamilyLawCaseType(str, Enum):
    """Family law case types."""

    DIVORCE = "divorce"
    CHILD_CUSTODY = "child_custody"
    CHILD_SUPPORT = "child_support"
    ADOPTION = "adoption"
    DOMESTIC_VIOLENCE = "domestic_violence"
    PRENUPTIAL = "prenuptial"
    PATERNITY = "paternity"
    OTHER = "other"


class CriminalDefenseCaseType(str, Enum):
    """Criminal defense case types."""

    DUI_DWI = "dui_dwi"
    MISDEMEANOR = "misdemeanor"
    FELONY = "felony"
    TRAFFIC_VIOLATION = "traffic_violation"
    WHITE_COLLAR = "white_collar"
    DOMESTIC_VIOLENCE = "domestic_violence"
    DRUG_OFFENSE = "drug_offense"
    OTHER = "other"


class ClientInformation(BaseModel):
    """Client information model."""

    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[Dict[str, str]] = None
    date_of_birth: Optional[datetime] = None
    emergency_contact: Optional[Dict[str, str]] = None
    preferred_communication: Optional[str] = "email"

    # Practice-specific fields
    marital_status: Optional[str] = None  # Family law
    employment_status: Optional[str] = None  # Personal injury
    criminal_history: Optional[bool] = None  # Criminal defense


class MatterInformation(BaseModel):
    """Matter information model with practice-specific fields."""

    practice_area: Optional[PracticeArea] = None
    work_type: Optional[WorkType] = None
    case_type: Optional[
        Union[PersonalInjuryCaseType, FamilyLawCaseType, CriminalDefenseCaseType]
    ] = None
    title: Optional[str] = None
    description: Optional[str] = None
    urgency: CaseUrgency = CaseUrgency.LOW

    # Common case fields
    incident_date: Optional[datetime] = None
    statute_of_limitations: Optional[datetime] = None

    # Personal injury specific
    injuries: Optional[List[str]] = None
    medical_treatment: Optional[bool] = None
    insurance_involved: Optional[bool] = None

    # Family law specific
    children_involved: Optional[bool] = None
    assets_involved: Optional[bool] = None
    domestic_violence_involved: Optional[bool] = None

    # Criminal defense specific
    charges: Optional[List[str]] = None
    court_date: Optional[datetime] = None
    arrest_date: Optional[datetime] = None
    bail_status: Optional[str] = None

    # Metadata
    estimated_value: Optional[float] = None
    complexity_score: Optional[int] = Field(None, ge=1, le=10)


class ConflictCheckResult(BaseModel):
    """Result of conflict checking."""

    has_conflicts: bool = False
    conflicts: List[Dict[str, Any]] = Field(default_factory=list)
    checked_at: datetime = Field(default_factory=datetime.utcnow)
    practice_area_specific_checks: Dict[str, Any] = Field(default_factory=dict)


class IntakeState(BaseLangGraphState):
    """
    Enhanced state for multi-practice intake agent.

    Extends the base LangGraph state with intake-specific fields
    and practice area support.
    """

    # Intake mode and flow control
    intake_mode: str = Field(
        default="client", description="Intake mode: client or staff"
    )
    current_step: str = Field(
        default="initial_contact", description="Current intake step"
    )
    completed_steps: List[str] = Field(
        default_factory=list, description="Completed steps"
    )

    # Client and matter information
    client: ClientInformation = Field(
        default_factory=ClientInformation, description="Client information"
    )
    matter: MatterInformation = Field(
        default_factory=MatterInformation, description="Matter information"
    )

    # Conflict checking
    conflict_check: Optional[ConflictCheckResult] = Field(
        None, description="Conflict check results"
    )

    # Practice area specific state
    practice_area_context: Dict[str, Any] = Field(
        default_factory=dict, description="Practice area specific context"
    )

    # Validation and errors
    validation_errors: List[str] = Field(
        default_factory=list, description="Validation errors"
    )
    missing_required_fields: List[str] = Field(
        default_factory=list, description="Missing required fields"
    )

    # Workflow state
    can_proceed: bool = Field(default=True, description="Whether intake can proceed")
    requires_immediate_attention: bool = Field(
        default=False, description="Requires immediate attention"
    )

    @validator("matter")
    def validate_matter_urgency(cls, v):
        """Validate matter urgency based on practice area."""
        if v.practice_area == PracticeArea.CRIMINAL_DEFENSE:
            if v.court_date and v.court_date <= datetime.utcnow().replace(
                hour=23, minute=59, second=59
            ):
                v.urgency = CaseUrgency.CRITICAL
            elif v.arrest_date and (datetime.utcnow() - v.arrest_date).days <= 1:
                v.urgency = CaseUrgency.HIGH

        # Set work_type based on practice_area
        if v.practice_area and not v.work_type:
            v.work_type = PRACTICE_AREA_TO_WORK_TYPE.get(
                v.practice_area, WorkType.LITIGATION
            )

        return v

    def get_required_fields_for_practice_area(self) -> List[str]:
        """Get required fields based on practice area."""
        base_fields = [
            "client.name",
            "client.email",
            "client.phone",
            "matter.description",
        ]

        if self.matter.practice_area == PracticeArea.PERSONAL_INJURY:
            return base_fields + ["matter.incident_date", "matter.injuries"]
        elif self.matter.practice_area == PracticeArea.FAMILY_LAW:
            return base_fields + ["matter.children_involved", "matter.assets_involved"]
        elif self.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE:
            return base_fields + ["matter.charges", "matter.arrest_date"]

        return base_fields

    def is_complete_for_practice_area(self) -> bool:
        """Check if intake is complete for the specific practice area."""
        required_fields = self.get_required_fields_for_practice_area()

        for field_path in required_fields:
            if not self._get_nested_field(field_path):
                return False

        return True

    def _get_nested_field(self, field_path: str) -> Any:
        """Get nested field value using dot notation."""
        parts = field_path.split(".")
        obj = self

        for part in parts:
            if hasattr(obj, part):
                obj = getattr(obj, part)
            else:
                return None

        return obj

    def get_display_label(self) -> str:
        """Get the display label (Case vs Matter) based on work type."""
        if self.matter.work_type == WorkType.LITIGATION:
            return "Case"
        else:
            return "Matter"

    def is_litigation_matter(self) -> bool:
        """Check if this is a litigation matter (displays as Case)."""
        return self.matter.work_type == WorkType.LITIGATION


# Compatibility aliases for tests
ClientInfo = ClientInformation
MatterInfo = MatterInformation
CaseInformation = MatterInformation  # Legacy alias
