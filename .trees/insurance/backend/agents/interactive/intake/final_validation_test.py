#!/usr/bin/env python3
"""
Final Validation Test for Enhanced LLM-Powered Intake System

This test validates the complete enhanced system with both traditional
and LLM-powered components working together.
"""

import sys
import os
import asyncio
from datetime import datetime, timedel<PERSON>

# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../"))
sys.path.insert(0, project_root)


async def test_enhanced_conflict_checker():
    """Test the enhanced conflict checker with both modes."""
    print("🔍 Testing Enhanced Conflict Checker")
    print("-" * 40)

    try:
        from backend.agents.interactive.intake.conflict_checker import (
            MultiPracticeConflictChecker,
        )
        from backend.agents.interactive.intake.state import (
            IntakeState,
            PracticeArea,
            CaseUrgency,
            ClientInformation,
            MatterInformation,
        )

        # Test traditional mode
        print("   Testing Traditional Mode...")
        checker_traditional = MultiPracticeConflictChecker(use_llm=False)
        assert checker_traditional.use_llm is False
        print("   ✅ Traditional conflict checker initialized")

        # Test LLM mode
        print("   Testing LLM Mode...")
        checker_llm = MultiPracticeConflictChecker(use_llm=True)
        print(
            f"   ✅ LLM conflict checker initialized (use_llm: {checker_llm.use_llm})"
        )

        # Create test case
        client = ClientInformation(
            name="Test Client", email="<EMAIL>", phone="555-0123"
        )

        matter = MatterInformation(
            title="Test Personal Injury Case",
            description="Motor vehicle accident with injuries and insurance involvement",
            practice_area=PracticeArea.PERSONAL_INJURY,
            urgency=CaseUrgency.HIGH,
            estimated_value=75000.0,
        )

        state = IntakeState(client=client, matter=matter)

        # Test traditional conflict checking
        print("   Testing Traditional Conflict Check...")
        result_traditional = await checker_traditional.check_conflicts(
            state, "test_tenant"
        )
        assert result_traditional is not None
        assert hasattr(result_traditional, "has_conflicts")
        print(
            f"   ✅ Traditional check complete: conflicts = {result_traditional.has_conflicts}"
        )

        # Test LLM conflict checking (will use traditional fallback if LLM not available)
        print("   Testing Enhanced Conflict Check...")
        result_llm = await checker_llm.check_conflicts(state, "test_tenant")
        assert result_llm is not None
        assert hasattr(result_llm, "has_conflicts")
        print(f"   ✅ Enhanced check complete: conflicts = {result_llm.has_conflicts}")

        return True

    except Exception as e:
        print(f"   ❌ Enhanced conflict checker test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_enhanced_risk_assessor():
    """Test the enhanced risk assessor with both modes."""
    print("\n📊 Testing Enhanced Risk Assessor")
    print("-" * 40)

    try:
        from backend.agents.interactive.intake.risk_assessment import (
            RiskAssessmentEngine,
        )
        from backend.agents.interactive.intake.state import (
            IntakeState,
            PracticeArea,
            CaseUrgency,
            ClientInformation,
            MatterInformation,
        )

        # Test traditional mode
        print("   Testing Traditional Mode...")
        assessor_traditional = RiskAssessmentEngine(use_llm=False)
        assert assessor_traditional.use_llm is False
        print("   ✅ Traditional risk assessor initialized")

        # Test LLM mode
        print("   Testing LLM Mode...")
        assessor_llm = RiskAssessmentEngine(use_llm=True)
        print(f"   ✅ LLM risk assessor initialized (use_llm: {assessor_llm.use_llm})")

        # Create test cases for different practice areas
        test_cases = [
            {
                "name": "Personal Injury",
                "client": ClientInformation(name="PI Client", email="<EMAIL>"),
                "matter": MatterInformation(
                    title="Car Accident Case",
                    description="Rear-end collision with back injuries",
                    practice_area=PracticeArea.PERSONAL_INJURY,
                    urgency=CaseUrgency.MEDIUM,
                    estimated_value=50000.0,
                ),
            },
            {
                "name": "Family Law",
                "client": ClientInformation(
                    name="Family Client", email="<EMAIL>"
                ),
                "matter": MatterInformation(
                    title="Divorce Case",
                    description="High-asset divorce with child custody",
                    practice_area=PracticeArea.FAMILY_LAW,
                    urgency=CaseUrgency.HIGH,
                    estimated_value=200000.0,
                ),
            },
            {
                "name": "Criminal Defense",
                "client": ClientInformation(
                    name="Criminal Client", email="<EMAIL>"
                ),
                "matter": MatterInformation(
                    title="DUI Case",
                    description="First-time DUI with no injuries",
                    practice_area=PracticeArea.CRIMINAL_DEFENSE,
                    urgency=CaseUrgency.CRITICAL,
                    court_date=datetime.now() + timedelta(days=3),
                ),
            },
        ]

        for test_case in test_cases:
            print(f"   Testing {test_case['name']} Case...")

            state = IntakeState(client=test_case["client"], matter=test_case["matter"])

            # Test traditional assessment
            result_traditional = await assessor_traditional.assess_risk(state)
            assert result_traditional is not None
            assert hasattr(result_traditional, "overall_risk_score")
            assert 0.0 <= result_traditional.overall_risk_score <= 1.0

            # Test LLM assessment (will use traditional fallback if LLM not available)
            result_llm = await assessor_llm.assess_risk(state)
            assert result_llm is not None
            assert hasattr(result_llm, "overall_risk_score")
            assert 0.0 <= result_llm.overall_risk_score <= 1.0

            print(
                f"     ✅ {test_case['name']}: Traditional={result_traditional.overall_risk_level.value}, Enhanced={result_llm.overall_risk_level.value}"
            )

        return True

    except Exception as e:
        print(f"   ❌ Enhanced risk assessor test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_entity_matching():
    """Test the enhanced entity matching functionality."""
    print("\n🔤 Testing Entity Matching")
    print("-" * 40)

    try:
        from backend.agents.interactive.intake.conflict_checker import EntityMatcher

        matcher = EntityMatcher(similarity_threshold=0.8)

        # Test exact matches
        exact_score = matcher.calculate_similarity("John Smith", "John Smith")
        assert exact_score == 1.0
        print(f"   ✅ Exact match: {exact_score}")

        # Test similar names
        similar_score = matcher.calculate_similarity("John Smith", "Jon Smith")
        assert 0.8 <= similar_score < 1.0
        print(f"   ✅ Similar names: {similar_score:.2f}")

        # Test different names
        different_score = matcher.calculate_similarity("John Smith", "Mary Johnson")
        assert different_score < 0.5
        print(f"   ✅ Different names: {different_score:.2f}")

        # Test find similar entities
        candidates = [
            "John Smith",
            "Jon Smith",
            "Johnny Smith",
            "Mary Johnson",
            "John Doe",
        ]
        matches = matcher.find_similar_entities("John Smith", candidates)

        assert len(matches) >= 2  # Should find at least John Smith and Jon Smith
        assert matches[0][1] == 1.0  # First match should be exact
        print(f"   ✅ Found {len(matches)} similar entities")

        return True

    except Exception as e:
        print(f"   ❌ Entity matching test failed: {e}")
        return False


def test_enum_consistency():
    """Test that all enums are consistent across modules."""
    print("\n🏷️  Testing Enum Consistency")
    print("-" * 40)

    try:
        from backend.agents.interactive.intake.conflict_types import (
            ConflictSeverity,
            ConflictType,
        )
        from backend.agents.interactive.intake.risk_assessment import (
            RiskLevel,
            RiskCategory,
        )
        from backend.agents.interactive.intake.state import PracticeArea, CaseUrgency

        # Test ConflictSeverity
        severities = [
            ConflictSeverity.LOW,
            ConflictSeverity.MEDIUM,
            ConflictSeverity.HIGH,
            ConflictSeverity.CRITICAL,
        ]
        assert len(severities) == 4
        print("   ✅ ConflictSeverity enum validated")

        # Test ConflictType
        types = [
            ConflictType.DIRECT_CLIENT,
            ConflictType.OPPOSING_PARTY,
            ConflictType.ETHICAL,
        ]
        assert len(types) == 3
        print("   ✅ ConflictType enum validated")

        # Test RiskLevel
        levels = [
            RiskLevel.VERY_LOW,
            RiskLevel.LOW,
            RiskLevel.MEDIUM,
            RiskLevel.HIGH,
            RiskLevel.VERY_HIGH,
        ]
        assert len(levels) == 5
        print("   ✅ RiskLevel enum validated")

        # Test RiskCategory
        categories = [
            RiskCategory.COMPLEXITY,
            RiskCategory.URGENCY,
            RiskCategory.CONFLICT,
        ]
        assert len(categories) == 3
        print("   ✅ RiskCategory enum validated")

        # Test PracticeArea
        areas = [
            PracticeArea.PERSONAL_INJURY,
            PracticeArea.FAMILY_LAW,
            PracticeArea.CRIMINAL_DEFENSE,
        ]
        assert len(areas) == 3
        print("   ✅ PracticeArea enum validated")

        # Test CaseUrgency
        urgencies = [
            CaseUrgency.LOW,
            CaseUrgency.MEDIUM,
            CaseUrgency.HIGH,
            CaseUrgency.CRITICAL,
        ]
        assert len(urgencies) == 4
        print("   ✅ CaseUrgency enum validated")

        return True

    except Exception as e:
        print(f"   ❌ Enum consistency test failed: {e}")
        return False


async def main():
    """Run all validation tests."""
    print("🧪 Final Validation Test Suite")
    print("=" * 60)
    print("Testing Enhanced LLM-Powered Intake System")
    print("=" * 60)

    tests = [
        test_enhanced_conflict_checker,
        test_enhanced_risk_assessor,
        test_entity_matching,
        test_enum_consistency,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()

            if result:
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")

        print()

    print("📋 Final Validation Summary")
    print("=" * 40)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Tests Failed: {total - passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("\n🎉 ALL VALIDATION TESTS PASSED!")
        print("✨ Enhanced LLM-Powered Intake System is fully validated and ready!")
        print("\n🚀 System Features Validated:")
        print("   ✅ Enhanced Conflict Detection with LLM Integration")
        print("   ✅ Advanced Risk Assessment with AI Intelligence")
        print("   ✅ Fuzzy Entity Matching and Similarity Scoring")
        print("   ✅ Practice Area Specialization")
        print("   ✅ Graceful Fallback to Traditional Algorithms")
        print("   ✅ Comprehensive Error Handling")
        print("   ✅ Type Safety and Enum Consistency")
        return True
    else:
        print("\n⚠️  Some validation tests failed")
        print("Please review the failed tests before deployment")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
