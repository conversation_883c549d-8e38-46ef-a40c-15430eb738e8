"""
LLM-Powered Success Prediction Model

This module provides sophisticated success prediction using Large Language Models
to analyze case strength, liability, damages, and provide probability assessments
with detailed legal reasoning and confidence intervals.

Key Features:
- Case strength analysis with legal precedent consideration
- Liability assessment with fault allocation
- Damages evaluation and recovery probability
- Settlement prediction with value ranges
- Trial outcome modeling with scenario analysis
- Comparative case analysis using legal databases
- Success probability with confidence intervals
- Strategic recommendations for case positioning
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import PracticeArea, IntakeState
from .llm_entity_extractor import EntityExtractionResult
from .llm_conflict_analyzer import ConflictAnalysis

logger = logging.getLogger(__name__)


class CaseStrength(str, Enum):
    """Case strength levels."""

    VERY_STRONG = "very_strong"
    STRONG = "strong"
    MODERATE = "moderate"
    WEAK = "weak"
    VERY_WEAK = "very_weak"


class LiabilityAssessment(str, Enum):
    """Liability assessment levels."""

    CLEAR_LIABILITY = "clear_liability"
    PROBABLE_LIABILITY = "probable_liability"
    DISPUTED_LIABILITY = "disputed_liability"
    WEAK_LIABILITY = "weak_liability"
    NO_LIABILITY = "no_liability"


class DamagesStrength(str, Enum):
    """Damages strength assessment."""

    SUBSTANTIAL_PROVABLE = "substantial_provable"
    MODERATE_PROVABLE = "moderate_provable"
    LIMITED_PROVABLE = "limited_provable"
    SPECULATIVE = "speculative"
    MINIMAL = "minimal"


@dataclass
class SuccessPrediction:
    """Comprehensive success prediction result."""

    # Overall prediction
    success_probability: float  # 0.0 to 1.0
    confidence_interval: Tuple[float, float]  # Lower and upper bounds
    case_strength: CaseStrength
    prediction_confidence: float  # 0.0 to 1.0

    # Detailed analysis
    liability_analysis: Dict[str, Any]
    damages_analysis: Dict[str, Any]
    procedural_analysis: Dict[str, Any]
    strategic_analysis: Dict[str, Any]

    # Outcome scenarios
    best_case_scenario: Dict[str, Any]
    most_likely_scenario: Dict[str, Any]
    worst_case_scenario: Dict[str, Any]

    # Settlement analysis
    settlement_probability: float
    settlement_value_range: Tuple[float, float]
    settlement_timing: str

    # Trial analysis
    trial_probability: float
    trial_success_probability: float
    trial_value_range: Tuple[float, float]

    # Comparative analysis
    similar_cases: List[Dict[str, Any]]
    precedent_impact: List[str]
    jurisdictional_factors: List[str]

    # Strategic recommendations
    strengths: List[str]
    weaknesses: List[str]
    opportunities: List[str]
    threats: List[str]

    # Legal reasoning
    legal_reasoning: str
    key_legal_issues: List[str]
    evidence_requirements: List[str]

    # Practice area insights
    practice_area_factors: Dict[str, Any]


class LLMSuccessPredictionOutput(BaseModel):
    """Structured output for LLM success prediction."""

    overall_prediction: Dict[str, Any] = Field(
        description="Overall success prediction and confidence"
    )
    liability_analysis: Dict[str, Any] = Field(
        description="Detailed liability analysis"
    )
    damages_analysis: Dict[str, Any] = Field(
        description="Damages assessment and recovery analysis"
    )
    procedural_analysis: Dict[str, Any] = Field(
        description="Procedural considerations and challenges"
    )
    strategic_analysis: Dict[str, Any] = Field(
        description="Strategic positioning and recommendations"
    )
    outcome_scenarios: Dict[str, Any] = Field(
        description="Best case, most likely, and worst case scenarios"
    )
    settlement_analysis: Dict[str, Any] = Field(
        description="Settlement probability and value analysis"
    )
    trial_analysis: Dict[str, Any] = Field(
        description="Trial outcome prediction and analysis"
    )
    comparative_analysis: Dict[str, Any] = Field(
        description="Similar cases and precedent analysis"
    )
    swot_analysis: Dict[str, Any] = Field(
        description="Strengths, weaknesses, opportunities, threats"
    )
    legal_issues: List[str] = Field(description="Key legal issues and challenges")
    evidence_requirements: List[str] = Field(
        description="Critical evidence needed for success"
    )
    practice_area_insights: Dict[str, Any] = Field(
        description="Practice area specific insights"
    )
    jurisdictional_factors: List[str] = Field(
        description="Jurisdiction-specific considerations"
    )
    confidence_metrics: Dict[str, float] = Field(
        description="Confidence levels for various predictions"
    )
    legal_reasoning: str = Field(
        description="Comprehensive legal reasoning for predictions"
    )


class LLMSuccessPredictor:
    """
    Advanced LLM-powered success prediction for legal matters.

    Uses sophisticated language models to analyze case facts, legal precedents,
    and strategic factors to predict case outcomes with detailed reasoning.
    """

    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.1):
        """Initialize the LLM success predictor."""
        self.llm = ChatOpenAI(model=llm_model, temperature=temperature)
        self.output_parser = PydanticOutputParser(
            pydantic_object=LLMSuccessPredictionOutput
        )

        self.prediction_prompt = ChatPromptTemplate.from_messages(
            [("system", self._get_system_prompt()), ("human", self._get_human_prompt())]
        )

    async def predict_success(
        self,
        intake_state: IntakeState,
        entity_extraction: EntityExtractionResult,
        conflict_analyses: List[ConflictAnalysis] = None,
        additional_context: Dict[str, Any] = None,
    ) -> SuccessPrediction:
        """
        Predict case success using comprehensive LLM analysis.

        Args:
            intake_state: Current intake state
            entity_extraction: Results from entity extraction
            conflict_analyses: Results from conflict analysis
            additional_context: Additional context information

        Returns:
            SuccessPrediction with detailed analysis
        """
        try:
            # Prepare comprehensive context
            context = self._prepare_prediction_context(
                intake_state, entity_extraction, conflict_analyses, additional_context
            )

            # Create the prompt
            prompt = self.prediction_prompt.format_prompt(
                case_description=intake_state.matter.description or "",
                practice_area=(
                    intake_state.matter.practice_area.value
                    if intake_state.matter.practice_area
                    else "unknown"
                ),
                matter_details=self._format_matter_details(intake_state.matter),
                client_information=self._format_client_info(intake_state.client),
                extracted_entities=self._format_entities_for_prediction(
                    entity_extraction
                ),
                timeline_events=self._format_timeline_for_prediction(
                    entity_extraction.timeline
                ),
                conflict_summary=self._format_conflicts_for_prediction(
                    conflict_analyses or []
                ),
                additional_context=context,
                format_instructions=self.output_parser.get_format_instructions(),
            )

            # Get LLM response
            response = await self.llm.ainvoke(prompt.to_messages())

            # Parse structured output
            parsed_result = self.output_parser.parse(response.content)

            # Convert to our internal format
            prediction = self._convert_llm_output(parsed_result, intake_state)

            logger.info(
                f"Success prediction complete: {prediction.success_probability:.1%} success probability"
            )

            return prediction

        except Exception as e:
            logger.error(f"Error during LLM success prediction: {str(e)}")
            return self._error_success_prediction(str(e))

    def _get_system_prompt(self) -> str:
        """Get the system prompt for success prediction."""
        return """You are an expert legal analyst specializing in case outcome prediction and strategic assessment. Your task is to analyze legal matters and predict success probability with the highest level of accuracy and practical utility.

SUCCESS PREDICTION FRAMEWORK:

1. LIABILITY ANALYSIS:
   - Legal theory strength: Validity of claims, statutory requirements, precedent support
   - Factual support: Evidence quality, witness credibility, documentation
   - Causation analysis: Proximate cause, but-for causation, intervening factors
   - Defenses evaluation: Available defenses, strength, likelihood of success
   - Comparative fault: Contributory negligence, comparative negligence analysis

2. DAMAGES ASSESSMENT:
   - Economic damages: Lost wages, medical expenses, property damage, future costs
   - Non-economic damages: Pain and suffering, emotional distress, loss of consortium
   - Punitive damages: Availability, standards, likelihood of award
   - Provability: Documentation, expert testimony, calculation methods
   - Collection potential: Defendant assets, insurance coverage, judgment enforcement

3. PROCEDURAL CONSIDERATIONS:
   - Statute of limitations: Compliance, discovery rule, tolling provisions
   - Jurisdiction: Proper venue, personal jurisdiction, subject matter jurisdiction
   - Standing: Proper parties, capacity to sue, real party in interest
   - Pleading requirements: Notice pleading, fact pleading, specificity requirements
   - Discovery challenges: Scope, privilege, work product, expert discovery

4. STRATEGIC FACTORS:
   - Judge characteristics: Tendencies, experience, judicial philosophy
   - Jury considerations: Venue demographics, case presentation, sympathy factors
   - Opposing counsel: Experience, reputation, resources, strategy
   - Client factors: Credibility, cooperation, resources, risk tolerance
   - Timeline: Case duration, scheduling, delay tactics, urgency factors

5. PRACTICE AREA SPECIFIC ANALYSIS:
   - Personal Injury: Medical causation, insurance coverage, comparative fault
   - Family Law: Best interests standard, judicial discretion, emotional factors
   - Criminal Defense: Constitutional issues, prosecutorial discretion, plea bargaining
   - Business Law: Commercial reasonableness, industry standards, economic analysis

6. OUTCOME SCENARIOS:
   - Best case: Optimal outcome with favorable rulings and maximum recovery
   - Most likely: Realistic outcome based on typical case progression
   - Worst case: Unfavorable outcome with adverse rulings and minimal recovery
   - Settlement scenarios: Timing, value ranges, negotiation dynamics

7. COMPARATIVE ANALYSIS:
   - Similar cases: Comparable facts, outcomes, jurisdictions
   - Precedent impact: Binding authority, persuasive authority, trend analysis
   - Statistical analysis: Success rates, average awards, settlement patterns
   - Market factors: Economic conditions, legal trends, policy changes

PREDICTION CRITERIA:
- Provide specific probability percentages with confidence intervals
- Consider both legal and practical factors
- Evaluate short-term and long-term implications
- Balance optimism with realistic expectations
- Consider alternative dispute resolution options
- Assess risk-adjusted expected values
- Maintain conservative approach to outcome prediction"""

    def _get_human_prompt(self) -> str:
        """Get the human prompt template."""
        return """Please perform comprehensive success prediction analysis for this legal matter:

CASE DESCRIPTION:
{case_description}

PRACTICE AREA:
{practice_area}

MATTER DETAILS:
{matter_details}

CLIENT INFORMATION:
{client_information}

EXTRACTED ENTITIES:
{extracted_entities}

TIMELINE EVENTS:
{timeline_events}

CONFLICT SUMMARY:
{conflict_summary}

ADDITIONAL CONTEXT:
{additional_context}

Perform thorough success prediction analysis following these steps:

1. LIABILITY ANALYSIS: Assess strength of legal claims and potential defenses
2. DAMAGES ASSESSMENT: Evaluate provable damages and recovery potential
3. PROCEDURAL ANALYSIS: Consider procedural challenges and requirements
4. STRATEGIC ANALYSIS: Evaluate strategic positioning and case management
5. SCENARIO MODELING: Develop best case, most likely, and worst case scenarios
6. SETTLEMENT ANALYSIS: Assess settlement probability and value ranges
7. TRIAL ANALYSIS: Predict trial outcomes and success probability
8. COMPARATIVE ANALYSIS: Consider similar cases and precedent impact
9. SWOT ANALYSIS: Identify strengths, weaknesses, opportunities, threats
10. EVIDENCE REQUIREMENTS: Identify critical evidence needed for success
11. PRACTICE AREA INSIGHTS: Apply practice area specific analysis
12. JURISDICTIONAL FACTORS: Consider jurisdiction-specific considerations

Provide your analysis in the following structured format:
{format_instructions}

Focus on accuracy, practical utility, and actionable strategic insights."""

    def _prepare_prediction_context(
        self,
        intake_state: IntakeState,
        entity_extraction: EntityExtractionResult,
        conflict_analyses: List[ConflictAnalysis],
        additional_context: Dict[str, Any],
    ) -> str:
        """Prepare comprehensive context for success prediction."""
        context_parts = []

        # Add temporal urgency factors
        if intake_state.matter.statute_of_limitations:
            days_until_sol = (
                intake_state.matter.statute_of_limitations - datetime.now()
            ).days
            context_parts.append(
                f"Statute of Limitations: {days_until_sol} days remaining"
            )

        if intake_state.matter.court_date:
            days_until_court = (intake_state.matter.court_date - datetime.now()).days
            context_parts.append(f"Court Date: {days_until_court} days away")

        # Add complexity and urgency indicators
        if entity_extraction.complexity_indicators:
            context_parts.append(
                f"Complexity Factors: {', '.join(entity_extraction.complexity_indicators)}"
            )

        if entity_extraction.urgency_indicators:
            context_parts.append(
                f"Urgency Factors: {', '.join(entity_extraction.urgency_indicators)}"
            )

        # Add practice area indicators
        if entity_extraction.practice_area_indicators:
            indicators = [
                f"{area.value}: {score:.2f}"
                for area, score in entity_extraction.practice_area_indicators.items()
            ]
            context_parts.append(f"Practice Area Indicators: {', '.join(indicators)}")

        # Add conflict impact
        if conflict_analyses:
            high_severity = [
                c for c in conflict_analyses if c.severity.value in ["high", "critical"]
            ]
            if high_severity:
                context_parts.append(
                    f"High Severity Conflicts: {len(high_severity)} may impact case strategy"
                )

        # Add additional context
        if additional_context:
            for key, value in additional_context.items():
                if value:
                    context_parts.append(f"{key.replace('_', ' ').title()}: {value}")

        return (
            "\n".join(context_parts)
            if context_parts
            else "No additional context available."
        )

    def _format_matter_details(self, matter) -> str:
        """Format matter details for prediction."""
        details = []

        if matter.case_type:
            details.append(f"Case Type: {matter.case_type}")
        if matter.estimated_value:
            details.append(f"Estimated Value: ${matter.estimated_value:,.2f}")
        if matter.incident_date:
            details.append(f"Incident Date: {matter.incident_date}")

        # Practice area specific details
        if matter.injuries:
            details.append(f"Injuries: {', '.join(matter.injuries)}")
        if matter.charges:
            details.append(f"Criminal Charges: {', '.join(matter.charges)}")
        if matter.medical_treatment is not None:
            details.append(
                f"Medical Treatment Sought: {'Yes' if matter.medical_treatment else 'No'}"
            )
        if matter.insurance_involved is not None:
            details.append(
                f"Insurance Coverage: {'Available' if matter.insurance_involved else 'Unknown/None'}"
            )

        return "\n".join(details) if details else "Limited matter details available."

    def _format_client_info(self, client) -> str:
        """Format client information for prediction."""
        info_parts = []

        if client.name:
            info_parts.append(f"Client: {client.name}")
        if client.referral_source:
            info_parts.append(f"Referral Source: {client.referral_source}")

        return (
            "\n".join(info_parts)
            if info_parts
            else "Limited client information available."
        )

    def _format_entities_for_prediction(
        self, entity_extraction: EntityExtractionResult
    ) -> str:
        """Format entities for success prediction analysis."""
        if not entity_extraction.entities:
            return "No entities extracted."

        entity_summary = []

        # Key parties
        if entity_extraction.key_parties:
            entity_summary.append(
                f"Key Parties: {', '.join(entity_extraction.key_parties)}"
            )

        # Categorize entities by role
        role_groups = {}
        for entity in entity_extraction.entities:
            role = entity.role.value
            if role not in role_groups:
                role_groups[role] = []
            role_groups[role].append(entity.name)

        for role, names in role_groups.items():
            if names:
                entity_summary.append(
                    f"{role.replace('_', ' ').title()}: {', '.join(names[:3])}"
                )  # Limit to 3 per role

        return "\n".join(entity_summary)

    def _format_timeline_for_prediction(
        self, timeline: List[Tuple[datetime, str, str]]
    ) -> str:
        """Format timeline for success prediction."""
        if not timeline:
            return "No timeline events identified."

        # Sort by date and format
        sorted_timeline = sorted(timeline, key=lambda x: x[0])
        timeline_parts = []

        for date, event, description in sorted_timeline[:5]:  # Show first 5 events
            timeline_parts.append(f"- {date.strftime('%Y-%m-%d')}: {event}")

        if len(timeline) > 5:
            timeline_parts.append(f"... and {len(timeline) - 5} more events")

        return "\n".join(timeline_parts)

    def _format_conflicts_for_prediction(
        self, conflict_analyses: List[ConflictAnalysis]
    ) -> str:
        """Format conflicts for success prediction."""
        if not conflict_analyses:
            return "No conflicts identified that would impact case success."

        conflict_summary = []

        # High severity conflicts that might impact strategy
        high_severity = [
            c for c in conflict_analyses if c.severity.value in ["high", "critical"]
        ]
        if high_severity:
            conflict_summary.append(
                f"High Severity Conflicts: {len(high_severity)} may require strategic adjustments"
            )

        # Waivable conflicts
        waivable = [c for c in conflict_analyses if c.waivable]
        if waivable:
            conflict_summary.append(
                f"Waivable Conflicts: {len(waivable)} with proper client consent"
            )

        return (
            "\n".join(conflict_summary)
            if conflict_summary
            else "Minor conflicts identified, unlikely to impact case success."
        )

    def _convert_llm_output(
        self, llm_output: LLMSuccessPredictionOutput, intake_state: IntakeState
    ) -> SuccessPrediction:
        """Convert LLM output to SuccessPrediction object."""

        overall_pred = llm_output.overall_prediction

        # Extract confidence interval
        confidence_interval = (0.0, 1.0)
        if "confidence_interval" in overall_pred:
            ci_data = overall_pred["confidence_interval"]
            if isinstance(ci_data, dict):
                confidence_interval = (
                    ci_data.get("lower", 0.0),
                    ci_data.get("upper", 1.0),
                )
            elif isinstance(ci_data, list) and len(ci_data) == 2:
                confidence_interval = tuple(ci_data)

        # Extract settlement value range
        settlement_range = (0.0, 0.0)
        if "value_range" in llm_output.settlement_analysis:
            range_data = llm_output.settlement_analysis["value_range"]
            if isinstance(range_data, dict):
                settlement_range = (
                    range_data.get("min", 0.0),
                    range_data.get("max", 0.0),
                )
            elif isinstance(range_data, list) and len(range_data) == 2:
                settlement_range = tuple(range_data)

        # Extract trial value range
        trial_range = (0.0, 0.0)
        if "value_range" in llm_output.trial_analysis:
            range_data = llm_output.trial_analysis["value_range"]
            if isinstance(range_data, dict):
                trial_range = (range_data.get("min", 0.0), range_data.get("max", 0.0))
            elif isinstance(range_data, list) and len(range_data) == 2:
                trial_range = tuple(range_data)

        # Extract case strength
        case_strength = CaseStrength.MODERATE
        if "case_strength" in overall_pred:
            try:
                case_strength = CaseStrength(overall_pred["case_strength"])
            except ValueError:
                pass

        # Extract SWOT analysis
        swot = llm_output.swot_analysis

        return SuccessPrediction(
            success_probability=overall_pred.get("success_probability", 0.5),
            confidence_interval=confidence_interval,
            case_strength=case_strength,
            prediction_confidence=llm_output.confidence_metrics.get("overall", 0.5),
            liability_analysis=llm_output.liability_analysis,
            damages_analysis=llm_output.damages_analysis,
            procedural_analysis=llm_output.procedural_analysis,
            strategic_analysis=llm_output.strategic_analysis,
            best_case_scenario=llm_output.outcome_scenarios.get("best_case", {}),
            most_likely_scenario=llm_output.outcome_scenarios.get("most_likely", {}),
            worst_case_scenario=llm_output.outcome_scenarios.get("worst_case", {}),
            settlement_probability=llm_output.settlement_analysis.get(
                "probability", 0.5
            ),
            settlement_value_range=settlement_range,
            settlement_timing=llm_output.settlement_analysis.get("timing", "Unknown"),
            trial_probability=llm_output.trial_analysis.get("probability", 0.5),
            trial_success_probability=llm_output.trial_analysis.get(
                "success_probability", 0.5
            ),
            trial_value_range=trial_range,
            similar_cases=llm_output.comparative_analysis.get("similar_cases", []),
            precedent_impact=llm_output.comparative_analysis.get(
                "precedent_impact", []
            ),
            jurisdictional_factors=llm_output.jurisdictional_factors,
            strengths=swot.get("strengths", []),
            weaknesses=swot.get("weaknesses", []),
            opportunities=swot.get("opportunities", []),
            threats=swot.get("threats", []),
            legal_reasoning=llm_output.legal_reasoning,
            key_legal_issues=llm_output.legal_issues,
            evidence_requirements=llm_output.evidence_requirements,
            practice_area_factors=llm_output.practice_area_insights,
        )

    def _error_success_prediction(self, error_message: str) -> SuccessPrediction:
        """Return error success prediction when LLM fails."""
        return SuccessPrediction(
            success_probability=0.0,
            confidence_interval=(0.0, 0.0),
            case_strength=CaseStrength.VERY_WEAK,
            prediction_confidence=0.0,
            liability_analysis={"error": "Analysis failed"},
            damages_analysis={"error": "Analysis failed"},
            procedural_analysis={"error": "Analysis failed"},
            strategic_analysis={"error": "Analysis failed"},
            best_case_scenario={"outcome": "Unknown due to system error"},
            most_likely_scenario={"outcome": "Unknown due to system error"},
            worst_case_scenario={"outcome": "System error occurred"},
            settlement_probability=0.0,
            settlement_value_range=(0.0, 0.0),
            settlement_timing="Unknown",
            trial_probability=0.0,
            trial_success_probability=0.0,
            trial_value_range=(0.0, 0.0),
            similar_cases=[],
            precedent_impact=[],
            jurisdictional_factors=["System error"],
            strengths=[],
            weaknesses=["System analysis failure"],
            opportunities=[],
            threats=["Unable to assess case risks"],
            legal_reasoning=f"Success prediction failed: {error_message}",
            key_legal_issues=["System error - manual analysis required"],
            evidence_requirements=["Manual case review needed"],
            practice_area_factors={"error": "Analysis failed"},
        )
