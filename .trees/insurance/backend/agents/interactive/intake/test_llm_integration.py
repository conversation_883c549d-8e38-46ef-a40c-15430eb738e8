"""
Test LLM Integration for Enhanced Intake System

This test demonstrates the comprehensive LLM-powered intake system with:
- Advanced entity extraction using GPT-4
- Sophisticated conflict analysis with legal reasoning
- Comprehensive risk assessment with outcome prediction
- Success probability modeling with detailed analysis
- Integrated legal intelligence throughout the process
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any

from .state import IntakeState, PracticeArea, CaseUrgency, ClientInfo, MatterInfo
from .llm_entity_extractor import LLMEntityExtractor
from .llm_conflict_analyzer import LLMConflictAnalyzer
from .llm_risk_intelligence import LLMRiskIntelligence
from .llm_success_predictor import LLMSuccessPredictor
from .conflict_checker import MultiPracticeConflictChecker
from .risk_assessment import RiskAssessmentEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LLMIntakeSystemDemo:
    """Demonstration of the comprehensive LLM-powered intake system."""

    def __init__(self):
        """Initialize the LLM intake system components."""
        self.entity_extractor = LLMEntityExtractor()
        self.conflict_analyzer = LLMConflictAnalyzer()
        self.risk_intelligence = LLMRiskIntelligence()
        self.success_predictor = LLMSuccessPredictor()
        self.conflict_checker = MultiPracticeConflictChecker(use_llm=True)
        self.risk_assessor = RiskAssessmentEngine(use_llm=True)

    async def run_comprehensive_demo(self):
        """Run comprehensive demonstration of LLM-powered intake."""
        print("🚀 Starting Comprehensive LLM-Powered Legal Intake Demo")
        print("=" * 60)

        # Test cases for different practice areas
        test_cases = [
            self._create_personal_injury_case(),
            self._create_family_law_case(),
            self._create_criminal_defense_case(),
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}: {test_case['title']}")
            print("-" * 40)

            await self._analyze_case(test_case)

    def _create_personal_injury_case(self) -> Dict[str, Any]:
        """Create a complex personal injury test case."""
        return {
            "title": "Complex Personal Injury Case",
            "client": ClientInfo(
                name="Sarah Johnson",
                email="<EMAIL>",
                phone="555-0123",
                address="123 Main St, Austin, TX 78701",
            ),
            "matter": MatterInfo(
                title="Motor Vehicle Accident with Multiple Injuries",
                description="""
                I was involved in a serious car accident on December 15, 2023, at the intersection of 6th Street and Congress Avenue in Austin, Texas. The other driver, Michael Rodriguez, ran a red light and hit my vehicle at approximately 45 mph. 
                
                I sustained multiple injuries including a herniated disc in my lower back, a concussion, and a fractured wrist. I've been receiving treatment from Dr. Emily Chen at Austin Orthopedic Center and Dr. James Wilson at Texas Neurology Associates. My medical bills have already exceeded $25,000.
                
                The other driver was insured by State Farm Insurance, and their adjuster, Jennifer Martinez, initially offered $15,000 to settle, which seems inadequate given my ongoing medical needs and lost wages. I work as a software engineer at Dell Technologies and have missed 6 weeks of work.
                
                There were two witnesses: Maria Gonzalez (a pedestrian) and David Kim (driver in adjacent lane). The police report indicates the other driver was cited for running a red light. I'm concerned about my long-term recovery and ability to return to work full-time.
                """,
                practice_area=PracticeArea.PERSONAL_INJURY,
                case_type="motor_vehicle_accident",
                urgency=CaseUrgency.HIGH,
                estimated_value=150000.0,
                incident_date=datetime(2023, 12, 15),
                statute_of_limitations=datetime(2025, 12, 15),
                injuries=["herniated disc", "concussion", "fractured wrist"],
                medical_treatment=True,
                insurance_involved=True,
            ),
        }

    def _create_family_law_case(self) -> Dict[str, Any]:
        """Create a complex family law test case."""
        return {
            "title": "High-Asset Divorce with Child Custody",
            "client": ClientInfo(
                name="Robert Thompson",
                email="<EMAIL>",
                phone="555-0456",
            ),
            "matter": MatterInfo(
                title="Divorce and Child Custody Matter",
                description="""
                I need to file for divorce from my wife, Lisa Thompson, after 12 years of marriage. We have two children: Emma (age 10) and Jake (age 7). Lisa and I have been separated for 6 months, and she has been living with the children at our family home in Westlake.
                
                Our assets include our primary residence valued at $850,000, a vacation home in Colorado worth $400,000, my 401k with approximately $300,000, Lisa's teacher retirement account with $150,000, and my business, Thompson Consulting LLC, which I started 5 years ago and is now worth approximately $500,000.
                
                Lisa is represented by Attorney Patricia Williams from Williams & Associates. I'm concerned about child custody arrangements as Lisa wants to move to Colorado with the children to be closer to her parents. I want joint custody and believe the children should remain in Austin where they have established schools and friends.
                
                There have been no incidents of domestic violence, but Lisa has accused me of being absent due to work travel. I travel approximately 2-3 days per week for my consulting business. We've tried marriage counseling with Dr. Susan Miller, but it was unsuccessful.
                """,
                practice_area=PracticeArea.FAMILY_LAW,
                case_type="divorce",
                urgency=CaseUrgency.MEDIUM,
                estimated_value=2200000.0,
                children_involved=True,
                assets_involved=True,
                domestic_violence_involved=False,
            ),
        }

    def _create_criminal_defense_case(self) -> Dict[str, Any]:
        """Create a complex criminal defense test case."""
        return {
            "title": "Federal White Collar Criminal Defense",
            "client": ClientInfo(
                name="Amanda Foster", email="<EMAIL>", phone="555-0789"
            ),
            "matter": MatterInfo(
                title="Federal Wire Fraud and Money Laundering Charges",
                description="""
                I was arrested yesterday by FBI agents at my office and charged with wire fraud and money laundering in connection with my role as CFO at TechStart Innovations. The charges relate to alleged financial irregularities in our Series B funding round that closed 18 months ago.
                
                The government alleges that I manipulated financial statements to inflate company valuation and diverted $2.3 million in investor funds to personal accounts. I strongly deny these allegations. The investigation appears to be led by Assistant U.S. Attorney Michael Chen from the Eastern District of Texas.
                
                My co-defendants include CEO Jonathan Blake and VP of Finance Rachel Martinez. We were all arrested simultaneously. The company's outside auditor, PricewaterhouseCoopers, has been cooperating with the investigation for the past 8 months.
                
                I have an initial appearance scheduled for tomorrow at 2:00 PM before Judge Patricia Rodriguez in the federal courthouse. I was released on a $500,000 bond with travel restrictions. The prosecutor mentioned potential additional charges and indicated they have extensive email and financial records.
                
                I'm extremely concerned about the impact on my family and career. I have two young children and my husband is a teacher. We cannot afford a lengthy legal battle, but I am innocent of these charges.
                """,
                practice_area=PracticeArea.CRIMINAL_DEFENSE,
                case_type="white_collar_crime",
                urgency=CaseUrgency.CRITICAL,
                court_date=datetime.now() + timedelta(days=1),
                arrest_date=datetime.now() - timedelta(days=1),
                charges=["wire fraud", "money laundering", "conspiracy"],
            ),
        }

    async def _analyze_case(self, test_case: Dict[str, Any]):
        """Analyze a test case using the LLM-powered system."""
        try:
            # Create intake state
            intake_state = IntakeState(
                client=test_case["client"], matter=test_case["matter"]
            )

            print(f"👤 Client: {intake_state.client.name}")
            print(f"⚖️  Practice Area: {intake_state.matter.practice_area.value}")
            print(f"📝 Case: {intake_state.matter.title}")

            # Step 1: LLM Entity Extraction
            print("\n🔍 Step 1: LLM Entity Extraction")
            entity_extraction = await self.entity_extractor.extract_entities(
                intake_state.matter.description, intake_state.matter.practice_area
            )

            print(f"   • Entities found: {len(entity_extraction.entities)}")
            print(f"   • Key parties: {', '.join(entity_extraction.key_parties[:3])}")
            print(f"   • Confidence: {entity_extraction.overall_confidence:.2f}")

            # Step 2: LLM Conflict Analysis
            print("\n⚠️  Step 2: LLM Conflict Analysis")
            conflict_analyses = await self.conflict_analyzer.analyze_conflicts(
                intake_state, entity_extraction, [], [], "demo_tenant"
            )

            conflicts_found = len([c for c in conflict_analyses if c.conflict_exists])
            print(f"   • Conflicts analyzed: {len(conflict_analyses)}")
            print(f"   • Conflicts found: {conflicts_found}")

            # Step 3: LLM Risk Intelligence
            print("\n📊 Step 3: LLM Risk Intelligence")
            risk_assessment = await self.risk_intelligence.assess_risk(
                intake_state, entity_extraction, conflict_analyses
            )

            print(f"   • Overall risk: {risk_assessment.overall_risk_level.value}")
            print(f"   • Recommendation: {risk_assessment.recommendation}")
            print(
                f"   • Success probability: {risk_assessment.success_probability:.1%}"
            )

            # Step 4: LLM Success Prediction
            print("\n🎯 Step 4: LLM Success Prediction")
            success_prediction = await self.success_predictor.predict_success(
                intake_state, entity_extraction, conflict_analyses
            )

            print(
                f"   • Success probability: {success_prediction.success_probability:.1%}"
            )
            print(f"   • Case strength: {success_prediction.case_strength.value}")
            print(
                f"   • Settlement probability: {success_prediction.settlement_probability:.1%}"
            )

            # Step 5: Integrated Analysis Summary
            print("\n📋 Step 5: Integrated Analysis Summary")
            print(
                f"   • Entity extraction confidence: {entity_extraction.overall_confidence:.1%}"
            )
            print(f"   • Risk level: {risk_assessment.overall_risk_level.value}")
            print(
                f"   • Success prediction: {success_prediction.success_probability:.1%}"
            )
            print(f"   • Recommendation: {risk_assessment.recommendation}")

            if risk_assessment.conditions:
                print("   • Conditions:")
                for condition in risk_assessment.conditions[:2]:
                    print(f"     - {condition}")

            print("\n✅ Analysis Complete")

        except Exception as e:
            logger.error(f"Error analyzing case: {str(e)}")
            print(f"❌ Error: {str(e)}")


async def main():
    """Run the LLM integration demo."""
    demo = LLMIntakeSystemDemo()
    await demo.run_comprehensive_demo()


if __name__ == "__main__":
    asyncio.run(main())
