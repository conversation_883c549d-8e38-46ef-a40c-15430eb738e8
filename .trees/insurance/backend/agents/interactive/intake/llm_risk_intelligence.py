"""
LLM-Powered Risk Assessment Intelligence

This module provides sophisticated risk assessment using Large Language Models
to analyze case facts, predict outcomes, evaluate complexity, and provide detailed
legal reasoning for comprehensive risk evaluation.

Key Features:
- Advanced case complexity analysis with legal reasoning
- Outcome prediction based on case facts and legal precedents
- Resource requirement estimation with detailed justification
- Success probability modeling with confidence intervals
- Practice-area specific risk modeling
- Temporal risk analysis (deadlines, statute of limitations)
- Financial risk assessment and fee structure recommendations
- Strategic risk evaluation and case positioning
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import PracticeArea, IntakeState, CaseUrgency
from .llm_entity_extractor import EntityExtractionResult
from .llm_conflict_analyzer import ConflictAnalysis
from .risk_assessment import RiskLevel, RiskCategory

logger = logging.getLogger(__name__)


class OutcomePrediction(str, Enum):
    """Predicted case outcomes."""

    HIGHLY_FAVORABLE = "highly_favorable"
    FAVORABLE = "favorable"
    MIXED = "mixed"
    UNFAVORABLE = "unfavorable"
    HIGHLY_UNFAVORABLE = "highly_unfavorable"
    UNCERTAIN = "uncertain"


class CaseComplexity(str, Enum):
    """Case complexity levels with detailed analysis."""

    ROUTINE = "routine"
    MODERATE = "moderate"
    COMPLEX = "complex"
    HIGHLY_COMPLEX = "highly_complex"
    EXCEPTIONAL = "exceptional"


class ResourceRequirement(str, Enum):
    """Resource requirement levels."""

    MINIMAL = "minimal"
    STANDARD = "standard"
    SUBSTANTIAL = "substantial"
    EXTENSIVE = "extensive"
    EXCEPTIONAL = "exceptional"


@dataclass
class LLMRiskAssessment:
    """Comprehensive LLM-powered risk assessment result."""

    # Overall assessment
    overall_risk_score: float  # 0.0 to 1.0
    overall_risk_level: RiskLevel
    recommendation: str  # "accept", "accept_with_conditions", "decline", "refer"
    confidence: float  # 0.0 to 1.0

    # Detailed analysis
    complexity_analysis: Dict[str, Any]
    outcome_prediction: Dict[str, Any]
    resource_assessment: Dict[str, Any]
    financial_analysis: Dict[str, Any]
    strategic_analysis: Dict[str, Any]

    # Risk factors
    risk_factors: List[Dict[str, Any]]
    mitigation_strategies: List[str]
    success_factors: List[str]

    # Predictions
    success_probability: float
    estimated_duration: str
    estimated_cost_range: Tuple[float, float]
    resource_requirements: ResourceRequirement

    # Legal reasoning
    legal_reasoning: str
    precedent_analysis: List[str]
    strategic_considerations: List[str]

    # Practice area insights
    practice_area_factors: Dict[str, Any]
    jurisdictional_factors: List[str]

    # Recommendations
    conditions: List[str]
    next_steps: List[str]
    escalation_triggers: List[str]


class LLMRiskIntelligenceOutput(BaseModel):
    """Structured output for LLM risk intelligence analysis."""

    overall_assessment: Dict[str, Any] = Field(
        description="Overall risk assessment and recommendation"
    )
    complexity_analysis: Dict[str, Any] = Field(
        description="Detailed complexity analysis"
    )
    outcome_prediction: Dict[str, Any] = Field(
        description="Predicted outcomes and probabilities"
    )
    resource_assessment: Dict[str, Any] = Field(
        description="Resource requirement analysis"
    )
    financial_analysis: Dict[str, Any] = Field(
        description="Financial risk and opportunity analysis"
    )
    strategic_analysis: Dict[str, Any] = Field(
        description="Strategic considerations and positioning"
    )
    risk_factors: List[Dict[str, Any]] = Field(
        description="Identified risk factors with analysis"
    )
    success_factors: List[str] = Field(description="Factors that support case success")
    mitigation_strategies: List[str] = Field(
        description="Strategies to mitigate identified risks"
    )
    practice_area_insights: Dict[str, Any] = Field(
        description="Practice area specific insights"
    )
    precedent_analysis: List[str] = Field(
        description="Relevant legal precedents and their impact"
    )
    jurisdictional_factors: List[str] = Field(
        description="Jurisdiction-specific considerations"
    )
    recommendations: Dict[str, Any] = Field(
        description="Detailed recommendations and conditions"
    )
    confidence_metrics: Dict[str, float] = Field(
        description="Confidence levels for various assessments"
    )
    legal_reasoning: str = Field(
        description="Comprehensive legal reasoning for the assessment"
    )


class LLMRiskIntelligence:
    """
    Advanced LLM-powered risk intelligence for legal intake.

    Uses sophisticated language models to provide comprehensive risk assessment
    with deep legal analysis, outcome prediction, and strategic guidance.
    """

    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.1):
        """Initialize the LLM risk intelligence system."""
        self.llm = ChatOpenAI(model=llm_model, temperature=temperature)
        self.output_parser = PydanticOutputParser(
            pydantic_object=LLMRiskIntelligenceOutput
        )

        self.assessment_prompt = ChatPromptTemplate.from_messages(
            [("system", self._get_system_prompt()), ("human", self._get_human_prompt())]
        )

    async def assess_risk(
        self,
        intake_state: IntakeState,
        entity_extraction: EntityExtractionResult,
        conflict_analyses: List[ConflictAnalysis] = None,
        additional_context: Dict[str, Any] = None,
    ) -> LLMRiskAssessment:
        """
        Perform comprehensive risk assessment using LLM intelligence.

        Args:
            intake_state: Current intake state
            entity_extraction: Results from entity extraction
            conflict_analyses: Results from conflict analysis
            additional_context: Additional context information

        Returns:
            LLMRiskAssessment with comprehensive analysis
        """
        try:
            # Prepare comprehensive context
            context = self._prepare_assessment_context(
                intake_state, entity_extraction, conflict_analyses, additional_context
            )

            # Create the prompt
            prompt = self.assessment_prompt.format_prompt(
                case_description=intake_state.matter.description or "",
                practice_area=(
                    intake_state.matter.practice_area.value
                    if intake_state.matter.practice_area
                    else "unknown"
                ),
                client_information=self._format_client_info(intake_state.client),
                matter_details=self._format_matter_details(intake_state.matter),
                extracted_entities=self._format_entities_summary(entity_extraction),
                conflict_analysis=self._format_conflict_summary(
                    conflict_analyses or []
                ),
                timeline_analysis=self._format_timeline_analysis(
                    entity_extraction.timeline
                ),
                additional_context=context,
                format_instructions=self.output_parser.get_format_instructions(),
            )

            # Get LLM response
            response = await self.llm.ainvoke(prompt.to_messages())

            # Parse structured output
            parsed_result = self.output_parser.parse(response.content)

            # Convert to our internal format
            risk_assessment = self._convert_llm_output(parsed_result, intake_state)

            logger.info(
                f"Risk assessment complete: {risk_assessment.overall_risk_level.value} risk, {risk_assessment.recommendation}"
            )

            return risk_assessment

        except Exception as e:
            logger.error(f"Error during LLM risk assessment: {str(e)}")
            return self._error_risk_assessment(str(e))

    def _get_system_prompt(self) -> str:
        """Get the system prompt for risk assessment."""
        return """You are an expert legal risk analyst with extensive experience in case evaluation, outcome prediction, and strategic legal planning. Your task is to provide comprehensive risk assessment for legal matters with the highest level of accuracy and practical utility.

RISK ASSESSMENT FRAMEWORK:

1. COMPLEXITY ANALYSIS:
   - Legal complexity: Novel legal issues, conflicting precedents, unsettled law
   - Factual complexity: Multiple parties, complex relationships, extensive discovery
   - Procedural complexity: Multi-jurisdictional, class actions, appeals
   - Expert requirements: Medical experts, technical experts, economic analysis
   - Timeline complexity: Tight deadlines, coordinated proceedings

2. OUTCOME PREDICTION:
   - Liability analysis: Strength of legal claims and defenses
   - Damages assessment: Provable damages and recovery potential
   - Precedent analysis: Relevant case law and trends
   - Judicial factors: Court tendencies, judge characteristics
   - Jury considerations: Venue factors, case presentation issues

3. RESOURCE ASSESSMENT:
   - Attorney time requirements: Research, discovery, trial preparation
   - Expert witness needs: Types, costs, availability
   - Discovery scope: Document review, depositions, investigations
   - Trial requirements: Length, complexity, preparation time
   - Appeal potential: Likelihood and resource implications

4. FINANCIAL ANALYSIS:
   - Fee structure recommendations: Hourly, contingency, hybrid
   - Cost-benefit analysis: Expected recovery vs. costs
   - Collection risk: Defendant's ability to pay
   - Insurance coverage: Available policies and limits
   - Economic damages: Lost income, medical expenses, pain and suffering

5. STRATEGIC ANALYSIS:
   - Case positioning: Strengths, weaknesses, opportunities, threats
   - Settlement potential: Likelihood, timing, value ranges
   - Trial strategy: Key issues, evidence, witness considerations
   - Alternative dispute resolution: Mediation, arbitration options
   - Public relations: Media attention, reputation considerations

6. PRACTICE AREA SPECIFIC FACTORS:
   - Personal Injury: Medical causation, comparative fault, insurance issues
   - Family Law: Emotional factors, child welfare, asset complexity
   - Criminal Defense: Constitutional issues, plea negotiations, sentencing factors
   - Business Law: Commercial relationships, regulatory compliance, market factors

7. RISK MITIGATION:
   - Case management strategies: Efficient discovery, motion practice
   - Client management: Communication, expectation setting, cooperation
   - Financial protection: Retainers, cost controls, fee agreements
   - Professional liability: Malpractice prevention, documentation
   - Ethical compliance: Conflict avoidance, confidentiality, competence

ASSESSMENT CRITERIA:
- Provide detailed legal reasoning for all assessments
- Consider both best-case and worst-case scenarios
- Evaluate short-term and long-term implications
- Balance optimism with realistic expectations
- Consider practical and strategic factors beyond pure legal analysis
- Maintain conservative approach to risk evaluation
- Prioritize client interests and firm sustainability"""

    def _get_human_prompt(self) -> str:
        """Get the human prompt template."""
        return """Please perform comprehensive risk assessment and intelligence analysis for this legal matter:

CASE DESCRIPTION:
{case_description}

PRACTICE AREA:
{practice_area}

CLIENT INFORMATION:
{client_information}

MATTER DETAILS:
{matter_details}

EXTRACTED ENTITIES SUMMARY:
{extracted_entities}

CONFLICT ANALYSIS SUMMARY:
{conflict_analysis}

TIMELINE ANALYSIS:
{timeline_analysis}

ADDITIONAL CONTEXT:
{additional_context}

Perform thorough risk intelligence analysis following these steps:

1. COMPLEXITY ASSESSMENT: Analyze legal, factual, and procedural complexity
2. OUTCOME PREDICTION: Predict likely outcomes with probability assessments
3. RESOURCE EVALUATION: Estimate time, cost, and resource requirements
4. FINANCIAL ANALYSIS: Assess financial risks and opportunities
5. STRATEGIC ANALYSIS: Evaluate strategic positioning and considerations
6. RISK IDENTIFICATION: Identify specific risk factors and their impact
7. SUCCESS FACTORS: Identify factors that support case success
8. MITIGATION STRATEGIES: Recommend risk mitigation approaches
9. PRACTICE AREA INSIGHTS: Apply practice area specific analysis
10. PRECEDENT ANALYSIS: Consider relevant legal precedents
11. JURISDICTIONAL FACTORS: Evaluate jurisdiction-specific considerations
12. RECOMMENDATIONS: Provide detailed recommendations and conditions

Provide your analysis in the following structured format:
{format_instructions}

Focus on practical utility, strategic insight, and actionable recommendations."""

    def _prepare_assessment_context(
        self,
        intake_state: IntakeState,
        entity_extraction: EntityExtractionResult,
        conflict_analyses: List[ConflictAnalysis],
        additional_context: Dict[str, Any],
    ) -> str:
        """Prepare comprehensive context for risk assessment."""
        context_parts = []

        # Add urgency and temporal factors
        if intake_state.matter.urgency:
            context_parts.append(f"Case Urgency: {intake_state.matter.urgency.value}")

        if intake_state.matter.statute_of_limitations:
            days_until_sol = (
                intake_state.matter.statute_of_limitations - datetime.now()
            ).days
            context_parts.append(
                f"Statute of Limitations: {days_until_sol} days remaining"
            )

        # Add complexity indicators from entity extraction
        if entity_extraction.complexity_indicators:
            context_parts.append(
                f"Complexity Indicators: {', '.join(entity_extraction.complexity_indicators)}"
            )

        # Add urgency indicators
        if entity_extraction.urgency_indicators:
            context_parts.append(
                f"Urgency Indicators: {', '.join(entity_extraction.urgency_indicators)}"
            )

        # Add conflict summary
        if conflict_analyses:
            high_severity_conflicts = [
                c for c in conflict_analyses if c.severity in ["high", "critical"]
            ]
            if high_severity_conflicts:
                context_parts.append(
                    f"High Severity Conflicts: {len(high_severity_conflicts)} identified"
                )

        # Add additional context
        if additional_context:
            for key, value in additional_context.items():
                if value:
                    context_parts.append(f"{key.replace('_', ' ').title()}: {value}")

        return (
            "\n".join(context_parts)
            if context_parts
            else "No additional context available."
        )

    def _format_client_info(self, client) -> str:
        """Format client information for assessment."""
        info_parts = []

        if client.name:
            info_parts.append(f"Name: {client.name}")
        if client.contact_method:
            info_parts.append(f"Preferred Contact: {client.contact_method}")
        if client.referral_source:
            info_parts.append(f"Referral Source: {client.referral_source}")

        return (
            "\n".join(info_parts)
            if info_parts
            else "Limited client information available."
        )

    def _format_matter_details(self, matter) -> str:
        """Format matter details for assessment."""
        details = []

        if matter.title:
            details.append(f"Title: {matter.title}")
        if matter.case_type:
            details.append(f"Case Type: {matter.case_type}")
        if matter.estimated_value:
            details.append(f"Estimated Value: ${matter.estimated_value:,.2f}")
        if matter.complexity_score:
            details.append(f"Complexity Score: {matter.complexity_score}/10")
        if matter.incident_date:
            details.append(f"Incident Date: {matter.incident_date}")
        if matter.court_date:
            details.append(f"Court Date: {matter.court_date}")

        # Practice area specific details
        if matter.injuries:
            details.append(f"Injuries: {', '.join(matter.injuries)}")
        if matter.charges:
            details.append(f"Charges: {', '.join(matter.charges)}")
        if matter.medical_treatment is not None:
            details.append(
                f"Medical Treatment: {'Yes' if matter.medical_treatment else 'No'}"
            )
        if matter.insurance_involved is not None:
            details.append(
                f"Insurance Involved: {'Yes' if matter.insurance_involved else 'No'}"
            )

        return "\n".join(details) if details else "Limited matter details available."

    def _format_entities_summary(
        self, entity_extraction: EntityExtractionResult
    ) -> str:
        """Format entities summary for assessment."""
        if not entity_extraction.entities:
            return "No entities extracted."

        summary_parts = []

        # Key parties
        if entity_extraction.key_parties:
            summary_parts.append(
                f"Key Parties: {', '.join(entity_extraction.key_parties)}"
            )

        # Entity counts by type
        entity_counts = {}
        for entity in entity_extraction.entities:
            entity_type = entity.entity_type.value
            entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1

        if entity_counts:
            counts_str = ", ".join([f"{k}: {v}" for k, v in entity_counts.items()])
            summary_parts.append(f"Entity Counts: {counts_str}")

        # High confidence entities
        high_conf_entities = [
            e for e in entity_extraction.entities if e.confidence > 0.8
        ]
        if high_conf_entities:
            names = [e.name for e in high_conf_entities[:5]]  # Top 5
            summary_parts.append(f"High Confidence Entities: {', '.join(names)}")

        return "\n".join(summary_parts)

    def _format_conflict_summary(
        self, conflict_analyses: List[ConflictAnalysis]
    ) -> str:
        """Format conflict analysis summary."""
        if not conflict_analyses:
            return "No conflicts identified."

        summary_parts = []

        # Count by severity
        severity_counts = {}
        for analysis in conflict_analyses:
            severity = analysis.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        if severity_counts:
            counts_str = ", ".join([f"{k}: {v}" for k, v in severity_counts.items()])
            summary_parts.append(f"Conflict Severity Counts: {counts_str}")

        # Waivable conflicts
        waivable_conflicts = [c for c in conflict_analyses if c.waivable]
        if waivable_conflicts:
            summary_parts.append(f"Waivable Conflicts: {len(waivable_conflicts)}")

        # Escalation required
        escalation_required = [c for c in conflict_analyses if c.escalation_required]
        if escalation_required:
            summary_parts.append(
                f"Conflicts Requiring Escalation: {len(escalation_required)}"
            )

        return "\n".join(summary_parts)

    def _format_timeline_analysis(
        self, timeline: List[Tuple[datetime, str, str]]
    ) -> str:
        """Format timeline analysis."""
        if not timeline:
            return "No timeline events identified."

        # Sort by date
        sorted_timeline = sorted(timeline, key=lambda x: x[0])

        timeline_parts = []
        for date, event, description in sorted_timeline[:5]:  # Show first 5 events
            timeline_parts.append(
                f"- {date.strftime('%Y-%m-%d')}: {event} - {description}"
            )

        if len(timeline) > 5:
            timeline_parts.append(f"... and {len(timeline) - 5} more events")

        return "\n".join(timeline_parts)

    def _convert_llm_output(
        self, llm_output: LLMRiskIntelligenceOutput, intake_state: IntakeState
    ) -> LLMRiskAssessment:
        """Convert LLM output to LLMRiskAssessment object."""

        overall_assessment = llm_output.overall_assessment

        # Extract cost range
        cost_range = (0.0, 0.0)
        if "cost_range" in llm_output.financial_analysis:
            cost_data = llm_output.financial_analysis["cost_range"]
            if isinstance(cost_data, dict):
                cost_range = (cost_data.get("min", 0.0), cost_data.get("max", 0.0))
            elif isinstance(cost_data, list) and len(cost_data) == 2:
                cost_range = tuple(cost_data)

        # Extract resource requirements
        resource_req = ResourceRequirement.STANDARD
        if "resource_level" in llm_output.resource_assessment:
            try:
                resource_req = ResourceRequirement(
                    llm_output.resource_assessment["resource_level"]
                )
            except ValueError:
                pass

        return LLMRiskAssessment(
            overall_risk_score=overall_assessment.get("risk_score", 0.5),
            overall_risk_level=RiskLevel(
                overall_assessment.get("risk_level", "medium")
            ),
            recommendation=overall_assessment.get(
                "recommendation", "accept_with_conditions"
            ),
            confidence=llm_output.confidence_metrics.get("overall", 0.5),
            complexity_analysis=llm_output.complexity_analysis,
            outcome_prediction=llm_output.outcome_prediction,
            resource_assessment=llm_output.resource_assessment,
            financial_analysis=llm_output.financial_analysis,
            strategic_analysis=llm_output.strategic_analysis,
            risk_factors=llm_output.risk_factors,
            mitigation_strategies=llm_output.mitigation_strategies,
            success_factors=llm_output.success_factors,
            success_probability=llm_output.outcome_prediction.get(
                "success_probability", 0.5
            ),
            estimated_duration=llm_output.resource_assessment.get(
                "estimated_duration", "Unknown"
            ),
            estimated_cost_range=cost_range,
            resource_requirements=resource_req,
            legal_reasoning=llm_output.legal_reasoning,
            precedent_analysis=llm_output.precedent_analysis,
            strategic_considerations=llm_output.strategic_analysis.get(
                "considerations", []
            ),
            practice_area_factors=llm_output.practice_area_insights,
            jurisdictional_factors=llm_output.jurisdictional_factors,
            conditions=llm_output.recommendations.get("conditions", []),
            next_steps=llm_output.recommendations.get("next_steps", []),
            escalation_triggers=llm_output.recommendations.get(
                "escalation_triggers", []
            ),
        )

    def _error_risk_assessment(self, error_message: str) -> LLMRiskAssessment:
        """Return error risk assessment when LLM fails."""
        return LLMRiskAssessment(
            overall_risk_score=0.9,
            overall_risk_level=RiskLevel.VERY_HIGH,
            recommendation="decline",
            confidence=0.0,
            complexity_analysis={"error": "Analysis failed"},
            outcome_prediction={"error": "Prediction failed"},
            resource_assessment={"error": "Assessment failed"},
            financial_analysis={"error": "Analysis failed"},
            strategic_analysis={"error": "Analysis failed"},
            risk_factors=[
                {
                    "category": "system_error",
                    "description": f"Risk assessment failed: {error_message}",
                    "severity": "critical",
                    "impact": "Cannot properly assess case risk",
                }
            ],
            mitigation_strategies=["Manual risk assessment required"],
            success_factors=[],
            success_probability=0.0,
            estimated_duration="Unknown",
            estimated_cost_range=(0.0, 0.0),
            resource_requirements=ResourceRequirement.EXCEPTIONAL,
            legal_reasoning=f"Risk assessment system error: {error_message}",
            precedent_analysis=[],
            strategic_considerations=["System error - manual review required"],
            practice_area_factors={"error": "Analysis failed"},
            jurisdictional_factors=["System error"],
            conditions=["Manual risk assessment required before proceeding"],
            next_steps=["System review and manual assessment"],
            escalation_triggers=["System error occurred"],
        )
