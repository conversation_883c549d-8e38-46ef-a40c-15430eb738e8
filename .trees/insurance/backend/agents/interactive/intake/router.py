"""
Intake Agent Router

This module provides routing functionality for the intake agent,
directing requests to the appropriate intake flow based on entry point
and user context.

Key Features:
- Client vs staff intake routing
- Practice area specific routing
- Urgency-based routing
- Context-aware flow selection
"""

import logging
from typing import Dict, Any

from langchain_core.runnables import RunnableConfig

from .agent import IntakeAgent
from .state import Practice<PERSON>rea, CaseUrgency

logger = logging.getLogger(__name__)


async def intake_router(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Main router for intake agent requests.

    Routes requests to the appropriate intake flow based on context
    and user type.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state with routing information
    """
    logger.info("Routing intake request")

    # Determine intake mode from context
    intake_mode = _determine_intake_mode(state, config)
    state["intake_mode"] = intake_mode

    # Set initial routing
    state["next"] = "initial_contact"
    state["current_step"] = "initial_contact"

    # Add welcome message based on mode
    welcome_message = _get_welcome_message(intake_mode)

    if "messages" not in state:
        state["messages"] = []

    # Only add welcome message if not already present
    if not any(
        msg.get("content", "").startswith("Welcome") for msg in state["messages"]
    ):
        state["messages"].append({"type": "ai", "content": welcome_message})

    logger.info(f"Routed to {intake_mode} intake mode")

    return state


async def intake_client_router(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Router for client-facing intake.

    Sets up the intake flow for clients accessing through the client portal.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state configured for client intake
    """
    logger.info("Routing to client intake")

    state["intake_mode"] = "client"
    state["next"] = "initial_contact"
    state["current_step"] = "initial_contact"

    # Client-specific welcome message
    welcome_message = """Welcome to our legal intake system. I'm here to help you submit information about your legal matter.

I'll guide you through a few questions to understand your situation and determine how we can best assist you. This process typically takes 5-10 minutes.

To get started, could you please tell me your full name?"""

    if "messages" not in state:
        state["messages"] = []

    if not any(
        msg.get("content", "").startswith("Welcome to our legal intake")
        for msg in state["messages"]
    ):
        state["messages"].append({"type": "ai", "content": welcome_message})

    return state


async def intake_staff_router(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Router for staff-facing intake.

    Sets up the intake flow for staff members using the admin interface.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state configured for staff intake
    """
    logger.info("Routing to staff intake")

    state["intake_mode"] = "staff"

    # Check if we have pre-filled data
    has_prefilled_data = state.get("client", {}).get("name") and state.get(
        "case", {}
    ).get("description")

    if has_prefilled_data:
        logger.info("Pre-filled data detected, skipping to confirmation")
        state["next"] = "summarize_and_confirm"
        state["current_step"] = "summarize_and_confirm"
    else:
        state["next"] = "initial_contact"
        state["current_step"] = "initial_contact"

    # Staff-specific welcome message
    welcome_message = """Welcome to the staff intake interface. I'll assist you in collecting client information and creating a new case.

As a staff member, you can:
- Enter client information directly
- Skip steps if you have pre-filled data
- Access additional validation and conflict checking tools

Let's begin the intake process."""

    if "messages" not in state:
        state["messages"] = []

    if not any(
        msg.get("content", "").startswith("Welcome to the staff intake")
        for msg in state["messages"]
    ):
        state["messages"].append({"type": "ai", "content": welcome_message})

    return state


async def practice_area_router(
    state: Dict[str, Any], config: RunnableConfig, practice_area: PracticeArea
) -> Dict[str, Any]:
    """
    Router for practice area specific intake.

    Pre-configures the intake flow for a specific practice area.

    Args:
        state: Current state
        config: Runnable configuration
        practice_area: Target practice area

    Returns:
        Updated state configured for specific practice area
    """
    logger.info(f"Routing to {practice_area.value} intake")

    # Set practice area in case information
    if "case" not in state:
        state["case"] = {}

    state["case"]["practice_area"] = practice_area.value

    # Set practice area specific configuration
    if practice_area == PracticeArea.CRIMINAL_DEFENSE:
        # Criminal cases are time-sensitive by default
        state["case"]["urgency"] = CaseUrgency.MEDIUM.value
        state["requires_immediate_attention"] = True

    # Add practice area specific welcome message
    practice_area_name = practice_area.value.replace("_", " ").title()

    practice_area_message = f"I see you need help with a {practice_area_name} matter. "

    if practice_area == PracticeArea.PERSONAL_INJURY:
        practice_area_message += "I'll collect information about your injury, the incident, and any medical treatment you've received."
    elif practice_area == PracticeArea.FAMILY_LAW:
        practice_area_message += "I'll gather information about your family situation, including any children and assets involved."
    elif practice_area == PracticeArea.CRIMINAL_DEFENSE:
        practice_area_message += "Given the time-sensitive nature of criminal matters, I'll prioritize collecting information about your charges and any upcoming court dates."
    elif practice_area == PracticeArea.ESTATE_PLANNING:
        practice_area_message += "I'll help you organize information about your assets, beneficiaries, and estate planning goals."
    elif practice_area == PracticeArea.IMMIGRATION:
        practice_area_message += "I'll collect information about your immigration status, goals, and any time-sensitive deadlines."
    elif practice_area == PracticeArea.REAL_ESTATE:
        practice_area_message += "I'll gather details about your property transaction, including timelines and parties involved."
    elif practice_area == PracticeArea.BANKRUPTCY:
        practice_area_message += "I'll collect information about your financial situation, debts, and assets to help determine the best path forward."

    state["messages"].append({"type": "system", "content": practice_area_message})

    return state


async def urgent_intake_router(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Router for urgent intake cases.

    Configures the intake flow for urgent cases that need immediate attention.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state configured for urgent processing
    """
    logger.info("Routing to urgent intake")

    # Set urgency flags
    state["requires_immediate_attention"] = True

    if "case" not in state:
        state["case"] = {}

    state["case"]["urgency"] = CaseUrgency.HIGH.value

    # Add urgent processing message
    urgent_message = """⚠️ URGENT INTAKE: This case has been flagged for immediate attention.

I'll expedite the intake process and ensure our team is notified immediately upon completion. Please provide all available information so we can assist you as quickly as possible."""

    state["messages"].append({"type": "system", "content": urgent_message})

    return state


def _determine_intake_mode(state: Dict[str, Any], config: RunnableConfig) -> str:
    """Determine the appropriate intake mode based on context."""

    # Check if mode is explicitly set
    if state.get("intake_mode"):
        return state["intake_mode"]

    # Check user context from config
    configurable = config.get("configurable", {})
    user_role = configurable.get("user_role", "client")

    # Staff roles use staff intake
    if user_role in ["staff", "attorney", "paralegal", "partner", "admin"]:
        return "staff"

    # Default to client intake
    return "client"


def _get_welcome_message(intake_mode: str) -> str:
    """Get appropriate welcome message for intake mode."""

    if intake_mode == "staff":
        return """Welcome to the staff intake interface. I'll assist you in collecting client information and creating a new case.

You can enter information directly or guide the client through the process. Let's begin."""

    else:  # client mode
        return """Welcome! I'm here to help you submit information about your legal matter.

I'll guide you through a few questions to understand your situation and determine how we can best assist you. This process typically takes 5-10 minutes.

To get started, could you please tell me your full name?"""
