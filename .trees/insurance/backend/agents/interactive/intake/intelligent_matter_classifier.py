"""
Intelligent LLM-Powered Matter Classifier

This module provides advanced matter classification using Large Language Models
for intelligent reasoning, context understanding, and nuanced classification
that goes far beyond simple keyword matching.

Key Features:
- LLM-powered semantic understanding
- Context-aware classification
- Nuanced legal reasoning
- Multi-factor analysis (facts, timeline, parties, damages, etc.)
- Confidence scoring with detailed reasoning
- Fallback keyword matching for reliability
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import (
    PracticeArea,
    WorkType,
    CaseUrgency,
    PersonalInjuryCaseType,
    FamilyLawCaseType,
    CriminalDefenseCaseType,
    PRACTICE_AREA_TO_WORK_TYPE,
)

logger = logging.getLogger(__name__)


class MatterClassificationOutput(BaseModel):
    """Structured output for LLM matter classification."""

    practice_area: str = Field(
        description="Primary practice area: personal_injury, family_law, or criminal_defense"
    )
    case_type: str = Field(description="Specific case type within the practice area")
    urgency: str = Field(description="Urgency level: low, medium, high, or critical")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0")
    reasoning: str = Field(description="Detailed reasoning for the classification")
    key_factors: List[str] = Field(
        description="Key factors that influenced the classification"
    )
    timeline_factors: List[str] = Field(description="Time-sensitive factors identified")
    parties_involved: List[str] = Field(description="Types of parties involved")
    potential_damages: List[str] = Field(
        description="Types of potential damages or relief sought"
    )
    complexity_indicators: List[str] = Field(
        description="Factors indicating case complexity"
    )
    red_flags: List[str] = Field(description="Potential issues or red flags identified")


@dataclass
class IntelligentClassificationResult:
    """Enhanced result of intelligent matter classification."""

    practice_area: PracticeArea
    work_type: WorkType
    case_type: str
    urgency: CaseUrgency
    confidence: float
    reasoning: str
    keywords_matched: List[str]
    display_label: str

    # Enhanced LLM-powered insights
    key_factors: List[str]
    timeline_factors: List[str]
    parties_involved: List[str]
    potential_damages: List[str]
    complexity_indicators: List[str]
    red_flags: List[str]
    llm_reasoning: str
    fallback_used: bool


class IntelligentMatterClassifier:
    """
    Advanced matter classifier using LLM reasoning combined with keyword fallback.

    This classifier uses sophisticated LLM analysis to understand:
    - Legal context and nuances
    - Timeline and urgency factors
    - Parties and relationships
    - Potential damages and relief
    - Case complexity indicators
    - Red flags and potential issues
    """

    def __init__(self, llm_model: str = "gpt-4", use_fallback: bool = True):
        """Initialize the intelligent classifier."""
        self.llm = ChatOpenAI(model=llm_model, temperature=0.1)
        self.use_fallback = use_fallback
        self.output_parser = PydanticOutputParser(
            pydantic_object=MatterClassificationOutput
        )

        # Fallback keyword classifier for reliability
        if use_fallback:
            from .case_classifier import MultiPracticeCaseClassifier

            self.fallback_classifier = MultiPracticeCaseClassifier()

        self.classification_prompt = ChatPromptTemplate.from_messages(
            [("system", self._get_system_prompt()), ("human", self._get_human_prompt())]
        )

    async def classify_matter(
        self, description: str, additional_context: Dict[str, Any] = None
    ) -> IntelligentClassificationResult:
        """
        Perform intelligent matter classification using LLM reasoning.

        Args:
            description: Matter description from client
            additional_context: Additional context (dates, charges, etc.)

        Returns:
            IntelligentClassificationResult with comprehensive analysis
        """
        if not description or len(description.strip()) < 5:
            return self._default_classification()

        additional_context = additional_context or {}

        try:
            # Primary LLM-based classification
            llm_result = await self._llm_classify(description, additional_context)

            # Fallback keyword classification for comparison
            fallback_result = None
            if self.use_fallback:
                fallback_result = self.fallback_classifier.classify_case(
                    description, additional_context
                )

            # Combine results intelligently
            final_result = self._combine_results(
                llm_result, fallback_result, description
            )

            logger.info(
                f"Matter classified as {final_result.practice_area.value} with confidence {final_result.confidence}"
            )

            return final_result

        except Exception as e:
            logger.error(f"Error in LLM classification: {str(e)}")

            # Fallback to keyword classification
            if self.use_fallback:
                logger.info("Falling back to keyword classification")
                fallback_result = self.fallback_classifier.classify_case(
                    description, additional_context
                )
                return self._convert_fallback_result(
                    fallback_result, description, fallback_used=True
                )
            else:
                return self._default_classification()

    async def _llm_classify(
        self, description: str, additional_context: Dict[str, Any]
    ) -> MatterClassificationOutput:
        """Perform LLM-based classification."""

        # Prepare context information
        context_str = self._format_additional_context(additional_context)

        # Create the prompt
        prompt = self.classification_prompt.format_prompt(
            description=description,
            additional_context=context_str,
            format_instructions=self.output_parser.get_format_instructions(),
        )

        # Get LLM response
        response = await self.llm.ainvoke(prompt.to_messages())

        # Parse structured output
        parsed_result = self.output_parser.parse(response.content)

        return parsed_result

    def _get_system_prompt(self) -> str:
        """Get the system prompt for matter classification."""
        return """You are an expert legal intake specialist with deep knowledge of Personal Injury, Family Law, and Criminal Defense practice areas. Your job is to intelligently classify legal matters based on client descriptions.

You must analyze the matter description and classify it into one of these practice areas:
- personal_injury: Auto accidents, slip/fall, medical malpractice, product liability, workplace injuries, wrongful death
- family_law: Divorce, child custody, child support, adoption, domestic violence, prenuptial agreements, paternity
- criminal_defense: DUI/DWI, misdemeanors, felonies, traffic violations, white collar crimes, drug offenses

For each practice area, determine the specific case type:

Personal Injury Types:
- auto_accident, slip_and_fall, medical_malpractice, product_liability, workplace_injury, wrongful_death, other

Family Law Types:
- divorce, child_custody, child_support, adoption, domestic_violence, prenuptial, paternity, other

Criminal Defense Types:
- dui_dwi, misdemeanor, felony, traffic_violation, white_collar, domestic_violence, drug_offense, other

Urgency Levels:
- critical: Immediate attention needed (court tomorrow, arrest warrants, custody issues)
- high: Priority processing (upcoming court dates, recent arrests, statute of limitations concerns)
- medium: Expedited processing (criminal cases, domestic violence, insurance deadlines)
- low: Standard processing

Analyze these factors:
1. Legal context and relationships between parties
2. Timeline and time-sensitive elements
3. Types of damages or relief sought
4. Complexity indicators
5. Potential red flags or ethical concerns
6. Jurisdiction-specific considerations

Provide detailed reasoning that demonstrates legal understanding, not just keyword matching."""

    def _get_human_prompt(self) -> str:
        """Get the human prompt template."""
        return """Please analyze this legal matter and provide a comprehensive classification:

Matter Description:
{description}

Additional Context:
{additional_context}

Provide your analysis in the following structured format:
{format_instructions}

Focus on legal reasoning and context, not just keywords. Consider the relationships between parties, timeline factors, potential damages, and case complexity."""

    def _format_additional_context(self, context: Dict[str, Any]) -> str:
        """Format additional context for the prompt."""
        if not context:
            return "No additional context provided."

        formatted_parts = []

        for key, value in context.items():
            if value is not None:
                formatted_parts.append(f"- {key.replace('_', ' ').title()}: {value}")

        return (
            "\n".join(formatted_parts)
            if formatted_parts
            else "No additional context provided."
        )

    def _combine_results(
        self,
        llm_result: MatterClassificationOutput,
        fallback_result: Optional[Any],
        description: str,
    ) -> IntelligentClassificationResult:
        """Combine LLM and fallback results intelligently."""

        # Convert LLM result to our format
        try:
            practice_area = PracticeArea(llm_result.practice_area)
        except ValueError:
            logger.warning(
                f"Invalid practice area from LLM: {llm_result.practice_area}"
            )
            practice_area = PracticeArea.PERSONAL_INJURY

        try:
            urgency = CaseUrgency(llm_result.urgency)
        except ValueError:
            logger.warning(f"Invalid urgency from LLM: {llm_result.urgency}")
            urgency = CaseUrgency.LOW

        work_type = PRACTICE_AREA_TO_WORK_TYPE.get(practice_area, WorkType.LITIGATION)
        display_label = "Case" if work_type == WorkType.LITIGATION else "Matter"

        # Extract keywords from fallback if available
        keywords_matched = []
        if fallback_result:
            keywords_matched = fallback_result.keywords_matched

        # Adjust confidence based on agreement between methods
        confidence = llm_result.confidence
        if fallback_result and fallback_result.practice_area == practice_area:
            confidence = min(confidence + 0.1, 1.0)  # Boost confidence if methods agree
        elif fallback_result and fallback_result.practice_area != practice_area:
            confidence = max(
                confidence - 0.1, 0.1
            )  # Reduce confidence if methods disagree

        return IntelligentClassificationResult(
            practice_area=practice_area,
            work_type=work_type,
            case_type=llm_result.case_type,
            urgency=urgency,
            confidence=confidence,
            reasoning=llm_result.reasoning,
            keywords_matched=keywords_matched,
            display_label=display_label,
            key_factors=llm_result.key_factors,
            timeline_factors=llm_result.timeline_factors,
            parties_involved=llm_result.parties_involved,
            potential_damages=llm_result.potential_damages,
            complexity_indicators=llm_result.complexity_indicators,
            red_flags=llm_result.red_flags,
            llm_reasoning=llm_result.reasoning,
            fallback_used=False,
        )

    def _convert_fallback_result(
        self, fallback_result: Any, description: str, fallback_used: bool = True
    ) -> IntelligentClassificationResult:
        """Convert fallback result to intelligent result format."""

        return IntelligentClassificationResult(
            practice_area=fallback_result.practice_area,
            work_type=fallback_result.work_type,
            case_type=fallback_result.case_type,
            urgency=fallback_result.urgency,
            confidence=fallback_result.confidence,
            reasoning=fallback_result.reasoning,
            keywords_matched=fallback_result.keywords_matched,
            display_label=fallback_result.display_label,
            key_factors=["Keyword-based classification"],
            timeline_factors=[],
            parties_involved=[],
            potential_damages=[],
            complexity_indicators=[],
            red_flags=[],
            llm_reasoning="Fallback keyword classification used",
            fallback_used=fallback_used,
        )

    def _default_classification(self) -> IntelligentClassificationResult:
        """Return default classification when no description is provided."""
        return IntelligentClassificationResult(
            practice_area=PracticeArea.PERSONAL_INJURY,
            work_type=WorkType.LITIGATION,
            case_type=PersonalInjuryCaseType.OTHER,
            urgency=CaseUrgency.LOW,
            confidence=0.1,
            reasoning="Default classification due to insufficient information.",
            keywords_matched=[],
            display_label="Case",
            key_factors=["Insufficient information"],
            timeline_factors=[],
            parties_involved=[],
            potential_damages=[],
            complexity_indicators=[],
            red_flags=["Insufficient information provided"],
            llm_reasoning="Default classification - no description provided",
            fallback_used=False,
        )
