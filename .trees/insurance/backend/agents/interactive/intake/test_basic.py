"""
Basic tests for the Multi-Practice Intake Agent

This module provides basic validation tests to ensure the intake agent
components are properly implemented and functional.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, Any

from .state import (
    IntakeState,
    PracticeArea,
    CaseUrgency,
    ClientInformation,
    CaseInformation,
)
from .case_classifier import MultiPracticeCaseClassifier, ClassificationResult
from .conflict_checker import MultiPracticeConflictChecker
from .agent import IntakeAgent
from .router import intake_router, intake_client_router, intake_staff_router


class TestIntakeState:
    """Test the IntakeState model."""

    def test_state_initialization(self):
        """Test basic state initialization."""
        from backend.agents.shared.core.state import AgentExecutionContext

        context = AgentExecutionContext(
            tenant_id="test-tenant", user_id="test-user", agent_type="intake"
        )

        state = IntakeState(messages=[], execution_context=context)

        assert state.intake_mode == "client"
        assert state.current_step == "initial_contact"
        assert state.can_proceed is True
        assert state.requires_immediate_attention is False

    def test_client_information(self):
        """Test client information model."""
        client = ClientInformation(
            name="<PERSON>", email="<EMAIL>", phone="555-1234"
        )

        assert client.name == "John Doe"
        assert client.email == "<EMAIL>"
        assert client.phone == "555-1234"

    def test_case_information(self):
        """Test case information model."""
        case = CaseInformation(
            practice_area=PracticeArea.PERSONAL_INJURY,
            description="Car accident on Highway 35",
            urgency=CaseUrgency.MEDIUM,
        )

        assert case.practice_area == PracticeArea.PERSONAL_INJURY
        assert case.description == "Car accident on Highway 35"
        assert case.urgency == CaseUrgency.MEDIUM


class TestCaseClassifier:
    """Test the MultiPracticeCaseClassifier."""

    def setup_method(self):
        """Set up test fixtures."""
        self.classifier = MultiPracticeCaseClassifier()

    def test_personal_injury_classification(self):
        """Test personal injury case classification."""
        description = "I was in a car accident last week and injured my back"
        result = self.classifier.classify_case(description)

        assert isinstance(result, ClassificationResult)
        assert result.practice_area == PracticeArea.PERSONAL_INJURY
        assert result.case_type == "auto_accident"
        assert result.confidence > 0
        assert "car accident" in result.keywords_matched

    def test_family_law_classification(self):
        """Test family law case classification."""
        description = "I need help with my divorce and child custody"
        result = self.classifier.classify_case(description)

        assert result.practice_area == PracticeArea.FAMILY_LAW
        assert result.case_type == "divorce"
        assert result.confidence > 0

    def test_criminal_defense_classification(self):
        """Test criminal defense case classification."""
        description = "I was arrested for DUI last night"
        result = self.classifier.classify_case(description)

        assert result.practice_area == PracticeArea.CRIMINAL_DEFENSE
        assert result.case_type == "dui_dwi"
        assert result.confidence > 0

    def test_urgency_detection(self):
        """Test urgency level detection."""
        urgent_description = "I have court tomorrow for my DUI case"
        result = self.classifier.classify_case(urgent_description)

        assert result.urgency in [CaseUrgency.HIGH, CaseUrgency.CRITICAL]

    def test_empty_description(self):
        """Test handling of empty description."""
        result = self.classifier.classify_case("")

        assert result.practice_area == PracticeArea.PERSONAL_INJURY
        assert result.confidence == 0.1


class TestConflictChecker:
    """Test the MultiPracticeConflictChecker."""

    def setup_method(self):
        """Set up test fixtures."""
        self.conflict_checker = MultiPracticeConflictChecker(use_llm=False)

    @pytest.mark.asyncio
    async def test_basic_conflict_check(self):
        """Test basic conflict checking functionality."""
        from backend.agents.shared.core.state import AgentExecutionContext

        context = AgentExecutionContext(
            tenant_id="test-tenant", user_id="test-user", agent_type="intake"
        )

        state = IntakeState(messages=[], execution_context=context)

        state.client.name = "John Doe"
        state.client.email = "<EMAIL>"
        state.matter.practice_area = PracticeArea.PERSONAL_INJURY
        state.matter.description = "Car accident case"

        result = await self.conflict_checker.check_conflicts(state, "test-tenant")

        assert result.has_conflicts is False  # No conflicts in test environment
        assert isinstance(result.conflicts, list)
        assert result.checked_at is not None


class TestIntakeAgent:
    """Test the IntakeAgent."""

    def setup_method(self):
        """Set up test fixtures."""
        self.agent = IntakeAgent()

    def test_agent_initialization(self):
        """Test agent initialization."""
        assert self.agent.agent_type == "intake"
        assert self.agent.agent_name == "IntakeAgent"
        assert len(self.agent.workflow_steps) > 0
        assert PracticeArea.PERSONAL_INJURY in self.agent.practice_area_configs

    def test_supported_practice_areas(self):
        """Test getting supported practice areas."""
        areas = self.agent.get_supported_practice_areas()

        assert "personal_injury" in areas
        assert "family_law" in areas
        assert "criminal_defense" in areas

    def test_workflow_steps(self):
        """Test getting workflow steps."""
        steps = self.agent.get_workflow_steps()

        expected_steps = [
            "initial_contact",
            "collect_personal_info",
            "collect_case_details",
            "practice_area_details",
            "check_conflicts",
            "summarize_and_confirm",
            "save_client_info",
        ]

        for step in expected_steps:
            assert step in steps

    @pytest.mark.asyncio
    async def test_case_classification_utility(self):
        """Test the case classification utility method."""
        description = "I slipped and fell at the grocery store"
        result = await self.agent.classify_matter_from_description(description)

        assert result["practice_area"] == "personal_injury"
        assert result["case_type"] == "slip_and_fall"
        assert "confidence" in result
        assert "reasoning" in result


class TestRouters:
    """Test the routing functions."""

    @pytest.mark.asyncio
    async def test_intake_router(self):
        """Test the main intake router."""
        state = {"messages": []}
        config = {"configurable": {"user_role": "client"}}

        result = await intake_router(state, config)

        assert result["intake_mode"] == "client"
        assert result["next"] == "initial_contact"
        assert result["current_step"] == "initial_contact"
        assert len(result["messages"]) > 0

    @pytest.mark.asyncio
    async def test_client_router(self):
        """Test the client intake router."""
        state = {"messages": []}
        config = {}

        result = await intake_client_router(state, config)

        assert result["intake_mode"] == "client"
        assert result["next"] == "initial_contact"
        assert any(
            "Welcome to our legal intake" in msg.get("content", "")
            for msg in result["messages"]
        )

    @pytest.mark.asyncio
    async def test_staff_router(self):
        """Test the staff intake router."""
        state = {"messages": []}
        config = {}

        result = await intake_staff_router(state, config)

        assert result["intake_mode"] == "staff"
        assert result["next"] == "initial_contact"
        assert any(
            "Welcome to the staff intake" in msg.get("content", "")
            for msg in result["messages"]
        )

    @pytest.mark.asyncio
    async def test_staff_router_with_prefilled_data(self):
        """Test staff router with pre-filled data."""
        state = {
            "messages": [],
            "client": {"name": "John Doe"},
            "case": {"description": "Test case"},
        }
        config = {}

        result = await intake_staff_router(state, config)

        assert result["intake_mode"] == "staff"
        assert result["next"] == "summarize_and_confirm"
        assert result["current_step"] == "summarize_and_confirm"


def test_practice_area_enum():
    """Test PracticeArea enum values."""
    assert PracticeArea.PERSONAL_INJURY.value == "personal_injury"
    assert PracticeArea.FAMILY_LAW.value == "family_law"
    assert PracticeArea.CRIMINAL_DEFENSE.value == "criminal_defense"


def test_case_urgency_enum():
    """Test CaseUrgency enum values."""
    assert CaseUrgency.LOW.value == "low"
    assert CaseUrgency.MEDIUM.value == "medium"
    assert CaseUrgency.HIGH.value == "high"
    assert CaseUrgency.CRITICAL.value == "critical"


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])
