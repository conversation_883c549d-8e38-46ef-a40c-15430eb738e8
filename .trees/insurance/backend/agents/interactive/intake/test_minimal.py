#!/usr/bin/env python3
"""
Minimal Testing Without Dependencies

Test what we can validate without external dependencies.
"""

import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../.."))


def test_file_structure():
    """Test that files were created."""
    print("📁 Testing File Structure...")

    expected_files = [
        "backend/agents/interactive/intake/state.py",
        "backend/agents/interactive/intake/case_classifier.py",
        "backend/agents/interactive/intake/intelligent_matter_classifier.py",
        "backend/agents/interactive/intake/conflict_checker.py",
        "backend/agents/interactive/intake/nodes.py",
        "backend/agents/interactive/intake/agent.py",
        "backend/agents/interactive/intake/router.py",
        "backend/api/agents/intake.py",
        "frontend/src/hooks/use-intake-agent.ts",
        "frontend/src/components/intake/multi-practice-intake.tsx",
    ]

    existing_files = 0
    total_files = len(expected_files)

    for file_path in expected_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
            existing_files += 1
        else:
            print(f"❌ {file_path}")

    print(f"\nFiles Created: {existing_files}/{total_files}")
    return existing_files >= total_files * 0.8  # 80% of files should exist


def test_terminology_in_files():
    """Test that terminology was updated in files."""
    print("\n🔄 Testing Terminology Updates...")

    files_to_check = [
        "backend/agents/interactive/intake/state.py",
        "backend/agents/interactive/intake/agent.py",
    ]

    terminology_checks = {
        "MatterInformation": 0,
        "matter": 0,
        "Case vs Matter": 0,
        "work_type": 0,
        "display_label": 0,
    }

    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, "r") as f:
                content = f.read()

            if "MatterInformation" in content:
                terminology_checks["MatterInformation"] += 1
            if "matter" in content.lower():
                terminology_checks["matter"] += 1
            if "work_type" in content:
                terminology_checks["work_type"] += 1
            if "display_label" in content:
                terminology_checks["display_label"] += 1
            if "Case" in content and "Matter" in content:
                terminology_checks["Case vs Matter"] += 1

    print("Terminology Usage:")
    for term, count in terminology_checks.items():
        print(f"  {term}: {count} files")

    # Check if most terminology is present
    present_terms = sum(1 for count in terminology_checks.values() if count > 0)
    total_terms = len(terminology_checks)

    print(f"\nTerminology Coverage: {present_terms}/{total_terms} terms found")
    return present_terms >= 4


def test_llm_structure():
    """Test that LLM structure is properly defined."""
    print("\n🧠 Testing LLM Structure...")

    classifier_file = (
        "backend/agents/interactive/intake/intelligent_matter_classifier.py"
    )

    if not os.path.exists(classifier_file):
        print("❌ Intelligent classifier file missing")
        return False

    with open(classifier_file, "r") as f:
        content = f.read()

    required_components = [
        "IntelligentMatterClassifier",
        "MatterClassificationOutput",
        "key_factors",
        "timeline_factors",
        "parties_involved",
        "potential_damages",
        "red_flags",
        "llm_reasoning",
        "fallback_classifier",
    ]

    found_components = 0
    for component in required_components:
        if component in content:
            print(f"✅ {component}")
            found_components += 1
        else:
            print(f"❌ {component}")

    print(f"\nLLM Components: {found_components}/{len(required_components)}")
    return found_components >= len(required_components) * 0.8


def main():
    """Run minimal tests."""
    print("🔍 MINIMAL TESTING (No Dependencies)")
    print("=" * 50)

    tests = [
        ("File Structure", test_file_structure),
        ("Terminology Updates", test_terminology_in_files),
        ("LLM Structure", test_llm_structure),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 {test_name.upper()}")
        result = test_func()
        if result:
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")

    print("\n" + "=" * 50)
    print("📊 MINIMAL TEST RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")

    if passed == total:
        print("✅ STRUCTURE IS READY")
        print("• All files created")
        print("• Terminology updated")
        print("• LLM structure defined")
        print("• Ready for dependency installation and real testing")
    elif passed >= 2:
        print("⚠️  MOSTLY READY")
        print("• Core structure in place")
        print("• Some components may need fixes")
        print("• Can proceed with dependency installation")
    else:
        print("❌ NOT READY")
        print("• Major structural issues")
        print("• Requires fixes before proceeding")

    print("\n🎯 HONEST TESTING STATUS:")
    print("=" * 50)
    print("❌ LLM Classification: NOT TESTED (requires dependencies)")
    print("❌ API Integration: NOT TESTED (requires deployment)")
    print("❌ Frontend Integration: NOT TESTED (requires React setup)")
    print("❌ Database Operations: NOT TESTED (requires DB)")
    print("❌ Performance: NOT TESTED")
    print("❌ Error Handling: NOT TESTED")
    print("✅ File Structure: TESTED")
    print("✅ Code Structure: TESTED")
    print("✅ Terminology: TESTED")

    print("\n🚀 TO COMPLETE TESTING:")
    print("1. Install dependencies: pip install langchain langchain-openai")
    print("2. Set up OpenAI API key")
    print("3. Deploy API endpoints")
    print("4. Set up React environment")
    print("5. Run comprehensive integration tests")
    print("6. Test with real user scenarios")
    print("7. Performance and load testing")
    print("8. Cost analysis for LLM usage")

    return passed >= 2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
