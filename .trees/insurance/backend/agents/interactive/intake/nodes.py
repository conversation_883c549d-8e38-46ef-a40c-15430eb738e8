"""
Practice-Specific Workflow Nodes for Multi-Practice Intake Agent

This module provides workflow nodes that handle different steps of the intake process
with practice-area specific logic and requirements.

Key Features:
- Practice area specific data collection
- Intelligent workflow routing
- Validation and error handling
- Urgency-based processing
- Context-aware prompting
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from .state import IntakeState, PracticeArea, WorkType, CaseUrgency
from .case_classifier import MultiPracticeCaseClassifier
from .intelligent_matter_classifier import IntelligentMatterClassifier
from .conflict_checker import MultiPractice<PERSON>onflict<PERSON><PERSON><PERSON>
from .risk_assessment import RiskAssessmentEngine

logger = logging.getLogger(__name__)


class IntakeNodes:
    """Enhanced workflow nodes for multi-practice intake."""

    def __init__(self):
        """Initialize the intake nodes."""
        self.classifier = MultiPracticeCaseClassifier()  # Fallback classifier
        self.intelligent_classifier = (
            IntelligentMatterClassifier()
        )  # Primary LLM classifier
        self.conflict_checker = MultiPracticeConflictChecker()
        self.risk_assessor = RiskAssessmentEngine()

    async def initial_contact(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Initial contact node - welcome and gather basic information.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Starting initial contact")

        # Get the last human message
        last_message = None
        if state.get("messages"):
            for msg in reversed(state["messages"]):
                if isinstance(msg, dict) and msg.get("type") == "human":
                    last_message = msg.get("content", "")
                    break
                elif hasattr(msg, "type") and msg.type == "human":
                    last_message = msg.content
                    break

        # Initialize client information if name is provided
        if last_message and len(last_message.strip()) > 2:
            # Try to extract name from message
            potential_name = self._extract_name_from_message(last_message)
            if potential_name:
                if "client" not in state:
                    state["client"] = {}
                state["client"]["name"] = potential_name

                # Move to next step
                state["current_step"] = "collect_personal_info"
                state["next"] = "collect_personal_info"

                response = f"Thank you, {potential_name}. I'll help you with your legal matter. Can you please provide your email address and phone number?"
            else:
                response = "I'd be happy to help you with your legal matter. Could you please start by telling me your full name?"
        else:
            response = "Hello! I'm here to help you with your legal matter. To get started, could you please tell me your full name?"

        # Add AI response
        state["messages"].append({"type": "ai", "content": response})

        return state

    async def collect_personal_info(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Collect personal information from the client.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Collecting personal information")

        # Get the last human message
        last_message = self._get_last_human_message(state)

        if not last_message:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I need some personal information to help you. Could you please provide your email address and phone number?",
                }
            )
            return state

        # Initialize client if not exists
        if "client" not in state:
            state["client"] = {}

        # Extract contact information
        email = self._extract_email(last_message)
        phone = self._extract_phone(last_message)

        if email:
            state["client"]["email"] = email
        if phone:
            state["client"]["phone"] = phone

        # Check if we have minimum required info
        client = state.get("client", {})
        missing_info = []

        if not client.get("name"):
            missing_info.append("name")
        if not client.get("email"):
            missing_info.append("email address")
        if not client.get("phone"):
            missing_info.append("phone number")

        if missing_info:
            missing_str = " and ".join(missing_info)
            state["messages"].append(
                {
                    "type": "ai",
                    "content": f"I still need your {missing_str}. Could you please provide that information?",
                }
            )
            return state

        # Move to case details collection
        state["current_step"] = "collect_case_details"
        state["next"] = "collect_case_details"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "collect_personal_info"
        ]

        state["messages"].append(
            {
                "type": "ai",
                "content": "Thank you for providing your contact information. Now, could you please describe your legal matter? Please provide as much detail as possible about what happened and what type of legal help you need.",
            }
        )

        return state

    async def collect_case_details(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Collect case details and classify the case.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Collecting case details")

        # Get the last human message
        last_message = self._get_last_human_message(state)

        if not last_message or len(last_message.strip()) < 10:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "Could you please provide more details about your legal matter? The more information you can share, the better I can help you.",
                }
            )
            return state

        # Initialize matter if not exists
        if "matter" not in state:
            state["matter"] = {}

        # Store the description
        state["matter"]["description"] = last_message

        # Classify the matter using intelligent LLM classifier
        try:
            classification = await self.intelligent_classifier.classify_matter(
                last_message
            )
        except Exception as e:
            logger.error(f"Error in intelligent classification: {str(e)}")
            # Fallback to simple classifier
            classification = self.classifier.classify_case(last_message)

        state["matter"]["practice_area"] = classification.practice_area.value
        state["matter"]["work_type"] = classification.work_type.value
        state["matter"]["case_type"] = classification.case_type
        state["matter"]["urgency"] = classification.urgency.value
        state["matter"]["display_label"] = classification.display_label

        # Store classification metadata
        state["matter"]["classification_confidence"] = classification.confidence
        state["matter"]["classification_reasoning"] = classification.reasoning

        # Set urgency flag for immediate attention cases
        if classification.urgency in [CaseUrgency.HIGH, CaseUrgency.CRITICAL]:
            state["requires_immediate_attention"] = True

        # Ask practice area specific follow-up questions
        follow_up_response = await self._get_practice_area_followup(
            classification.practice_area, classification.case_type, state
        )

        state["messages"].append({"type": "ai", "content": follow_up_response})

        # Determine next step based on practice area and urgency
        if classification.urgency == CaseUrgency.CRITICAL:
            state["current_step"] = "urgent_processing"
            state["next"] = "check_conflicts"  # Skip some steps for urgent cases
        else:
            state["current_step"] = "practice_area_details"
            state["next"] = "practice_area_details"

        state["completed_steps"] = state.get("completed_steps", []) + [
            "collect_case_details"
        ]

        return state

    async def practice_area_details(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Collect practice area specific details.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Collecting practice area specific details")

        practice_area = state.get("matter", {}).get("practice_area")
        if not practice_area:
            # Fallback to matter details if practice area not set
            state["next"] = "collect_case_details"
            return state

        # Get the last human message
        last_message = self._get_last_human_message(state)

        # Process based on practice area
        if practice_area == PracticeArea.PERSONAL_INJURY.value:
            return await self._handle_personal_injury_details(state, last_message)
        elif practice_area == PracticeArea.FAMILY_LAW.value:
            return await self._handle_family_law_details(state, last_message)
        elif practice_area == PracticeArea.CRIMINAL_DEFENSE.value:
            return await self._handle_criminal_defense_details(state, last_message)
        else:
            # Unknown practice area, move to conflict check
            state["current_step"] = "check_conflicts"
            state["next"] = "check_conflicts"
            return state

    async def check_conflicts(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Enhanced conflict checking with integrated risk assessment.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with conflict and risk assessment results
        """
        logger.info("Performing enhanced conflict checking and risk assessment")

        # Get tenant ID from config
        tenant_id = config.get("configurable", {}).get("tenant_id")
        if not tenant_id:
            logger.error("No tenant ID provided for conflict check")
            state["next"] = "summarize_and_confirm"
            return state

        try:
            # Convert state to IntakeState for analysis
            intake_state = self._dict_to_intake_state(state)

            # Perform enhanced conflict check
            conflict_result = await self.conflict_checker.check_conflicts(
                intake_state, tenant_id
            )

            # Perform comprehensive risk assessment with LLM intelligence
            # Extract entity extraction results if available from conflict checker
            entity_extraction = None
            conflict_analyses = None

            if (
                hasattr(self.conflict_checker, "llm_entity_extractor")
                and self.conflict_checker.use_llm
            ):
                # Re-extract entities for risk assessment (could be cached in future)
                entity_extraction = (
                    await self.conflict_checker.llm_entity_extractor.extract_entities(
                        intake_state.matter.description or "",
                        intake_state.matter.practice_area,
                        self.conflict_checker._prepare_entity_context(intake_state),
                    )
                )

                # Get conflict analyses if available
                if hasattr(self.conflict_checker, "llm_conflict_analyzer"):
                    existing_clients = (
                        await self.conflict_checker._get_existing_clients(tenant_id)
                    )
                    existing_matters = (
                        await self.conflict_checker._get_existing_matters(tenant_id)
                    )
                    conflict_analyses = await self.conflict_checker.llm_conflict_analyzer.analyze_conflicts(
                        intake_state,
                        entity_extraction,
                        existing_clients,
                        existing_matters,
                        tenant_id,
                    )

            # Perform comprehensive risk assessment
            risk_result = await self.risk_assessor.assess_risk(
                intake_state, conflict_result, entity_extraction, conflict_analyses
            )

            # Store conflict check results
            state["conflict_check"] = {
                "has_conflicts": conflict_result.has_conflicts,
                "conflicts": conflict_result.conflicts,
                "checked_at": conflict_result.checked_at.isoformat(),
                "practice_area_specific_checks": conflict_result.practice_area_specific_checks,
            }

            # Store risk assessment results
            state["risk_assessment"] = {
                "overall_risk_score": risk_result.overall_risk_score,
                "overall_risk_level": risk_result.overall_risk_level.value,
                "category_scores": {
                    k.value: v for k, v in risk_result.category_scores.items()
                },
                "category_levels": {
                    k.value: v.value for k, v in risk_result.category_levels.items()
                },
                "recommendation": risk_result.recommendation,
                "conditions": risk_result.conditions,
                "mitigation_strategies": risk_result.mitigation_strategies,
                "estimated_hours": risk_result.estimated_hours,
                "estimated_complexity": risk_result.estimated_complexity,
                "success_probability": risk_result.success_probability,
                "requires_immediate_attention": risk_result.requires_immediate_attention,
                "deadline_risks": risk_result.deadline_risks,
                "high_risk_factors": [
                    {
                        "category": f.category.value,
                        "factor_name": f.factor_name,
                        "description": f.description,
                        "risk_score": f.risk_score,
                        "requires_attention": f.requires_attention,
                    }
                    for f in risk_result.high_risk_factors
                ],
            }

            # Determine next step based on conflicts and risk assessment
            if conflict_result.has_conflicts:
                # Handle conflicts
                conflict_message = self._format_conflict_message(
                    conflict_result.conflicts
                )
                state["messages"].append({"type": "ai", "content": conflict_message})

                # Check if conflicts are critical
                has_critical_conflicts = any(
                    c.get("severity") == "critical" for c in conflict_result.conflicts
                )

                if has_critical_conflicts or risk_result.recommendation == "decline":
                    state["can_proceed"] = False
                    state["next"] = "FINISH"
                else:
                    state["next"] = "risk_assessment_review"
            else:
                # No conflicts, but check risk assessment
                if risk_result.recommendation == "decline":
                    state["messages"].append(
                        {
                            "type": "ai",
                            "content": "While no conflicts were found, our risk assessment indicates this case may not be suitable for our firm at this time.",
                        }
                    )
                    state["can_proceed"] = False
                    state["next"] = "FINISH"
                elif risk_result.recommendation == "accept_with_conditions":
                    state["messages"].append(
                        {
                            "type": "ai",
                            "content": "No conflicts found. However, this case requires special consideration and conditions for acceptance.",
                        }
                    )
                    state["next"] = "risk_assessment_review"
                else:
                    state["messages"].append(
                        {
                            "type": "ai",
                            "content": "Excellent! I've completed the conflict check and risk assessment. No conflicts were found and the case appears suitable for our firm.",
                        }
                    )
                    state["next"] = "summarize_and_confirm"

            # Set urgency flags based on risk assessment
            if risk_result.requires_immediate_attention:
                state["requires_immediate_attention"] = True
                state["case"]["urgency"] = "critical"

            state["current_step"] = "check_conflicts"
            state["completed_steps"] = state.get("completed_steps", []) + [
                "check_conflicts",
                "risk_assessment",
            ]

        except Exception as e:
            logger.error(f"Error during conflict check and risk assessment: {str(e)}")
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I encountered an issue during the analysis, but we can proceed with your intake. Our team will perform a thorough review before officially taking on your case.",
                }
            )
            state["next"] = "summarize_and_confirm"

        return state

    async def risk_assessment_review(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Review risk assessment results and present to user.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Reviewing risk assessment results")

        risk_assessment = state.get("risk_assessment", {})
        if not risk_assessment:
            # No risk assessment available, proceed to summary
            state["next"] = "summarize_and_confirm"
            return state

        # Format risk assessment message
        risk_message = self._format_risk_assessment_message(risk_assessment)

        state["messages"].append({"type": "ai", "content": risk_message})

        # Determine next step based on recommendation
        recommendation = risk_assessment.get("recommendation", "accept")

        if recommendation == "decline":
            state["can_proceed"] = False
            state["next"] = "FINISH"
        elif recommendation == "accept_with_conditions":
            # Present conditions and ask for confirmation
            conditions_message = self._format_conditions_message(
                risk_assessment.get("conditions", [])
            )
            state["messages"].append(
                {
                    "type": "ai",
                    "content": conditions_message
                    + "\n\nDo you agree to proceed under these conditions? Please respond with 'yes' to continue or 'no' to decline.",
                }
            )
            state["current_step"] = "risk_conditions_confirmation"
            state["next"] = "handle_risk_conditions_response"
        else:
            state["next"] = "summarize_and_confirm"

        state["completed_steps"] = state.get("completed_steps", []) + [
            "risk_assessment_review"
        ]

        return state

    async def handle_risk_conditions_response(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Handle user response to risk conditions.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Handling risk conditions response")

        # Get the last user message
        messages = state.get("messages", [])
        if not messages:
            state["next"] = "summarize_and_confirm"
            return state

        last_message = messages[-1]
        if last_message.get("type") != "human":
            state["next"] = "summarize_and_confirm"
            return state

        user_response = last_message.get("content", "").lower().strip()

        if user_response in ["yes", "y", "agree", "accept", "proceed"]:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "Thank you for agreeing to the conditions. We'll proceed with your case under these terms.",
                }
            )
            state["conditions_accepted"] = True
            state["next"] = "summarize_and_confirm"
        elif user_response in ["no", "n", "decline", "disagree"]:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I understand. Unfortunately, we cannot proceed without these conditions. Thank you for your time.",
                }
            )
            state["can_proceed"] = False
            state["conditions_accepted"] = False
            state["next"] = "FINISH"
        else:
            # Unclear response, ask for clarification
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I need a clear yes or no response. Do you agree to proceed under the stated conditions? Please respond with 'yes' to continue or 'no' to decline.",
                }
            )
            state["next"] = "handle_risk_conditions_response"

        state["current_step"] = "risk_conditions_confirmation"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "risk_conditions_response"
        ]

        return state

    async def summarize_and_confirm(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Summarize collected information and confirm with client.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Summarizing and confirming information")

        # Generate summary
        summary = self._generate_intake_summary(state)

        state["messages"].append(
            {
                "type": "ai",
                "content": f"Let me summarize the information you've provided:\n\n{summary}\n\nIs this information correct? If you need to make any changes, please let me know. Otherwise, I'll proceed to save your information and create your case.",
            }
        )

        state["current_step"] = "summarize_and_confirm"
        state["next"] = "save_client_info"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "summarize_and_confirm"
        ]

        return state

    async def save_client_info(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Save client information and create the case.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Saving client information")

        # TODO: Implement actual database saving
        # For now, just simulate success

        practice_area = (
            state.get("case", {}).get("practice_area", "").replace("_", " ").title()
        )
        urgency = state.get("case", {}).get("urgency", "low")

        success_message = f"Thank you! I've successfully created your {practice_area} case in our system."

        if urgency in ["high", "critical"]:
            success_message += " Due to the urgent nature of your case, our team will prioritize your matter and contact you within 24 hours."
        else:
            success_message += " Our team will review your case and contact you within 2-3 business days to discuss next steps."

        success_message += "\n\nYou should receive a confirmation email shortly with your case details and next steps."

        state["messages"].append({"type": "ai", "content": success_message})

        state["current_step"] = "completed"
        state["next"] = "FINISH"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "save_client_info"
        ]

        return state

    # Helper methods

    def _get_last_human_message(self, state: Dict[str, Any]) -> Optional[str]:
        """Get the last human message from the state."""
        if not state.get("messages"):
            return None

        for msg in reversed(state["messages"]):
            if isinstance(msg, dict) and msg.get("type") == "human":
                return msg.get("content", "")
            elif hasattr(msg, "type") and msg.type == "human":
                return msg.content

        return None

    def _extract_name_from_message(self, message: str) -> Optional[str]:
        """Extract a name from a message."""
        # Simple name extraction - could be enhanced with NLP
        words = message.strip().split()

        # Look for "My name is..." or "I'm..."
        if "name is" in message.lower():
            idx = message.lower().find("name is") + 8
            remaining = message[idx:].strip()
            name_parts = remaining.split()[:2]  # Take first two words as name
            if len(name_parts) >= 2:
                return " ".join(name_parts)

        # If message is short and looks like a name
        if len(words) == 2 and all(
            word.isalpha() and word[0].isupper() for word in words
        ):
            return " ".join(words)

        return None

    def _extract_email(self, text: str) -> Optional[str]:
        """Extract email address from text."""
        import re

        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        matches = re.findall(email_pattern, text)
        return matches[0] if matches else None

    def _extract_phone(self, text: str) -> Optional[str]:
        """Extract phone number from text."""
        import re

        # Simple phone pattern - could be enhanced
        phone_patterns = [
            r"\b\d{3}-\d{3}-\d{4}\b",
            r"\b\(\d{3}\)\s*\d{3}-\d{4}\b",
            r"\b\d{10}\b",
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            if matches:
                return matches[0]

        return None

    async def _get_practice_area_followup(
        self, practice_area: PracticeArea, case_type: str, state: Dict[str, Any]
    ) -> str:
        """Get practice area specific follow-up questions."""
        if practice_area == PracticeArea.PERSONAL_INJURY:
            if case_type == "auto_accident":
                return "I see this involves an auto accident. Can you tell me when the accident occurred and if you received medical treatment for any injuries?"
            elif case_type == "slip_and_fall":
                return "I understand you had a slip and fall incident. When did this happen, and where did it occur? Did you seek medical attention?"
            elif case_type == "medical_malpractice":
                return "This appears to be a medical malpractice matter. Can you tell me when the incident occurred and which medical provider was involved?"
            else:
                return "Can you tell me when this incident occurred and whether you sustained any injuries that required medical treatment?"

        elif practice_area == PracticeArea.FAMILY_LAW:
            if case_type == "divorce":
                return "I understand you're seeking help with a divorce. Are there children involved, and do you and your spouse have significant assets to divide?"
            elif case_type == "child_custody":
                return "This involves child custody. Can you tell me about the current custody arrangement and what changes you're seeking?"
            else:
                return "Can you provide more details about your family law matter, including whether children are involved and any urgent deadlines?"

        elif practice_area == PracticeArea.CRIMINAL_DEFENSE:
            if case_type == "dui_dwi":
                return "I see this is a DUI/DWI matter. When were you arrested, and do you have a court date scheduled? This is time-sensitive."
            else:
                return "This appears to be a criminal matter. When did the arrest or charges occur, and do you have any upcoming court dates? Criminal cases are time-sensitive."

        return "Can you provide more specific details about your legal matter and any important dates or deadlines?"

    async def _handle_personal_injury_details(
        self, state: Dict[str, Any], message: str
    ) -> Dict[str, Any]:
        """Handle personal injury specific details collection."""
        if not message:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I need more information about your injury case. When did the incident occur, and what injuries did you sustain?",
                }
            )
            return state

        # Extract dates and injury information
        incident_date = self._extract_date(message)
        if incident_date:
            state["case"]["incident_date"] = incident_date

        # Check for medical treatment mentions
        if any(
            word in message.lower()
            for word in [
                "doctor",
                "hospital",
                "treatment",
                "medical",
                "er",
                "emergency",
            ]
        ):
            state["case"]["medical_treatment"] = True

        # Check for insurance mentions
        if any(word in message.lower() for word in ["insurance", "claim", "adjuster"]):
            state["case"]["insurance_involved"] = True

        # Move to conflict check
        state["current_step"] = "check_conflicts"
        state["next"] = "check_conflicts"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "practice_area_details"
        ]

        state["messages"].append(
            {
                "type": "ai",
                "content": "Thank you for providing those details. I'll now check for any potential conflicts of interest.",
            }
        )

        return state

    async def _handle_family_law_details(
        self, state: Dict[str, Any], message: str
    ) -> Dict[str, Any]:
        """Handle family law specific details collection."""
        if not message:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I need more information about your family law matter. Are there children involved, and are there significant assets to consider?",
                }
            )
            return state

        # Check for children involvement
        if any(
            word in message.lower()
            for word in ["child", "children", "kid", "custody", "visitation"]
        ):
            state["case"]["children_involved"] = True

        # Check for assets
        if any(
            word in message.lower()
            for word in ["house", "property", "assets", "money", "savings", "401k"]
        ):
            state["case"]["assets_involved"] = True

        # Check for domestic violence
        if any(
            word in message.lower()
            for word in ["abuse", "violence", "restraining", "protection", "threats"]
        ):
            state["case"]["domestic_violence_involved"] = True
            state["case"][
                "urgency"
            ] = "high"  # Domestic violence cases are high priority

        # Move to conflict check
        state["current_step"] = "check_conflicts"
        state["next"] = "check_conflicts"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "practice_area_details"
        ]

        state["messages"].append(
            {
                "type": "ai",
                "content": "Thank you for providing those details. I'll now check for any potential conflicts of interest.",
            }
        )

        return state

    async def _handle_criminal_defense_details(
        self, state: Dict[str, Any], message: str
    ) -> Dict[str, Any]:
        """Handle criminal defense specific details collection."""
        if not message:
            state["messages"].append(
                {
                    "type": "ai",
                    "content": "I need more information about your criminal matter. When were you arrested or charged, and do you have any upcoming court dates?",
                }
            )
            return state

        # Extract arrest date
        arrest_date = self._extract_date(message)
        if arrest_date:
            state["case"]["arrest_date"] = arrest_date

        # Look for court date mentions
        if any(
            word in message.lower()
            for word in ["court", "hearing", "trial", "arraignment"]
        ):
            court_date = self._extract_future_date(message)
            if court_date:
                state["case"]["court_date"] = court_date
                # Set urgency based on court date proximity
                days_until_court = (
                    datetime.fromisoformat(court_date) - datetime.utcnow()
                ).days
                if days_until_court <= 1:
                    state["case"]["urgency"] = "critical"
                elif days_until_court <= 7:
                    state["case"]["urgency"] = "high"

        # Check bail status
        if any(
            word in message.lower()
            for word in ["bail", "bond", "released", "custody", "jail"]
        ):
            if "released" in message.lower() or "bail" in message.lower():
                state["case"]["bail_status"] = "released"
            else:
                state["case"]["bail_status"] = "in_custody"

        # Extract charges
        charges = self._extract_charges(message)
        if charges:
            state["case"]["charges"] = charges

        # Criminal cases are time-sensitive
        if state["case"].get("urgency", "low") == "low":
            state["case"]["urgency"] = "medium"

        # Move to conflict check
        state["current_step"] = "check_conflicts"
        state["next"] = "check_conflicts"
        state["completed_steps"] = state.get("completed_steps", []) + [
            "practice_area_details"
        ]

        urgency_msg = ""
        if state["case"].get("urgency") in ["high", "critical"]:
            urgency_msg = " Given the time-sensitive nature of your case, we'll prioritize your matter."

        state["messages"].append(
            {
                "type": "ai",
                "content": f"Thank you for providing those details. I'll now check for any potential conflicts of interest.{urgency_msg}",
            }
        )

        return state

    def _extract_date(self, text: str) -> Optional[str]:
        """Extract date from text."""
        import re
        from datetime import datetime

        # Simple date patterns
        date_patterns = [
            r"\b\d{1,2}/\d{1,2}/\d{4}\b",
            r"\b\d{1,2}-\d{1,2}-\d{4}\b",
            r"\b\d{4}-\d{1,2}-\d{1,2}\b",
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    # Try to parse the date
                    date_str = matches[0]
                    if "/" in date_str:
                        parsed_date = datetime.strptime(date_str, "%m/%d/%Y")
                    elif "-" in date_str and date_str.count("-") == 2:
                        if len(date_str.split("-")[0]) == 4:
                            parsed_date = datetime.strptime(date_str, "%Y-%m-%d")
                        else:
                            parsed_date = datetime.strptime(date_str, "%m-%d-%Y")
                    else:
                        continue

                    return parsed_date.isoformat()
                except ValueError:
                    continue

        return None

    def _extract_future_date(self, text: str) -> Optional[str]:
        """Extract future date from text (for court dates)."""
        # This would be more sophisticated in a real implementation
        # For now, just use the same date extraction
        return self._extract_date(text)

    def _extract_charges(self, text: str) -> List[str]:
        """Extract criminal charges from text."""
        charges = []

        # Common charge patterns
        charge_keywords = [
            "dui",
            "dwi",
            "assault",
            "battery",
            "theft",
            "burglary",
            "robbery",
            "fraud",
            "drug possession",
            "trafficking",
            "domestic violence",
            "disorderly conduct",
            "public intoxication",
            "speeding",
            "reckless driving",
        ]

        text_lower = text.lower()
        for keyword in charge_keywords:
            if keyword in text_lower:
                charges.append(keyword.title())

        return charges

    def _dict_to_intake_state(self, state_dict: Dict[str, Any]) -> IntakeState:
        """Convert dictionary state to IntakeState object."""
        from backend.agents.shared.core.state import AgentExecutionContext

        # Create a minimal execution context
        execution_context = AgentExecutionContext(
            tenant_id="default", user_id="default", agent_type="intake"
        )

        # Create IntakeState with minimal required fields
        intake_state = IntakeState(
            messages=state_dict.get("messages", []), execution_context=execution_context
        )

        # Copy client information
        if "client" in state_dict:
            client_data = state_dict["client"]
            intake_state.client.name = client_data.get("name")
            intake_state.client.email = client_data.get("email")
            intake_state.client.phone = client_data.get("phone")

        # Copy case information
        if "case" in state_dict:
            case_data = state_dict["case"]
            intake_state.case.description = case_data.get("description")
            if case_data.get("practice_area"):
                intake_state.case.practice_area = PracticeArea(
                    case_data["practice_area"]
                )
            if case_data.get("case_type"):
                intake_state.case.case_type = case_data["case_type"]
            if case_data.get("urgency"):
                intake_state.case.urgency = CaseUrgency(case_data["urgency"])

        return intake_state

    def _format_conflict_message(self, conflicts: List[Dict[str, Any]]) -> str:
        """Format conflict check results into a user-friendly message."""
        if not conflicts:
            return "No conflicts found."

        message = "I found the following potential conflicts:\n\n"

        for i, conflict in enumerate(conflicts, 1):
            severity = conflict.get("severity", "unknown")
            description = conflict.get("description", "Unknown conflict")

            message += f"{i}. {description} (Severity: {severity.title()})\n"

        # Add guidance based on severity
        critical_conflicts = [c for c in conflicts if c.get("severity") == "critical"]
        if critical_conflicts:
            message += "\nDue to critical conflicts, we cannot represent you in this matter. I recommend seeking alternative legal counsel."
        else:
            message += "\nOur team will review these potential conflicts and determine if we can proceed with representation."

        return message

    def _format_risk_assessment_message(self, risk_assessment: Dict[str, Any]) -> str:
        """Format risk assessment results into a user-friendly message."""
        overall_level = risk_assessment.get("overall_risk_level", "medium")
        recommendation = risk_assessment.get("recommendation", "accept")

        message = f"**Risk Assessment Complete**\n\n"
        message += (
            f"Overall Risk Level: **{overall_level.replace('_', ' ').title()}**\n\n"
        )

        # Add category breakdown for medium+ risk
        if overall_level not in ["very_low", "low"]:
            category_levels = risk_assessment.get("category_levels", {})
            if category_levels:
                message += "**Risk Category Breakdown:**\n"
                for category, level in category_levels.items():
                    if level not in ["very_low", "low"]:
                        message += f"• {category.replace('_', ' ').title()}: {level.replace('_', ' ').title()}\n"
                message += "\n"

        # Add high-risk factors
        high_risk_factors = risk_assessment.get("high_risk_factors", [])
        if high_risk_factors:
            message += "**Key Risk Factors:**\n"
            for factor in high_risk_factors[:3]:  # Show top 3
                message += f"• {factor.get('description', 'Unknown risk factor')}\n"
            message += "\n"

        # Add resource estimates
        estimated_hours = risk_assessment.get("estimated_hours")
        estimated_complexity = risk_assessment.get("estimated_complexity")
        success_probability = risk_assessment.get("success_probability")

        if estimated_hours or estimated_complexity or success_probability:
            message += "**Case Estimates:**\n"
            if estimated_hours:
                message += f"• Estimated Hours: {estimated_hours}\n"
            if estimated_complexity:
                message += f"• Complexity Level: {estimated_complexity.replace('_', ' ').title()}\n"
            if success_probability:
                message += f"• Success Probability: {success_probability:.0%}\n"
            message += "\n"

        # Add deadline risks
        deadline_risks = risk_assessment.get("deadline_risks", [])
        if deadline_risks:
            message += "**Time-Sensitive Factors:**\n"
            for risk in deadline_risks:
                message += f"• {risk}\n"
            message += "\n"

        return message

    def _format_conditions_message(self, conditions: List[str]) -> str:
        """Format conditions for case acceptance."""
        if not conditions:
            return "No special conditions required."

        message = "**Conditions for Case Acceptance:**\n\n"
        for i, condition in enumerate(conditions, 1):
            message += f"{i}. {condition}\n"

        return message

    def _generate_intake_summary(self, state: Dict[str, Any]) -> str:
        """Generate a summary of collected intake information."""
        summary_parts = []

        # Client information
        client = state.get("client", {})
        if client.get("name"):
            summary_parts.append(f"**Client:** {client['name']}")
        if client.get("email"):
            summary_parts.append(f"**Email:** {client['email']}")
        if client.get("phone"):
            summary_parts.append(f"**Phone:** {client['phone']}")

        # Case information
        case = state.get("case", {})
        if case.get("practice_area"):
            practice_area = case["practice_area"].replace("_", " ").title()
            summary_parts.append(f"**Practice Area:** {practice_area}")

        if case.get("case_type"):
            case_type = case["case_type"].replace("_", " ").title()
            summary_parts.append(f"**Case Type:** {case_type}")

        if case.get("description"):
            summary_parts.append(f"**Description:** {case['description']}")

        if case.get("urgency"):
            urgency = case["urgency"].title()
            summary_parts.append(f"**Urgency:** {urgency}")

        # Practice area specific details
        if case.get("incident_date"):
            summary_parts.append(f"**Incident Date:** {case['incident_date']}")

        if case.get("court_date"):
            summary_parts.append(f"**Court Date:** {case['court_date']}")

        if case.get("charges"):
            charges = ", ".join(case["charges"])
            summary_parts.append(f"**Charges:** {charges}")

        # Conflict check results
        conflict_check = state.get("conflict_check", {})
        if conflict_check.get("has_conflicts"):
            summary_parts.append(
                "**Conflicts:** Potential conflicts identified (details provided above)"
            )
        else:
            summary_parts.append("**Conflicts:** No conflicts found")

        # Risk assessment results
        risk_assessment = state.get("risk_assessment", {})
        if risk_assessment:
            risk_level = risk_assessment.get("overall_risk_level", "unknown")
            recommendation = risk_assessment.get("recommendation", "unknown")
            summary_parts.append(
                f"**Risk Level:** {risk_level.replace('_', ' ').title()}"
            )
            summary_parts.append(
                f"**Recommendation:** {recommendation.replace('_', ' ').title()}"
            )

            if risk_assessment.get("estimated_complexity"):
                complexity = risk_assessment["estimated_complexity"]
                summary_parts.append(
                    f"**Complexity:** {complexity.replace('_', ' ').title()}"
                )

            if risk_assessment.get("success_probability"):
                success_prob = risk_assessment["success_probability"]
                summary_parts.append(f"**Success Probability:** {success_prob:.0%}")

        return "\n".join(summary_parts)
