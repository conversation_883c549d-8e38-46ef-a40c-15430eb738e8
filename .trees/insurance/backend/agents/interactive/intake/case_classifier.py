"""
Multi-Practice Case Classifier

This module provides intelligent case classification for multiple practice areas,
determining the appropriate practice area, case type, and urgency level based
on client input and case details.

Key Features:
- Natural language processing for case description analysis
- Practice area detection (Personal Injury, Family Law, Criminal Defense)
- Case type classification within each practice area
- Urgency level assessment
- Confidence scoring for classification decisions
"""

import re
from typing import Dict, Any, Tuple, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from .state import (
    PracticeArea,
    WorkType,
    CaseUrgency,
    PersonalInjuryCaseType,
    FamilyLawCaseType,
    CriminalDefenseCaseType,
    PRACTICE_AREA_TO_WORK_TYPE,
)


@dataclass
class ClassificationResult:
    """Result of matter classification."""

    practice_area: PracticeArea
    work_type: WorkType
    case_type: str
    urgency: CaseUrgency
    confidence: float
    reasoning: str
    keywords_matched: List[str]
    display_label: str  # "Case" or "Matter"


class MultiPracticeCaseClassifier:
    """
    Intelligent matter classifier for multiple practice areas.

    Uses keyword matching, pattern recognition, and contextual analysis
    to classify matters into appropriate practice areas and types, with
    proper Case vs Matter terminology based on work type.
    """

    def __init__(self):
        """Initialize the classifier with keyword patterns."""
        self.personal_injury_keywords = {
            PersonalInjuryCaseType.AUTO_ACCIDENT: [
                "car accident",
                "auto accident",
                "vehicle collision",
                "crash",
                "rear-end",
                "t-bone",
                "head-on",
                "fender bender",
                "traffic accident",
                "motor vehicle",
            ],
            PersonalInjuryCaseType.SLIP_AND_FALL: [
                "slip and fall",
                "slip",
                "fall",
                "tripped",
                "wet floor",
                "icy",
                "stairs",
                "sidewalk",
                "premises liability",
                "store accident",
            ],
            PersonalInjuryCaseType.MEDICAL_MALPRACTICE: [
                "medical malpractice",
                "doctor",
                "hospital",
                "surgery",
                "misdiagnosis",
                "medical error",
                "physician",
                "nurse",
                "medication error",
                "surgical error",
            ],
            PersonalInjuryCaseType.PRODUCT_LIABILITY: [
                "defective product",
                "product liability",
                "recalled",
                "manufacturing defect",
                "design defect",
                "dangerous product",
                "faulty",
            ],
            PersonalInjuryCaseType.WORKPLACE_INJURY: [
                "work injury",
                "workplace accident",
                "workers comp",
                "on the job",
                "construction accident",
                "industrial accident",
                "occupational",
            ],
            PersonalInjuryCaseType.WRONGFUL_DEATH: [
                "wrongful death",
                "death",
                "died",
                "fatal",
                "deceased",
                "survivor",
            ],
        }

        self.family_law_keywords = {
            FamilyLawCaseType.DIVORCE: [
                "divorce",
                "separation",
                "marriage dissolution",
                "split up",
                "end marriage",
            ],
            FamilyLawCaseType.CHILD_CUSTODY: [
                "custody",
                "visitation",
                "parenting time",
                "child support",
                "kids",
                "children",
                "parental rights",
            ],
            FamilyLawCaseType.CHILD_SUPPORT: [
                "child support",
                "support payments",
                "maintenance",
                "alimony",
            ],
            FamilyLawCaseType.ADOPTION: [
                "adoption",
                "adopt",
                "parental rights",
                "guardianship",
            ],
            FamilyLawCaseType.DOMESTIC_VIOLENCE: [
                "domestic violence",
                "abuse",
                "restraining order",
                "protection order",
                "harassment",
                "stalking",
                "threats",
            ],
            FamilyLawCaseType.PRENUPTIAL: [
                "prenup",
                "prenuptial",
                "premarital agreement",
                "marriage contract",
            ],
            FamilyLawCaseType.PATERNITY: [
                "paternity",
                "father",
                "biological parent",
                "dna test",
            ],
        }

        self.criminal_defense_keywords = {
            CriminalDefenseCaseType.DUI_DWI: [
                "dui",
                "dwi",
                "drunk driving",
                "intoxicated",
                "breathalyzer",
                "sobriety",
                "alcohol",
                "impaired driving",
            ],
            CriminalDefenseCaseType.TRAFFIC_VIOLATION: [
                "speeding",
                "traffic ticket",
                "reckless driving",
                "license suspended",
                "moving violation",
                "traffic court",
            ],
            CriminalDefenseCaseType.DRUG_OFFENSE: [
                "drug",
                "possession",
                "marijuana",
                "cocaine",
                "heroin",
                "narcotics",
                "controlled substance",
                "drug trafficking",
            ],
            CriminalDefenseCaseType.MISDEMEANOR: [
                "misdemeanor",
                "petty theft",
                "shoplifting",
                "disorderly conduct",
                "public intoxication",
                "minor offense",
            ],
            CriminalDefenseCaseType.FELONY: [
                "felony",
                "burglary",
                "robbery",
                "assault",
                "battery",
                "theft",
                "fraud",
                "embezzlement",
                "serious crime",
            ],
            CriminalDefenseCaseType.WHITE_COLLAR: [
                "white collar",
                "fraud",
                "embezzlement",
                "tax evasion",
                "money laundering",
                "securities fraud",
                "insider trading",
            ],
        }

        self.urgency_indicators = {
            CaseUrgency.CRITICAL: [
                "court tomorrow",
                "hearing today",
                "arrest warrant",
                "jail",
                "custody",
                "emergency",
                "urgent",
                "asap",
                "immediate",
            ],
            CaseUrgency.HIGH: [
                "court date",
                "hearing",
                "deadline",
                "statute of limitations",
                "time sensitive",
                "soon",
                "this week",
            ],
            CaseUrgency.MEDIUM: [
                "insurance deadline",
                "settlement offer",
                "discovery deadline",
            ],
        }

    def classify_case(
        self, description: str, additional_context: Dict[str, Any] = None
    ) -> ClassificationResult:
        """
        Classify a matter based on description and additional context.

        Args:
            description: Matter description from client
            additional_context: Additional context (dates, charges, etc.)

        Returns:
            ClassificationResult with practice area, work type, case type, and urgency
        """
        if not description:
            return self._default_classification()

        description_lower = description.lower()
        additional_context = additional_context or {}

        # Classify practice area and case type
        practice_area, case_type, confidence, keywords = self._classify_practice_area(
            description_lower
        )

        # Determine work type and display label
        work_type = PRACTICE_AREA_TO_WORK_TYPE.get(practice_area, WorkType.LITIGATION)
        display_label = "Case" if work_type == WorkType.LITIGATION else "Matter"

        # Determine urgency
        urgency = self._determine_urgency(
            description_lower, additional_context, practice_area
        )

        # Generate reasoning
        reasoning = self._generate_reasoning(
            practice_area, case_type, urgency, keywords, display_label
        )

        return ClassificationResult(
            practice_area=practice_area,
            work_type=work_type,
            case_type=case_type,
            urgency=urgency,
            confidence=confidence,
            reasoning=reasoning,
            keywords_matched=keywords,
            display_label=display_label,
        )

    def _classify_practice_area(
        self, description: str
    ) -> Tuple[PracticeArea, str, float, List[str]]:
        """Classify the practice area and case type."""
        scores = {
            PracticeArea.PERSONAL_INJURY: self._score_keywords(
                description, self.personal_injury_keywords
            ),
            PracticeArea.FAMILY_LAW: self._score_keywords(
                description, self.family_law_keywords
            ),
            PracticeArea.CRIMINAL_DEFENSE: self._score_keywords(
                description, self.criminal_defense_keywords
            ),
        }

        # Find the highest scoring practice area
        best_practice_area = max(scores.keys(), key=lambda k: scores[k][0])
        best_score, best_case_type, matched_keywords = scores[best_practice_area]

        # If no clear match, default to personal injury
        if best_score == 0:
            return PracticeArea.PERSONAL_INJURY, PersonalInjuryCaseType.OTHER, 0.1, []

        # Calculate confidence based on score
        confidence = min(best_score / 3.0, 1.0)  # Normalize to 0-1

        return best_practice_area, best_case_type, confidence, matched_keywords

    def _score_keywords(
        self, description: str, keyword_dict: Dict[str, List[str]]
    ) -> Tuple[float, str, List[str]]:
        """Score keywords for a specific practice area."""
        best_score = 0
        best_case_type = None
        best_keywords = []

        for case_type, keywords in keyword_dict.items():
            score = 0
            matched_keywords = []

            for keyword in keywords:
                if keyword in description:
                    score += 1
                    matched_keywords.append(keyword)

            if score > best_score:
                best_score = score
                best_case_type = case_type
                best_keywords = matched_keywords

        return best_score, best_case_type or "other", best_keywords

    def _determine_urgency(
        self, description: str, context: Dict[str, Any], practice_area: PracticeArea
    ) -> CaseUrgency:
        """Determine case urgency based on description and context."""
        # Check for explicit urgency indicators
        for urgency, keywords in self.urgency_indicators.items():
            for keyword in keywords:
                if keyword in description:
                    return urgency

        # Practice area specific urgency rules
        if practice_area == PracticeArea.CRIMINAL_DEFENSE:
            # Check for court dates
            if context.get("court_date"):
                court_date = context["court_date"]
                if isinstance(court_date, str):
                    try:
                        court_date = datetime.fromisoformat(court_date)
                    except:
                        court_date = None

                if court_date:
                    days_until_court = (court_date - datetime.utcnow()).days
                    if days_until_court <= 1:
                        return CaseUrgency.CRITICAL
                    elif days_until_court <= 7:
                        return CaseUrgency.HIGH

            # Check for arrest date
            if context.get("arrest_date"):
                arrest_date = context["arrest_date"]
                if isinstance(arrest_date, str):
                    try:
                        arrest_date = datetime.fromisoformat(arrest_date)
                    except:
                        arrest_date = None

                if arrest_date and (datetime.utcnow() - arrest_date).days <= 2:
                    return CaseUrgency.HIGH

        # Default urgency
        return CaseUrgency.LOW

    def _generate_reasoning(
        self,
        practice_area: PracticeArea,
        case_type: str,
        urgency: CaseUrgency,
        keywords: List[str],
        display_label: str,
    ) -> str:
        """Generate human-readable reasoning for the classification."""
        reasoning_parts = []

        reasoning_parts.append(
            f"Classified as {practice_area.value.replace('_', ' ').title()} ({display_label})"
        )

        if case_type != "other":
            reasoning_parts.append(f"Type: {case_type.replace('_', ' ').title()}")

        if keywords:
            reasoning_parts.append(f"Based on keywords: {', '.join(keywords)}")

        reasoning_parts.append(f"Urgency level: {urgency.value.title()}")

        return ". ".join(reasoning_parts) + "."

    def _default_classification(self) -> ClassificationResult:
        """Return default classification when no description is provided."""
        return ClassificationResult(
            practice_area=PracticeArea.PERSONAL_INJURY,
            work_type=WorkType.LITIGATION,
            case_type=PersonalInjuryCaseType.OTHER,
            urgency=CaseUrgency.LOW,
            confidence=0.1,
            reasoning="Default classification due to insufficient information (Case).",
            keywords_matched=[],
            display_label="Case",
        )
