"""
Task CRUD Agent Implementation

This module implements the Task CRUD Agent, which is responsible for:
1. Creating new tasks with title, description, due date, priority, and assignee
2. Reading tasks with filtering by status, date range, and assignee
3. Updating task properties including status transitions
4. Deleting tasks with proper validation

The Task CRUD Agent serves as an interactive agent for task management operations,
providing a natural language interface for task-related operations.

Usage:
    from backend.agents.interactive.task_crud.agent import TaskCrudAgent

    # Create a task crud agent
    agent = TaskCrudAgent()

    # Execute the agent
    result = await agent.invoke(
        {
            "messages": [
                HumanMessage(content="Create a new task to review the contract")
            ]
        },
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.task_crud.config import (
    TaskCrudAgentConfig,
    get_task_crud_agent_config,
)
from backend.agents.shared.core.base_agent import BaseAgent
from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)

# Define constants


class TaskCrudAgent(BaseAgent):
    """
    Task CRUD Agent for managing tasks.

    This agent handles creating, reading, updating, and deleting tasks
    through natural language interaction.
    """

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        tool_executor=None,
        db_client=None,
    ):
        """
        Initialize the task CRUD agent.

        Args:
            config: Configuration dictionary for the agent
            tool_executor: Tool executor for executing tools
            db_client: Database client for database operations
        """
        # Default configuration
        default_config = {
            "name": "task_crud_agent",
            "description": "Task CRUD Agent for managing task operations",
            "version": "0.1.0",
            "tools": ["create_task", "read_task", "update_task", "delete_task"],
            "model": "gpt-4",
            "temperature": 0.1,
        }

        # Merge provided config with defaults
        final_config = {**default_config}
        if config:
            final_config.update(config.dict() if hasattr(config, "dict") else config)

        super().__init__(
            agent_type="task_crud", agent_name="TaskCrudAgent", config=final_config
        )
        self.tool_executor = tool_executor or get_tool_executor()
        self.db_client = db_client

        # Set up nodes
        self.nodes = {
            "create_task": None,
            "read_task": None,
            "update_task": None,
            "delete_task": None,
            "parse_date": None,
        }

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Initialize the task CRUD agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Initializing task CRUD agent")

        # Extract configurable values
        configurable = config.get("configurable", {})
        tenant_id = configurable.get("tenant_id", "unknown")

        # Initialize state if needed
        if "operation" not in state:
            state["operation"] = None

        if "task_id" not in state:
            state["task_id"] = None

        if "task_data" not in state:
            state["task_data"] = {}

        if "task_filter" not in state:
            state["task_filter"] = {}

        if "tasks" not in state:
            state["tasks"] = []

        if "current_task" not in state:
            state["current_task"] = None

        if "parsed_date" not in state:
            state["parsed_date"] = None

        if "error" not in state:
            state["error"] = None

        # Initialize memory
        if "memory" in state:
            state["memory"]["initialized_at"] = datetime.now(timezone.utc).isoformat()
            state["memory"]["agent_name"] = self.name
            state["memory"]["agent_type"] = self.agent_type
            state["memory"]["agent_version"] = self.version

        logger.info(f"Task CRUD agent initialized for tenant {tenant_id}")
        return state

    async def execute(
        self, state: Dict[str, Any], _config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Execute the task CRUD agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Executing task CRUD agent")

        # If there's an error, add it to the messages
        if state.get("error"):
            state["messages"].append(
                {
                    "role": "assistant",
                    "content": (
                        f"I encountered an error: {state['error']}. Please try again."
                    ),
                    "metadata": {"error": state["error"]},
                }
            )
            state["error"] = None
            state["next"] = "FINISH"
            return state

        # If there's no operation, route to the appropriate node
        if not state.get("operation"):
            # This will be handled by the router node
            state["next"] = "taskCrudRouter"
            return state

        # If there's an operation but no next node, set it based on the operation
        if not state.get("next"):
            operation = state.get("operation")
            if operation == "create":
                state["next"] = "create_task"
            elif operation == "read" or operation == "list":
                state["next"] = "read_task"
            elif operation == "update":
                state["next"] = "update_task"
            elif operation == "delete":
                state["next"] = "delete_task"
            else:
                # Default to FINISH if we can't determine the next node
                state["next"] = "FINISH"

        logger.info(
            f"Task CRUD agent execution complete, next node: {state.get('next')}"
        )
        return state

    async def cleanup(
        self, state: Dict[str, Any], _config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Clean up the task CRUD agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Cleaning up task CRUD agent")

        # If there's no next node, set it to FINISH
        if not state.get("next"):
            state["next"] = "FINISH"

        return state

    def create_graph(self):
        """
        Create a LangGraph StateGraph for the TaskCrudAgent.

        Returns:
            Compiled StateGraph
        """
        from typing import Dict, Any, TypedDict
        from langgraph.graph import StateGraph

        # Create a simple state schema for task operations
        class TaskCrudState(TypedDict):
            messages: list
            tenant_id: str
            user_id: str
            thread_id: str
            status: str
            task_operation: str
            task_data: dict

        # Create the graph
        graph = StateGraph(TaskCrudState)

        # Add the agent as a node
        graph.add_node(self.agent_name, self)

        # Set entry point
        graph.set_entry_point(self.agent_name)

        # Set finish point
        graph.set_finish_point(self.agent_name)

        return graph.compile()

    async def _parse_date(
        self, date_string: str, _state: Dict[str, Any], config: RunnableConfig
    ) -> Optional[str]:
        """
        Parse a date string into a standardized format.

        Args:
            date_string: The date string to parse
            state: Current state
            config: Runnable configuration

        Returns:
            The parsed date in ISO format, or None if parsing failed
        """
        logger.info(f"Parsing date: {date_string}")

        # Extract configurable values
        configurable = config.get("configurable", {})
        tenant_id = configurable.get("tenant_id", "unknown")

        try:
            parsed_date = await self.tool_executor.execute_tool(
                tool_name="parse_date",
                tool_args={"date_string": date_string},
                tenant_id=tenant_id,
            )

            if parsed_date:
                logger.info(f"Parsed date: {parsed_date}")
                return parsed_date
            else:
                logger.warning(f"Failed to parse date: {date_string}")
                return None
        except Exception as e:
            logger.error(f"Error parsing date: {str(e)}")
            return None
