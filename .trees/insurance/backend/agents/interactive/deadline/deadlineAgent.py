"""
Interactive Deadline Agent for CopilotKit Integration

This agent handles quick deadline calculation requests by integrating with the MCP Rules Engine.
It processes user requests, calls the deadlines tool, and emits structured responses for AG-UI.

This is the Python equivalent of the TypeScript deadlineAgent.ts file.
"""

import os
import re
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig

from .tools.deadlinesTool import (
    DeadlinesInput,
    DeadlinesOutput,
    DeadlineItem,
    deadlines_tool,
)

# Set up logging
logger = logging.getLogger(__name__)


class DeadlineAgentState:
    """State interface for the deadline agent."""

    def __init__(
        self,
        messages: List[Any] = None,
        deadlinesResult: Optional[DeadlinesOutput] = None,
        error: Optional[str] = None,
    ):
        self.messages = messages or []
        self.deadlinesResult = deadlinesResult
        self.error = error


def extract_deadline_parameters(message: str) -> Dict[str, Any]:
    """Extract deadline parameters from user message."""
    params = {}

    # Extract jurisdiction (common patterns)
    jurisdiction_patterns = [
        (
            r"(?:jurisdiction|state|in)\s+([A-Z]{2}(?:_STATE)?)",
            lambda m: m.group(1).upper(),
        ),
        (r"(?:texas|tx)", lambda m: "TX_STATE"),
        (r"(?:california|ca)", lambda m: "CA_STATE"),
        (r"(?:new york|ny)", lambda m: "NY_STATE"),
        (r"(?:florida|fl)", lambda m: "FL_STATE"),
    ]

    for pattern, extractor in jurisdiction_patterns:
        match = re.search(pattern, message, re.IGNORECASE)
        if match:
            params["jurisdiction"] = extractor(match)
            break

    # Extract trigger code (common patterns)
    trigger_patterns = [
        (r"service of process", "SERVICE_OF_PROCESS"),
        (r"complaint filed", "COMPLAINT_FILED"),
        (r"discovery request", "DISCOVERY_REQUEST"),
        (r"deposition notice", "DEPOSITION_NOTICE"),
        (r"motion filed", "MOTION_FILED"),
        (r"accident date", "ACCIDENT_DATE"),
        (r"injury date", "INJURY_DATE"),
    ]

    for pattern, trigger_code in trigger_patterns:
        if re.search(pattern, message, re.IGNORECASE):
            params["triggerCode"] = trigger_code
            break

    # Extract date (YYYY-MM-DD format)
    date_match = re.search(r"(\d{4}-\d{2}-\d{2})", message)
    if date_match:
        params["startDate"] = date_match.group(1)

    # Extract practice area
    if re.search(
        r"personal injury|PI|auto accident|slip and fall", message, re.IGNORECASE
    ):
        params["practiceArea"] = "personal_injury"
    elif re.search(r"medical malpractice|malpractice", message, re.IGNORECASE):
        params["practiceArea"] = "medical_malpractice"
    elif re.search(r"workers compensation|workers comp", message, re.IGNORECASE):
        params["practiceArea"] = "workers_compensation"

    return params


def validate_deadline_parameters(params: Dict[str, Any]) -> Optional[DeadlinesInput]:
    """Validate and complete deadline parameters."""
    # Set defaults
    defaults = {
        "jurisdiction": "TX_STATE",
        "triggerCode": "SERVICE_OF_PROCESS",
        "startDate": datetime.now().strftime("%Y-%m-%d"),
        "practiceArea": "personal_injury",
    }

    # Merge with extracted params
    result = {**defaults, **params}

    # Validate required fields
    if not all(
        [result.get("jurisdiction"), result.get("triggerCode"), result.get("startDate")]
    ):
        return None

    # Validate date format
    if not re.match(r"^\d{4}-\d{2}-\d{2}$", result["startDate"]):
        return None

    return DeadlinesInput(**result)


async def deadlineAgent(
    state: DeadlineAgentState, config: RunnableConfig
) -> Dict[str, Any]:
    """
    Main deadline agent function.

    Args:
        state: Current agent state
        config: Runnable configuration

    Returns:
        Updated state with deadline results
    """
    try:
        logger.info("DeadlineAgent: Processing request")

        # Check feature flag first
        if os.getenv("FEATURE_MCP_RULES_ENGINE") != "true":
            logger.info(
                "DeadlineAgent: MCP Rules Engine feature flag is disabled, returning empty result"
            )

            fallback_result = DeadlinesOutput(
                deadlines=[],
                jurisdiction="TX_STATE",
                triggerCode="SERVICE_OF_PROCESS",
                startDate=datetime.now().strftime("%Y-%m-%d"),
                practiceArea="personal_injury",
                calculatedAt=datetime.now().isoformat(),
                source="feature_disabled",
            )

            response_message = AIMessage(
                content='{"role": "deadline_results", "data": {}, "message": "Deadline calculation is currently unavailable."}',
                additional_kwargs={
                    "role": "deadline_results",
                    "deadlines": [],
                    "metadata": fallback_result.__dict__,
                },
            )

            return {
                "messages": [response_message],
                "deadlinesResult": fallback_result,
            }

        # Get the latest user message
        if not state.messages:
            raise ValueError("No messages found in state")

        last_message = state.messages[-1]
        if not isinstance(last_message, HumanMessage):
            raise ValueError("No user message found")

        user_message = last_message.content
        logger.info(f"DeadlineAgent: User message: {user_message}")

        # Extract parameters from user message
        extracted_params = extract_deadline_parameters(user_message)
        logger.info(f"DeadlineAgent: Extracted parameters: {extracted_params}")

        # Validate and complete parameters
        valid_params = validate_deadline_parameters(extracted_params)
        if not valid_params:
            raise ValueError("Could not extract valid deadline parameters from message")

        logger.info(f"DeadlineAgent: Valid parameters: {valid_params}")

        # Call the deadlines tool
        deadlines_result = await deadlines_tool.invoke(valid_params)
        logger.info(f"DeadlineAgent: Tool result: {deadlines_result}")

        # Create response message
        response_content = {
            "role": "deadline_results",
            "data": deadlines_result.__dict__,
            "message": f"Found {len(deadlines_result.deadlines)} deadlines for {valid_params.jurisdiction} jurisdiction",
        }

        response_message = AIMessage(
            content=str(response_content),
            additional_kwargs={
                "role": "deadline_results",
                "deadlines": [d.__dict__ for d in deadlines_result.deadlines],
                "metadata": {
                    "jurisdiction": deadlines_result.jurisdiction,
                    "triggerCode": deadlines_result.triggerCode,
                    "startDate": deadlines_result.startDate,
                    "practiceArea": deadlines_result.practiceArea,
                    "calculatedAt": deadlines_result.calculatedAt,
                    "source": deadlines_result.source,
                },
            },
        )

        logger.info("DeadlineAgent: Response created")

        return {
            "messages": [response_message],
            "deadlinesResult": deadlines_result,
        }

    except Exception as error:
        logger.error(f"DeadlineAgent: Error processing request: {error}")

        error_message = AIMessage(
            content='{"role": "deadline_error", "error": "'
            + str(error)
            + '", "message": "Sorry, I encountered an error while calculating deadlines. Please try again with more specific information."}',
        )

        return {
            "messages": [error_message],
            "error": str(error),
        }


def shouldTriggerDeadlineAgent(message: str) -> bool:
    """Check if message should trigger deadline agent."""
    triggers = [
        r"deadline",
        r"due date",
        r"statute of limitations",
        r"filing deadline",
        r"discovery deadline",
        r"court deadline",
        r"legal deadline",
        r"time limit",
        r"when is.*due",
        r"calculate.*deadline",
        r"show.*deadline",
        r"find.*deadline",
    ]

    return any(re.search(trigger, message, re.IGNORECASE) for trigger in triggers)
