"""
Tests for Master Router

This module contains comprehensive tests for the master router functionality,
including intent detection, routing logic, and integration with the calendar graph.
"""

from unittest.mock import MagicMock, patch

import pytest
from langchain_core.messages import HumanMessage

# Mock the problematic imports before importing the master router
with patch.dict(
    "sys.modules",
    {
        "backend.agents.interactive.calendar_crud.graph": <PERSON><PERSON><PERSON>(),
        "backend.agents.interactive.calendar_crud.agent": <PERSON><PERSON>ock(),
        "backend.agents.interactive.calendar_crud.nodes": <PERSON><PERSON>ock(),
        "pi_lawyer.agents.base_agent": Magic<PERSON>ock(),
        "pi_lawyer.agents.interactive": MagicMock(),
    },
):
    from backend.agents.interactive.master_router import (
        _is_calendar_intent,
        _is_document_intent,
        _is_deadline_intent,
        _enhanced_keyword_detection,
        enhanced_parallel_master_router,
        get_workflow_info,
        master_router,
    )


class TestMasterRouter:
    """Test cases for the master router function."""

    @pytest.mark.asyncio
    async def test_master_router_calendar_intent(self):
        """Test master router with calendar intent."""
        # Arrange
        state = {
            "messages": [
                HumanMessage(content="Schedule a meeting with <PERSON> tomorrow at 2pm")
            ],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "calendar_graph"}

    @pytest.mark.asyncio
    async def test_master_router_document_intent(self):
        """Test master router with document intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="Draft a demand letter for my client")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "document_agent"}

    @pytest.mark.asyncio
    async def test_master_router_deadline_intent(self):
        """Test master router with deadline intent."""
        # Arrange
        state = {
            "messages": [
                HumanMessage(content="Check the statute of limitations for this case")
            ],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "deadline_agent"}

    @pytest.mark.asyncio
    async def test_master_router_case_intent(self):
        """Test master router with case intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="case.create new personal injury case")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "matter_client_agent"}

    @pytest.mark.asyncio
    async def test_master_router_client_intent(self):
        """Test master router with client intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="client.update contact information")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "matter_client_agent"}

    @pytest.mark.asyncio
    async def test_master_router_task_intent(self):
        """Test master router with task intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="task.create follow up with client")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "task_graph"}

    @pytest.mark.asyncio
    async def test_master_router_research_intent(self):
        """Test master router with research intent."""
        # Arrange
        state = {
            "messages": [
                HumanMessage(content="Research case law on personal injury claims")
            ],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "research_agent"}

    @pytest.mark.asyncio
    async def test_master_router_intake_intent(self):
        """Test master router with intake intent."""
        # Arrange
        state = {
            "messages": [
                HumanMessage(content="New client intake for car accident case")
            ],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "intake_agent"}

    @pytest.mark.asyncio
    async def test_master_router_no_messages(self):
        """Test master router with no messages."""
        # Arrange
        state = {
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_empty_messages(self):
        """Test master router with empty messages list."""
        # Arrange
        state = {
            "messages": [],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_no_user_input(self):
        """Test master router with no user input in messages."""
        # Arrange
        state = {
            "messages": [{"type": "system", "content": "System message"}],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_unknown_intent(self):
        """Test master router with unknown intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="Hello, how are you?")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_dict_message_format(self):
        """Test master router with dictionary message format."""
        # Arrange
        state = {
            "messages": [{"type": "human", "content": "Schedule a meeting tomorrow"}],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}

        # Act
        result = await master_router(state, config)

        # Assert
        assert result == {"next": "calendar_graph"}


class TestCalendarIntentDetection:
    """Test cases for calendar intent detection."""

    def test_calendar_intent_schedule_keywords(self):
        """Test calendar intent detection with schedule keywords."""
        test_cases = [
            "Schedule a meeting with John",
            "Book an appointment for tomorrow",
            "Create meeting for next week",
            "Set appointment with client",
            "Add event to calendar",
            "Plan meeting for Friday",
            "Arrange a conference call",
            "Organize meeting with team",
        ]

        for test_case in test_cases:
            assert _is_calendar_intent(
                test_case
            ), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_view_keywords(self):
        """Test calendar intent detection with view keywords."""
        test_cases = [
            "Show my calendar",
            "View calendar for next week",
            "Check my schedule",
            "What's on my calendar today?",
            "Calendar events for tomorrow",
            "Upcoming meetings",
            "Today's schedule",
            "This week's schedule",
        ]

        for test_case in test_cases:
            assert _is_calendar_intent(
                test_case
            ), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_update_keywords(self):
        """Test calendar intent detection with update keywords."""
        test_cases = [
            "Reschedule the meeting",
            "Move meeting to tomorrow",
            "Change appointment time",
            "Update event details",
            "Modify meeting time",
            "Edit appointment",
        ]

        for test_case in test_cases:
            assert _is_calendar_intent(
                test_case
            ), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_delete_keywords(self):
        """Test calendar intent detection with delete keywords."""
        test_cases = [
            "Cancel the meeting",
            "Delete event from calendar",
            "Remove appointment",
            "Cancel appointment with client",
        ]

        for test_case in test_cases:
            assert _is_calendar_intent(
                test_case
            ), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_availability_keywords(self):
        """Test calendar intent detection with availability keywords."""
        test_cases = [
            "When am I free tomorrow?",
            "Available time next week",
            "Check my free time",
            "When can we meet?",
            "Find time for meeting",
            "Check availability",
        ]

        for test_case in test_cases:
            assert _is_calendar_intent(
                test_case
            ), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_explicit_namespace(self):
        """Test calendar intent detection with explicit namespace."""
        test_cases = [
            "calendar.create new event",
            "calendar.list events",
            "calendar.update meeting time",
            "calendar.delete appointment",
        ]

        for test_case in test_cases:
            assert _is_calendar_intent(
                test_case
            ), f"Failed to detect calendar intent in: {test_case}"

    def test_non_calendar_intent(self):
        """Test that non-calendar intents are not detected as calendar intents."""
        test_cases = [
            "Hello, how are you?",
            "Research case law",
            "Create a new case",
            "Update client information",
            "Draft a document",
            "What is the weather like?",
            "task.create follow up",
        ]

        for test_case in test_cases:
            assert not _is_calendar_intent(
                test_case
            ), f"Incorrectly detected calendar intent in: {test_case}"


class TestMasterGraphCreation:
    """Test cases for master graph creation."""

    def test_get_workflow_info(self):
        """Test workflow info retrieval."""
        # Act
        info = get_workflow_info()

        # Assert
        assert info["name"] == "master_routing_workflow"
        assert info["version"] == "1.0.0"
        assert "master_router" in info["nodes"]
        assert "calendar_graph" in info["nodes"]
        assert "task_graph" in info["nodes"]
        assert "document_agent" in info["nodes"]
        assert "deadline_agent" in info["nodes"]
        assert info["entry_point"] == "master_router"
        assert "Intent detection and routing" in info["capabilities"]
        assert "Document generation routing" in info["capabilities"]
        assert "Deadline tracking routing" in info["capabilities"]
        assert "calendar.*" in info["supported_intents"]
        assert "document.*" in info["supported_intents"]
        assert "deadline.*" in info["supported_intents"]


class TestDocumentIntentDetection:
    """Test cases for document intent detection."""

    def test_document_intent_creation_keywords(self):
        """Test document intent detection with creation keywords."""
        test_cases = [
            "Create a demand letter",
            "Generate a settlement agreement",
            "Draft a court filing",
            "Write a legal brief",
            "New document for client",
            "Make a contract",
            "Compose a motion",
            "Prepare a pleading",
        ]

        for test_case in test_cases:
            assert _is_document_intent(
                test_case
            ), f"Failed to detect document intent in: {test_case}"

    def test_document_intent_document_types(self):
        """Test document intent detection with specific document types."""
        test_cases = [
            "I need a demand letter",
            "Create settlement agreement",
            "Draft court filing",
            "Generate legal brief",
            "Write a contract",
            "Prepare an agreement",
            "Create a motion",
            "Draft a pleading",
            "Generate complaint",
            "Write an answer",
            "Create discovery request",
            "Draft deposition notice",
            "Generate subpoena",
        ]

        for test_case in test_cases:
            assert _is_document_intent(
                test_case
            ), f"Failed to detect document intent in: {test_case}"

    def test_document_intent_actions(self):
        """Test document intent detection with document actions."""
        test_cases = [
            "Draft letter for the client",
            "Write a legal document",
            "Compose a formal letter",
            "Generate legal agreement",
            "Create legal document",
            "Draft court filing",
            "Edit document content",
            "Update the agreement",
            "Revise the contract",
            "Modify the document",
            "Review document content",
            "Check the document",
            "Proofread document",
        ]

        for test_case in test_cases:
            assert _is_document_intent(
                test_case
            ), f"Failed to detect document intent in: {test_case}"

    def test_document_intent_explicit_namespace(self):
        """Test document intent detection with explicit namespace."""
        test_cases = [
            "document.create new letter",
            "document.generate agreement",
            "document.draft filing",
            "document.edit contract",
        ]

        for test_case in test_cases:
            assert _is_document_intent(
                test_case
            ), f"Failed to detect document intent in: {test_case}"

    def test_document_intent_negative_cases(self):
        """Test that non-document intents are not detected as document intents."""
        test_cases = [
            "Schedule a meeting",
            "Create a task",
            "Research case law",
            "Check calendar",
            "Update client information",
            "Call the client",
            "Review emails",
        ]

        for test_case in test_cases:
            assert not _is_document_intent(
                test_case
            ), f"Incorrectly detected document intent in: {test_case}"


class TestDeadlineIntentDetection:
    """Test cases for deadline intent detection."""

    def test_deadline_intent_tracking_keywords(self):
        """Test deadline intent detection with tracking keywords."""
        test_cases = [
            "Check deadlines for this case",
            "What are the upcoming deadlines?",
            "Show me the statute of limitations",
            "When is the filing deadline?",
            "Check court deadlines",
            "Review discovery deadlines",
            "What's the response deadline?",
            "Track deadline for motion",
            "Monitor case deadlines",
        ]

        for test_case in test_cases:
            assert _is_deadline_intent(
                test_case
            ), f"Failed to detect deadline intent in: {test_case}"

    def test_deadline_intent_calculation_keywords(self):
        """Test deadline intent detection with calculation keywords."""
        test_cases = [
            "Calculate the statute of limitations",
            "Determine filing deadline",
            "Compute response time",
            "When is this due?",
            "Calculate deadline for appeal",
            "Determine limitation period",
            "When does the statute expire?",
            "Calculate discovery cutoff",
        ]

        for test_case in test_cases:
            assert _is_deadline_intent(
                test_case
            ), f"Failed to detect deadline intent in: {test_case}"

    def test_deadline_intent_legal_deadlines(self):
        """Test deadline intent detection with legal deadline terms."""
        test_cases = [
            "What's the statute of limitations?",
            "Check limitation period",
            "Review time limits",
            "Filing period deadline",
            "Response time requirements",
            "Discovery cutoff date",
            "Trial date approaching",
            "Hearing date deadline",
            "Motion deadline check",
            "Appeal deadline calculation",
            "Settlement deadline review",
        ]

        for test_case in test_cases:
            assert _is_deadline_intent(
                test_case
            ), f"Failed to detect deadline intent in: {test_case}"

    def test_deadline_intent_explicit_namespace(self):
        """Test deadline intent detection with explicit namespace."""
        test_cases = [
            "deadline.check upcoming",
            "deadline.calculate statute",
            "deadline.track court dates",
            "deadline.monitor case",
        ]

        for test_case in test_cases:
            assert _is_deadline_intent(
                test_case
            ), f"Failed to detect deadline intent in: {test_case}"

    def test_deadline_intent_negative_cases(self):
        """Test that non-deadline intents are not detected as deadline intents."""
        test_cases = [
            "Schedule a meeting",
            "Create a document",
            "Research case law",
            "Update task status",
            "Call the client",
            "Review contract terms",
            "Draft a letter",
        ]

        for test_case in test_cases:
            assert not _is_deadline_intent(
                test_case
            ), f"Incorrectly detected deadline intent in: {test_case}"


class TestEnhancedKeywordDetection:
    """Test cases for enhanced keyword detection with confidence scoring."""

    def test_enhanced_keyword_detection_high_confidence(self):
        """Test enhanced keyword detection with high-confidence patterns."""
        test_cases = [
            ("draft demand letter", "document_agent", 0.95),
            ("calculate deadline", "deadline_agent", 0.90),
            ("schedule meeting", "calendar_graph", 0.95),
            ("create task", "task_graph", 0.90),
        ]

        for user_input, expected_agent, min_confidence in test_cases:
            result = _enhanced_keyword_detection(user_input)
            assert (
                result.agent == expected_agent
            ), f"Expected {expected_agent}, got {result.agent} for: {user_input}"
            assert (
                result.confidence_score >= min_confidence
            ), f"Expected confidence >= {min_confidence}, got {result.confidence_score} for: {user_input}"

    def test_enhanced_keyword_detection_medium_confidence(self):
        """Test enhanced keyword detection with medium-confidence patterns."""
        test_cases = [
            ("document", "document_agent"),
            ("deadline", "deadline_agent"),
            ("schedule", "calendar_graph"),
            ("task", "task_graph"),
        ]

        for user_input, expected_agent in test_cases:
            result = _enhanced_keyword_detection(user_input)
            assert (
                result.agent == expected_agent
            ), f"Expected {expected_agent}, got {result.agent} for: {user_input}"
            assert (
                0.6 <= result.confidence_score <= 0.8
            ), f"Expected medium confidence (0.6-0.8), got {result.confidence_score} for: {user_input}"

    def test_enhanced_keyword_detection_no_match(self):
        """Test enhanced keyword detection with no matches."""
        test_cases = [
            "hello world",
            "how are you",
            "random text",
        ]

        for user_input in test_cases:
            result = _enhanced_keyword_detection(user_input)
            assert (
                result.agent == "supervisor_agent"
            ), f"Expected supervisor_agent, got {result.agent} for: {user_input}"
            assert (
                result.confidence_score == 0.0
            ), f"Expected confidence 0.0, got {result.confidence_score} for: {user_input}"

    def test_enhanced_keyword_detection_pattern_weights(self):
        """Test that pattern weights are correctly recorded."""
        result = _enhanced_keyword_detection("draft demand letter")
        assert result.matched_patterns, "Expected matched patterns to be recorded"
        assert result.pattern_weights, "Expected pattern weights to be recorded"
        assert len(result.matched_patterns) > 0, "Expected at least one matched pattern"
