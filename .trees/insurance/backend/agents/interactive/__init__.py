"""
Interactive Agents Package

This package contains the interactive agent implementations for the AiLex system.
These agents are designed for quick response times (< 5 seconds) and direct user
interaction. They are used in chat-based interfaces.

Agents in this package include:
- Intake Agent: Multi-practice intake agent for Personal Injury, Family Law, and Criminal Defense
- Research Agent: Handles quick research queries and queues longer research tasks
- Task CRUD Agent: Creates, reads, updates, and deletes tasks
- Calendar CRUD Agent: Manages calendar events with Google/Outlook integration
- Case & Client CRUD Agent: Manages case and client information
"""

# Import the consolidated intake agent
try:
    from .intake import (
        IntakeAgent,
        intake_router,
        intake_client_router,
        intake_staff_router,
        IntakeState,
        PracticeArea,
        CaseUrgency,
        MultiPracticeCaseClassifier,
        MultiPracticeConflictChecker,
    )

    __all__ = [
        "IntakeAgent",
        "intake_router",
        "intake_client_router",
        "intake_staff_router",
        "IntakeState",
        "PracticeArea",
        "CaseUrgency",
        "MultiPracticeCaseClassifier",
        "MultiPracticeConflictChecker",
    ]

except ImportError as e:
    print(f"Warning: Could not import intake agent: {e}")
    __all__ = []
