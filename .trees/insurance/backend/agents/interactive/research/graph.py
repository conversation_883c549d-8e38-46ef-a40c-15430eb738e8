"""
AiLex Research Agent Graph

This module defines the LangGraph workflow for the AiLex research agent.
It connects the various nodes defined in the nodes.py module to create
a complete research workflow.

The graph supports multiple research paths:
1. Legal research path: Uses vector search, graph expansion, and reranking
2. Web search path: Uses web search as an alternative to legal database search
3. Long research path: Queues extensive research tasks for asynchronous processing

Each path converges at the answer generation stage to provide a consistent
user experience regardless of the research approach.
"""

import logging

from langgraph.graph import END, StateGraph

from backend.agents.interactive.research.node_classes import (
    ExitGuardNode,
    QueryGenNode,
    RerankNode,
)
from backend.agents.interactive.research.nodes import (
    adaptive_classify_query,
    collect_citations,
    enhanced_graph_expand,
    handle_error,
    hybrid_vector_search,
    llm_answer,
    long_research_queue,
    process_case_documents,
    route_by_query_type,
    temporal_cached_retrieval,
    web_citation_mapper,
    web_search,
)
from backend.agents.interactive.research.agentic_wrapper import agentic_research_wrapper
from backend.agents.interactive.research.state import (
    ResearchState,
)

# Configure logger
logger = logging.getLogger(__name__)


def create_research_graph() -> StateGraph:
    """
    Create the enhanced research workflow graph with agentic wrapper.

    This function builds a LangGraph StateGraph that implements the research workflow
    with the agentic wrapper as the entry point for enhanced quality and self-correction.
    The agentic wrapper orchestrates the entire research process with evaluation and
    iterative refinement capabilities.

    Returns:
        StateGraph: Compiled research workflow graph
    """
    # Create the graph with ResearchState as the state type
    workflow = StateGraph(ResearchState)

    # Add the agentic wrapper as the main entry point
    workflow.add_node("agentic_research", agentic_research_wrapper)

    # Add nodes for query classification and routing (used by base pipeline)
    workflow.add_node("classify_query", adaptive_classify_query)
    workflow.add_node("route_query", route_by_query_type)

    # Create node instances
    query_gen = QueryGenNode(agent_name="researchAgent", node_name="query_gen")
    rerank_node = RerankNode(agent_name="researchAgent", node_name="rerank")
    exit_guard_node = ExitGuardNode(agent_name="researchAgent", node_name="exit_guard")

    # Add nodes for legal research path
    workflow.add_node("gpt_query_gen", query_gen)
    workflow.add_node(
        "vector_search", temporal_cached_retrieval
    )  # Use temporal-aware cached retrieval
    workflow.add_node("process_case_documents", process_case_documents)
    workflow.add_node("graph_expand", enhanced_graph_expand)
    workflow.add_node("rerank", rerank_node)
    workflow.add_node("collect_citations", collect_citations)

    # Add nodes for web search path
    workflow.add_node("web_search", web_search)
    workflow.add_node("web_citation_mapper", web_citation_mapper)

    # Add nodes for answer generation and validation
    workflow.add_node("llm_answer", llm_answer)
    workflow.add_node("exit_guard", exit_guard_node)

    # Add node for long research path
    workflow.add_node("long_research_queue", long_research_queue)

    # Add error handling node
    workflow.add_node("error", handle_error)

    # Define edges for query classification and routing
    workflow.add_edge("classify_query", "route_query")

    # Define conditional edges for routing based on query type
    workflow.add_conditional_edges(
        "route_query",
        lambda state: {
            "legal_research": "gpt_query_gen",
            "web_search": "web_search",
            "deep_research": "long_research_queue",
            "non_legal": "web_search",
            # Default to legal research
            None: "gpt_query_gen",
        }.get(state.query_type, "gpt_query_gen"),
    )

    # Define edges for legal research path
    workflow.add_edge("gpt_query_gen", "vector_search")
    workflow.add_edge("vector_search", "process_case_documents")
    workflow.add_edge("process_case_documents", "graph_expand")
    workflow.add_edge("graph_expand", "rerank")
    workflow.add_edge("rerank", "collect_citations")

    # Define edges for web search path
    workflow.add_edge("web_search", "web_citation_mapper")
    workflow.add_edge("web_citation_mapper", "llm_answer")

    # Define common edges for answer generation and validation
    workflow.add_edge("collect_citations", "llm_answer")
    workflow.add_edge("llm_answer", "exit_guard")
    workflow.add_edge("exit_guard", END)

    # Define edge for long research path
    workflow.add_edge("long_research_queue", END)

    # Define edge for error handling
    workflow.add_edge("error", END)

    # Set entry point to agentic wrapper
    workflow.set_entry_point("agentic_research")

    # Add direct edge from agentic wrapper to end (it handles everything internally)
    workflow.add_edge("agentic_research", END)

    # Compile the graph
    return workflow.compile()


def create_base_research_graph() -> StateGraph:
    """
    Create the base research workflow graph for use by the agentic wrapper.

    This function builds the traditional research workflow without the agentic wrapper,
    for use as the base pipeline within the agentic execution loop.

    Returns:
        StateGraph: Compiled base research workflow graph
    """
    # Create the graph with ResearchState as the state type
    workflow = StateGraph(ResearchState)

    # Add nodes for query classification and routing
    workflow.add_node("classify_query", adaptive_classify_query)
    workflow.add_node("route_query", route_by_query_type)

    # Create node instances
    query_gen = QueryGenNode(agent_name="researchAgent", node_name="query_gen")
    rerank_node = RerankNode(agent_name="researchAgent", node_name="rerank")
    exit_guard_node = ExitGuardNode(agent_name="researchAgent", node_name="exit_guard")

    # Add nodes for legal research path
    workflow.add_node("gpt_query_gen", query_gen)
    workflow.add_node(
        "vector_search", temporal_cached_retrieval
    )  # Use temporal-aware cached retrieval
    workflow.add_node("process_case_documents", process_case_documents)
    workflow.add_node("graph_expand", enhanced_graph_expand)
    workflow.add_node("rerank", rerank_node)
    workflow.add_node("collect_citations", collect_citations)

    # Add nodes for web search path
    workflow.add_node("web_search", web_search)
    workflow.add_node("web_citation_mapper", web_citation_mapper)

    # Add nodes for answer generation and validation
    workflow.add_node("llm_answer", llm_answer)
    workflow.add_node("exit_guard", exit_guard_node)

    # Add node for long research path
    workflow.add_node("long_research_queue", long_research_queue)

    # Add error handling node
    workflow.add_node("error", handle_error)

    # Define edges for query classification and routing
    workflow.add_edge("classify_query", "route_query")

    # Define conditional edges for routing based on query type
    workflow.add_conditional_edges(
        "route_query",
        lambda state: {
            "legal_research": "gpt_query_gen",
            "web_search": "web_search",
            "deep_research": "long_research_queue",
            "non_legal": "web_search",
            # Default to legal research
            None: "gpt_query_gen",
        }.get(state.query_type, "gpt_query_gen"),
    )

    # Define edges for legal research path
    workflow.add_edge("gpt_query_gen", "vector_search")
    workflow.add_edge("vector_search", "process_case_documents")
    workflow.add_edge("process_case_documents", "graph_expand")
    workflow.add_edge("graph_expand", "rerank")
    workflow.add_edge("rerank", "collect_citations")

    # Define edges for web search path
    workflow.add_edge("web_search", "web_citation_mapper")
    workflow.add_edge("web_citation_mapper", "llm_answer")

    # Define common edges for answer generation and validation
    workflow.add_edge("collect_citations", "llm_answer")
    workflow.add_edge("llm_answer", "exit_guard")
    workflow.add_edge("exit_guard", END)

    # Define edge for long research path
    workflow.add_edge("long_research_queue", END)

    # Define edge for error handling
    workflow.add_edge("error", END)

    # Set entry point
    workflow.set_entry_point("classify_query")

    # Compile the graph
    return workflow.compile()


# Create and compile the graphs
graph = create_research_graph()  # Main graph with agentic wrapper
base_graph = create_base_research_graph()  # Base graph for agentic wrapper to use
