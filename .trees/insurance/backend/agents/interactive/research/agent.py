"""
AiLex Research Agent Implementation

This module provides the ResearchAgent class, which implements the research agent
for the AiLex system. The research agent handles quick research queries and queues
longer research tasks.

The agent uses a hybrid retrieval pipeline with:
- Query generation using LLMs
- Legal document search via laws-API service
- Graph expansion using laws-API knowledge graph
- Document recommendations via laws-API
- Web search as an alternative path

The agent supports multiple research paths:
1. Legal research path: Uses laws-API for search, recommendations, and graph expansion
2. Web search path: Uses web search as an alternative to legal database search
3. Long research path: Queues extensive research tasks for asynchronous processing
"""

import logging
from typing import Any, Dict, List, Optional

from pydantic import Field

# Note: graph import moved to avoid circular dependencies
from backend.agents.interactive.research.state import (
    ResearchState,
    UserContext,
)
from backend.agents.shared.core.base_agent import BaseAgent
from langchain_core.runnables import RunnableConfig

# Configure logger
logger = logging.getLogger(__name__)


# Default configuration for ResearchAgent
DEFAULT_RESEARCH_CONFIG = {
    "name": "research_agent",
    "description": "Research agent for legal research and web search",
    "version": "1.0.0",
    "tools": [
        "legal_search",
        "legal_recommend",
        "legal_graph",
        "web_search",
        "collect_citations",
    ],
    "model": "gpt-4o",
    "temperature": 0.3,
    "legal_search_provider": "laws_api",
    "reranking_provider": "voyage",
    "web_search_provider": "perplexity",
    "max_documents": 10,
    "max_web_results": 5,
}


class ResearchAgent(BaseAgent):
    """
    Research Agent for the AiLex system.

    This agent handles quick research queries and queues longer research tasks.
    It supports both legal database search and web search paths.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Research Agent.

        Args:
            config: Configuration dictionary for the agent
        """
        # Merge provided config with defaults
        final_config = {**DEFAULT_RESEARCH_CONFIG}
        if config:
            final_config.update(config)

        super().__init__(
            agent_type="research", agent_name="ResearchAgent", config=final_config
        )
        # Graph will be created when needed to avoid circular dependencies

    def _register_tools(self) -> None:
        """Register tools for the agent."""
        # Register legal search tool (replaces vector_search)
        self.register_tool("legal_search", self._legal_search)

        # Register legal recommendation tool
        self.register_tool("legal_recommend", self._legal_recommend)

        # Register legal graph tool (replaces graph_expand)
        self.register_tool("legal_graph", self._legal_graph)

        # Register web search tool
        self.register_tool("web_search", self._web_search)

        # Register citation collection tool
        self.register_tool("collect_citations", self._collect_citations)

    async def _legal_search(
        self,
        query: str,
        jurisdiction: str,
        practice_areas: List[str],
        state: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """
        Perform legal document search using laws-API.

        Args:
            query: The search query
            jurisdiction: The jurisdiction to search
            practice_areas: The practice areas to search
            state: The current state

        Returns:
            List of search results
        """
        from backend.services.laws_api_client import (
            LawsApiClient,
            SearchRequest,
            PracticeArea,
        )

        try:
            async with LawsApiClient() as client:
                search_request = SearchRequest(
                    query=query,
                    jurisdiction=[jurisdiction] if jurisdiction else ["texas"],
                    limit=self.config.max_documents,
                    filters=(
                        {
                            "practice_area": [
                                (
                                    PracticeArea.PERSONAL_INJURY.value
                                    if area.lower()
                                    in ["personal_injury", "personal injury", "pi"]
                                    else area.lower()
                                )
                                for area in practice_areas
                            ]
                        }
                        if practice_areas
                        else None
                    ),
                )

                results = await client.search(search_request)

                # Convert to dict format
                return [
                    {
                        "id": result.id,
                        "title": result.title,
                        "content": result.content,
                        "jurisdiction": result.jurisdiction,
                        "relevance_score": result.relevance_score,
                        "citation": result.citation,
                        "url": result.url,
                    }
                    for result in results
                ]
        except Exception as e:
            logger.error(f"Legal search error: {e}")
            return []

    async def _legal_recommend(
        self, content: str, document_id: Optional[str], state: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get legal document recommendations using laws-API.

        Args:
            content: Content to get recommendations for
            document_id: Optional document ID for recommendations
            state: The current state

        Returns:
            List of recommended documents
        """
        from backend.services.laws_api_client import LawsApiClient, RecommendRequest

        try:
            async with LawsApiClient() as client:
                recommend_request = RecommendRequest(
                    content=content,
                    document_id=document_id,
                    jurisdiction=["texas"],
                    limit=5,
                )

                results = await client.recommend(recommend_request)

                # Convert to dict format
                return [
                    {
                        "id": result.id,
                        "title": result.title,
                        "content": result.content,
                        "jurisdiction": result.jurisdiction,
                        "similarity_score": result.similarity_score,
                        "relationship_type": result.relationship_type.value,
                    }
                    for result in results
                ]
        except Exception as e:
            logger.error(f"Legal recommendation error: {e}")
            return []

    async def _legal_graph(
        self, entity_id: str, entity_type: Optional[str], state: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Query legal knowledge graph using laws-API.

        Args:
            entity_id: ID of the entity to explore
            entity_type: Type of entity
            state: The current state

        Returns:
            List of related entities and relationships
        """
        from backend.services.laws_api_client import (
            LawsApiClient,
            GraphRequest,
            EntityType,
        )

        try:
            async with LawsApiClient() as client:
                graph_request = GraphRequest(
                    entity_id=entity_id,
                    entity_type=EntityType(entity_type) if entity_type else None,
                    depth=2,
                    limit=20,
                )

                result = await client.graph_query(graph_request)

                # Convert to dict format
                return [
                    {
                        "nodes": [
                            {
                                "id": node.id,
                                "label": node.label,
                                "type": node.type.value,
                                "properties": node.properties,
                            }
                            for node in result.nodes
                        ],
                        "edges": [
                            {
                                "source": edge.source,
                                "target": edge.target,
                                "relationship": edge.relationship.value,
                                "weight": edge.weight,
                            }
                            for edge in result.edges
                        ],
                    }
                ]
        except Exception as e:
            logger.error(f"Legal graph query error: {e}")
            return []

    async def _web_search(
        self, query: str, state: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Perform web search as an alternative research path.

        Args:
            query: The search query
            state: The current state

        Returns:
            List of web search results
        """
        # Implementation would use Perplexity API or similar
        logger.info(f"Web search for query: {query}")
        return []

    async def _collect_citations(
        self, documents: List[Dict[str, Any]], state: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Collect and format citations from search results.

        Args:
            documents: List of documents to format as citations
            state: The current state

        Returns:
            List of formatted citations
        """
        logger.info(f"Collecting citations for {len(documents)} documents")
        return []

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Initialize the research agent.

        Args:
            state: Current state
            config: Runtime configuration

        Returns:
            Updated state
        """
        logger.info("Initializing ResearchAgent")

        # Set agent status
        state["status"] = "initializing"
        state["agent_type"] = "research"

        # Get the last user message as the research question
        messages = state.get("messages", [])
        user_messages = [msg for msg in messages if msg.get("role") == "user"]

        if not user_messages:
            state["messages"] = state.get("messages", []) + [
                {"role": "system", "content": "Please provide a research question."}
            ]
            state["status"] = "waiting_for_input"
            return state

        question = user_messages[-1]["content"]

        # Initialize research-specific state
        state["research"] = {
            "question": question,
            "search_results": [],
            "recommendations": [],
            "graph_results": [],
            "web_results": [],
            "citations": [],
            "research_summary": "",
            "confidence_score": 0.0,
            "next_steps": [],
        }

        state["status"] = "initialized"
        logger.info("ResearchAgent initialized successfully")

        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Execute the research agent.

        Args:
            state: Current state
            config: Runtime configuration

        Returns:
            Updated state
        """
        logger.info("Executing ResearchAgent")

        state["status"] = "executing"

        # Get research question and context
        research = state.get("research", {})
        question = research.get("question", "")

        if not question:
            state["messages"] = state.get("messages", []) + [
                {
                    "role": "assistant",
                    "content": "No research question found. Please provide a question to research.",
                }
            ]
            state["status"] = "error"
            return state

        try:
            # For now, provide a placeholder response
            # TODO: Implement actual research graph execution
            answer = f"Research results for: {question}\n\nThis is a placeholder response. The research functionality will be implemented with the actual research graph."

            # Add the answer to messages
            state["messages"] = state.get("messages", []) + [
                {
                    "role": "assistant",
                    "content": answer,
                    "metadata": {
                        "agent": "research",
                        "question": question,
                        "citations": [],
                        "confidence": 0.8,
                    },
                }
            ]

            # Update research state
            state["research"]["research_summary"] = answer
            state["research"]["confidence_score"] = 0.8
            state["status"] = "completed"

        except Exception as e:
            logger.error(f"Error executing research: {str(e)}")
            state["messages"] = state.get("messages", []) + [
                {
                    "role": "assistant",
                    "content": "I encountered an error while researching your question. Please try again later.",
                    "metadata": {"error": str(e)},
                }
            ]
            state["status"] = "error"

        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Clean up after research agent execution.

        Args:
            state: Current state
            config: Runtime configuration

        Returns:
            Updated state
        """
        logger.info("Cleaning up ResearchAgent")

        # Ensure final status is set
        if state.get("status") not in ["completed", "error"]:
            state["status"] = "completed"

        # Add completion timestamp
        import time

        state["completed_at"] = time.time()

        logger.info("ResearchAgent cleanup completed")

        return state
