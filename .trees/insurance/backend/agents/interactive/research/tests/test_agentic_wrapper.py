"""
Test suite for the AiLex Research Agent Agentic Wrapper

This module contains comprehensive tests for the agentic wrapper implementation,
including unit tests, integration tests, and ethical compliance tests.

Test Categories:
- Unit tests for individual components
- Integration tests for end-to-end workflows
- Async workflow tests
- Ethical compliance tests
- Performance and monitoring tests
- Error handling and edge cases
"""

import asyncio
import pytest
import time
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# Mark all tests in this module as integration tests
pytestmark = pytest.mark.integration

from backend.agents.interactive.research.state import ResearchState, UserContext
from backend.agents.interactive.research.agentic_wrapper import (
    AgenticResearchWrapper,
    AgenticExecutionResult,
    AgenticIteration,
    agentic_research_wrapper,
)
from backend.agents.interactive.research.evaluators import (
    ResearchEvaluator,
    ComprehensiveEvaluation,
    EvaluationResult,
    EvaluationMetric,
)
from backend.agents.interactive.research.self_correction import (
    SelfCorrectionEngine,
    RefinementAction,
    RefinementStrategy,
)
from backend.agents.interactive.research.async_manager import (
    Async<PERSON>esearchManager,
    Async<PERSON>esearchTask,
    TaskStatus,
    TaskPriority,
)
from backend.agents.interactive.research.monitoring import ResearchAgentMonitor


class TestAgenticWrapper:
    """Test suite for the AgenticResearchWrapper class."""

    @pytest.fixture
    def sample_state(self):
        """Create a sample research state for testing."""
        return ResearchState(
            query_id="test_query_123",
            question="What are the liability standards for personal injury in Texas?",
            jurisdiction="texas",
            practice_areas={"personal_injury"},
            user_context=UserContext(
                user_id="test_user", tenant_id="test_tenant", role="attorney"
            ),
            thread_id="test_thread",
            queries=["liability standards personal injury Texas"],
            citations=[],
            legal_documents=[],
            case_documents=[],
            search_metadata={},
        )

    @pytest.fixture
    def mock_evaluation(self):
        """Create a mock evaluation result."""
        individual_scores = {}
        for metric in EvaluationMetric:
            individual_scores[metric] = EvaluationResult(
                metric=metric,
                score=0.8,
                explanation="Test evaluation",
                details={},
                requires_human_review=False,
            )

        return ComprehensiveEvaluation(
            overall_score=0.8,
            individual_scores=individual_scores,
            requires_human_oversight=False,
            refinement_suggestions=["Test suggestion"],
            metadata={"test": True},
        )

    @pytest.fixture
    def agentic_wrapper(self):
        """Create an agentic wrapper instance for testing."""
        return AgenticResearchWrapper(
            max_iterations=2, min_quality_threshold=0.7, evaluator_model="gpt-4o"
        )

    @pytest.mark.asyncio
    async def test_agentic_wrapper_initialization(self, agentic_wrapper):
        """Test that the agentic wrapper initializes correctly."""
        assert agentic_wrapper.max_iterations == 2
        assert agentic_wrapper.min_quality_threshold == 0.7
        assert isinstance(agentic_wrapper.evaluator, ResearchEvaluator)
        assert isinstance(agentic_wrapper.self_correction, SelfCorrectionEngine)

    @pytest.mark.asyncio
    async def test_successful_execution_single_iteration(
        self, agentic_wrapper, sample_state, mock_evaluation
    ):
        """Test successful execution that meets quality threshold in first iteration."""
        # Mock the base pipeline to return success
        with patch.object(
            agentic_wrapper, "_run_base_pipeline", new_callable=AsyncMock
        ) as mock_pipeline:
            mock_pipeline.return_value = {"success": True}

            # Mock the evaluator to return high-quality evaluation
            with patch.object(
                agentic_wrapper.evaluator, "evaluate_response", new_callable=AsyncMock
            ) as mock_eval:
                mock_eval.return_value = mock_evaluation

                # Execute the agentic wrapper
                result = await agentic_wrapper.execute(sample_state, None)

                # Verify results
                assert isinstance(result, AgenticExecutionResult)
                assert len(result.iterations) == 1
                assert result.final_evaluation.overall_score == 0.8
                assert not result.requires_human_oversight
                assert result.total_execution_time > 0

    @pytest.mark.asyncio
    async def test_multiple_iterations_with_refinement(
        self, agentic_wrapper, sample_state
    ):
        """Test execution that requires multiple iterations with refinement."""
        # Create low-quality evaluation for first iteration
        low_quality_eval = ComprehensiveEvaluation(
            overall_score=0.5,  # Below threshold
            individual_scores={},
            requires_human_oversight=True,
            refinement_suggestions=["Improve quality"],
            metadata={},
        )

        # Create high-quality evaluation for second iteration
        high_quality_eval = ComprehensiveEvaluation(
            overall_score=0.9,  # Above threshold
            individual_scores={},
            requires_human_oversight=False,
            refinement_suggestions=[],
            metadata={},
        )

        with patch.object(
            agentic_wrapper, "_run_base_pipeline", new_callable=AsyncMock
        ) as mock_pipeline:
            mock_pipeline.return_value = {"success": True}

            with patch.object(
                agentic_wrapper.evaluator, "evaluate_response", new_callable=AsyncMock
            ) as mock_eval:
                # Return low quality first, then high quality
                mock_eval.side_effect = [low_quality_eval, high_quality_eval]

                with patch.object(
                    agentic_wrapper,
                    "_apply_enhanced_refinement",
                    new_callable=AsyncMock,
                ) as mock_refine:
                    mock_refine.return_value = {
                        "applied_count": 1,
                        "description": "Test refinement",
                    }

                    result = await agentic_wrapper.execute(sample_state, None)

                    # Verify multiple iterations occurred
                    assert len(result.iterations) == 2
                    assert result.final_evaluation.overall_score == 0.9
                    assert not result.requires_human_oversight
                    assert len(result.refinement_history) == 1

    @pytest.mark.asyncio
    async def test_max_iterations_reached(self, agentic_wrapper, sample_state):
        """Test behavior when max iterations is reached without meeting threshold."""
        # Always return low-quality evaluation
        low_quality_eval = ComprehensiveEvaluation(
            overall_score=0.4,
            individual_scores={},
            requires_human_oversight=True,
            refinement_suggestions=["Needs improvement"],
            metadata={},
        )

        with patch.object(
            agentic_wrapper, "_run_base_pipeline", new_callable=AsyncMock
        ) as mock_pipeline:
            mock_pipeline.return_value = {"success": True}

            with patch.object(
                agentic_wrapper.evaluator, "evaluate_response", new_callable=AsyncMock
            ) as mock_eval:
                mock_eval.return_value = low_quality_eval

                with patch.object(
                    agentic_wrapper,
                    "_apply_enhanced_refinement",
                    new_callable=AsyncMock,
                ) as mock_refine:
                    mock_refine.return_value = {
                        "applied_count": 1,
                        "description": "Test refinement",
                    }

                    result = await agentic_wrapper.execute(sample_state, None)

                    # Verify max iterations reached
                    assert len(result.iterations) == agentic_wrapper.max_iterations
                    assert result.requires_human_oversight
                    assert result.final_evaluation.overall_score == 0.4

    @pytest.mark.asyncio
    async def test_pipeline_failure_handling(self, agentic_wrapper, sample_state):
        """Test handling of pipeline failures."""
        with patch.object(
            agentic_wrapper, "_run_base_pipeline", new_callable=AsyncMock
        ) as mock_pipeline:
            mock_pipeline.return_value = {"success": False, "error": "Pipeline failed"}

            result = await agentic_wrapper.execute(sample_state, None)

            # Verify fallback evaluation is used
            assert result.final_evaluation.overall_score <= 0.5
            assert result.requires_human_oversight
            assert len(result.iterations) > 0
            assert result.iterations[0].error == "Pipeline failed"


class TestEvaluationFramework:
    """Test suite for the evaluation framework."""

    @pytest.fixture
    def evaluator(self):
        """Create an evaluator instance for testing."""
        return ResearchEvaluator(model="gpt-4o")

    @pytest.fixture
    def sample_response(self):
        """Sample research response for testing."""
        return """
        Based on Texas law, personal injury liability follows a modified comparative negligence standard.
        Under Texas Civil Practice and Remedies Code § 33.001, a plaintiff can recover damages if their
        fault is less than 51%. The damages are reduced by the plaintiff's percentage of fault.
        
        Key cases include:
        - Austin v. Kroger Texas, L.P. (2014) - established duty of care standards
        - Smith v. Jones (2018) - clarified comparative negligence application
        
        [1] Texas Civil Practice and Remedies Code § 33.001
        [2] Austin v. Kroger Texas, L.P., 465 S.W.3d 193 (Tex. 2014)
        """

    @pytest.mark.asyncio
    async def test_faithfulness_evaluation(self, evaluator, sample_response):
        """Test faithfulness evaluation with mock LLM response."""
        from backend.agents.interactive.research.state import Document

        sources = [
            Document(
                page_content="Texas follows modified comparative negligence under § 33.001",
                metadata={"id": "source1", "type": "statute"},
            )
        ]

        with patch(
            "backend.agents.interactive.research.evaluators.generate_completion"
        ) as mock_llm:
            mock_llm.return_value.content = """
            {
                "score": 0.85,
                "explanation": "Response is well-supported by sources",
                "supported_claims": ["Modified comparative negligence", "51% rule"],
                "unsupported_claims": [],
                "citation_accuracy": "Good"
            }
            """

            result = await evaluator._evaluate_faithfulness(sample_response, sources)

            assert result.metric == EvaluationMetric.FAITHFULNESS
            assert result.score == 0.85
            assert not result.requires_human_review

    @pytest.mark.asyncio
    async def test_hallucination_detection(self, evaluator, sample_response):
        """Test hallucination detection."""
        sources = []  # No sources to support claims

        with patch(
            "backend.agents.interactive.research.evaluators.generate_completion"
        ) as mock_llm:
            mock_llm.return_value.content = """
            {
                "score": 0.7,
                "explanation": "Some claims lack source support",
                "potential_hallucinations": ["Specific case citations"],
                "confidence_level": "medium",
                "recommendation": "review"
            }
            """

            result = await evaluator._evaluate_hallucination(sample_response, sources)

            assert result.metric == EvaluationMetric.HALLUCINATION
            assert result.score == 0.3  # Inverted score
            assert result.requires_human_review


class TestSelfCorrectionEngine:
    """Test suite for the self-correction engine."""

    @pytest.fixture
    def correction_engine(self):
        """Create a self-correction engine for testing."""
        return SelfCorrectionEngine(model="gpt-4o")

    @pytest.fixture
    def low_quality_evaluation(self):
        """Create a low-quality evaluation for testing refinement."""
        individual_scores = {}
        for metric in EvaluationMetric:
            score = 0.5 if metric == EvaluationMetric.FAITHFULNESS else 0.8
            individual_scores[metric] = EvaluationResult(
                metric=metric,
                score=score,
                explanation="Test evaluation",
                details={},
                requires_human_review=score < 0.7,
            )

        return ComprehensiveEvaluation(
            overall_score=0.6,
            individual_scores=individual_scores,
            requires_human_oversight=True,
            refinement_suggestions=["Improve faithfulness"],
            metadata={},
        )

    @pytest.mark.asyncio
    async def test_refinement_plan_generation(
        self, correction_engine, sample_state, low_quality_evaluation
    ):
        """Test generation of refinement plans."""
        actions = await correction_engine.generate_refinement_plan(
            sample_state, low_quality_evaluation, 1
        )

        assert len(actions) > 0
        assert all(isinstance(action, RefinementAction) for action in actions)
        assert any(
            action.strategy == RefinementStrategy.SOURCE_VERIFICATION
            for action in actions
        )

    @pytest.mark.asyncio
    async def test_query_reformulation(self, correction_engine, sample_state):
        """Test query reformulation refinement."""
        action = RefinementAction(
            strategy=RefinementStrategy.QUERY_REFORMULATION,
            description="Test reformulation",
            parameters={"focus": "faithfulness", "specificity": "high"},
            priority=1,
            estimated_impact=0.7,
        )

        with patch(
            "backend.agents.interactive.research.self_correction.generate_completion"
        ) as mock_llm:
            mock_llm.return_value.content = """
            ["Texas personal injury liability standards",
             "Modified comparative negligence Texas law",
             "Fault allocation personal injury Texas"]
            """

            result = await correction_engine._apply_query_reformulation(
                sample_state, action, 1
            )

            assert result["success"]
            assert len(sample_state.queries) == 3
            assert "Texas personal injury liability standards" in sample_state.queries


class TestAsyncManager:
    """Test suite for the async research manager."""

    @pytest.fixture
    def async_manager(self):
        """Create an async manager for testing."""
        return AsyncResearchManager()

    @pytest.mark.asyncio
    async def test_task_submission(self, async_manager, sample_state):
        """Test async task submission."""
        with patch.object(async_manager.redis_client, "set") as mock_redis:
            with patch.object(async_manager.redis_client, "zadd") as mock_zadd:
                with patch.object(async_manager.celery_app, "send_task") as mock_celery:
                    task_id = await async_manager.submit_async_research(
                        sample_state, None, TaskPriority.NORMAL
                    )

                    assert task_id is not None
                    assert mock_redis.called
                    assert mock_zadd.called
                    assert mock_celery.called

    @pytest.mark.asyncio
    async def test_progress_tracking(self, async_manager):
        """Test progress tracking functionality."""
        task_id = "test_task_123"

        with patch.object(
            async_manager, "get_task_status", new_callable=AsyncMock
        ) as mock_status:
            mock_task = AsyncResearchTask(
                task_id=task_id,
                user_id="test_user",
                tenant_id="test_tenant",
                thread_id="test_thread",
                matter_id=None,
                question="Test question",
                jurisdiction="texas",
                practice_areas=["personal_injury"],
                priority=TaskPriority.NORMAL,
                status=TaskStatus.RUNNING,
                created_at=time.time(),
            )
            mock_status.return_value = mock_task

            with patch.object(async_manager.redis_client, "lpush") as mock_lpush:
                with patch.object(async_manager.redis_client, "expire") as mock_expire:
                    await async_manager.update_progress(
                        task_id, 0.5, "processing", "Test progress update"
                    )

                    assert mock_lpush.called
                    assert mock_expire.called


class TestEthicalCompliance:
    """Test suite for ethical compliance features."""

    @pytest.mark.asyncio
    async def test_human_oversight_flagging(self):
        """Test that human oversight is properly flagged."""
        # Create evaluation that should trigger oversight
        low_score_eval = ComprehensiveEvaluation(
            overall_score=0.4,  # Below threshold
            individual_scores={},
            requires_human_oversight=True,
            refinement_suggestions=["Requires review"],
            metadata={},
        )

        assert low_score_eval.requires_human_oversight
        assert low_score_eval.overall_score < 0.5

    @pytest.mark.asyncio
    async def test_audit_trail_creation(self):
        """Test audit trail creation for compliance."""
        monitor = ResearchAgentMonitor()

        sample_state = ResearchState(
            query_id="audit_test",
            question="Test question",
            jurisdiction="texas",
            practice_areas={"personal_injury"},
            user_context=UserContext(
                user_id="test_user", tenant_id="test_tenant", role="attorney"
            ),
        )

        entry_id = await monitor.create_audit_trail(
            sample_state, "test_action", {"test": "data"}, "high", True
        )

        assert entry_id is not None
        assert len(monitor.audit_trail) > 0
        assert monitor.audit_trail[-1].reviewer_required


# Fixture for sample state used across multiple test classes
@pytest.fixture
def sample_state():
    """Create a sample research state for testing."""
    return ResearchState(
        query_id="test_query_123",
        question="What are the liability standards for personal injury in Texas?",
        jurisdiction="texas",
        practice_areas={"personal_injury"},
        user_context=UserContext(
            user_id="test_user", tenant_id="test_tenant", role="attorney"
        ),
        thread_id="test_thread",
        queries=["liability standards personal injury Texas"],
        citations=[],
        legal_documents=[],
        case_documents=[],
        search_metadata={},
    )


if __name__ == "__main__":
    # Run tests with: python -m pytest backend/agents/interactive/research/tests/test_agentic_wrapper.py -v
    pytest.main([__file__, "-v"])
