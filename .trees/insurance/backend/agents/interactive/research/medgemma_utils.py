"""
MedGemma Utility Functions

Utility functions for checking MedGemma availability and settings.
"""

import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# Supabase imports for system settings
try:
    from supabase import create_client, Client

    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase not available. Install with: pip install supabase")
    SUPABASE_AVAILABLE = False
    create_client = None
    Client = None


async def is_medgemma_enabled() -> bool:
    """
    Check if MedGemma is enabled via superadmin settings.

    Returns:
        bool: True if MedGemma is enabled, False otherwise
    """
    if not SUPABASE_AVAILABLE:
        logger.info("Supabase not available - MedGemma disabled by default")
        return False

    try:
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_KEY")

        if not url or not key:
            logger.warning(
                "Supabase credentials not set - MedGemma disabled by default"
            )
            return False

        supabase = create_client(url, key)
        response = (
            supabase.table("system_settings")
            .select("value")
            .eq("key", "medgemma_enabled")
            .execute()
        )

        if response.data and len(response.data) > 0:
            enabled = response.data[0]["value"].lower() == "true"
            logger.info(
                f"MedGemma setting loaded: {'enabled' if enabled else 'disabled'}"
            )
            return enabled
        else:
            logger.info("MedGemma setting not found - disabled by default")
            return False

    except Exception as e:
        logger.error(f"Error checking MedGemma settings: {str(e)}")
        return False


def get_medgemma_status() -> dict:
    """
    Get comprehensive MedGemma status information.

    Returns:
        dict: Status information including availability, settings, and dependencies
    """
    status = {
        "supabase_available": SUPABASE_AVAILABLE,
        "credentials_configured": bool(
            os.getenv("SUPABASE_URL") and os.getenv("SUPABASE_KEY")
        ),
        "vertex_ai_configured": bool(os.getenv("GOOGLE_CLOUD_PROJECT")),
        "enabled": False,
        "ready": False,
    }

    # Check if MedGemma is enabled (synchronous version for status checks)
    if status["supabase_available"] and status["credentials_configured"]:
        try:
            url = os.getenv("SUPABASE_URL")
            key = os.getenv("SUPABASE_KEY")
            supabase = create_client(url, key)
            response = (
                supabase.table("system_settings")
                .select("value")
                .eq("key", "medgemma_enabled")
                .execute()
            )

            if response.data and len(response.data) > 0:
                status["enabled"] = response.data[0]["value"].lower() == "true"
        except Exception as e:
            logger.error(f"Error checking MedGemma status: {str(e)}")

    # MedGemma is ready if enabled and all dependencies are configured
    status["ready"] = (
        status["enabled"]
        and status["supabase_available"]
        and status["credentials_configured"]
        and status["vertex_ai_configured"]
    )

    return status
