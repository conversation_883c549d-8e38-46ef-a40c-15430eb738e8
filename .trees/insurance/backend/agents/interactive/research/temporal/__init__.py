"""
Temporal Query Processing Module for Research Agent

This module provides intelligent temporal awareness for legal research queries,
including natural language date parsing and temporal cue detection using
Gemini 2.5 Pro for enhanced understanding of temporal context.

Key Features:
- Intelligent temporal cue detection using Gemini 2.5 Pro
- Natural language date parsing and normalization
- Legal context-aware temporal filtering
- Integration with existing Calendar Agent date parsing logic
- Support for relative and absolute temporal expressions
"""

from backend.agents.interactive.research.temporal.query_parser import (
    TemporalQueryParser,
    TemporalContext,
    TemporalFilter,
)

__all__ = [
    "TemporalQueryParser",
    "TemporalContext",
    "TemporalFilter",
]
