"""
Unit Tests for Long-Context Features in Research Agent

This module contains comprehensive unit tests for the long-context processing
features including BM25 scoring, token counting, summarization, and error handling.
"""

import asyncio
import pytest
from unittest.mock import Mock, patch, AsyncMock
from typing import List

from backend.agents.interactive.research.nodes import (
    BM25Scorer,
    count_tokens_approximate,
    estimate_tokens_for_summarization,
    should_summarize_document,
    summarize_long_document,
    apply_long_context_summarization,
    configure_search_metadata,
    get_chunk_strategy,
    should_use_long_context,
    safe_bm25_search,
    safe_summarize_document,
    SearchErrorHandler,
    hybrid_vector_search,
)
from backend.agents.interactive.research.state import (
    ResearchState,
    Document,
    UserContext,
)


class TestBM25Scorer:
    """Test cases for BM25 scoring functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.documents = [
            Document(
                page_content="Personal injury law in Texas requires proving negligence and damages.",
                metadata={"id": "doc1", "jurisdiction": "texas", "source": "test"},
            ),
            Document(
                page_content="Medical malpractice cases involve professional negligence by healthcare providers.",
                metadata={"id": "doc2", "jurisdiction": "texas", "source": "test"},
            ),
            Document(
                page_content="Contract law governs agreements between parties in commercial transactions.",
                metadata={"id": "doc3", "jurisdiction": "texas", "source": "test"},
            ),
        ]
        self.scorer = BM25Scorer()

    def test_preprocess_text(self):
        """Test text preprocessing for legal documents."""
        text = "Smith v. Jones, 42 U.S.C. § 1983, personal injury case"
        tokens = self.scorer.preprocess_text(text)

        # Should preserve legal citations and filter stop words
        assert "smith_v_jones" in tokens
        assert "42_u.s.c._1983" in tokens
        assert "personal" in tokens
        assert "injury" in tokens
        assert "case" in tokens
        assert "the" not in tokens  # Stop word filtered

    def test_fit_documents(self):
        """Test fitting BM25 model on documents."""
        self.scorer.fit(self.documents)

        assert len(self.scorer.documents) == 3
        assert len(self.scorer.doc_lengths) == 3
        assert self.scorer.avg_doc_length > 0
        assert "negligence" in self.scorer.doc_freqs
        assert self.scorer.doc_freqs["negligence"] == 2  # Appears in 2 documents

    def test_get_idf(self):
        """Test IDF calculation."""
        self.scorer.fit(self.documents)

        # Term appearing in 2 out of 3 documents
        idf_negligence = self.scorer.get_idf("negligence")
        assert idf_negligence > 0

        # Term appearing in 1 out of 3 documents
        idf_contract = self.scorer.get_idf("contract")
        assert idf_contract > idf_negligence  # Should have higher IDF

        # Non-existent term
        idf_nonexistent = self.scorer.get_idf("nonexistent")
        assert idf_nonexistent == 0

    def test_search(self):
        """Test BM25 search functionality."""
        self.scorer.fit(self.documents)

        results = self.scorer.search("personal injury negligence", top_k=2)

        assert len(results) <= 2
        assert all(isinstance(result, tuple) for result in results)
        assert all(len(result) == 2 for result in results)

        # Results should be sorted by score (descending)
        if len(results) > 1:
            assert results[0][1] >= results[1][1]


class TestTokenCounting:
    """Test cases for token counting utilities."""

    def test_count_tokens_approximate(self):
        """Test approximate token counting."""
        # Short text
        short_text = "Hello world"
        tokens = count_tokens_approximate(short_text)
        assert tokens == 2  # 11 chars / 4 ≈ 2.75 → 2

        # Long text
        long_text = "This is a much longer text that should result in more tokens " * 10
        tokens = count_tokens_approximate(long_text)
        assert tokens > 100

        # Empty text
        assert count_tokens_approximate("") == 0

        # Model-specific adjustments
        gpt35_tokens = count_tokens_approximate("test text", "gpt-3.5-turbo")
        gpt4_tokens = count_tokens_approximate("test text", "gpt-4")
        assert gpt35_tokens >= gpt4_tokens  # GPT-3.5 uses slightly more tokens

    def test_estimate_tokens_for_summarization(self):
        """Test summarization token estimation."""
        text = "A" * 4000  # 4000 characters ≈ 1000 tokens
        target = estimate_tokens_for_summarization(text)

        assert 200 <= target <= 2000  # Within bounds
        assert target == 300  # 30% of 1000 tokens

        # Very short text
        short_text = "Short"
        target_short = estimate_tokens_for_summarization(short_text)
        assert target_short == 200  # Minimum bound

        # Very long text
        long_text = "A" * 40000  # ≈ 10000 tokens
        target_long = estimate_tokens_for_summarization(long_text)
        assert target_long == 2000  # Maximum bound

    def test_should_summarize_document(self):
        """Test document summarization decision logic."""
        # Long document
        long_doc = Document(
            page_content="A" * 20000,
            metadata={"id": "long_doc"},  # ≈ 5000 tokens
        )
        assert should_summarize_document(long_doc, threshold=4000)

        # Short document
        short_doc = Document(page_content="Short content", metadata={"id": "short_doc"})
        assert not should_summarize_document(short_doc, threshold=4000)

        # Already summarized document
        summarized_doc = Document(
            page_content="A" * 20000,
            metadata={"id": "summarized_doc", "summarized": True},
        )
        assert not should_summarize_document(summarized_doc, threshold=4000)


class TestConfiguration:
    """Test cases for configuration and metadata support."""

    def setup_method(self):
        """Set up test fixtures."""
        self.user_context = UserContext(
            user_id="test_user", tenant_id="test_tenant", role="attorney"
        )
        self.state = ResearchState(
            question="Test question", user_context=self.user_context
        )

    def test_configure_search_metadata(self):
        """Test search metadata configuration."""
        configure_search_metadata(
            self.state,
            chunk_strategy="full_sections",
            long_context_model="gemini-1.5-pro",
            token_threshold=5000,
        )

        assert self.state.search_metadata["chunk_strategy"] == "full_sections"
        assert (
            self.state.search_metadata["long_context_config"]["model"]
            == "gemini-1.5-pro"
        )
        assert (
            self.state.search_metadata["long_context_config"]["token_threshold"] == 5000
        )
        assert self.state.search_metadata["long_context_config"]["enabled"] is True

    def test_get_chunk_strategy(self):
        """Test chunk strategy retrieval."""
        # Default strategy
        assert get_chunk_strategy(self.state) == "standard"

        # Configured strategy
        configure_search_metadata(self.state, chunk_strategy="full_sections")
        assert get_chunk_strategy(self.state) == "full_sections"

    def test_should_use_long_context(self):
        """Test long-context usage decision."""
        # Default (disabled)
        assert not should_use_long_context(self.state)

        # Enabled with full_sections
        configure_search_metadata(self.state, chunk_strategy="full_sections")
        assert should_use_long_context(self.state)

        # Disabled with standard
        configure_search_metadata(self.state, chunk_strategy="standard")
        assert not should_use_long_context(self.state)


class TestErrorHandling:
    """Test cases for error handling and fallbacks."""

    def setup_method(self):
        """Set up test fixtures."""
        self.error_handler = SearchErrorHandler()
        self.documents = [
            Document(
                page_content="Test document content",
                metadata={"id": "test_doc", "source": "test"},
            )
        ]

    def test_circuit_breaker_bm25(self):
        """Test BM25 circuit breaker functionality."""
        # Initially should not skip
        assert not self.error_handler.should_skip_bm25()

        # Record failures
        for _ in range(3):
            self.error_handler.record_bm25_failure()

        # Should skip after max failures
        assert self.error_handler.should_skip_bm25()

    def test_circuit_breaker_summarization(self):
        """Test summarization circuit breaker functionality."""
        # Initially should not skip
        assert not self.error_handler.should_skip_summarization()

        # Record failures
        for _ in range(3):
            self.error_handler.record_summarization_failure()

        # Should skip after max failures
        assert self.error_handler.should_skip_summarization()

    @pytest.mark.asyncio
    async def test_safe_bm25_search(self):
        """Test safe BM25 search with error handling."""
        from backend.agents.interactive.research.nodes import search_error_handler

        queries = ["test query"]

        # Normal operation
        results = await safe_bm25_search(self.documents, queries)
        assert isinstance(results, list)

        # With circuit breaker active - use global error handler
        import os

        original_failures = search_error_handler.bm25_failures
        original_last_failure = search_error_handler.last_failure_time.get("bm25", 0)

        # Set circuit breaker to active state
        search_error_handler.bm25_failures = 3
        search_error_handler.last_failure_time["bm25"] = os.times().elapsed

        try:
            results = await safe_bm25_search(self.documents, queries)
            assert results == []  # Should return empty list
        finally:
            # Reset to original state
            search_error_handler.bm25_failures = original_failures
            if original_last_failure == 0:
                search_error_handler.last_failure_time.pop("bm25", None)
            else:
                search_error_handler.last_failure_time["bm25"] = original_last_failure

    @pytest.mark.asyncio
    async def test_safe_summarize_document(self):
        """Test safe document summarization with retries."""
        user_context = UserContext(
            user_id="test_user", tenant_id="test_tenant", role="attorney"
        )
        state = ResearchState(question="Test question", user_context=user_context)
        configure_search_metadata(state, chunk_strategy="full_sections")

        doc = Document(
            page_content="A" * 20000,
            metadata={"id": "test_doc"},  # Long document
        )

        # Mock the summarization to fail then succeed
        with patch(
            "backend.agents.interactive.research.nodes.summarize_long_document"
        ) as mock_summarize:
            mock_summarize.side_effect = [Exception("API Error"), doc]

            result = await safe_summarize_document(doc, state, max_retries=1)
            assert result == doc  # Should return document after retry
            assert mock_summarize.call_count == 2  # Initial call + 1 retry


@pytest.mark.asyncio
async def test_hybrid_vector_search_integration():
    """Integration test for hybrid vector search."""
    user_context = UserContext(
        user_id="test_user", tenant_id="test_tenant", role="attorney"
    )
    state = ResearchState(
        question="Personal injury law in Texas",
        user_context=user_context,
        queries=["personal injury", "negligence"],
        jurisdiction="texas",
    )

    # Configure for long-context processing
    configure_search_metadata(state, chunk_strategy="full_sections")

    # Mock the vector_search function
    with patch(
        "backend.agents.interactive.research.nodes.vector_search"
    ) as mock_vector_search:
        mock_vector_search.return_value = {"status": "success"}

        # Mock documents in state
        state.legal_documents = [
            Document(
                page_content="Personal injury law requires proving negligence.",
                metadata={"id": "doc1", "source": "laws_api"},
            )
        ]

        config = Mock()
        result = await hybrid_vector_search(state, config)

        assert result["status"] == "success"
        assert result["next"] == "graph_expand"
        assert "hybrid_search" in state.search_metadata
