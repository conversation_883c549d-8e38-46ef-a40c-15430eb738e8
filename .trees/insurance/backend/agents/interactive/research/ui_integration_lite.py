"""
Lightweight UI Integration for Research Agent

This module provides a simplified version of UI integration that can be imported
quickly without heavy dependencies, then lazily loads the full functionality
when needed.
"""

import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Types of UI notifications."""

    PROGRESS_UPDATE = "progress_update"
    CITATION_HIGHLIGHT = "citation_highlight"
    HUMAN_OVERSIGHT_REQUIRED = "human_oversight_required"
    TASK_COMPLETED = "task_completed"
    ERROR_ALERT = "error_alert"
    REFINEMENT_SUGGESTION = "refinement_suggestion"


class AlertSeverity(Enum):
    """Alert severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class UINotification:
    """UI notification data structure."""

    type: NotificationType
    message: str
    severity: AlertSeverity = AlertSeverity.INFO
    data: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None


@dataclass
class TaskProgress:
    """Task progress data structure."""

    task_id: str
    status: str
    progress: float
    message: str
    data: Optional[Dict[str, Any]] = None


class UIManager:
    """Lightweight UI manager with lazy loading."""

    def __init__(self):
        """Initialize the UI manager without loading heavy dependencies."""
        self._initialized = False
        self._full_manager = None

    def _ensure_initialized(self):
        """Lazy initialization of the full UI manager."""
        if not self._initialized:
            try:
                # Import the actual UI manager only when needed
                from backend.agents.interactive.research.ui_integration import (
                    UIManager as FullUIManager,
                )

                self._full_manager = FullUIManager()
                self._initialized = True
                logger.info("Full UI manager initialized successfully")

            except Exception as e:
                logger.error(f"Failed to initialize full UI manager: {e}")
                # Create a mock manager for graceful degradation
                self._full_manager = MockUIManager()
                self._initialized = True

    async def send_notification(self, notification: UINotification):
        """Send a UI notification."""
        self._ensure_initialized()
        if hasattr(self._full_manager, "send_notification"):
            await self._full_manager.send_notification(notification)
        else:
            logger.info(
                f"Mock notification: {notification.type.value} - {notification.message}"
            )

    async def update_progress(self, progress: TaskProgress):
        """Update task progress."""
        self._ensure_initialized()
        if hasattr(self._full_manager, "update_progress"):
            await self._full_manager.update_progress(progress)
        else:
            logger.info(
                f"Mock progress update: {progress.task_id} - {progress.progress}% - {progress.message}"
            )

    async def highlight_citation(self, citation_data: Dict[str, Any]):
        """Highlight a citation in the UI."""
        self._ensure_initialized()
        if hasattr(self._full_manager, "highlight_citation"):
            await self._full_manager.highlight_citation(citation_data)
        else:
            logger.info(f"Mock citation highlight: {citation_data}")

    async def request_human_oversight(self, oversight_data: Dict[str, Any]):
        """Request human oversight."""
        self._ensure_initialized()
        if hasattr(self._full_manager, "request_human_oversight"):
            await self._full_manager.request_human_oversight(oversight_data)
        else:
            logger.info(f"Mock human oversight request: {oversight_data}")

    def register_websocket_handler(self, handler):
        """Register a WebSocket handler for UI notifications."""
        self._ensure_initialized()
        if hasattr(self._full_manager, "register_websocket_handler"):
            self._full_manager.register_websocket_handler(handler)
        else:
            logger.info(f"Mock WebSocket handler registration: {handler}")
            # Store the handler for potential future use
            if not hasattr(self, "_websocket_handlers"):
                self._websocket_handlers = []
            self._websocket_handlers.append(handler)


class MockUIManager:
    """Mock UI manager for graceful degradation."""

    async def send_notification(self, notification: UINotification):
        """Mock notification sending."""
        logger.info(
            f"Mock notification: {notification.type.value} - {notification.message}"
        )

    async def update_progress(self, progress: TaskProgress):
        """Mock progress update."""
        logger.info(
            f"Mock progress: {progress.task_id} - {progress.progress}% - {progress.message}"
        )

    async def highlight_citation(self, citation_data: Dict[str, Any]):
        """Mock citation highlighting."""
        logger.info(f"Mock citation: {citation_data}")

    async def request_human_oversight(self, oversight_data: Dict[str, Any]):
        """Mock oversight request."""
        logger.info(f"Mock oversight: {oversight_data}")


# Global UI manager instance
ui_manager = UIManager()
