"""
AiLex Research Agent Agentic Wrapper

This module implements the agentic wrapper for the Research Agent that adds
self-correction loops, evaluation metrics, and iterative refinement capabilities.

Key Features:
- Runs base research pipeline with evaluation
- Implements self-correction loops for quality improvement
- Supports async processing for long-running tasks
- Integrates with evaluation framework for quality assessment
- Provides human oversight flags for ethical compliance
- Tracks iteration metrics for monitoring and debugging

The wrapper replaces the main entry point in the LangGraph workflow and
orchestrates the entire agentic research process.
"""

import logging
import time
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.research.state import ResearchState
from backend.agents.interactive.research.evaluators import (
    ResearchEvaluator,
    ComprehensiveEvaluation,
)
from backend.agents.interactive.research.self_correction import SelfCorrectionEngine
from backend.agents.interactive.research.ui_integration import ui_manager
from backend.agents.interactive.research.monitoring import research_monitor

# Configure logger
logger = logging.getLogger(__name__)


@dataclass
class AgenticIteration:
    """Represents a single iteration in the agentic loop."""

    iteration_number: int
    start_time: float
    end_time: Optional[float]
    evaluation_results: Optional[ComprehensiveEvaluation]
    refinement_applied: Optional[str]
    success: bool
    error: Optional[str] = None


@dataclass
class AgenticExecutionResult:
    """Result of agentic execution with all iterations."""

    final_state: ResearchState
    final_evaluation: ComprehensiveEvaluation
    iterations: List[AgenticIteration]
    total_execution_time: float
    requires_human_oversight: bool
    refinement_history: List[str]


class AgenticResearchWrapper:
    """
    Agentic wrapper for the Research Agent that implements self-correction loops.

    This wrapper orchestrates the research process with evaluation and refinement,
    providing a more autonomous and quality-aware research experience.
    """

    def __init__(
        self,
        max_iterations: int = 3,
        min_quality_threshold: float = 0.8,
        evaluator_model: str = "gpt-4o",
    ):
        """
        Initialize the agentic wrapper.

        Args:
            max_iterations: Maximum number of refinement iterations
            min_quality_threshold: Minimum quality score to accept
            evaluator_model: Model to use for evaluation
        """
        self.max_iterations = max_iterations
        self.min_quality_threshold = min_quality_threshold
        self.evaluator = ResearchEvaluator(model=evaluator_model)
        self.self_correction = SelfCorrectionEngine(model=evaluator_model)

        # Import base graph here to avoid circular imports
        from backend.agents.interactive.research.graph import create_base_research_graph

        self.base_graph = create_base_research_graph()

    async def execute(
        self, state: ResearchState, config: RunnableConfig
    ) -> AgenticExecutionResult:
        """
        Execute the agentic research process with self-correction loops.

        Args:
            state: Initial research state
            config: Runtime configuration

        Returns:
            Agentic execution result with all iterations
        """
        start_time = time.time()
        iterations = []
        current_state = state

        logger.info(f"Starting agentic research for query: {state.question[:50]}...")

        # Initialize metadata for tracking
        if "agentic_metadata" not in current_state.search_metadata:
            current_state.search_metadata["agentic_metadata"] = {
                "start_time": start_time,
                "max_iterations": self.max_iterations,
                "quality_threshold": self.min_quality_threshold,
            }

        for iteration in range(self.max_iterations):
            iteration_start = time.time()
            logger.info(f"Starting iteration {iteration + 1}/{self.max_iterations}")

            # Create iteration record
            current_iteration = AgenticIteration(
                iteration_number=iteration + 1,
                start_time=iteration_start,
                end_time=None,
                evaluation_results=None,
                refinement_applied=None,
                success=False,
            )

            try:
                # Run the base research pipeline
                pipeline_result = await self._run_base_pipeline(current_state, config)

                if not pipeline_result.get("success", False):
                    current_iteration.error = pipeline_result.get(
                        "error", "Pipeline execution failed"
                    )
                    current_iteration.end_time = time.time()
                    iterations.append(current_iteration)
                    continue

                # Extract response and sources for evaluation
                response = current_state.answer or ""
                sources = current_state.legal_documents + current_state.case_documents

                # Evaluate the response
                evaluation = await self.evaluator.evaluate_response(
                    current_state, response, sources
                )

                current_iteration.evaluation_results = evaluation
                current_iteration.end_time = time.time()
                current_iteration.success = True

                # Store evaluation in state metadata
                current_state.search_metadata[
                    f"iteration_{iteration + 1}_evaluation"
                ] = {
                    "overall_score": evaluation.overall_score,
                    "requires_oversight": evaluation.requires_human_oversight,
                    "individual_scores": {
                        metric.value: result.score
                        for metric, result in evaluation.individual_scores.items()
                    },
                }

                # Send UI updates
                await self._send_ui_updates(current_state, evaluation, iteration + 1)

                logger.info(
                    f"Iteration {iteration + 1} completed. Score: {evaluation.overall_score:.2f}"
                )

                # Check if quality threshold is met
                if evaluation.overall_score >= self.min_quality_threshold:
                    logger.info(
                        f"Quality threshold met ({evaluation.overall_score:.2f} >= {self.min_quality_threshold})"
                    )
                    iterations.append(current_iteration)
                    break

                # Apply refinement if not the last iteration
                if iteration < self.max_iterations - 1:
                    refinement_result = await self._apply_enhanced_refinement(
                        current_state, evaluation, iteration + 1
                    )
                    current_iteration.refinement_applied = refinement_result.get(
                        "description", "Enhanced refinement applied"
                    )
                    logger.info(
                        f"Applied enhanced refinement: {refinement_result.get('applied_count', 0)} actions"
                    )

                iterations.append(current_iteration)

            except Exception as e:
                logger.error(f"Error in iteration {iteration + 1}: {str(e)}")
                current_iteration.error = str(e)
                current_iteration.end_time = time.time()
                iterations.append(current_iteration)

                # Continue to next iteration unless it's the last one
                if iteration == self.max_iterations - 1:
                    break

        # Get final evaluation (use last successful iteration)
        final_evaluation = None
        for iteration in reversed(iterations):
            if iteration.evaluation_results:
                final_evaluation = iteration.evaluation_results
                break

        # Create fallback evaluation if none succeeded
        if not final_evaluation:
            logger.warning("No successful evaluations, creating fallback")
            final_evaluation = await self._create_fallback_evaluation(current_state)

        # Update state with final agentic metadata
        total_time = time.time() - start_time
        current_state.search_metadata["agentic_metadata"].update(
            {
                "end_time": time.time(),
                "total_execution_time": total_time,
                "iterations_completed": len(iterations),
                "final_score": final_evaluation.overall_score,
                "requires_human_oversight": final_evaluation.requires_human_oversight,
            }
        )

        # Set human oversight flag in state
        if final_evaluation.requires_human_oversight:
            current_state.search_metadata["human_oversight_required"] = True
            logger.warning("Human oversight required for this research result")

        # Track comprehensive metrics
        execution_result = AgenticExecutionResult(
            final_state=current_state,
            final_evaluation=final_evaluation,
            iterations=iterations,
            total_execution_time=total_time,
            requires_human_oversight=final_evaluation.requires_human_oversight,
            refinement_history=[
                iter.refinement_applied
                for iter in iterations
                if iter.refinement_applied
            ],
        )
        await self._track_comprehensive_metrics(
            current_state, execution_result, final_evaluation
        )

        logger.info(
            f"Agentic research completed in {total_time:.2f}s with {len(iterations)} iterations"
        )

        return AgenticExecutionResult(
            final_state=current_state,
            final_evaluation=final_evaluation,
            iterations=iterations,
            total_execution_time=total_time,
            requires_human_oversight=final_evaluation.requires_human_oversight,
            refinement_history=[
                iter.refinement_applied
                for iter in iterations
                if iter.refinement_applied
            ],
        )

    async def _run_base_pipeline(
        self, state: ResearchState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Run the base research pipeline.

        Args:
            state: Current research state
            config: Runtime configuration

        Returns:
            Pipeline execution result
        """
        try:
            # Execute the base research graph
            result = await self.base_graph.ainvoke(state, config)

            # Update state with result
            if isinstance(result, dict):
                for key, value in result.items():
                    if hasattr(state, key):
                        setattr(state, key, value)

            return {"success": True, "result": result}

        except Exception as e:
            logger.error(f"Base pipeline execution failed: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _apply_enhanced_refinement(
        self, state: ResearchState, evaluation: ComprehensiveEvaluation, iteration: int
    ) -> Dict[str, Any]:
        """
        Apply enhanced refinement using the self-correction engine.

        Args:
            state: Current research state
            evaluation: Evaluation results
            iteration: Current iteration number

        Returns:
            Refinement result summary
        """
        try:
            # Generate refinement plan
            refinement_actions = await self.self_correction.generate_refinement_plan(
                state, evaluation, iteration
            )

            if not refinement_actions:
                return {
                    "applied_count": 0,
                    "description": "No specific refinements identified",
                    "actions": [],
                }

            # Apply refinement actions
            result = await self.self_correction.apply_refinement_actions(
                state, refinement_actions, iteration
            )

            # Create description from applied refinements
            descriptions = [r["description"] for r in result.get("refinements", [])]
            description = (
                "; ".join(descriptions)
                if descriptions
                else "Enhanced refinement applied"
            )

            return {
                "applied_count": result.get("applied_count", 0),
                "description": description,
                "actions": result.get("refinements", []),
                "iteration": iteration,
            }

        except Exception as e:
            logger.error(f"Enhanced refinement failed: {str(e)}")
            return {
                "applied_count": 0,
                "description": f"Refinement error: {str(e)}",
                "actions": [],
                "error": str(e),
            }

    async def _create_fallback_evaluation(
        self, state: ResearchState
    ) -> ComprehensiveEvaluation:
        """
        Create a fallback evaluation when all iterations fail.

        Args:
            state: Current research state

        Returns:
            Fallback evaluation with conservative scores
        """
        from backend.agents.interactive.research.evaluators import (
            EvaluationResult,
            ComprehensiveEvaluation,
            EvaluationMetric as EM,
        )

        # Create conservative evaluation results
        individual_scores = {}
        for metric in EM:
            individual_scores[metric] = EvaluationResult(
                metric=metric,
                score=0.3,  # Conservative score
                explanation="Fallback evaluation due to iteration failures",
                details={"fallback": True},
                requires_human_review=True,
            )

        return ComprehensiveEvaluation(
            overall_score=0.3,
            individual_scores=individual_scores,
            requires_human_oversight=True,
            refinement_suggestions=["Manual review required due to processing errors"],
            metadata={"fallback_evaluation": True},
        )

    async def _send_ui_updates(
        self, state: ResearchState, evaluation: ComprehensiveEvaluation, iteration: int
    ) -> None:
        """
        Send UI updates for the current iteration.

        Args:
            state: Current research state
            evaluation: Evaluation results
            iteration: Current iteration number
        """
        try:
            user_id = state.user_context.user_id
            thread_id = state.thread_id or "unknown"

            # Send citation highlights
            if state.citations:
                await ui_manager.send_citation_highlights(
                    user_id, thread_id, state, evaluation
                )

            # Send oversight alert if needed
            if evaluation.requires_human_oversight:
                await ui_manager.send_oversight_alert(
                    user_id, thread_id, evaluation, state
                )

            # Send refinement suggestions if score is low
            if evaluation.overall_score < 0.8 and evaluation.refinement_suggestions:
                await ui_manager.send_refinement_suggestions(
                    user_id,
                    thread_id,
                    evaluation.refinement_suggestions,
                    evaluation.overall_score,
                )

        except Exception as e:
            logger.error(f"Failed to send UI updates: {str(e)}")

    async def _track_comprehensive_metrics(
        self,
        state: ResearchState,
        execution_result: AgenticExecutionResult,
        evaluation: ComprehensiveEvaluation,
    ) -> None:
        """
        Track comprehensive metrics for monitoring and analytics.

        Args:
            state: Research state
            execution_result: Agentic execution result
            evaluation: Final evaluation
        """
        try:
            # Track performance metrics
            await research_monitor.track_performance(
                state,
                execution_result,
                tokens_used=state.search_metadata.get("tokens_used", 0),
                api_calls=state.search_metadata.get("api_calls", 0),
                cache_hits=state.search_metadata.get("cache_hits", 0),
            )

            # Track quality metrics
            await research_monitor.track_quality(
                state, evaluation, len(execution_result.iterations)
            )

            # Track ethical compliance
            await research_monitor.track_ethical_compliance(state, evaluation)

            # Track usage analytics
            processing_mode = (
                "async" if state.search_metadata.get("async_processing") else "sync"
            )
            await research_monitor.track_usage(state, processing_mode)

            # Create audit trail
            await research_monitor.create_audit_trail(
                state,
                "agentic_research_completed",
                {
                    "execution_time": execution_result.total_execution_time,
                    "iterations": len(execution_result.iterations),
                    "final_score": evaluation.overall_score,
                    "oversight_required": execution_result.requires_human_oversight,
                },
                compliance_impact=(
                    "medium" if execution_result.requires_human_oversight else "low"
                ),
                reviewer_required=execution_result.requires_human_oversight,
            )

            logger.debug("Comprehensive metrics tracking completed")

        except Exception as e:
            logger.error(f"Failed to track comprehensive metrics: {str(e)}")


# Main entry point function for LangGraph integration
async def agentic_research_wrapper(
    state: ResearchState, config: RunnableConfig
) -> Dict[str, Any]:
    """
    Main agentic wrapper function for LangGraph integration.

    This function serves as the entry point for the agentic research process,
    replacing the original research pipeline with an enhanced version that
    includes evaluation and self-correction capabilities.

    Args:
        state: Research state
        config: Runtime configuration

    Returns:
        Updated state dictionary for LangGraph
    """
    try:
        # Create agentic wrapper instance
        wrapper = AgenticResearchWrapper()

        # Execute agentic research
        result = await wrapper.execute(state, config)

        # Update state with final results
        final_state = result.final_state

        # Add agentic execution metadata
        final_state.search_metadata["agentic_execution"] = {
            "iterations_completed": len(result.iterations),
            "total_execution_time": result.total_execution_time,
            "final_score": result.final_evaluation.overall_score,
            "requires_human_oversight": result.requires_human_oversight,
            "refinement_history": result.refinement_history,
        }

        # Set confidence flags based on evaluation
        if result.final_evaluation.overall_score < 0.6:
            final_state.low_confidence_rerank = True

        if result.requires_human_oversight:
            final_state.search_metadata["human_oversight_required"] = True

        logger.info(
            f"Agentic wrapper completed successfully. Final score: {result.final_evaluation.overall_score:.2f}"
        )

        return {"status": "success", "next": "FINISH"}

    except Exception as e:
        logger.error(f"Agentic wrapper failed: {str(e)}")

        # Add error information to state
        state.search_metadata["agentic_error"] = {
            "error": str(e),
            "fallback_used": True,
        }

        # Set human oversight required due to error
        state.search_metadata["human_oversight_required"] = True

        return {"status": "error", "error": str(e), "next": "FINISH"}
