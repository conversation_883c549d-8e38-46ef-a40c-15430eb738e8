"""
Cache Performance Monitoring for Research Agent

This module provides monitoring and metrics collection for the semantic cache
and temporal awareness features of the Research Agent.
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from backend.agents.interactive.research.cache import Semantic<PERSON>ache, CacheConfig

# Configure logger
logger = logging.getLogger(__name__)


@dataclass
class CacheMetrics:
    """Cache performance metrics."""

    tenant_id: str
    timestamp: datetime
    total_queries: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    avg_similarity_score: float = 0.0
    temporal_queries: int = 0
    stale_cache_hits: int = 0
    avg_response_time_ms: float = 0.0

    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        if self.total_queries == 0:
            return 0.0
        return self.cache_hits / self.total_queries

    @property
    def temporal_query_rate(self) -> float:
        """Calculate temporal query rate."""
        if self.total_queries == 0:
            return 0.0
        return self.temporal_queries / self.total_queries


class CacheMonitor:
    """
    Monitor cache performance and collect metrics.

    This class tracks cache performance metrics and provides
    insights into the effectiveness of semantic caching and
    temporal awareness features.
    """

    def __init__(self):
        """Initialize cache monitor."""
        self.metrics_history: Dict[str, List[CacheMetrics]] = {}
        self.current_metrics: Dict[str, CacheMetrics] = {}

    def record_query(
        self,
        tenant_id: str,
        cache_hit: bool,
        similarity_score: Optional[float] = None,
        is_temporal: bool = False,
        is_stale: bool = False,
        response_time_ms: float = 0.0,
    ):
        """
        Record a query and its cache performance.

        Args:
            tenant_id: Tenant identifier
            cache_hit: Whether the query resulted in a cache hit
            similarity_score: Similarity score for cache hits
            is_temporal: Whether temporal filtering was applied
            is_stale: Whether the cached result was stale
            response_time_ms: Response time in milliseconds
        """
        # Initialize metrics for tenant if not exists
        if tenant_id not in self.current_metrics:
            self.current_metrics[tenant_id] = CacheMetrics(
                tenant_id=tenant_id, timestamp=datetime.now()
            )

        metrics = self.current_metrics[tenant_id]
        metrics.total_queries += 1

        if cache_hit:
            metrics.cache_hits += 1
            if similarity_score:
                # Update running average of similarity scores
                current_avg = metrics.avg_similarity_score
                total_hits = metrics.cache_hits
                metrics.avg_similarity_score = (
                    current_avg * (total_hits - 1) + similarity_score
                ) / total_hits
            if is_stale:
                metrics.stale_cache_hits += 1
        else:
            metrics.cache_misses += 1

        if is_temporal:
            metrics.temporal_queries += 1

        # Update running average of response times
        current_avg = metrics.avg_response_time_ms
        total_queries = metrics.total_queries
        metrics.avg_response_time_ms = (
            current_avg * (total_queries - 1) + response_time_ms
        ) / total_queries

        logger.debug(
            f"Recorded query for tenant {tenant_id}: "
            f"hit={cache_hit}, temporal={is_temporal}, "
            f"response_time={response_time_ms:.2f}ms"
        )

    def get_current_metrics(self, tenant_id: str) -> Optional[CacheMetrics]:
        """
        Get current metrics for a tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            Optional[CacheMetrics]: Current metrics or None if not found
        """
        return self.current_metrics.get(tenant_id)

    def get_metrics_summary(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get a summary of cache metrics for a tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            Dict[str, Any]: Metrics summary
        """
        metrics = self.current_metrics.get(tenant_id)
        if not metrics:
            return {"error": "No metrics found for tenant"}

        return {
            "tenant_id": tenant_id,
            "period": {
                "start": metrics.timestamp.isoformat(),
                "end": datetime.now().isoformat(),
            },
            "performance": {
                "total_queries": metrics.total_queries,
                "cache_hit_rate": f"{metrics.hit_rate:.2%}",
                "avg_similarity_score": f"{metrics.avg_similarity_score:.3f}",
                "avg_response_time_ms": f"{metrics.avg_response_time_ms:.2f}",
                "temporal_query_rate": f"{metrics.temporal_query_rate:.2%}",
            },
            "cache_health": {
                "stale_hits": metrics.stale_cache_hits,
                "stale_hit_rate": f"{metrics.stale_cache_hits / max(metrics.cache_hits, 1):.2%}",
            },
        }

    async def get_cache_stats(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get detailed cache statistics from Redis.

        Args:
            tenant_id: Tenant identifier

        Returns:
            Dict[str, Any]: Cache statistics
        """
        try:
            cache_config = CacheConfig()
            async with SemanticCache(cache_config) as cache:
                stats = await cache.get_cache_stats(tenant_id)
                return stats
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}

    def reset_metrics(self, tenant_id: str):
        """
        Reset metrics for a tenant.

        Args:
            tenant_id: Tenant identifier
        """
        if tenant_id in self.current_metrics:
            # Archive current metrics
            if tenant_id not in self.metrics_history:
                self.metrics_history[tenant_id] = []

            self.metrics_history[tenant_id].append(self.current_metrics[tenant_id])

            # Reset current metrics
            self.current_metrics[tenant_id] = CacheMetrics(
                tenant_id=tenant_id, timestamp=datetime.now()
            )

            logger.info(f"Reset metrics for tenant {tenant_id}")

    def export_metrics(self, tenant_id: str, format: str = "json") -> str:
        """
        Export metrics for a tenant.

        Args:
            tenant_id: Tenant identifier
            format: Export format ("json" or "csv")

        Returns:
            str: Exported metrics
        """
        metrics = self.current_metrics.get(tenant_id)
        if not metrics:
            return ""

        if format == "json":
            return json.dumps(asdict(metrics), default=str, indent=2)
        elif format == "csv":
            # Simple CSV format
            headers = [
                "tenant_id",
                "timestamp",
                "total_queries",
                "cache_hits",
                "cache_misses",
                "hit_rate",
                "avg_similarity_score",
                "temporal_queries",
                "stale_cache_hits",
                "avg_response_time_ms",
            ]
            values = [
                metrics.tenant_id,
                metrics.timestamp.isoformat(),
                metrics.total_queries,
                metrics.cache_hits,
                metrics.cache_misses,
                f"{metrics.hit_rate:.3f}",
                f"{metrics.avg_similarity_score:.3f}",
                metrics.temporal_queries,
                metrics.stale_cache_hits,
                f"{metrics.avg_response_time_ms:.2f}",
            ]
            return ",".join(headers) + "\n" + ",".join(map(str, values))
        else:
            raise ValueError(f"Unsupported format: {format}")


# Global cache monitor instance
cache_monitor = CacheMonitor()


def record_cache_query(
    tenant_id: str,
    cache_hit: bool,
    similarity_score: Optional[float] = None,
    is_temporal: bool = False,
    is_stale: bool = False,
    response_time_ms: float = 0.0,
):
    """
    Convenience function to record cache query metrics.

    Args:
        tenant_id: Tenant identifier
        cache_hit: Whether the query resulted in a cache hit
        similarity_score: Similarity score for cache hits
        is_temporal: Whether temporal filtering was applied
        is_stale: Whether the cached result was stale
        response_time_ms: Response time in milliseconds
    """
    cache_monitor.record_query(
        tenant_id=tenant_id,
        cache_hit=cache_hit,
        similarity_score=similarity_score,
        is_temporal=is_temporal,
        is_stale=is_stale,
        response_time_ms=response_time_ms,
    )


async def get_tenant_cache_report(tenant_id: str) -> Dict[str, Any]:
    """
    Get comprehensive cache report for a tenant.

    Args:
        tenant_id: Tenant identifier

    Returns:
        Dict[str, Any]: Comprehensive cache report
    """
    metrics_summary = cache_monitor.get_metrics_summary(tenant_id)
    cache_stats = await cache_monitor.get_cache_stats(tenant_id)

    return {
        "tenant_id": tenant_id,
        "generated_at": datetime.now().isoformat(),
        "metrics": metrics_summary,
        "cache_stats": cache_stats,
    }
