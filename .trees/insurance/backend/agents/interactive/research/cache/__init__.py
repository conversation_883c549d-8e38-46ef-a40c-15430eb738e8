"""
Semantic Cache Module for Research Agent

This module provides semantic caching capabilities for the Research Agent,
enabling intelligent caching based on query similarity using embeddings.

Key Features:
- Embedding-based similarity matching using Voyage embeddings
- Redis-backed storage with TTL and LRU eviction
- Tenant isolation for security and compliance
- Async-first design for high performance
- Legal safeguards with freshness warnings
"""

from backend.agents.interactive.research.cache.semantic_cache import (
    SemanticCache,
    CacheResult,
    CacheConfig,
)

__all__ = [
    "SemanticCache",
    "CacheResult",
    "CacheConfig",
]
