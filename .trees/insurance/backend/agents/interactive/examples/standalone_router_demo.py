#!/usr/bin/env python3
"""
Standalone Enhanced Master Router Demo

This demo showcases the enhanced master router intent detection without dependencies.
It demonstrates how the router correctly identifies different types of user intents.

Usage:
    python standalone_router_demo.py
"""

import re
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def is_document_intent(user_input: str) -> bool:
    """Check if input indicates document-related intent."""
    user_input_lower = user_input.lower()

    # Document intent keywords
    document_keywords = [
        "create document",
        "generate document",
        "draft document",
        "write document",
        "new document",
        "make document",
        "compose document",
        "demand letter",
        "settlement agreement",
        "court filing",
        "legal brief",
        "contract",
        "agreement",
        "motion",
        "pleading",
        "complaint",
        "answer",
        "discovery request",
        "deposition notice",
        "subpoena",
        "draft letter",
        "draft agreement",
        "draft contract",
        "draft motion",
        "write letter",
        "write agreement",
        "write contract",
        "write brief",
        "write document",
        "compose letter",
        "compose agreement",
        "compose motion",
        "generate letter",
        "generate agreement",
        "generate contract",
        "edit document",
        "update document",
        "revise document",
        "modify document",
        "review document",
        "check document",
        "proofread document",
        "check the document",
        "document template",
        "legal template",
        "legal form",
    ]

    for keyword in document_keywords:
        if keyword in user_input_lower:
            return True

    # Document intent patterns
    document_patterns = [
        r"document\.",
        r"(?:draft|write|compose|generate).*(?:letter|agreement|contract|motion|brief|filing|pleading|document)",
        r"(?:demand|settlement|court|legal).*(?:letter|agreement|filing|brief|document)",
        r"(?:edit|update|revise|modify).*(?:document|letter|agreement|contract)",
        r"(?:create|generate|make).*(?:document|letter|agreement|contract|motion|brief)",
        r"document.*(?:template|form)",
        r"legal.*document",
    ]

    for pattern in document_patterns:
        if re.search(pattern, user_input_lower):
            return True

    return False


def is_deadline_intent(user_input: str) -> bool:
    """Check if input indicates deadline-related intent."""
    user_input_lower = user_input.lower()

    # Deadline intent keywords
    deadline_keywords = [
        "deadline",
        "due date",
        "statute of limitations",
        "filing deadline",
        "court deadline",
        "discovery deadline",
        "response deadline",
        "check deadlines",
        "upcoming deadlines",
        "deadline reminder",
        "calculate deadline",
        "deadline calculation",
        "when is due",
        "deadline tracker",
        "track deadline",
        "monitor deadline",
        "statute",
        "limitation period",
        "time limit",
        "filing period",
        "response time",
        "discovery cutoff",
        "trial date",
        "hearing date",
        "motion deadline",
        "appeal deadline",
        "settlement deadline",
        "deadline alert",
        "deadline notification",
        "deadline warning",
        "missed deadline",
        "approaching deadline",
        "overdue",
    ]

    for keyword in deadline_keywords:
        if keyword in user_input_lower:
            return True

    # Deadline intent patterns
    deadline_patterns = [
        r"deadline\.",
        r"(?:check|show|view|list|get).*deadline",
        r"(?:calculate|compute|determine).*(?:deadline|due.*date|statute)",
        r"(?:statute|limitation).*(?:period|deadline|date)",
        r"(?:filing|court|discovery|response).*(?:deadline|due|date)",
        r"deadline.*(?:check|calculate|track|monitor|alert|reminder)",
        r"when.*(?:due|deadline|expires|statute)",
        r"(?:upcoming|approaching|missed|overdue).*deadline",
    ]

    for pattern in deadline_patterns:
        if re.search(pattern, user_input_lower):
            return True

    return False


def demo_intent_detection(user_input: str, description: str) -> None:
    """Demo intent detection for a single input."""
    print(f"\n{'='*60}")
    print(f"Demo: {description}")
    print(f"Input: '{user_input}'")
    print(f"{'='*60}")

    # Test intent detection (deadline has priority over document)
    intents = []

    deadline_detected = is_deadline_intent(user_input)
    document_detected = is_document_intent(user_input)

    if deadline_detected:
        intents.append("deadline_agent")

    if document_detected:
        intents.append("document_agent")

    # Check for explicit namespaces
    user_lower = user_input.lower()
    if user_lower.startswith("document."):
        intents.append("document_agent (explicit)")
    elif user_lower.startswith("deadline."):
        intents.append("deadline_agent (explicit)")

    # Determine primary routing (explicit namespaces first, then deadline priority)
    if user_lower.startswith("document."):
        primary_agent = "document_agent"
    elif user_lower.startswith("deadline."):
        primary_agent = "deadline_agent"
    elif deadline_detected:
        primary_agent = "deadline_agent"  # Deadline has priority over document
    elif document_detected:
        primary_agent = "document_agent"
    else:
        primary_agent = "supervisor_agent"

    print(f"✅ Primary Route: {primary_agent}")

    if intents:
        print(f"🎯 Detected Intents: {', '.join(intents)}")
    else:
        print("🎯 No specific intents detected (would route to supervisor)")

    # Provide context
    agent_descriptions = {
        "document_agent": "Legal document generation and management",
        "deadline_agent": "Deadline tracking and calculation",
        "supervisor_agent": "Complex routing decisions and fallback",
    }

    description = agent_descriptions.get(primary_agent, "Unknown agent")
    print(f"📋 Agent Purpose: {description}")


def main():
    """Run the enhanced master router demo."""
    print("🚀 Enhanced Master Router Demo")
    print("Demonstrating NEW document and deadline agent routing")

    # Test cases focusing on new agents
    test_cases = [
        # Document intents (NEW!)
        ("Draft a demand letter for my client", "Document Intent - Document Creation"),
        ("Generate a settlement agreement", "Document Intent - Legal Document"),
        ("Write a legal document for the case", "Document Intent - General Document"),
        ("Edit document content", "Document Intent - Document Editing"),
        ("Create a contract for the client", "Document Intent - Contract Creation"),
        ("Review the filing", "Document Intent - Document Review"),
        ("document.create new contract", "Document Intent - Explicit Namespace"),
        # Deadline intents (NEW!)
        (
            "Check the statute of limitations for this case",
            "Deadline Intent - Statute Check",
        ),
        ("What are the upcoming deadlines?", "Deadline Intent - Deadline Tracking"),
        ("Calculate filing deadline", "Deadline Intent - Deadline Calculation"),
        ("When is the discovery cutoff?", "Deadline Intent - Legal Deadline"),
        ("Track deadline for motion", "Deadline Intent - Deadline Tracking"),
        ("deadline.track court dates", "Deadline Intent - Explicit Namespace"),
        # Edge cases and negative tests
        ("Create a task", "Negative Test - Should NOT be document intent"),
        ("Schedule a meeting", "Negative Test - Should NOT be deadline intent"),
        ("Research case law", "Negative Test - Should NOT be document/deadline"),
        ("Hello, how are you?", "Fallback Test - General greeting"),
    ]

    # Run all test cases
    for user_input, description in test_cases:
        demo_intent_detection(user_input, description)

    print(f"\n{'='*60}")
    print("✅ Demo completed successfully!")
    print("📊 Summary:")
    print(
        "  - ✨ Document Agent: NEW! Handles legal document generation and management"
    )
    print("  - ✨ Deadline Agent: NEW! Manages deadline tracking and calculations")
    print("  - 🎯 Enhanced routing with explicit namespace support")
    print("  - 🔍 Comprehensive intent detection with keyword and pattern matching")
    print("  - 🚀 Ready for integration with full master router!")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
