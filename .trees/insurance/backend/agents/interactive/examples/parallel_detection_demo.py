#!/usr/bin/env python3
"""
Parallel LLM + Keyword Detection Demo

This demo showcases the enhanced parallel detection system with:
1. Enhanced keyword detection with confidence scoring
2. Simulated LLM detection (for demo purposes)
3. Consensus validation and conflict resolution
4. Performance metrics and logging

Usage:
    python parallel_detection_demo.py
"""

import asyncio
import json
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Literal

# Standalone implementation to avoid dependency issues
import re


@dataclass
class RoutingResult:
    """Result from a single routing detection method."""

    agent: str
    confidence: float
    method: Literal["llm", "keyword", "consensus", "fallback", "explicit"]
    reasoning: str
    response_time: float


@dataclass
class KeywordDetectionResult:
    """Result from keyword detection with detailed scoring."""

    agent: str
    confidence_score: float  # 0.0 to 1.0
    matched_patterns: List[str]
    pattern_weights: Dict[str, float]


def _enhanced_keyword_detection(user_input: str) -> KeywordDetectionResult:
    """Enhanced keyword detection with weighted confidence scoring."""

    # High-confidence patterns (weight: 0.85-0.95)
    high_confidence_patterns = {
        "document_agent": [
            (r"draft\s+(demand\s+letter|settlement\s+agreement)", 0.95),
            (r"generate\s+(contract|agreement|motion)", 0.90),
            (r"write\s+(legal\s+document|brief|pleading)", 0.90),
            (r"create\s+(document|contract|agreement)", 0.85),
        ],
        "deadline_agent": [
            (r"statute\s+of\s+limitations", 0.95),
            (r"calculate\s+deadline", 0.90),
            (r"when\s+is\s+.+\s+due", 0.85),
            (r"track\s+deadline", 0.90),
            (r"filing\s+deadline", 0.90),
        ],
        "calendar_graph": [
            (r"schedule\s+(meeting|appointment|deposition)", 0.95),
            (r"book\s+(time|appointment)", 0.90),
            (r"calendar\s+(event|meeting)", 0.85),
        ],
        "task_graph": [
            (r"create\s+(task|todo)", 0.90),
            (r"add\s+task", 0.85),
            (r"mark\s+task\s+(complete|done)", 0.90),
        ],
    }

    # Medium-confidence patterns (weight: 0.6-0.8)
    medium_confidence_patterns = {
        "document_agent": [
            (r"document", 0.6),
            (r"contract", 0.7),
            (r"agreement", 0.6),
            (r"draft", 0.65),
            (r"motion", 0.65),
        ],
        "deadline_agent": [
            (r"deadline", 0.7),
            (r"due\s+date", 0.8),
            (r"filing\s+date", 0.8),
            (r"statute", 0.75),
        ],
        "calendar_graph": [
            (r"schedule", 0.7),
            (r"meeting", 0.6),
            (r"appointment", 0.7),
        ],
        "task_graph": [
            (r"task", 0.6),
            (r"todo", 0.65),
        ],
    }

    user_lower = user_input.lower()
    agent_scores = {}
    matched_patterns = {}
    pattern_weights = {}

    # Score high-confidence patterns first
    for agent, patterns in high_confidence_patterns.items():
        score = 0.0
        matches = []
        weights = {}
        for pattern, weight in patterns:
            if re.search(pattern, user_lower):
                score = max(score, weight)
                matches.append(pattern)
                weights[pattern] = weight
        if score > 0:
            agent_scores[agent] = score
            matched_patterns[agent] = matches
            pattern_weights[agent] = weights

    # If no high-confidence matches, check medium-confidence
    if not agent_scores:
        for agent, patterns in medium_confidence_patterns.items():
            score = 0.0
            matches = []
            weights = {}
            for pattern, weight in patterns:
                if re.search(pattern, user_lower):
                    score = max(score, weight)
                    matches.append(pattern)
                    weights[pattern] = weight
            if score > 0:
                agent_scores[agent] = score
                matched_patterns[agent] = matches
                pattern_weights[agent] = weights

    # Return best match or supervisor
    if agent_scores:
        best_agent = max(agent_scores.keys(), key=lambda k: agent_scores[k])
        return KeywordDetectionResult(
            agent=best_agent,
            confidence_score=agent_scores[best_agent],
            matched_patterns=matched_patterns[best_agent],
            pattern_weights=pattern_weights[best_agent],
        )
    else:
        return KeywordDetectionResult(
            agent="supervisor_agent",
            confidence_score=0.0,
            matched_patterns=[],
            pattern_weights={},
        )


def _is_deadline_document_conflict(agent1: str, agent2: str) -> bool:
    """Check if this is a deadline vs document conflict."""
    return {agent1, agent2} == {"deadline_agent", "document_agent"}


def _generate_consensus(
    llm_result: Optional[RoutingResult], keyword_result: RoutingResult, user_input: str
) -> RoutingResult:
    """Generate consensus routing decision from parallel detection results."""

    # Case 1: LLM failed or unavailable
    if llm_result is None:
        return RoutingResult(
            agent=keyword_result.agent,
            confidence=keyword_result.confidence,
            method="fallback",
            reasoning=f"LLM unavailable, using keyword result: {keyword_result.reasoning}",
            response_time=keyword_result.response_time,
        )

    # Case 2: Perfect agreement - boost confidence
    if llm_result.agent == keyword_result.agent:
        boosted_confidence = min(
            0.95, (llm_result.confidence + keyword_result.confidence) / 2 + 0.15
        )
        return RoutingResult(
            agent=llm_result.agent,
            confidence=boosted_confidence,
            method="consensus",
            reasoning=f"LLM and keyword agree: {llm_result.reasoning}",
            response_time=max(llm_result.response_time, keyword_result.response_time),
        )

    # Case 3: Disagreement - resolve with domain logic
    return _resolve_disagreement(llm_result, keyword_result, user_input)


def _resolve_disagreement(
    llm_result: RoutingResult, keyword_result: RoutingResult, user_input: str
) -> RoutingResult:
    """Resolve disagreements between LLM and keyword detection."""

    # High confidence keyword beats medium confidence LLM
    if keyword_result.confidence >= 0.85 and llm_result.confidence < 0.75:
        return RoutingResult(
            agent=keyword_result.agent,
            confidence=keyword_result.confidence * 0.9,
            method="keyword",
            reasoning=f"High-confidence keyword overrides LLM: {keyword_result.reasoning}",
            response_time=keyword_result.response_time,
        )

    # High confidence LLM beats medium confidence keyword
    if llm_result.confidence >= 0.8 and keyword_result.confidence < 0.7:
        return RoutingResult(
            agent=llm_result.agent,
            confidence=llm_result.confidence * 0.9,
            method="llm",
            reasoning=f"High-confidence LLM overrides keyword: {llm_result.reasoning}",
            response_time=llm_result.response_time,
        )

    # Special case: deadline vs document conflict
    if _is_deadline_document_conflict(llm_result.agent, keyword_result.agent):
        # Deadline action words take priority
        deadline_actions = [
            "track",
            "check",
            "calculate",
            "monitor",
            "when",
            "due",
            "expires",
        ]
        document_actions = ["draft", "write", "create", "generate", "edit", "review"]

        user_lower = user_input.lower()
        deadline_score = sum(1 for action in deadline_actions if action in user_lower)
        document_score = sum(1 for action in document_actions if action in user_lower)

        if deadline_score > document_score:
            preferred = (
                llm_result if llm_result.agent == "deadline_agent" else keyword_result
            )
        elif document_score > deadline_score:
            preferred = (
                llm_result if llm_result.agent == "document_agent" else keyword_result
            )
        else:
            preferred = (
                llm_result
                if llm_result.confidence >= keyword_result.confidence
                else keyword_result
            )

        return RoutingResult(
            agent=preferred.agent,
            confidence=preferred.confidence * 0.9,
            method="conflict_resolution",
            reasoning=f"Deadline vs document conflict resolved: {preferred.reasoning}",
            response_time=preferred.response_time,
        )

    # Default: prefer LLM for complex understanding
    if llm_result.confidence >= 0.65:
        return RoutingResult(
            agent=llm_result.agent,
            confidence=llm_result.confidence * 0.8,
            method="llm",
            reasoning=f"LLM chosen over keyword disagreement: {llm_result.reasoning}",
            response_time=llm_result.response_time,
        )

    # Low confidence all around - use supervisor
    return RoutingResult(
        agent="supervisor_agent",
        confidence=0.5,
        method="fallback",
        reasoning="Low confidence from both LLM and keyword detection",
        response_time=max(llm_result.response_time, keyword_result.response_time),
    )


@dataclass
class MockLLMResult:
    """Mock LLM result for demo purposes."""

    agent: str
    confidence: float
    reasoning: str


class MockLLMDetector:
    """Mock LLM detector that simulates different scenarios."""

    def __init__(self):
        # Predefined responses for demo scenarios
        self.responses = {
            "Draft a demand letter for my client": MockLLMResult(
                agent="document_agent",
                confidence=0.92,
                reasoning="User wants to create a legal document (demand letter)",
            ),
            "Track deadline for motion filing": MockLLMResult(
                agent="deadline_agent",
                confidence=0.88,
                reasoning="User wants to track a legal deadline, focus is on tracking not document creation",
            ),
            "Schedule a client meeting": MockLLMResult(
                agent="calendar_graph",
                confidence=0.95,
                reasoning="Clear scheduling request for calendar management",
            ),
            "Create a task to review contracts": MockLLMResult(
                agent="task_graph",
                confidence=0.85,
                reasoning="User wants to create a task item for contract review",
            ),
            "Research negligence law": MockLLMResult(
                agent="research_agent",
                confidence=0.90,
                reasoning="Legal research request for case law and statutes",
            ),
            "Draft motion for deadline extension": MockLLMResult(
                agent="document_agent",
                confidence=0.87,
                reasoning="Primary action is drafting a legal document (motion)",
            ),
            "When is the statute of limitations?": MockLLMResult(
                agent="deadline_agent",
                confidence=0.93,
                reasoning="Question about legal deadline/time limit",
            ),
            "Update client contact information": MockLLMResult(
                agent="matter_client_agent",
                confidence=0.89,
                reasoning="Client data management request",
            ),
            "Hello, how are you?": MockLLMResult(
                agent="supervisor_agent",
                confidence=0.45,
                reasoning="General greeting, unclear intent",
            ),
            # Conflict scenarios for testing
            "Review the motion deadline": MockLLMResult(
                agent="document_agent",  # LLM thinks it's about document review
                confidence=0.75,
                reasoning="User wants to review a document (motion)",
            ),
        }

    async def detect(self, user_input: str) -> Optional[RoutingResult]:
        """Simulate LLM detection with realistic delays."""
        # Simulate network delay
        await asyncio.sleep(0.1 + (len(user_input) * 0.001))  # 100ms + processing time

        # Simulate occasional failures (5% failure rate)
        import random

        if random.random() < 0.05:
            raise Exception("Simulated LLM API failure")

        mock_result = self.responses.get(user_input)
        if not mock_result:
            # Default response for unknown inputs
            mock_result = MockLLMResult(
                agent="supervisor_agent",
                confidence=0.6,
                reasoning="Unknown input, routing to supervisor for clarification",
            )

        return RoutingResult(
            agent=mock_result.agent,
            confidence=mock_result.confidence,
            method="llm",
            reasoning=mock_result.reasoning,
            response_time=0.1,
        )


async def simulate_parallel_detection(
    user_input: str, llm_detector: MockLLMDetector
) -> Dict:
    """Simulate parallel LLM + keyword detection."""
    start_time = time.time()

    print(f"\n{'='*80}")
    print(f"🔍 PARALLEL DETECTION: '{user_input}'")
    print(f"{'='*80}")

    # Start both detections in parallel
    print("⚡ Starting parallel detection...")

    async def keyword_detection():
        kw_start = time.time()
        result = _enhanced_keyword_detection(user_input)
        kw_time = time.time() - kw_start
        return RoutingResult(
            agent=result.agent,
            confidence=result.confidence_score,
            method="keyword",
            reasoning=f"Matched patterns: {', '.join(result.matched_patterns) if result.matched_patterns else 'no patterns'}",
            response_time=kw_time,
        )

    async def llm_detection():
        try:
            return await llm_detector.detect(user_input)
        except Exception as e:
            print(f"❌ LLM Detection Failed: {e}")
            return None

    # Execute in parallel
    try:
        llm_result, keyword_result = await asyncio.gather(
            llm_detection(), keyword_detection(), return_exceptions=True
        )

        # Handle exceptions
        if isinstance(llm_result, Exception):
            print(f"❌ LLM Error: {llm_result}")
            llm_result = None

        if isinstance(keyword_result, Exception):
            print(f"❌ Keyword Error: {keyword_result}")
            keyword_result = RoutingResult(
                agent="supervisor_agent",
                confidence=0.0,
                method="fallback",
                reasoning="Keyword detection failed",
                response_time=0.0,
            )

    except Exception as e:
        print(f"❌ Parallel execution failed: {e}")
        llm_result = None
        keyword_result = await keyword_detection()

    # Display results
    print(f"\n📊 DETECTION RESULTS:")
    print(f"┌─ 🤖 LLM Detection:")
    if llm_result:
        print(f"│   Agent: {llm_result.agent}")
        print(f"│   Confidence: {llm_result.confidence:.2f}")
        print(f"│   Reasoning: {llm_result.reasoning}")
        print(f"│   Time: {llm_result.response_time:.3f}s")
    else:
        print(f"│   Status: FAILED")

    print(f"├─ 🔤 Keyword Detection:")
    print(f"│   Agent: {keyword_result.agent}")
    print(f"│   Confidence: {keyword_result.confidence:.2f}")
    print(f"│   Reasoning: {keyword_result.reasoning}")
    print(f"│   Time: {keyword_result.response_time:.3f}s")

    # Generate consensus
    consensus_result = _generate_consensus(llm_result, keyword_result, user_input)

    print(f"└─ 🎯 CONSENSUS DECISION:")
    print(f"    Final Agent: {consensus_result.agent}")
    print(f"    Final Confidence: {consensus_result.confidence:.2f}")
    print(f"    Method: {consensus_result.method}")
    print(f"    Reasoning: {consensus_result.reasoning}")

    # Analysis
    total_time = time.time() - start_time
    print(f"\n⏱️  PERFORMANCE:")
    print(f"    Total Time: {total_time:.3f}s")

    if llm_result and keyword_result:
        agreement = (
            "✅ AGREE" if llm_result.agent == keyword_result.agent else "❌ DISAGREE"
        )
        print(f"    LLM vs Keyword: {agreement}")

        if llm_result.agent != keyword_result.agent:
            print(f"    Conflict Resolution: {consensus_result.method} method chosen")

            # Special analysis for deadline vs document conflicts
            if _is_deadline_document_conflict(llm_result.agent, keyword_result.agent):
                print(f"    🔥 Deadline vs Document Conflict Detected!")
                print(f"       Resolution Logic Applied")

    return {
        "user_input": user_input,
        "llm_result": llm_result,
        "keyword_result": keyword_result,
        "consensus_result": consensus_result,
        "total_time": total_time,
        "agreement": llm_result.agent == keyword_result.agent if llm_result else False,
    }


async def main():
    """Run the parallel detection demo."""
    print("🚀 PARALLEL LLM + KEYWORD DETECTION DEMO")
    print("Showcasing intelligent consensus-based routing")

    llm_detector = MockLLMDetector()

    # Test cases covering different scenarios
    test_cases = [
        # Perfect agreement cases
        "Draft a demand letter for my client",
        "Schedule a client meeting",
        "Create a task to review contracts",
        "Research negligence law",
        # Conflict resolution cases
        "Track deadline for motion filing",  # Should prefer deadline over document
        "Draft motion for deadline extension",  # Should prefer document over deadline
        "Review the motion deadline",  # Conflict case - keyword might see "motion" (document) but LLM sees "deadline"
        # Edge cases
        "When is the statute of limitations?",
        "Update client contact information",
        "Hello, how are you?",  # Low confidence case
        # LLM failure simulation (will randomly fail ~5% of the time)
        "Handle this complex legal matter",
    ]

    results = []
    agreements = 0
    total_cases = 0

    for test_case in test_cases:
        result = await simulate_parallel_detection(test_case, llm_detector)
        results.append(result)

        if result["llm_result"] and result["agreement"]:
            agreements += 1
        if result["llm_result"]:  # Only count cases where LLM succeeded
            total_cases += 1

        await asyncio.sleep(0.5)  # Pause between demos

    # Summary statistics
    print(f"\n{'='*80}")
    print(f"📈 DEMO SUMMARY")
    print(f"{'='*80}")
    print(f"Total Test Cases: {len(test_cases)}")
    print(
        f"LLM Success Rate: {total_cases}/{len(test_cases)} ({total_cases/len(test_cases)*100:.1f}%)"
    )
    if total_cases > 0:
        print(
            f"Agreement Rate: {agreements}/{total_cases} ({agreements/total_cases*100:.1f}%)"
        )

    avg_time = sum(r["total_time"] for r in results) / len(results)
    print(f"Average Response Time: {avg_time:.3f}s")

    # Method distribution
    methods = [r["consensus_result"].method for r in results]
    method_counts = {method: methods.count(method) for method in set(methods)}
    print(f"\nRouting Method Distribution:")
    for method, count in method_counts.items():
        print(f"  {method}: {count} ({count/len(results)*100:.1f}%)")

    print(f"\n✅ Demo completed! The parallel system provides:")
    print(f"   🎯 Higher accuracy through consensus validation")
    print(f"   ⚡ Fast response times with parallel execution")
    print(f"   🛡️  Reliable fallback when LLM fails")
    print(f"   🧠 Intelligent conflict resolution")


if __name__ == "__main__":
    asyncio.run(main())
