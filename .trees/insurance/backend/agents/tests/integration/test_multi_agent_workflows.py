"""
Multi-agent workflow integration tests.

This module tests complex workflows involving multiple agents working together,
including orchestration, data flow, and error handling across agent boundaries.
"""

import asyncio
import pytest
from unittest.mock import patch

from backend.agents.shared.testing import (
    BaseAgentTest,
    StateFactory,
    AgentFactory,
    AgentAssertions,
    MockManager,
    TestDataManager,
    WorkflowValidator,
    TestScenarioRunner,
)


class TestMultiAgentWorkflows(BaseAgentTest):
    """Test complex multi-agent workflows and orchestration."""

    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.mock_manager = MockManager()
        self.test_data_manager = TestDataManager("test_data")
        self.workflow_validator = WorkflowValidator()

    def teardown_method(self):
        """Clean up after each test method."""
        self.mock_manager.cleanup()
        self.workflow_validator.clear_trace()

    @pytest.mark.integration
    async def test_legal_research_pipeline(self):
        """Test complete legal research pipeline with multiple agents."""
        # Create specialized agents
        intake_agent = self.mock_manager.create_mock_agent("IntakeAgent")
        research_agent = self.mock_manager.create_mock_agent("ResearchAgent")
        analysis_agent = self.mock_manager.create_mock_agent("AnalysisAgent")
        summary_agent = self.mock_manager.create_mock_agent("SummaryAgent")

        # Mock external services
        mock_pinecone = self.mock_manager.create_mock_pinecone_client()
        mock_openai = self.mock_manager.create_mock_openai_client()

        # Configure research results
        mock_pinecone.Index().query.return_value = {
            "matches": [
                {
                    "id": "case-law-1",
                    "score": 0.95,
                    "metadata": {
                        "title": "Personal Injury Precedent Case",
                        "content": "Relevant case law for personal injury claims...",
                        "jurisdiction": "Texas",
                        "year": "2023",
                    },
                },
                {
                    "id": "statute-1",
                    "score": 0.88,
                    "metadata": {
                        "title": "Texas Civil Practice Code",
                        "content": "Statute of limitations provisions...",
                        "type": "statute",
                    },
                },
            ]
        }

        with (
            patch("pinecone.Pinecone", return_value=mock_pinecone),
            patch("openai.OpenAI", return_value=mock_openai),
        ):
            # Create initial state with user query
            state = StateFactory.create_empty_state()
            state.add_message(
                "user",
                "I was injured in a car accident in Texas 18 months ago. What are my legal options?",
            )

            # Execute research pipeline
            self.workflow_validator.trace_execution(
                "intake_start", {"query": "car accident injury"}
            )

            # 1. Intake Agent - Process and categorize the query
            await intake_agent.initialize(state)
            await intake_agent.execute(state)
            state.add_message(
                "system",
                "Query categorized as: personal injury, motor vehicle accident, Texas jurisdiction",
            )
            await intake_agent.cleanup(state)

            self.workflow_validator.trace_execution(
                "intake_complete", {"category": "personal_injury"}
            )

            # 2. Research Agent - Find relevant legal information
            await research_agent.initialize(state)
            await research_agent.execute(state)
            state.add_message(
                "system", "Research completed: Found 2 relevant legal documents"
            )
            await research_agent.cleanup(state)

            self.workflow_validator.trace_execution(
                "research_complete", {"documents_found": 2}
            )

            # 3. Analysis Agent - Analyze the research results
            await analysis_agent.initialize(state)
            await analysis_agent.execute(state)
            state.add_message(
                "system",
                "Analysis: Case is within statute of limitations, strong precedent available",
            )
            await analysis_agent.cleanup(state)

            self.workflow_validator.trace_execution(
                "analysis_complete", {"within_statute": True}
            )

            # 4. Summary Agent - Generate final response
            await summary_agent.initialize(state)
            await summary_agent.execute(state)
            state.add_message(
                "assistant",
                "Based on my research, you have viable legal options for your car accident injury. The 2-year statute of limitations in Texas means you still have time to file a claim.",
            )
            await summary_agent.cleanup(state)

            self.workflow_validator.trace_execution(
                "summary_complete", {"response_generated": True}
            )

            # Validate the complete workflow
            AgentAssertions.assert_state_valid(state)
            AgentAssertions.assert_message_contains(state, "viable legal options")
            AgentAssertions.assert_message_contains(state, "statute of limitations")

            # Verify all agents completed their lifecycle
            for agent in [intake_agent, research_agent, analysis_agent, summary_agent]:
                AgentAssertions.assert_agent_lifecycle_completed(agent)

            # Validate workflow execution
            validation_results = self.workflow_validator.validate_workflow()
            assert (
                len(validation_results["trace"]) == 5
            ), "Workflow should have 5 traced steps"

    @pytest.mark.integration
    async def test_document_processing_workflow(self):
        """Test document processing workflow with multiple specialized agents."""
        # Create document processing agents
        upload_agent = self.mock_manager.create_mock_agent("DocumentUploadAgent")
        ocr_agent = self.mock_manager.create_mock_agent("OCRAgent")
        classification_agent = self.mock_manager.create_mock_agent(
            "ClassificationAgent"
        )
        extraction_agent = self.mock_manager.create_mock_agent("ExtractionAgent")
        storage_agent = self.mock_manager.create_mock_agent("StorageAgent")

        # Mock tool executor for document processing
        tool_responses = {
            "document_upload": {
                "document_id": "doc-12345",
                "file_type": "pdf",
                "size_bytes": 1024000,
                "pages": 15,
            },
            "ocr_processor": {
                "text_content": "This is a legal contract between parties...",
                "confidence": 0.95,
                "pages_processed": 15,
            },
            "document_classifier": {
                "document_type": "contract",
                "confidence": 0.88,
                "categories": ["legal", "contract", "commercial"],
            },
            "entity_extractor": {
                "entities": [
                    {"type": "person", "value": "John Doe", "confidence": 0.92},
                    {"type": "date", "value": "2024-01-15", "confidence": 0.95},
                    {"type": "amount", "value": "$50,000", "confidence": 0.89},
                ]
            },
            "document_storage": {
                "storage_id": "storage-67890",
                "indexed": True,
                "searchable": True,
            },
        }

        mock_executor = self.mock_manager.create_mock_tool_executor(tool_responses)

        with patch(
            "shared.core.tools.executor.ToolExecutor", return_value=mock_executor
        ):
            # Create initial state with document upload request
            state = StateFactory.create_empty_state()
            state.add_message("user", "Please process the uploaded contract document")
            state.metadata["uploaded_file"] = "contract.pdf"

            # Execute document processing workflow

            # 1. Upload Agent - Handle document upload
            await upload_agent.initialize(state)
            await upload_agent.execute(state)
            upload_result = await mock_executor.execute_tool(
                "document_upload",
                {"file": "contract.pdf"},
                state.user_context["tenant_id"],
            )
            state.metadata["document_id"] = upload_result["document_id"]
            await upload_agent.cleanup(state)

            # 2. OCR Agent - Extract text from document
            await ocr_agent.initialize(state)
            await ocr_agent.execute(state)
            ocr_result = await mock_executor.execute_tool(
                "ocr_processor",
                {"document_id": state.metadata["document_id"]},
                state.user_context["tenant_id"],
            )
            state.metadata["text_content"] = ocr_result["text_content"]
            await ocr_agent.cleanup(state)

            # 3. Classification Agent - Classify document type
            await classification_agent.initialize(state)
            await classification_agent.execute(state)
            classification_result = await mock_executor.execute_tool(
                "document_classifier",
                {"text": state.metadata["text_content"]},
                state.user_context["tenant_id"],
            )
            state.metadata["document_type"] = classification_result["document_type"]
            await classification_agent.cleanup(state)

            # 4. Extraction Agent - Extract entities and key information
            await extraction_agent.initialize(state)
            await extraction_agent.execute(state)
            extraction_result = await mock_executor.execute_tool(
                "entity_extractor",
                {"text": state.metadata["text_content"]},
                state.user_context["tenant_id"],
            )
            state.metadata["entities"] = extraction_result["entities"]
            await extraction_agent.cleanup(state)

            # 5. Storage Agent - Store processed document
            await storage_agent.initialize(state)
            await storage_agent.execute(state)
            storage_result = await mock_executor.execute_tool(
                "document_storage",
                {"document_data": state.metadata},
                state.user_context["tenant_id"],
            )
            state.metadata["storage_id"] = storage_result["storage_id"]
            await storage_agent.cleanup(state)

            # Add final response
            state.add_message(
                "assistant",
                f"Document processed successfully. Type: {state.metadata['document_type']}, Entities found: {len(state.metadata['entities'])}",
            )

            # Validate the workflow
            AgentAssertions.assert_state_valid(state)
            AgentAssertions.assert_message_contains(state, "processed successfully")

            # Verify all tools were executed
            AgentAssertions.assert_tool_executed(mock_executor, "document_upload", 1)
            AgentAssertions.assert_tool_executed(mock_executor, "ocr_processor", 1)
            AgentAssertions.assert_tool_executed(
                mock_executor, "document_classifier", 1
            )
            AgentAssertions.assert_tool_executed(mock_executor, "entity_extractor", 1)
            AgentAssertions.assert_tool_executed(mock_executor, "document_storage", 1)

            # Verify metadata flow
            assert state.metadata["document_type"] == "contract"
            assert len(state.metadata["entities"]) == 3
            assert state.metadata["storage_id"] == "storage-67890"

    @pytest.mark.integration
    async def test_case_management_orchestration(self):
        """Test case management workflow with orchestration between agents."""
        # Create case management agents
        case_creator = self.mock_manager.create_mock_agent("CaseCreatorAgent")
        task_manager = self.mock_manager.create_mock_agent("TaskManagerAgent")
        calendar_agent = self.mock_manager.create_mock_agent("CalendarAgent")
        notification_agent = self.mock_manager.create_mock_agent("NotificationAgent")

        # Mock database operations
        mock_session = self.mock_manager.create_mock_database_session()

        # Configure database responses
        mock_session.query().filter().first.return_value = None  # No existing case
        mock_session.add.return_value = None
        mock_session.commit.return_value = None

        with patch("sqlalchemy.orm.sessionmaker", return_value=lambda: mock_session):
            # Create initial state with case creation request
            state = StateFactory.create_empty_state()
            state.add_message(
                "user", "Create a new personal injury case for client John Doe"
            )

            # Case data
            case_data = {
                "client_name": "John Doe",
                "case_type": "personal_injury",
                "incident_date": "2024-01-15",
                "description": "Motor vehicle accident on Highway 35",
            }
            state.metadata["case_data"] = case_data

            # Execute case management workflow

            # 1. Case Creator - Create the case record
            await case_creator.initialize(state)
            await case_creator.execute(state)
            state.metadata["case_id"] = "case-12345"
            state.add_message(
                "system", f"Case created with ID: {state.metadata['case_id']}"
            )
            await case_creator.cleanup(state)

            # 2. Task Manager - Create initial tasks
            await task_manager.initialize(state)
            await task_manager.execute(state)

            initial_tasks = [
                {
                    "id": "task-1",
                    "title": "Gather medical records",
                    "due_date": "2024-02-01",
                },
                {
                    "id": "task-2",
                    "title": "Contact insurance company",
                    "due_date": "2024-01-25",
                },
                {
                    "id": "task-3",
                    "title": "Interview witnesses",
                    "due_date": "2024-02-10",
                },
            ]
            state.metadata["tasks"] = initial_tasks
            state.add_message("system", f"Created {len(initial_tasks)} initial tasks")
            await task_manager.cleanup(state)

            # 3. Calendar Agent - Schedule important dates
            await calendar_agent.initialize(state)
            await calendar_agent.execute(state)

            calendar_events = [
                {"id": "event-1", "title": "Client consultation", "date": "2024-01-20"},
                {
                    "id": "event-2",
                    "title": "Medical records review",
                    "date": "2024-02-05",
                },
                {
                    "id": "event-3",
                    "title": "Case strategy meeting",
                    "date": "2024-02-15",
                },
            ]
            state.metadata["calendar_events"] = calendar_events
            state.add_message(
                "system", f"Scheduled {len(calendar_events)} calendar events"
            )
            await calendar_agent.cleanup(state)

            # 4. Notification Agent - Send notifications
            await notification_agent.initialize(state)
            await notification_agent.execute(state)

            notifications = [
                {
                    "type": "email",
                    "recipient": "<EMAIL>",
                    "subject": "New case assigned",
                },
                {
                    "type": "sms",
                    "recipient": "+**********",
                    "message": "Case tasks created",
                },
                {"type": "dashboard", "message": "Case dashboard updated"},
            ]
            state.metadata["notifications"] = notifications
            state.add_message("system", f"Sent {len(notifications)} notifications")
            await notification_agent.cleanup(state)

            # Add final response
            state.add_message(
                "assistant",
                f"Case management setup complete for {case_data['client_name']}. Case ID: {state.metadata['case_id']}, Tasks: {len(initial_tasks)}, Events: {len(calendar_events)}",
            )

            # Validate the orchestration
            AgentAssertions.assert_state_valid(state)
            AgentAssertions.assert_message_contains(
                state, "Case management setup complete"
            )

            # Verify all agents completed
            for agent in [
                case_creator,
                task_manager,
                calendar_agent,
                notification_agent,
            ]:
                AgentAssertions.assert_agent_lifecycle_completed(agent)

            # Verify data flow between agents
            assert state.metadata["case_id"] == "case-12345"
            assert len(state.metadata["tasks"]) == 3
            assert len(state.metadata["calendar_events"]) == 3
            assert len(state.metadata["notifications"]) == 3

            # Verify database interactions
            mock_session.add.assert_called()
            mock_session.commit.assert_called()

    @pytest.mark.integration
    async def test_error_propagation_across_agents(self):
        """Test error handling and propagation across multiple agents."""
        # Create agents with potential failure points
        agent1 = self.mock_manager.create_mock_agent("Agent1")
        agent2 = self.mock_manager.create_mock_agent("Agent2")
        agent3 = self.mock_manager.create_mock_agent("Agent3")

        # Configure agent2 to fail
        agent2.execute.side_effect = Exception("Agent2 processing error")

        state = StateFactory.create_empty_state()
        state.add_message("user", "Process this request through all agents")

        # Execute workflow with error handling
        try:
            # Agent 1 - Should succeed
            await agent1.initialize(state)
            await agent1.execute(state)
            state.add_message("system", "Agent1 completed successfully")
            await agent1.cleanup(state)

            # Agent 2 - Should fail
            await agent2.initialize(state)
            try:
                await agent2.execute(state)
            except Exception as e:
                state.add_message("system", f"Agent2 failed: {str(e)}", {"error": True})
                # Continue with error recovery
            await agent2.cleanup(state)

            # Agent 3 - Should handle the error and continue
            await agent3.initialize(state)
            await agent3.execute(state)
            state.add_message("system", "Agent3 completed with error recovery")
            await agent3.cleanup(state)

        except Exception as e:
            state.add_message("system", f"Workflow failed: {str(e)}", {"error": True})

        # Validate error handling
        AgentAssertions.assert_state_valid(state)
        AgentAssertions.assert_message_contains(state, "Agent2 failed")
        AgentAssertions.assert_message_contains(state, "error recovery")

        # Verify agent execution patterns
        AgentAssertions.assert_agent_lifecycle_completed(agent1)
        agent2.initialize.assert_called_once()
        agent2.execute.assert_called_once()
        agent2.cleanup.assert_called_once()
        AgentAssertions.assert_agent_lifecycle_completed(agent3)

    @pytest.mark.integration
    async def test_concurrent_multi_agent_execution(self):
        """Test concurrent execution of multiple independent agent workflows."""
        # Create multiple agent sets for parallel workflows
        workflow1_agents = [
            self.mock_manager.create_mock_agent("W1_Agent1"),
            self.mock_manager.create_mock_agent("W1_Agent2"),
        ]

        workflow2_agents = [
            self.mock_manager.create_mock_agent("W2_Agent1"),
            self.mock_manager.create_mock_agent("W2_Agent2"),
        ]

        async def execute_workflow(agents, workflow_id):
            state = StateFactory.create_empty_state(tenant_id=f"tenant-{workflow_id}")
            state.add_message("user", f"Execute workflow {workflow_id}")

            for i, agent in enumerate(agents):
                await agent.initialize(state)
                await agent.execute(state)
                state.add_message(
                    "system", f"Workflow {workflow_id} - Agent {i+1} completed"
                )
                await agent.cleanup(state)

            return state

        # Execute workflows concurrently
        workflow1_task = asyncio.create_task(execute_workflow(workflow1_agents, 1))
        workflow2_task = asyncio.create_task(execute_workflow(workflow2_agents, 2))

        results = await asyncio.gather(workflow1_task, workflow2_task)

        # Validate concurrent execution
        state1, state2 = results

        AgentAssertions.assert_state_valid(state1)
        AgentAssertions.assert_state_valid(state2)
        AgentAssertions.assert_tenant_isolation(state1, "tenant-1")
        AgentAssertions.assert_tenant_isolation(state2, "tenant-2")

        # Verify all agents completed
        for agent in workflow1_agents + workflow2_agents:
            AgentAssertions.assert_agent_lifecycle_completed(agent)
