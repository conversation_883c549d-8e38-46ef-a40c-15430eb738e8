"""
Unit tests for base agent functionality.

This module tests the core functionality of the BaseAgent class,
including lifecycle management, state handling, and tool execution.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock

from backend.agents.shared.testing import (
    BaseAgentTest,
    StateFactory,
    AgentFactory,
    AgentAssertions,
    MockManager,
)
from shared.core.base_agent import BaseAgent, AgentConfig
from shared.core.state import AiLexState


class TestBaseAgent(BaseAgentTest):
    """Test base agent functionality."""

    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.mock_manager = MockManager()
        self.agent_config = AgentFactory.create_agent_config(
            name="TestAgent", description="Test agent for unit testing"
        )

    def teardown_method(self):
        """Clean up after each test method."""
        self.mock_manager.cleanup()

    @pytest.mark.unit
    def test_agent_config_creation(self):
        """Test agent configuration creation."""
        config = AgentFactory.create_agent_config()

        assert config.name == "TestAgent"
        assert config.description == "Test agent for unit testing"
        assert isinstance(config.tools, list)
        assert config.model == "gpt-4o"
        assert config.temperature == 0.7

    @pytest.mark.unit
    def test_state_factory_empty_state(self):
        """Test empty state creation."""
        state = StateFactory.create_empty_state()

        AgentAssertions.assert_state_valid(state)
        assert len(state.messages) == 0
        assert state.user_context["role"] == "attorney"

    @pytest.mark.unit
    def test_state_factory_with_messages(self):
        """Test state creation with messages."""
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"},
        ]

        state = StateFactory.create_state_with_messages(messages)

        AgentAssertions.assert_state_valid(state)
        AgentAssertions.assert_message_count(state, 2)
        AgentAssertions.assert_message_added(state, "user", "Hello", 0)
        AgentAssertions.assert_message_added(state, "assistant", "Hi there!", 1)

    @pytest.mark.unit
    def test_conversation_state_creation(self):
        """Test conversation state creation."""
        state = StateFactory.create_conversation_state(conversation_length=2)

        AgentAssertions.assert_state_valid(state)
        AgentAssertions.assert_message_count(state, 4)  # 2 pairs of messages
        AgentAssertions.assert_message_contains(state, "legal case")

    @pytest.mark.unit
    def test_error_state_creation(self):
        """Test error state creation."""
        error_message = "Test error occurred"
        state = StateFactory.create_error_state(error_message)

        AgentAssertions.assert_state_valid(state)
        AgentAssertions.assert_message_count(state, 1)
        AgentAssertions.assert_message_added(state, "system", error_message)

    @pytest.mark.unit
    def test_multi_tenant_states(self):
        """Test multi-tenant state creation."""
        states = StateFactory.create_multi_tenant_states(
            tenant_count=3, messages_per_tenant=2
        )

        assert len(states) == 3

        for i, state in enumerate(states):
            AgentAssertions.assert_state_valid(state)
            AgentAssertions.assert_tenant_isolation(state, f"tenant-{i+1}")
            AgentAssertions.assert_message_count(state, 4)  # 2 pairs of messages

    @pytest.mark.unit
    def test_mock_agent_creation(self):
        """Test mock agent creation."""
        mock_agent = self.mock_manager.create_mock_agent("TestAgent", self.agent_config)

        assert mock_agent.config == self.agent_config
        assert mock_agent.name == "TestAgent"
        assert hasattr(mock_agent, "initialize")
        assert hasattr(mock_agent, "execute")
        assert hasattr(mock_agent, "cleanup")

    @pytest.mark.unit
    async def test_mock_agent_lifecycle(self):
        """Test mock agent lifecycle execution."""
        mock_agent = self.mock_manager.create_mock_agent("TestAgent", self.agent_config)
        state = StateFactory.create_empty_state()

        # Execute lifecycle
        await mock_agent.initialize(state)
        await mock_agent.execute(state)
        await mock_agent.cleanup(state)

        # Verify calls
        AgentAssertions.assert_agent_lifecycle_completed(mock_agent)

    @pytest.mark.unit
    def test_mock_tool_executor(self):
        """Test mock tool executor."""
        tool_responses = {"test_tool": {"result": "success", "data": "test_data"}}

        mock_executor = self.mock_manager.create_mock_tool_executor(tool_responses)

        assert mock_executor is not None
        assert hasattr(mock_executor, "execute_tool")

    @pytest.mark.unit
    async def test_mock_tool_execution(self):
        """Test mock tool execution."""
        tool_responses = {"test_tool": {"result": "success", "data": "test_data"}}

        mock_executor = self.mock_manager.create_mock_tool_executor(tool_responses)

        result = await mock_executor.execute_tool(
            "test_tool", {"arg1": "value1"}, "tenant-123"
        )

        assert result["result"] == "success"
        assert result["data"] == "test_data"

        AgentAssertions.assert_tool_executed(mock_executor, "test_tool", 1)

    @pytest.mark.unit
    def test_agent_assertions_state_validation(self):
        """Test agent assertions for state validation."""
        valid_state = StateFactory.create_empty_state()

        # This should not raise an exception
        AgentAssertions.assert_state_valid(valid_state)

        # Test invalid state
        invalid_state = AiLexState(messages=[], user_context={}, thread_id="")

        with pytest.raises(AssertionError):
            AgentAssertions.assert_state_valid(invalid_state)

    @pytest.mark.unit
    def test_agent_assertions_message_validation(self):
        """Test agent assertions for message validation."""
        state = StateFactory.create_empty_state()
        state.add_message("user", "Test message")

        AgentAssertions.assert_message_count(state, 1)
        AgentAssertions.assert_message_added(state, "user", "Test message")
        AgentAssertions.assert_message_contains(state, "Test message")

        # Test assertion failures
        with pytest.raises(AssertionError):
            AgentAssertions.assert_message_count(state, 2)

        with pytest.raises(AssertionError):
            AgentAssertions.assert_message_added(state, "assistant", "Wrong message")

    @pytest.mark.unit
    def test_tenant_isolation_assertion(self):
        """Test tenant isolation assertion."""
        tenant_id = "tenant-123"
        state = StateFactory.create_empty_state(tenant_id=tenant_id)

        # This should not raise an exception
        AgentAssertions.assert_tenant_isolation(state, tenant_id)

        # Test with wrong tenant ID
        with pytest.raises(AssertionError):
            AgentAssertions.assert_tenant_isolation(state, "wrong-tenant")

    @pytest.mark.unit
    async def test_performance_measurement(self):
        """Test performance measurement utilities."""

        async def test_operation():
            import asyncio

            await asyncio.sleep(0.1)  # Simulate work
            return {"result": "success"}

        result, duration = await self.measure_performance(test_operation())

        assert result["result"] == "success"
        assert duration >= 0.1
        assert duration < 1.0  # Should complete quickly

    @pytest.mark.unit
    async def test_timeout_handling(self):
        """Test timeout handling in async operations."""

        async def slow_operation():
            import asyncio

            await asyncio.sleep(2.0)  # Simulate slow operation
            return {"result": "success"}

        with pytest.raises(asyncio.TimeoutError):
            await self.run_with_timeout(slow_operation(), timeout=0.5)

    @pytest.mark.unit
    def test_user_context_validation(self):
        """Test user context validation."""
        valid_context = {
            "user_id": "user-123",
            "tenant_id": "tenant-123",
            "role": "attorney",
            "assigned_case_ids": [],
            "settings": {},
        }

        # This should not raise an exception
        AgentAssertions.assert_user_context_valid(valid_context)

        # Test invalid context
        invalid_context = {
            "user_id": "",  # Empty user_id
            "tenant_id": "tenant-123",
            "role": "attorney",
        }

        with pytest.raises(AssertionError):
            AgentAssertions.assert_user_context_valid(invalid_context)
