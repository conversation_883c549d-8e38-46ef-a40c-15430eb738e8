"""
Advanced performance tests using load testing and chaos engineering.

This module demonstrates advanced performance testing capabilities including
load testing, chaos engineering, and complex scenario validation.
"""

import asyncio
import pytest

from backend.agents.shared.testing import (
    BaseAgentTest,
    StateFactory,
    MockManager,
    LoadTester,
    LoadTestConfig,
    ChaosEngineer,
    ChaosConfig,
    WorkflowValidator,
    TestScenarioRunner,
)


class TestAdvancedPerformance(BaseAgentTest):
    """Advanced performance testing with load testing and chaos engineering."""

    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.mock_manager = MockManager()

    def teardown_method(self):
        """Clean up after each test method."""
        self.mock_manager.cleanup()

    @pytest.mark.performance
    async def test_load_testing_agent_execution(self, load_tester):
        """Test agent execution under load."""
        agent = self.mock_manager.create_mock_agent("LoadTestAgent")

        async def execute_agent_workflow():
            state = StateFactory.create_empty_state()
            await agent.initialize(state)
            await agent.execute(state)
            await agent.cleanup(state)
            return {"status": "completed", "messages": len(state.messages)}

        # Run load test
        results = await load_tester.run_load_test(execute_agent_workflow)

        # Validate load test results
        assert (
            results["success_rate"] >= 0.95
        ), f"Success rate too low: {results['success_rate']}"
        assert (
            results["avg_response_time"] < 1.0
        ), f"Average response time too high: {results['avg_response_time']}"
        assert (
            results["throughput"] >= 10.0
        ), f"Throughput too low: {results['throughput']}"

        # Validate percentiles
        assert (
            results["percentiles"]["p95"] < 2.0
        ), "95th percentile response time too high"
        assert (
            results["percentiles"]["p99"] < 5.0
        ), "99th percentile response time too high"

    @pytest.mark.performance
    async def test_high_concurrency_load(self):
        """Test system behavior under high concurrency."""
        config = LoadTestConfig(
            concurrent_users=20,
            requests_per_user=10,
            ramp_up_time=2.0,
            think_time_min=0.05,
            think_time_max=0.2,
        )
        load_tester = LoadTester(config)

        agent = self.mock_manager.create_mock_agent("HighConcurrencyAgent")

        async def high_load_operation():
            state = StateFactory.create_conversation_state(conversation_length=5)
            await agent.execute(state)
            return {"processed_messages": len(state.messages)}

        results = await load_tester.run_load_test(high_load_operation)

        # Validate high concurrency performance
        assert results["total_requests"] == 200  # 20 users * 10 requests
        assert results["success_rate"] >= 0.90, "High concurrency success rate too low"
        assert results["throughput"] >= 20.0, "High concurrency throughput too low"

    @pytest.mark.performance
    async def test_chaos_engineering_resilience(self, chaos_engineer):
        """Test system resilience using chaos engineering."""
        agent = self.mock_manager.create_mock_agent("ResilientAgent")

        # Configure chaos to inject failures
        chaos_config = ChaosConfig(
            failure_rate=0.3,  # 30% failure rate
            latency_injection=True,
            latency_min=0.1,
            latency_max=1.0,
        )
        chaos_engineer = ChaosEngineer(chaos_config)

        success_count = 0
        total_attempts = 20

        async with chaos_engineer.inject_chaos():
            for i in range(total_attempts):
                try:
                    state = StateFactory.create_empty_state()

                    # Inject chaos before each operation
                    await chaos_engineer.maybe_inject_failure(f"operation_{i}")

                    await agent.initialize(state)
                    await agent.execute(state)
                    await agent.cleanup(state)

                    success_count += 1

                except Exception:
                    # Expected due to chaos injection
                    pass

        # Validate resilience - should handle some failures gracefully
        success_rate = success_count / total_attempts
        assert (
            success_rate >= 0.5
        ), f"System not resilient enough: {success_rate} success rate"
        assert (
            success_rate <= 0.85
        ), f"Chaos not effective enough: {success_rate} success rate"

    @pytest.mark.performance
    async def test_workflow_validation_under_load(self, workflow_validator):
        """Test workflow validation under load conditions."""
        agent = self.mock_manager.create_mock_agent("WorkflowAgent")

        # Add validation rules
        def validate_initialization(trace):
            init_steps = [step for step in trace if "initialize" in step["step"]]
            return len(init_steps) > 0

        def validate_execution_order(trace):
            steps = [step["step"] for step in trace]
            expected_order = ["initialize", "execute", "cleanup"]
            return all(step in steps for step in expected_order)

        workflow_validator.add_validation_rule(
            "initialization", validate_initialization
        )
        workflow_validator.add_validation_rule(
            "execution_order", validate_execution_order
        )

        # Run workflow under load
        config = LoadTestConfig(concurrent_users=10, requests_per_user=5)
        load_tester = LoadTester(config)

        async def validated_workflow():
            state = StateFactory.create_empty_state()

            workflow_validator.trace_execution("initialize", {"agent": "WorkflowAgent"})
            await agent.initialize(state)

            workflow_validator.trace_execution(
                "execute", {"state_messages": len(state.messages)}
            )
            await agent.execute(state)

            workflow_validator.trace_execution("cleanup", {"final_state": "cleaned"})
            await agent.cleanup(state)

            return {"status": "completed"}

        # Execute load test
        results = await load_tester.run_load_test(validated_workflow)

        # Validate workflow
        validation_results = workflow_validator.validate_workflow()

        assert results["success_rate"] >= 0.95, "Load test success rate too low"
        assert validation_results[
            "passed"
        ], f"Workflow validation failed: {validation_results}"

    @pytest.mark.performance
    async def test_scenario_based_performance(self, scenario_runner):
        """Test performance using scenario-based testing."""
        agent = self.mock_manager.create_mock_agent("ScenarioAgent")

        # Define test scenarios
        async def setup_light_load():
            return StateFactory.create_empty_state()

        async def test_light_load():
            state = StateFactory.create_empty_state()
            start_time = asyncio.get_event_loop().time()
            await agent.execute(state)
            duration = asyncio.get_event_loop().time() - start_time
            return {"duration": duration, "load": "light"}

        async def setup_heavy_load():
            return StateFactory.create_conversation_state(conversation_length=20)

        async def test_heavy_load():
            state = StateFactory.create_conversation_state(conversation_length=20)
            start_time = asyncio.get_event_loop().time()
            await agent.execute(state)
            duration = asyncio.get_event_loop().time() - start_time
            return {"duration": duration, "load": "heavy"}

        async def test_error_scenario():
            # This should fail
            raise Exception("Intentional test failure")

        # Add scenarios
        scenario_runner.add_scenario(
            "light_load_performance",
            setup_light_load,
            test_light_load,
            expected_outcome="success",
        )

        scenario_runner.add_scenario(
            "heavy_load_performance",
            setup_heavy_load,
            test_heavy_load,
            expected_outcome="success",
        )

        scenario_runner.add_scenario(
            "error_handling", None, test_error_scenario, expected_outcome="failure"
        )

        # Run all scenarios
        results = await scenario_runner.run_all_scenarios()

        # Validate scenario results
        assert len(results) == 3, "Not all scenarios executed"

        light_load_result = next(
            r for r in results if r["name"] == "light_load_performance"
        )
        heavy_load_result = next(
            r for r in results if r["name"] == "heavy_load_performance"
        )
        error_result = next(r for r in results if r["name"] == "error_handling")

        assert light_load_result["success"], "Light load scenario failed"
        assert heavy_load_result["success"], "Heavy load scenario failed"
        assert error_result[
            "success"
        ], "Error scenario should have succeeded (expected failure)"

        # Performance assertions
        assert light_load_result["test_result"]["duration"] < 1.0, "Light load too slow"
        assert heavy_load_result["test_result"]["duration"] < 5.0, "Heavy load too slow"

    @pytest.mark.performance
    async def test_memory_pressure_performance(self):
        """Test performance under memory pressure conditions."""
        agent = self.mock_manager.create_mock_agent("MemoryAgent")

        # Create memory pressure by generating large states
        large_states = []
        for i in range(10):
            state = StateFactory.create_conversation_state(conversation_length=100)
            # Add large metadata to increase memory usage
            state.metadata["large_data"] = "x" * 10000  # 10KB per state
            large_states.append(state)

        async def memory_intensive_operation():
            # Process all large states
            for state in large_states:
                await agent.execute(state)
            return {"processed_states": len(large_states)}

        # Measure performance under memory pressure
        start_time = asyncio.get_event_loop().time()
        result = await memory_intensive_operation()
        duration = asyncio.get_event_loop().time() - start_time

        # Validate memory performance
        assert result["processed_states"] == 10, "Not all states processed"
        assert duration < 10.0, f"Memory intensive operation too slow: {duration}s"

    @pytest.mark.performance
    async def test_concurrent_chaos_load(self):
        """Test system under concurrent load with chaos injection."""
        agent = self.mock_manager.create_mock_agent("ChaosLoadAgent")

        # Configure moderate chaos
        chaos_config = ChaosConfig(
            failure_rate=0.2, latency_injection=True, latency_min=0.05, latency_max=0.5
        )
        chaos_engineer = ChaosEngineer(chaos_config)

        # Configure moderate load
        load_config = LoadTestConfig(
            concurrent_users=8, requests_per_user=5, ramp_up_time=1.0
        )
        load_tester = LoadTester(load_config)

        async def chaotic_operation():
            async with chaos_engineer.inject_chaos():
                state = StateFactory.create_empty_state()

                # Inject potential chaos
                await chaos_engineer.maybe_inject_failure("chaotic_operation")

                await agent.execute(state)
                return {"status": "completed"}

        # Run load test with chaos
        results = await load_tester.run_load_test(chaotic_operation)

        # Validate chaotic load performance
        # Should handle some failures but maintain reasonable performance
        assert (
            results["success_rate"] >= 0.7
        ), f"Success rate too low under chaos: {results['success_rate']}"
        assert (
            results["throughput"] >= 5.0
        ), f"Throughput too low under chaos: {results['throughput']}"
        assert len(results["errors"]) > 0, "Chaos should have caused some errors"
