"""
Matter and Client CRUD Agent Implementation

This module implements the Matter and Client CRUD Agent, which is responsible for:
1. Creating new matters and clients
2. Reading matters and clients with filtering
3. Updating matter and client properties
4. Deleting matters and clients with proper validation

The Matter and Client CRUD Agent serves as an interactive agent for matter and
client management operations,
providing a natural language interface for matter and client-related operations.
"""

import logging
from typing import Any, Dict, Optional

from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph

from backend.agents.matter_client.nodes import (
    create_matter,
    create_client,
    delete_matter,
    delete_client,
    read_matter,
    read_client,
    update_matter,
    update_client,
)
from backend.agents.matter_client.router import matter_client_router
from backend.agents.shared.core.base_agent import BaseAgent

# Set up logging
logger = logging.getLogger(__name__)


class MatterClientAgent(BaseAgent):
    """
    Matter and Client CRUD Agent for managing matters and clients.

    This agent provides a natural language interface for matter and client
    management operations,
    including creating, reading, updating, and deleting matters and clients.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Matter and Client CRUD Agent.

        Args:
            config: Configuration dictionary for the agent
        """
        # Default configuration
        default_config = {
            "name": "matter_client_agent",
            "description": "Matter and Client CRUD Agent for managing matters and clients",
            "version": "0.1.0",
            "tools": [
                "create_matter",
                "read_matter",
                "update_matter",
                "delete_matter",
                "create_client",
                "read_client",
                "update_client",
                "delete_client",
            ],
            "model": "gpt-4",
            "temperature": 0.1,
        }

        # Merge provided config with defaults
        final_config = {**default_config}
        if config:
            final_config.update(config)

        super().__init__(
            agent_type="matter_client",
            agent_name="MatterClientAgent",
            config=final_config,
        )

    def create_graph(self):
        """
        Create a LangGraph StateGraph for the MatterClientAgent.

        Returns:
            Compiled StateGraph
        """
        from typing import Dict, Any, TypedDict

        # Create a simple state schema for matter/client operations
        class MatterClientState(TypedDict):
            messages: list
            tenant_id: str
            user_id: str
            thread_id: str
            status: str
            operation_type: str
            operation_data: dict

        # Create the graph with the new API
        graph = StateGraph(MatterClientState)

        # Add nodes
        graph.add_node("router", matter_client_router)
        graph.add_node("create_matter", create_matter)
        graph.add_node("read_matter", read_matter)
        graph.add_node("update_matter", update_matter)
        graph.add_node("delete_matter", delete_matter)
        graph.add_node("create_client", create_client)
        graph.add_node("read_client", read_client)
        graph.add_node("update_client", update_client)
        graph.add_node("delete_client", delete_client)

        # Add edges
        graph.add_edge("router", "create_matter")
        graph.add_edge("router", "read_matter")
        graph.add_edge("router", "update_matter")
        graph.add_edge("router", "delete_matter")
        graph.add_edge("router", "create_client")
        graph.add_edge("router", "read_client")
        graph.add_edge("router", "update_client")
        graph.add_edge("router", "delete_client")

        # Add edges from operation nodes to END
        graph.add_edge("create_matter", END)
        graph.add_edge("read_matter", END)
        graph.add_edge("update_matter", END)
        graph.add_edge("delete_matter", END)
        graph.add_edge("create_client", END)
        graph.add_edge("read_client", END)
        graph.add_edge("update_client", END)
        graph.add_edge("delete_client", END)

        # Set the entry point
        graph.set_entry_point("router")

        # Compile the graph
        return graph.compile()

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Initialize the matter client agent.

        Args:
            state: Current state
            config: Runtime configuration

        Returns:
            Updated state
        """
        logger.info("Initializing MatterClientAgent")

        # Set agent status
        state["status"] = "initializing"
        state["agent_type"] = "matter_client"

        # Initialize operation-specific state
        state["operation_type"] = state.get("operation_type", "unknown")
        state["operation_data"] = state.get("operation_data", {})

        state["status"] = "initialized"
        logger.info("MatterClientAgent initialized successfully")

        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Execute the matter client agent.

        Args:
            state: Current state
            config: Runtime configuration

        Returns:
            Updated state
        """
        logger.info("Executing MatterClientAgent")

        state["status"] = "executing"

        # Get operation type and data
        operation_type = state.get("operation_type", "unknown")
        operation_data = state.get("operation_data", {})

        try:
            # For now, provide a placeholder response
            # TODO: Implement actual matter/client operations
            result = f"Executed {operation_type} operation with data: {operation_data}"

            # Add the result to messages
            state["messages"] = state.get("messages", []) + [
                {
                    "role": "assistant",
                    "content": result,
                    "metadata": {
                        "agent": "matter_client",
                        "operation": operation_type,
                        "data": operation_data,
                    },
                }
            ]

            state["status"] = "completed"

        except Exception as e:
            logger.error(f"Error executing matter client operation: {str(e)}")
            state["messages"] = state.get("messages", []) + [
                {
                    "role": "assistant",
                    "content": "I encountered an error while processing your request. Please try again later.",
                    "metadata": {"error": str(e)},
                }
            ]
            state["status"] = "error"

        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Clean up after matter client agent execution.

        Args:
            state: Current state
            config: Runtime configuration

        Returns:
            Updated state
        """
        logger.info("Cleaning up MatterClientAgent")

        # Ensure final status is set
        if state.get("status") not in ["completed", "error"]:
            state["status"] = "completed"

        # Add completion timestamp
        import time

        state["completed_at"] = time.time()

        logger.info("MatterClientAgent cleanup completed")

        return state

    async def invoke(
        self, state: Dict[str, Any], config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """
        Invoke the agent.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Invoking Matter and Client CRUD Agent")

        # Set default config if not provided
        if config is None:
            config = {}

        # Extract configurable parameters
        configurable = config.get("configurable", {})
        tenant_id = configurable.get("tenant_id")
        user_id = configurable.get("user_id")
        thread_id = configurable.get("thread_id")

        # Log the invocation
        logger.info(
            f"Invoking Matter and Client CRUD Agent for tenant {tenant_id}, "
            f"user {user_id}, thread {thread_id}"
        )

        # Add tenant_id and user_id to state if not present
        if tenant_id and "tenant_id" not in state:
            state["tenant_id"] = tenant_id

        if user_id and "user_id" not in state:
            state["user_id"] = user_id

        # Execute the graph
        result = await self.graph.ainvoke(state, config)

        return result
