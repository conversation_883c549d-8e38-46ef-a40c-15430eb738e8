"""
Matter and Client CRUD Agent Package

This package provides the Matter and Client CRUD Agent implementation, which is
responsible for:
1. Creating new matters and clients
2. Reading matters and clients with filtering
3. Updating matter and client properties
4. Deleting matters and clients with proper validation

The Matter and Client CRUD Agent serves as an interactive agent for matter and
client management operations,
providing a natural language interface for matter and client-related operations.

Usage:
    from backend.agents.matter_client import MatterClientAgent, matter_client_router

    # Create a matter client agent
    agent = MatterClientAgent()

    # Execute the agent
    result = await agent.invoke(
        {"messages": [HumanMessage(content="Create a new matter for <PERSON>")]},
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

from backend.agents.matter_client.agent import MatterClientAgent
from backend.agents.matter_client.nodes import (
    create_matter,
    create_client,
    delete_matter,
    delete_client,
    read_matter,
    read_client,
    update_matter,
    update_client,
)
from backend.agents.matter_client.router import matter_client_router

__all__ = [
    "MatterClientAgent",
    "matter_client_router",
    "create_matter",
    "read_matter",
    "update_matter",
    "delete_matter",
    "create_client",
    "read_client",
    "update_client",
    "delete_client",
]
