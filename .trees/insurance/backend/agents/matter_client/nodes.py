"""
Matter and Client CRUD Agent Nodes

This module provides the operation nodes for the Matter and Client CRUD Agent,
which implement the CRUD operations for matters and clients.
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict
from uuid import UUID

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig

from backend.agents.matter_client.tools.db import (
    create_matter_db,
    create_client_db,
    delete_matter_db,
    delete_client_db,
    get_matter_by_id,
    get_client_by_id,
    list_matters_db,
    list_clients_db,
    update_matter_db,
    update_client_db,
)
from shared.core.llm.selector import resolve_llm

# Set up logging
logger = logging.getLogger(__name__)


# Matter CRUD nodes
async def create_matter(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Create a new matter.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Creating matter")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id and user_id
    tenant_id = state.get("tenant_id")
    user_id = state.get("user_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content="Error: Tenant ID is required to create a matter.")
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "matterCrudAgent", "create_matter")

    # Extract matter information from user input
    prompt_template = """Extract matter information from the user's request.

User request: {input}

Extract the following information:
- title: The title of the matter
- description: A description of the matter
- status: The status of the matter (active, closed, pending)
- practice_area: The practice area (litigation, corporate, family, etc.)
- sensitive: Whether the matter contains sensitive information (true/false)
- client_id: The ID of the client associated with the matter (if provided)

Return the information as a JSON object with these fields. If a field is not
provided, set it to null.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        matter_info = json.loads(content.strip())

        # Create the matter
        matter = await create_matter_db(
            tenant_id=UUID(tenant_id),
            title=matter_info.get("title"),
            description=matter_info.get("description"),
            status=matter_info.get("status", "active"),
            practice_area=matter_info.get("practice_area", "litigation"),
            sensitive=matter_info.get("sensitive", False),
            client_id=(
                UUID(matter_info.get("client_id"))
                if matter_info.get("client_id")
                else None
            ),
            created_by=UUID(user_id) if user_id else None,
            matter_metadata=matter_info.get("metadata", {}),
        )

        # Add response message
        state["messages"] = state.get("messages", []) + [
            AIMessage(
                content=(
                    f"Matter created successfully!\n\nTitle: {matter['title']}\n"
                    f"Status: {matter['status']}\nID: {matter['id']}"
                )
            )
        ]
    except Exception as e:
        logger.error(f"Error creating matter: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error creating matter: {str(e)}")
        ]

    return state


async def read_matter(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Read matter information.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Reading matter")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id
    tenant_id = state.get("tenant_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(
                content="Error: Tenant ID is required to read matter information."
            )
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "matterCrudAgent", "read_matter")

    # Extract matter ID or filter criteria from user input
    prompt_template = """Extract matter ID or filter criteria from the user's request.

User request: {input}

Extract one of the following:
1. matter_id: The ID of a specific matter to retrieve
2. filter criteria:
   - status: Filter by matter status (active, closed, pending)
   - client_id: Filter by client ID
   - search_term: Search term for matter title or description

Return the information as a JSON object with these fields. If a field is not
provided, set it to null.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        query_info = json.loads(content.strip())

        # Check if a specific matter ID was provided
        if query_info.get("matter_id"):
            # Get the case by ID
            matter = await get_matter_by_id(
                matter_id=UUID(query_info["matter_id"]),
                tenant_id=UUID(tenant_id),
            )

            if matter:
                # Add response message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(
                        content=(
                            f"Matter found:\n\nTitle: {matter['title']}\n"
                            f"Description: {matter['description']}\n"
                            f"Status: {matter['status']}\nID: {matter['id']}"
                        )
                    )
                ]
            else:
                # Add error message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(
                        content=f"Matter not found with ID: {query_info['matter_id']}"
                    )
                ]
        else:
            # List matters with optional filtering
            matters = await list_matters_db(
                tenant_id=UUID(tenant_id),
                status=query_info.get("status"),
                client_id=(
                    UUID(query_info["client_id"])
                    if query_info.get("client_id")
                    else None
                ),
                limit=10,
                offset=0,
            )

            if matters:
                # Format the matter list
                matter_list = "\n\n".join(
                    [
                        (
                            f"Title: {matter['title']}\nStatus: {matter['status']}\n"
                            f"ID: {matter['id']}"
                        )
                        for matter in matters
                    ]
                )

                # Add response message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(content=f"Found {len(matters)} matters:\n\n{matter_list}")
                ]
            else:
                # Add response message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(content="No matters found matching your criteria.")
                ]
    except Exception as e:
        logger.error(f"Error reading matter: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error reading matter: {str(e)}")
        ]

    return state


async def update_matter(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Update an existing matter.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Updating matter")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id and user_id
    tenant_id = state.get("tenant_id")
    user_id = state.get("user_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content="Error: Tenant ID is required to update a matter.")
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "matterCrudAgent", "update_matter")

    # Extract matter information from user input
    prompt_template = """Extract matter update information from the user's request.

User request: {input}

Extract the following information:
- matter_id: The ID of the matter to update (required)
- title: The updated title of the matter
- description: The updated description of the matter
- status: The updated status of the matter (active, closed, pending)
- practice_area: The updated practice area (litigation, corporate, family, etc.)
- sensitive: Whether the matter contains sensitive information (true/false)
- client_id: The ID of the client associated with the matter

Return the information as a JSON object with these fields. If a field is not
provided or should not be updated, set it to null.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        matter_info = json.loads(content.strip())

        # Check if matter_id is provided
        if not matter_info.get("matter_id"):
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(content="Error: Matter ID is required to update a matter.")
            ]
            return state

        # Update the matter
        matter = await update_matter_db(
            matter_id=UUID(matter_info["matter_id"]),
            tenant_id=UUID(tenant_id),
            title=matter_info.get("title"),
            description=matter_info.get("description"),
            status=matter_info.get("status"),
            practice_area=matter_info.get("practice_area"),
            sensitive=matter_info.get("sensitive"),
            client_id=(
                UUID(matter_info["client_id"]) if matter_info.get("client_id") else None
            ),
            updated_by=UUID(user_id) if user_id else None,
            matter_metadata=matter_info.get("metadata"),
        )

        if matter:
            # Add response message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=(
                        f"Matter updated successfully!\n\nTitle: {matter['title']}\n"
                        f"Status: {matter['status']}\nID: {matter['id']}"
                    )
                )
            ]
        else:
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=f"Matter not found with ID: {matter_info['matter_id']}"
                )
            ]
    except Exception as e:
        logger.error(f"Error updating matter: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error updating matter: {str(e)}")
        ]

    return state


async def delete_matter(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Delete a matter.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Deleting matter")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id
    tenant_id = state.get("tenant_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content="Error: Tenant ID is required to delete a matter.")
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "matterCrudAgent", "delete_matter")

    # Extract matter ID from user input
    prompt_template = """Extract matter ID from the user's request.

User request: {input}

Extract the following information:
- matter_id: The ID of the matter to delete (required)

Return the information as a JSON object with this field.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        matter_info = json.loads(content.strip())

        # Check if matter_id is provided
        if not matter_info.get("matter_id"):
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(content="Error: Matter ID is required to delete a matter.")
            ]
            return state

        # Delete the matter
        success = await delete_matter_db(
            matter_id=UUID(matter_info["matter_id"]),
            tenant_id=UUID(tenant_id),
        )

        if success:
            # Add response message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=f"Matter deleted successfully with ID: {matter_info['matter_id']}"
                )
            ]
        else:
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=f"Matter not found with ID: {matter_info['matter_id']}"
                )
            ]
    except Exception as e:
        logger.error(f"Error deleting matter: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error deleting matter: {str(e)}")
        ]

    return state


# Client CRUD nodes
async def create_client(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Create a new client.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Creating client")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id and user_id
    tenant_id = state.get("tenant_id")
    user_id = state.get("user_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content="Error: Tenant ID is required to create a client.")
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "caseCrudAgent", "create_client")

    # Extract client information from user input
    prompt_template = """Extract client information from the user's request.

User request: {input}

Extract the following information:
- first_name: The client's first name
- last_name: The client's last name
- middle_name: The client's middle name
- date_of_birth: The client's date of birth (YYYY-MM-DD)
- ssn_last_four: Last four digits of SSN
- email: The client's email address
- phone_primary: The client's primary phone number
- phone_secondary: The client's secondary phone number
- preferred_contact_method: The client's preferred contact method (email,
  phone, mail)
- address: The client's address (as a JSON object with street, city, state,
  zip)
- occupation: The client's occupation
- employer: The client's employer
- notes: Notes about the client

Return the information as a JSON object with these fields. If a field is not
provided, set it to null.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        client_info = json.loads(content.strip())

        # Parse date of birth if provided
        dob = None
        if client_info.get("date_of_birth"):
            try:
                dob = datetime.strptime(client_info["date_of_birth"], "%Y-%m-%d").date()
            except ValueError:
                logger.warning(f"Invalid date format: {client_info['date_of_birth']}")

        # Create the client
        client = await create_client_db(
            tenant_id=UUID(tenant_id),
            first_name=client_info.get("first_name"),
            last_name=client_info.get("last_name"),
            middle_name=client_info.get("middle_name"),
            date_of_birth=dob,
            ssn_last_four=client_info.get("ssn_last_four"),
            email=client_info.get("email"),
            phone_primary=client_info.get("phone_primary"),
            phone_secondary=client_info.get("phone_secondary"),
            preferred_contact_method=client_info.get("preferred_contact_method"),
            address=client_info.get("address"),
            occupation=client_info.get("occupation"),
            employer=client_info.get("employer"),
            notes=client_info.get("notes"),
            created_by=UUID(user_id) if user_id else None,
            metadata=client_info.get("metadata", {}),
        )

        # Add response message
        state["messages"] = state.get("messages", []) + [
            AIMessage(
                content=(
                    f"Client created successfully!\n\nName: {client['first_name']} "
                    f"{client['last_name']}\nEmail: {client['email']}\n"
                    f"ID: {client['id']}"
                )
            )
        ]
    except Exception as e:
        logger.error(f"Error creating client: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error creating client: {str(e)}")
        ]

    return state


async def read_client(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Read client information.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Reading client")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id
    tenant_id = state.get("tenant_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(
                content="Error: Tenant ID is required to read client information."
            )
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "caseCrudAgent", "read_client")

    # Extract client ID or filter criteria from user input
    prompt_template = """Extract client ID or filter criteria from the user's request.

User request: {input}

Extract one of the following:
1. client_id: The ID of a specific client to retrieve
2. filter criteria:
   - is_active: Filter by active status (true/false)
   - search_term: Search term for client name, email, or phone

Return the information as a JSON object with these fields. If a field is not
provided, set it to null.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        query_info = json.loads(content.strip())

        # Check if a specific client ID was provided
        if query_info.get("client_id"):
            # Get the client by ID
            client = await get_client_by_id(
                client_id=UUID(query_info["client_id"]),
                tenant_id=UUID(tenant_id),
            )

            if client:
                # Add response message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(
                        content=(
                            f"Client found:\n\nName: {client['first_name']} "
                            f"{client['last_name']}\nEmail: {client['email']}\n"
                            f"Phone: {client['phone_primary']}\nID: {client['id']}"
                        )
                    )
                ]
            else:
                # Add error message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(
                        content=f"Client not found with ID: {query_info['client_id']}"
                    )
                ]
        else:
            # List clients with optional filtering
            clients = await list_clients_db(
                tenant_id=UUID(tenant_id),
                is_active=query_info.get("is_active"),
                search_term=query_info.get("search_term"),
                limit=10,
                offset=0,
            )

            if clients:
                # Format the client list
                client_list = "\n\n".join(
                    [
                        (
                            f"Name: {client['first_name']} {client['last_name']}\n"
                            f"Email: {client['email']}\nPhone: {client['phone_primary']}\n"
                            f"ID: {client['id']}"
                        )
                        for client in clients
                    ]
                )

                # Add response message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(content=f"Found {len(clients)} clients:\n\n{client_list}")
                ]
            else:
                # Add response message
                state["messages"] = state.get("messages", []) + [
                    AIMessage(content="No clients found matching your criteria.")
                ]
    except Exception as e:
        logger.error(f"Error reading client: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error reading client: {str(e)}")
        ]

    return state


async def update_client(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Update an existing client.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Updating client")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id and user_id
    tenant_id = state.get("tenant_id")
    user_id = state.get("user_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content="Error: Tenant ID is required to update a client.")
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "caseCrudAgent", "update_client")

    # Extract client information from user input
    prompt_template = """Extract client update information from the user's request.

User request: {input}

Extract the following information:
- client_id: The ID of the client to update (required)
- first_name: The updated first name of the client
- last_name: The updated last name of the client
- middle_name: The updated middle name of the client
- date_of_birth: The updated date of birth of the client (YYYY-MM-DD)
- ssn_last_four: Updated last four digits of SSN
- email: The updated email address of the client
- phone_primary: The updated primary phone number of the client
- phone_secondary: The updated secondary phone number of the client
- preferred_contact_method: The updated preferred contact method of the client
  (email, phone, mail)
- address: The updated address of the client (as a JSON object with street,
  city, state, zip)
- occupation: The updated occupation of the client
- employer: The updated employer of the client
- notes: Updated notes about the client
- is_active: Whether the client is active (true/false)

Return the information as a JSON object with these fields. If a field is not
provided or should not be updated, set it to null.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        client_info = json.loads(content.strip())

        # Check if client_id is provided
        if not client_info.get("client_id"):
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(content="Error: Client ID is required to update a client.")
            ]
            return state

        # Parse date of birth if provided
        dob = None
        if client_info.get("date_of_birth"):
            try:
                dob = datetime.strptime(client_info["date_of_birth"], "%Y-%m-%d").date()
            except ValueError:
                logger.warning(f"Invalid date format: {client_info['date_of_birth']}")

        # Update the client
        client = await update_client_db(
            client_id=UUID(client_info["client_id"]),
            tenant_id=UUID(tenant_id),
            first_name=client_info.get("first_name"),
            last_name=client_info.get("last_name"),
            middle_name=client_info.get("middle_name"),
            date_of_birth=dob,
            ssn_last_four=client_info.get("ssn_last_four"),
            email=client_info.get("email"),
            phone_primary=client_info.get("phone_primary"),
            phone_secondary=client_info.get("phone_secondary"),
            preferred_contact_method=client_info.get("preferred_contact_method"),
            address=client_info.get("address"),
            occupation=client_info.get("occupation"),
            employer=client_info.get("employer"),
            notes=client_info.get("notes"),
            is_active=client_info.get("is_active"),
            updated_by=UUID(user_id) if user_id else None,
            metadata=client_info.get("metadata"),
        )

        if client:
            # Add response message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=(
                        f"Client updated successfully!\n\nName: {client['first_name']} "
                        f"{client['last_name']}\nEmail: {client['email']}\n"
                        f"ID: {client['id']}"
                    )
                )
            ]
        else:
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=f"Client not found with ID: {client_info['client_id']}"
                )
            ]
    except Exception as e:
        logger.error(f"Error updating client: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error updating client: {str(e)}")
        ]

    return state


async def delete_client(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Delete a client.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Deleting client")

    # Get the user input
    messages = state.get("messages", [])
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    # Get tenant_id
    tenant_id = state.get("tenant_id")

    if not tenant_id:
        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content="Error: Tenant ID is required to delete a client.")
        ]
        return state

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "caseCrudAgent", "delete_client")

    # Extract client ID from user input
    prompt_template = """Extract client ID from the user's request.

User request: {input}

Extract the following information:
- client_id: The ID of the client to delete (required)

Return the information as a JSON object with this field.

JSON:"""

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Call the LLM
        response = await llm.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500,
            response_format={"type": "json_object"},
        )

        # Extract the content from the response
        content = (
            response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
        )

        # Parse the response
        client_info = json.loads(content.strip())

        # Check if client_id is provided
        if not client_info.get("client_id"):
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(content="Error: Client ID is required to delete a client.")
            ]
            return state

        # Delete the client
        success = await delete_client_db(
            client_id=UUID(client_info["client_id"]),
            tenant_id=UUID(tenant_id),
        )

        if success:
            # Add response message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=(
                        f"Client deleted successfully with ID: "
                        f"{client_info['client_id']}"
                    )
                )
            ]
        else:
            # Add error message
            state["messages"] = state.get("messages", []) + [
                AIMessage(
                    content=f"Client not found with ID: {client_info['client_id']}"
                )
            ]
    except Exception as e:
        logger.error(f"Error deleting client: {str(e)}")

        # Add error message
        state["messages"] = state.get("messages", []) + [
            AIMessage(content=f"Error deleting client: {str(e)}")
        ]

    return state
