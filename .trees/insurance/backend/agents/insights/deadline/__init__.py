"""
Deadline Insights Agent Module

This module provides the Deadline Insights Agent for the AiLex system, which performs
long-running deadline analysis and insights generation. It complements the existing
interactive deadline agent by providing batch processing, conflict detection, and
comprehensive deadline analytics.

The agent follows the established LangGraph patterns and integrates with:
- MCP Rules Engine for deadline calculations
- Redis for caching and job queuing
- Supabase for data persistence
- Existing deadline schemas and data models

Usage:
    from backend.agents.insights.deadline import DeadlineInsightsAgent

    # Create agent
    agent = DeadlineInsightsAgent()

    # Create and execute workflow
    graph = agent.create_graph()
    result = await graph.ainvoke(initial_state, config)

Architecture:
    - DeadlineInsightsAgent: Main agent class with lifecycle management
    - DeadlineInsightsState: State model for workflow tracking
    - DeadlineInsightsNodes: LangGraph nodes for processing steps
    - Comprehensive error handling and logging
    - Integration with existing MCP Rules Engine patterns
"""

from .agent import DeadlineInsightsAgent
from .state import (
    DeadlineInsightsState,
    AnalysisType,
    ConflictSeverity,
    DeadlineConflict,
    DeadlineAnalysis,
    BatchProcessingConfig,
)
from .nodes import DeadlineInsightsNodes
from .database import DeadlineRepository

__all__ = [
    "DeadlineInsightsAgent",
    "DeadlineInsightsState",
    "AnalysisType",
    "ConflictSeverity",
    "DeadlineConflict",
    "DeadlineAnalysis",
    "BatchProcessingConfig",
    "DeadlineInsightsNodes",
    "DeadlineRepository",
]

# Version information
__version__ = "1.0.0"
__author__ = "AiLex Development Team"
__description__ = (
    "Deadline Insights Agent for long-running deadline analysis and insights generation"
)
