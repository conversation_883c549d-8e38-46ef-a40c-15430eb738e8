"""
MCP Rules Engine Client for Python

This module provides a Python client for the MCP Rules Engine API, mirroring the
functionality of the TypeScript client with proper Secret Manager integration,
caching, and error handling.

Usage:
    from backend.agents.insights.deadline.mcp_client import McpClient, init_mcp_client

    # Initialize client with Secret Manager integration
    client = await init_mcp_client(tenant_id="tenant-123")

    # Calculate deadlines
    deadlines = await client.calculate_deadlines(
        jurisdiction="TX",
        trigger_code="ACCIDENT",
        start_date="2023-01-15",
        practice_area="personal_injury"
    )
"""

import os
import json
import time
import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum

import aiohttp

try:
    from google.cloud import secretmanager
except ImportError:
    # Mock for testing environments without Google Cloud SDK
    secretmanager = None

# Import monitoring
try:
    from .monitoring import (
        track_performance,
        record_metric,
        record_alert,
        MetricType,
        AlertSeverity,
    )
except ImportError:
    # Fallback for environments without monitoring
    def track_performance(*args, **kwargs):
        class NoOpTracker:
            def __enter__(self):
                return self

            def __exit__(self, *args):
                pass

        return NoOpTracker()

    def record_metric(*args, **kwargs):
        pass

    def record_alert(*args, **kwargs):
        pass

    class MetricType:
        COUNTER = "counter"
        HISTOGRAM = "histogram"

    class AlertSeverity:
        ERROR = "error"
        WARNING = "warning"


# Set up logging
logger = logging.getLogger(__name__)

# Constants
MCP_RULES_BASE = os.getenv("MCP_RULES_BASE", "https://rules.ailexlaw.com")
MCP_API_KEY_SECRET = os.getenv(
    "MCP_API_KEY", "projects/PROJECT_ID/secrets/mcp-key-TENANT/versions/latest"
)
GOOGLE_CLOUD_PROJECT = os.getenv("GOOGLE_CLOUD_PROJECT", "texas-laws-personalinjury")
DEFAULT_TIMEOUT = 30  # seconds
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 1  # seconds
CACHE_TTL = 600  # 10 minutes

# Cache for API keys
api_key_cache: Dict[str, Dict[str, Union[str, int]]] = {}


class CircuitBreakerState(Enum):
    """Circuit breaker states."""

    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half-open"


class McpApiError(Exception):
    """MCP API Error with status code and error code."""

    def __init__(
        self, message: str, status_code: int = 500, error_code: str = "UNKNOWN_ERROR"
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        super().__init__(f"{message} (Status: {status_code}, Code: {error_code})")


class McpClient:
    """Client for the MCP Rules Engine API."""

    def __init__(
        self,
        base_url: str,
        api_key: str,
        timeout: int = DEFAULT_TIMEOUT,
        max_retries: int = DEFAULT_MAX_RETRIES,
        retry_delay: int = DEFAULT_RETRY_DELAY,
    ):
        """Initialize the MCP client.

        Args:
            base_url: Base URL for the MCP Rules Engine API
            api_key: API key for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            retry_delay: Base delay between retries in seconds
        """
        self.base_url = base_url.rstrip("/")  # Remove trailing slash
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Circuit breaker state
        self.circuit_breaker_state = CircuitBreakerState.CLOSED
        self.consecutive_failures = 0
        self.last_failure_time = 0
        self.failure_threshold = 5
        self.recovery_timeout = 60 * 1000  # 1 minute in milliseconds

        # Session
        self._session = None

    async def __aenter__(self):
        """Async context manager entry."""
        if self._session is None:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session is not None:
            await self._session.close()
            self._session = None

    def get_circuit_breaker_state(self) -> Dict[str, Any]:
        """Get the current circuit breaker state."""
        return {
            "state": self.circuit_breaker_state.value,
            "consecutiveFailures": self.consecutive_failures,
            "lastFailureTime": self.last_failure_time,
        }

    async def calculate_deadlines(
        self,
        jurisdiction: str,
        trigger_code: str,
        start_date: str,
        practice_area: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Calculate deadlines for a given jurisdiction and trigger code.

        Args:
            jurisdiction: Jurisdiction code (e.g., "TX")
            trigger_code: Trigger code (e.g., "ACCIDENT")
            start_date: Start date in YYYY-MM-DD format
            practice_area: Optional practice area (e.g., "personal_injury")

        Returns:
            Dictionary containing deadline information
        """
        request = {
            "jurisdiction": jurisdiction,
            "triggerCode": trigger_code,
            "startDate": start_date,
        }

        if practice_area:
            request["practiceArea"] = practice_area

        # Track performance and record metrics
        with track_performance(
            operation="mcp_calculate_deadlines",
            labels={
                "jurisdiction": jurisdiction,
                "trigger_code": trigger_code,
                "practice_area": practice_area or "unknown",
            },
        ):
            result = await self._make_request(
                "/api/v1/deadlines/calculate", method="POST", data=request
            )

            # Record success metrics
            record_metric(
                name="mcp_deadlines_calculated",
                value=len(result.get("deadlines", [])),
                metric_type=MetricType.COUNTER,
                labels={
                    "jurisdiction": jurisdiction,
                    "practice_area": practice_area or "unknown",
                },
            )

            return result

    async def health_check(self) -> Dict[str, Any]:
        """Check the health of the MCP Rules Engine API.

        Returns:
            Dictionary containing health status information
        """
        return await self._make_request("/api/v1/health", method="GET")

    async def _make_request(
        self, endpoint: str, method: str = "GET", data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make an HTTP request with retry logic, error handling, and circuit breaker.

        Args:
            endpoint: API endpoint path
            method: HTTP method (GET, POST, etc.)
            data: Request data for POST/PUT requests

        Returns:
            Response data as dictionary

        Raises:
            McpApiError: If the request fails after retries
        """
        # Check circuit breaker state
        if self.circuit_breaker_state == CircuitBreakerState.OPEN:
            time_since_last_failure = int(time.time() * 1000) - self.last_failure_time
            if time_since_last_failure < self.recovery_timeout:
                raise McpApiError(
                    "Circuit breaker is OPEN - service temporarily unavailable",
                    503,
                    "CIRCUIT_BREAKER_OPEN",
                )
            else:
                # Transition to half-open for testing
                self.circuit_breaker_state = CircuitBreakerState.HALF_OPEN

        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json", "x-api-key": self.api_key}

        # Create session if needed
        if self._session is None:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )

        # Implement retry logic
        for attempt in range(self.max_retries + 1):
            try:
                if method.upper() == "GET":
                    async with self._session.get(url, headers=headers) as response:
                        return await self._handle_response(response)
                elif method.upper() == "POST":
                    async with self._session.post(
                        url, headers=headers, json=data
                    ) as response:
                        return await self._handle_response(response)
                else:
                    raise McpApiError(
                        f"Unsupported HTTP method: {method}", 400, "INVALID_METHOD"
                    )

            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                # Handle network errors with retry logic
                if attempt < self.max_retries:
                    # Exponential backoff
                    delay = self.retry_delay * (2**attempt)
                    logger.warning(
                        f"Request failed, retrying in {delay}s (attempt {attempt+1}/{self.max_retries}): {str(e)}"
                    )
                    await asyncio.sleep(delay)
                else:
                    # Update circuit breaker state
                    self._record_failure()
                    raise McpApiError(
                        f"Request failed after {self.max_retries} retries: {str(e)}",
                        500,
                        "REQUEST_FAILED",
                    )

    async def _handle_response(
        self, response: aiohttp.ClientResponse
    ) -> Dict[str, Any]:
        """Handle API response with proper error handling.

        Args:
            response: aiohttp response object

        Returns:
            Response data as dictionary

        Raises:
            McpApiError: If the response indicates an error
        """
        if response.status >= 200 and response.status < 300:
            # Success - reset circuit breaker if in half-open state
            if self.circuit_breaker_state == CircuitBreakerState.HALF_OPEN:
                self.circuit_breaker_state = CircuitBreakerState.CLOSED
                self.consecutive_failures = 0

            try:
                return await response.json()
            except (json.JSONDecodeError, aiohttp.ContentTypeError):
                # Non-JSON response
                text = await response.text()
                return {"message": text}
        else:
            # Error response
            self._record_failure()

            try:
                error_data = await response.json()
                error_message = error_data.get("message", "Unknown error")
                error_code = error_data.get("code", "API_ERROR")
            except (json.JSONDecodeError, aiohttp.ContentTypeError):
                error_message = (
                    await response.text() or f"HTTP Error: {response.status}"
                )
                error_code = "API_ERROR"

            raise McpApiError(error_message, response.status, error_code)

    def _record_failure(self):
        """Record a failure and update circuit breaker state."""
        self.consecutive_failures += 1
        self.last_failure_time = int(time.time() * 1000)

        # Record failure metric
        record_metric(
            name="mcp_circuit_breaker_failures",
            value=1,
            metric_type=MetricType.COUNTER,
            labels={"consecutive_failures": str(self.consecutive_failures)},
        )

        if self.consecutive_failures >= self.failure_threshold:
            self.circuit_breaker_state = CircuitBreakerState.OPEN
            logger.warning(
                f"Circuit breaker OPEN after {self.consecutive_failures} consecutive failures"
            )

            # Record circuit breaker open alert
            record_alert(
                alert_id=f"mcp_circuit_breaker_open_{int(time.time())}",
                title="MCP Circuit Breaker Opened",
                description=f"Circuit breaker opened after {self.consecutive_failures} consecutive failures",
                severity=AlertSeverity.ERROR,
                metadata={
                    "consecutive_failures": self.consecutive_failures,
                    "failure_threshold": self.failure_threshold,
                    "base_url": self.base_url,
                },
            )


async def get_mcp_api_key(tenant_id: str) -> str:
    """Retrieve MCP API key from Secret Manager with caching.

    Args:
        tenant_id: Tenant ID for which to retrieve the API key

    Returns:
        API key string

    Raises:
        Exception: If API key retrieval fails
    """
    if secretmanager is None:
        raise ImportError(
            "Google Cloud Secret Manager is not available. Install google-cloud-secret-manager."
        )

    cache_key = f"mcp_api_key_{tenant_id}"
    now = int(time.time())

    # Return cached key if still valid
    if cache_key in api_key_cache and now < api_key_cache[cache_key].get("expiry", 0):
        return api_key_cache[cache_key]["key"]

    try:
        # Format secret name with tenant ID
        secret_name = MCP_API_KEY_SECRET.replace("TENANT", tenant_id)

        # Initialize Secret Manager client
        client = secretmanager.SecretManagerServiceClient()

        # Access secret version
        response = client.access_secret_version(name=secret_name)
        api_key = response.payload.data.decode("UTF-8")

        if not api_key:
            raise ValueError("Secret Manager returned empty API key")

        # Cache the key
        api_key_cache[cache_key] = {"key": api_key, "expiry": now + CACHE_TTL}

        logger.info(f"Retrieved MCP API key for tenant {tenant_id} from Secret Manager")
        return api_key

    except Exception as e:
        logger.error(f"Failed to retrieve MCP API key for tenant {tenant_id}: {str(e)}")
        raise


async def init_mcp_client(tenant_id: str) -> McpClient:
    """Initialize MCP client with API key from Secret Manager.

    Args:
        tenant_id: Tenant ID for which to initialize the client

    Returns:
        Initialized McpClient instance

    Raises:
        Exception: If client initialization fails
    """
    api_key = await get_mcp_api_key(tenant_id)

    return McpClient(
        base_url=MCP_RULES_BASE,
        api_key=api_key,
        timeout=DEFAULT_TIMEOUT,
        max_retries=DEFAULT_MAX_RETRIES,
        retry_delay=DEFAULT_RETRY_DELAY,
    )
