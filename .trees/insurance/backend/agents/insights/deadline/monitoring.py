"""
Monitoring and Observability for MCP Rules Engine Integration

This module provides comprehensive monitoring, logging, metrics collection,
and alerting capabilities for the MCP integration in production environments.
"""

import os
import time
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum

# Set up structured logging
logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics we collect."""

    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class AlertSeverity(Enum):
    """Alert severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class McpMetric:
    """Represents a metric for MCP operations."""

    name: str
    value: float
    metric_type: MetricType
    labels: Dict[str, str]
    timestamp: datetime
    tenant_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert metric to dictionary for logging/export."""
        return {
            **asdict(self),
            "timestamp": self.timestamp.isoformat(),
            "metric_type": self.metric_type.value,
        }


@dataclass
class McpAlert:
    """Represents an alert for MCP operations."""

    alert_id: str
    title: str
    description: str
    severity: AlertSeverity
    tenant_id: Optional[str]
    metadata: Dict[str, Any]
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary for logging/export."""
        return {
            **asdict(self),
            "timestamp": self.timestamp.isoformat(),
            "severity": self.severity.value,
        }


class McpMonitor:
    """Main monitoring class for MCP operations."""

    def __init__(self, enable_cloud_logging: bool = True, enable_metrics: bool = True):
        """
        Initialize MCP monitor.

        Args:
            enable_cloud_logging: Whether to enable Google Cloud Logging
            enable_metrics: Whether to enable metrics collection
        """
        self.enable_cloud_logging = enable_cloud_logging
        self.enable_metrics = enable_metrics
        self.metrics_buffer: List[McpMetric] = []
        self.alerts_buffer: List[McpAlert] = []

        # Initialize cloud logging if available
        if self.enable_cloud_logging:
            self._init_cloud_logging()

    def _init_cloud_logging(self):
        """Initialize Google Cloud Logging."""
        try:
            from google.cloud import logging as cloud_logging

            # Initialize the client
            self.cloud_logging_client = cloud_logging.Client()
            self.cloud_logging_client.setup_logging()

            logger.info("Google Cloud Logging initialized successfully")
        except ImportError:
            logger.warning(
                "Google Cloud Logging not available - using local logging only"
            )
            self.cloud_logging_client = None
        except Exception as e:
            logger.error(f"Failed to initialize Google Cloud Logging: {str(e)}")
            self.cloud_logging_client = None

    def record_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType,
        labels: Optional[Dict[str, str]] = None,
        tenant_id: Optional[str] = None,
    ):
        """
        Record a metric for MCP operations.

        Args:
            name: Metric name
            value: Metric value
            metric_type: Type of metric
            labels: Additional labels for the metric
            tenant_id: Tenant ID for tenant-specific metrics
        """
        if not self.enable_metrics:
            return

        metric = McpMetric(
            name=name,
            value=value,
            metric_type=metric_type,
            labels=labels or {},
            timestamp=datetime.now(timezone.utc),
            tenant_id=tenant_id,
        )

        self.metrics_buffer.append(metric)

        # Log metric for immediate visibility
        logger.info(
            "MCP Metric",
            extra={
                "metric": metric.to_dict(),
                "service": "mcp-rules-engine",
                "operation": name,
            },
        )

        # Flush buffer if it gets too large
        if len(self.metrics_buffer) > 100:
            self.flush_metrics()

    def record_alert(
        self,
        alert_id: str,
        title: str,
        description: str,
        severity: AlertSeverity,
        tenant_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        Record an alert for MCP operations.

        Args:
            alert_id: Unique alert identifier
            title: Alert title
            description: Alert description
            severity: Alert severity level
            tenant_id: Tenant ID for tenant-specific alerts
            metadata: Additional alert metadata
        """
        alert = McpAlert(
            alert_id=alert_id,
            title=title,
            description=description,
            severity=severity,
            tenant_id=tenant_id,
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc),
        )

        self.alerts_buffer.append(alert)

        # Log alert immediately
        log_level = {
            AlertSeverity.INFO: logging.INFO,
            AlertSeverity.WARNING: logging.WARNING,
            AlertSeverity.ERROR: logging.ERROR,
            AlertSeverity.CRITICAL: logging.CRITICAL,
        }.get(severity, logging.INFO)

        logger.log(
            log_level,
            f"MCP Alert: {title}",
            extra={
                "alert": alert.to_dict(),
                "service": "mcp-rules-engine",
                "alert_type": "mcp_integration",
            },
        )

        # Flush alerts buffer
        if len(self.alerts_buffer) > 50:
            self.flush_alerts()

    def flush_metrics(self):
        """Flush metrics buffer to external systems."""
        if not self.metrics_buffer:
            return

        try:
            # Export to Google Cloud Monitoring if available
            self._export_metrics_to_cloud()

            # Clear buffer after successful export
            self.metrics_buffer.clear()

        except Exception as e:
            logger.error(f"Failed to flush metrics: {str(e)}")

    def flush_alerts(self):
        """Flush alerts buffer to external systems."""
        if not self.alerts_buffer:
            return

        try:
            # Export alerts to monitoring systems
            self._export_alerts_to_cloud()

            # Clear buffer after successful export
            self.alerts_buffer.clear()

        except Exception as e:
            logger.error(f"Failed to flush alerts: {str(e)}")

    def _export_metrics_to_cloud(self):
        """Export metrics to Google Cloud Monitoring."""
        try:
            from google.cloud import monitoring_v3

            client = monitoring_v3.MetricServiceClient()
            project_name = f"projects/{os.getenv('GOOGLE_CLOUD_PROJECT', 'texas-laws-personalinjury')}"

            for metric in self.metrics_buffer:
                # Create time series data
                series = monitoring_v3.TimeSeries()
                series.metric.type = f"custom.googleapis.com/mcp/{metric.name}"

                # Add labels
                for key, value in metric.labels.items():
                    series.metric.labels[key] = value

                if metric.tenant_id:
                    series.metric.labels["tenant_id"] = metric.tenant_id

                # Add resource labels
                series.resource.type = "generic_node"
                series.resource.labels["location"] = "global"
                series.resource.labels["namespace"] = "mcp-rules-engine"
                series.resource.labels["node_id"] = "deadline-insights-agent"

                # Add data point
                point = monitoring_v3.Point()
                point.value.double_value = metric.value
                point.interval.end_time.seconds = int(metric.timestamp.timestamp())
                series.points = [point]

                # Create the time series
                client.create_time_series(name=project_name, time_series=[series])

        except ImportError:
            logger.debug("Google Cloud Monitoring not available")
        except Exception as e:
            logger.error(f"Failed to export metrics to Cloud Monitoring: {str(e)}")

    def _export_alerts_to_cloud(self):
        """Export alerts to external monitoring systems."""
        # For now, just log alerts - can be extended to integrate with
        # PagerDuty, Slack, email notifications, etc.
        for alert in self.alerts_buffer:
            logger.info(
                f"Alert exported: {alert.alert_id}", extra={"alert": alert.to_dict()}
            )


class McpPerformanceTracker:
    """Context manager for tracking MCP operation performance."""

    def __init__(
        self,
        monitor: McpMonitor,
        operation: str,
        tenant_id: Optional[str] = None,
        labels: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize performance tracker.

        Args:
            monitor: MCP monitor instance
            operation: Operation name being tracked
            tenant_id: Tenant ID for the operation
            labels: Additional labels for metrics
        """
        self.monitor = monitor
        self.operation = operation
        self.tenant_id = tenant_id
        self.labels = labels or {}
        self.start_time = None
        self.success = False
        self.error = None

    def __enter__(self):
        """Start tracking performance."""
        self.start_time = time.time()

        # Record operation start
        self.monitor.record_metric(
            name=f"{self.operation}_started",
            value=1,
            metric_type=MetricType.COUNTER,
            labels={**self.labels, "operation": self.operation},
            tenant_id=self.tenant_id,
        )

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """End tracking and record metrics."""
        if self.start_time is None:
            return

        duration = time.time() - self.start_time

        # Determine success/failure
        self.success = exc_type is None
        if exc_val:
            self.error = str(exc_val)

        # Record duration metric
        self.monitor.record_metric(
            name=f"{self.operation}_duration_seconds",
            value=duration,
            metric_type=MetricType.HISTOGRAM,
            labels={
                **self.labels,
                "operation": self.operation,
                "success": str(self.success).lower(),
            },
            tenant_id=self.tenant_id,
        )

        # Record completion counter
        self.monitor.record_metric(
            name=f"{self.operation}_completed",
            value=1,
            metric_type=MetricType.COUNTER,
            labels={
                **self.labels,
                "operation": self.operation,
                "success": str(self.success).lower(),
            },
            tenant_id=self.tenant_id,
        )

        # Record error if operation failed
        if not self.success:
            self.monitor.record_alert(
                alert_id=f"{self.operation}_error_{int(time.time())}",
                title=f"MCP Operation Failed: {self.operation}",
                description=f"Operation {self.operation} failed: {self.error}",
                severity=AlertSeverity.ERROR,
                tenant_id=self.tenant_id,
                metadata={
                    "operation": self.operation,
                    "duration": duration,
                    "error": self.error,
                    "labels": self.labels,
                },
            )


# Global monitor instance
_global_monitor: Optional[McpMonitor] = None


def get_monitor() -> McpMonitor:
    """Get or create the global MCP monitor instance."""
    global _global_monitor

    if _global_monitor is None:
        _global_monitor = McpMonitor(
            enable_cloud_logging=os.getenv("ENABLE_CLOUD_LOGGING", "true").lower()
            == "true",
            enable_metrics=os.getenv("ENABLE_MCP_METRICS", "true").lower() == "true",
        )

    return _global_monitor


def track_performance(
    operation: str,
    tenant_id: Optional[str] = None,
    labels: Optional[Dict[str, str]] = None,
) -> McpPerformanceTracker:
    """
    Create a performance tracker for an MCP operation.

    Args:
        operation: Operation name
        tenant_id: Tenant ID
        labels: Additional labels

    Returns:
        Performance tracker context manager
    """
    return McpPerformanceTracker(get_monitor(), operation, tenant_id, labels)


def record_metric(
    name: str,
    value: float,
    metric_type: MetricType,
    labels: Optional[Dict[str, str]] = None,
    tenant_id: Optional[str] = None,
):
    """Record a metric using the global monitor."""
    get_monitor().record_metric(name, value, metric_type, labels, tenant_id)


def record_alert(
    alert_id: str,
    title: str,
    description: str,
    severity: AlertSeverity,
    tenant_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
):
    """Record an alert using the global monitor."""
    get_monitor().record_alert(
        alert_id, title, description, severity, tenant_id, metadata
    )
