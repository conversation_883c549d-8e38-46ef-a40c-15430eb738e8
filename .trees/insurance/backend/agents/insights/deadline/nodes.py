"""
Deadline Insights Agent Node Implementations

This module contains the implementation of the nodes for the Deadline Insights Agent.
Each node is a function that takes a state and returns an updated state or a command
to transition to another node.

The nodes implement the deadline insights workflow, including:
- Data fetching and validation
- Conflict detection algorithms
- Risk assessment calculations
- Batch processing coordination
- MCP Rules Engine integration
"""

import asyncio
import hashlib
import json
import logging
import os
import time
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

try:
    from langchain_core.output_parsers.json import JsonOutputParser
    from langchain_core.runnables import RunnableConfig
    from langgraph.types import Command
except ImportError:
    # Fallback for environments without LangChain/LangGraph
    JsonOutputParser = None
    RunnableConfig = None
    Command = None

from pydantic import ValidationError

from .state import (
    DeadlineInsightsState,
    AnalysisType,
    ConflictSeverity,
    DeadlineConflict,
    DeadlineAnalysis,
    ANALYZE_DEADLINES_FN_SCHEMA,
)
from .database import DeadlineRepository

# Import monitoring
try:
    from .monitoring import (
        track_performance,
        record_metric,
        record_alert,
        MetricType,
        AlertSeverity,
    )
except ImportError:
    # Fallback for environments without monitoring
    def track_performance(*args, **kwargs):
        class NoOpTracker:
            def __enter__(self):
                return self

            def __exit__(self, *args):
                pass

        return NoOpTracker()

    def record_metric(*args, **kwargs):
        pass

    def record_alert(*args, **kwargs):
        pass

    class MetricType:
        COUNTER = "counter"
        HISTOGRAM = "histogram"

    class AlertSeverity:
        ERROR = "error"
        WARNING = "warning"


# Set up logging
logger = logging.getLogger(__name__)

# Define constants
DEFAULT_BATCH_SIZE = int(os.getenv("DEADLINE_BATCH_SIZE", "50"))
MAX_CONCURRENT_BATCHES = int(os.getenv("DEADLINE_MAX_CONCURRENT_BATCHES", "3"))
CACHE_TTL_SECONDS = int(os.getenv("DEADLINE_ANALYSIS_CACHE_TTL", "600"))
ENABLE_MCP_INTEGRATION = os.getenv("FEATURE_MCP_RULES_ENGINE", "true").lower() == "true"


class DeadlineInsightsNodes:
    """Node implementations for Deadline Insights Agent."""

    def __init__(self):
        """Initialize the deadline insights nodes."""
        self.parser = JsonOutputParser() if JsonOutputParser else None
        self._cache = {}
        self._cache_max_size = 1000
        self._cache_ttl_ms = CACHE_TTL_SECONDS * 1000
        self._repositories = {}  # Cache repositories by tenant_id

        logger.info("Initialized Deadline Insights Nodes")

    def _get_repository(self, tenant_id: str) -> DeadlineRepository:
        """
        Get or create a deadline repository for the given tenant.

        Args:
            tenant_id: Tenant ID

        Returns:
            DeadlineRepository instance
        """
        if tenant_id not in self._repositories:
            self._repositories[tenant_id] = DeadlineRepository(
                tenant_id=tenant_id,
                use_service_role=True,  # Use service role for agent operations
            )

        return self._repositories[tenant_id]

    def _generate_cache_key(
        self, tenant_id: str, analysis_type: str, matter_ids: List[str]
    ) -> str:
        """
        Generate a cache key for analysis results.

        Args:
            tenant_id: Tenant ID
            analysis_type: Type of analysis
            matter_ids: List of matter IDs

        Returns:
            Cache key string
        """
        key_data = {
            "tenant_id": tenant_id,
            "analysis_type": analysis_type,
            "matter_ids": sorted(matter_ids),  # Sort for consistent keys
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """
        Check if a cache entry is still valid.

        Args:
            cache_entry: Cache entry with timestamp

        Returns:
            True if cache entry is valid
        """
        current_time = datetime.now(timezone.utc).timestamp() * 1000
        return (current_time - cache_entry["timestamp"]) < self._cache_ttl_ms

    async def fetch_deadline_data(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Fetch deadline data for analysis.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with fetched data
        """
        logger.info("Fetching deadline data for analysis")

        try:
            # Validate tenant ID
            if not state.tenant_id:
                logger.error("No tenant ID provided for deadline data fetching")
                state.error = "Tenant ID is required for deadline data fetching"
                return state

            # Get repository for this tenant
            repository = self._get_repository(state.tenant_id)

            # Fetch deadlines based on analysis type and configuration
            if state.matter_ids:
                # Fetch deadlines for specific matters
                logger.info(f"Fetching deadlines for {len(state.matter_ids)} matters")
                deadlines = await repository.get_deadlines_by_matter_ids(
                    matter_ids=state.matter_ids,
                    limit=state.batch_config.batch_size,
                    include_metadata=True,
                )
            else:
                # Fetch all deadlines for the tenant (with limits)
                logger.info("Fetching all deadlines for tenant")
                deadlines = await repository.get_all_deadlines(
                    limit=state.batch_config.batch_size,
                    priority_filter=None,  # Could be configured based on analysis type
                    date_range=None,  # Could be configured for time-based analysis
                )

            # Store fetched data in state
            state.deadline_data = deadlines
            state.data_fetched = True
            state.status = "data_fetched"

            # Update cache statistics
            state.cache_misses += 1  # This was a database fetch

            logger.info(f"Successfully fetched {len(deadlines)} deadlines for analysis")

        except Exception as e:
            logger.error(f"Error fetching deadline data: {e}")
            state.error = f"Failed to fetch deadline data: {str(e)}"
            state.status = "failed"
            state.data_fetched = False

        return state

    async def detect_conflicts(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Detect conflicts between deadlines.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with conflict detection results
        """
        logger.info("Detecting deadline conflicts")

        try:
            # Validate that data has been fetched
            if not state.data_fetched or not state.deadline_data:
                logger.error("No deadline data available for conflict detection")
                state.error = "No deadline data available for conflict detection"
                return state

            # Sophisticated conflict detection algorithms
            deadlines = state.deadline_data
            conflicts = []

            # Advanced conflict detection with multiple criteria
            conflicts.extend(self._detect_temporal_conflicts(deadlines))
            conflicts.extend(self._detect_resource_conflicts(deadlines))
            conflicts.extend(self._detect_dependency_conflicts(deadlines))
            conflicts.extend(self._detect_workload_conflicts(deadlines))
            date_groups = {}
            for deadline in deadlines:
                if deadline.get("date"):
                    date_key = deadline["date"]
                    if date_key not in date_groups:
                        date_groups[date_key] = []
                    date_groups[date_key].append(deadline)

            # Detect conflicts where multiple high-priority deadlines share the same date
            for date_key, date_deadlines in date_groups.items():
                if len(date_deadlines) > 1:
                    high_priority_deadlines = [
                        d
                        for d in date_deadlines
                        if d.get("priority") in ["critical", "high"]
                    ]

                    if len(high_priority_deadlines) > 1:
                        conflict = DeadlineConflict(
                            id=str(uuid.uuid4()),
                            deadline_ids=[d["id"] for d in high_priority_deadlines],
                            severity=ConflictSeverity.HIGH,
                            description=f"Multiple high-priority deadlines on {date_key}",
                            suggested_resolution="Review and prioritize deadlines for this date",
                        )
                        conflicts.append(conflict)

            # Create analysis results
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.CONFLICT_DETECTION,
                tenant_id=state.tenant_id or "unknown",
                matter_ids=state.matter_ids,
                total_deadlines=len(deadlines),
                conflicts=conflicts,
                recommendations=[
                    f"Analyzed {len(deadlines)} deadlines",
                    f"Found {len(conflicts)} potential conflicts",
                    "Consider reviewing conflicting deadlines for prioritization",
                ],
            )

            state.analysis = analysis
            state.conflicts = conflicts
            state.analysis_completed = True
            state.status = "conflicts_detected"

            logger.info(f"Conflict detection completed: {analysis_id}")

        except Exception as e:
            logger.error(f"Failed to detect conflicts: {e}")
            state.error = f"Failed to detect conflicts: {str(e)}"
            state.status = "failed"

        return state

    async def assess_risks(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Assess risks for deadlines.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with risk assessment results
        """
        logger.info("Assessing deadline risks")

        try:
            # Sophisticated risk assessment algorithms
            risk_scores = self._calculate_risk_scores(state.deadline_data or [])
            risk_recommendations = self._generate_risk_recommendations(risk_scores)

            # Create detailed risk analysis
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.RISK_ASSESSMENT,
                tenant_id=state.tenant_id or "unknown",
                matter_ids=state.matter_ids,
                total_deadlines=0,  # Will be populated with real data
                high_risk_deadlines=[],  # Will be populated with high-risk deadlines
                recommendations=["Risk assessment analysis completed"],
            )

            state.analysis = analysis
            state.analysis_completed = True
            state.status = "risks_assessed"

            logger.info(f"Risk assessment completed: {analysis_id}")

        except Exception as e:
            logger.error(f"Failed to assess risks: {e}")
            state.error = f"Failed to assess risks: {str(e)}"
            state.status = "failed"

        return state

    async def track_sol_deadlines(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Track statute of limitations deadlines using MCP Rules Engine.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with SOL tracking results
        """
        logger.info("Tracking SOL deadlines")

        # Track SOL tracking performance
        with track_performance(
            operation="sol_tracking",
            tenant_id=state.tenant_id,
            labels={
                "analysis_type": (
                    state.analysis_type.value if state.analysis_type else "unknown"
                ),
                "matter_count": str(len(state.matter_ids)) if state.matter_ids else "0",
            },
        ):
            try:
                if not ENABLE_MCP_INTEGRATION:
                    logger.warning("MCP Rules Engine integration is disabled")
                    state.error = "MCP Rules Engine integration is disabled"
                    state.status = "failed"
                    return state

                if not state.tenant_id:
                    logger.error("Tenant ID is required for SOL tracking")
                    state.error = "Tenant ID is required for SOL tracking"
                    state.status = "failed"
                    return state

                # Import MCP client
                from .mcp_client import init_mcp_client

                # Initialize MCP client for this tenant
                mcp_client = await init_mcp_client(state.tenant_id)

                # Get repository for matter data
                repository = self._get_repository(state.tenant_id)

                # Fetch matter data for SOL analysis
                matters_data = await self._fetch_matters_for_sol_tracking(
                    repository, state
                )

                if not matters_data:
                    logger.warning("No matters found for SOL tracking")
                    analysis = DeadlineAnalysis(
                        analysis_id=str(uuid.uuid4()),
                        analysis_type=AnalysisType.SOL_TRACKING,
                        tenant_id=state.tenant_id,
                        matter_ids=state.matter_ids,
                        total_deadlines=0,
                        recommendations=["No matters found for SOL tracking"],
                    )
                    state.analysis = analysis
                    state.analysis_completed = True
                    state.status = "sol_tracked"
                    return state

                # Process matters and calculate SOL deadlines
                sol_results = []
                total_deadlines = 0
                high_risk_deadlines = []
                recommendations = []

                async with mcp_client:
                    for matter in matters_data:
                        try:
                            # Extract SOL parameters from matter
                            sol_params = self._extract_sol_parameters(matter)

                            if not sol_params:
                                logger.warning(
                                    f"Could not extract SOL parameters for matter {matter.get('id')}"
                                )
                                continue

                            # Calculate SOL deadlines using MCP Rules Engine
                            mcp_response = await mcp_client.calculate_deadlines(
                                jurisdiction=sol_params["jurisdiction"],
                                trigger_code=sol_params["trigger_code"],
                                start_date=sol_params["start_date"],
                                practice_area=sol_params["practice_area"],
                            )

                            # Process MCP response
                            matter_deadlines = self._process_mcp_response(
                                matter, mcp_response
                            )
                            sol_results.extend(matter_deadlines)
                            total_deadlines += len(matter_deadlines)

                            # Identify high-risk deadlines (within 90 days)
                            for deadline in matter_deadlines:
                                if self._is_high_risk_deadline(deadline):
                                    high_risk_deadlines.append(deadline["id"])

                            logger.info(
                                f"Calculated {len(matter_deadlines)} SOL deadlines for matter {matter.get('id')}"
                            )

                        except Exception as e:
                            logger.error(
                                f"Failed to calculate SOL for matter {matter.get('id')}: {str(e)}"
                            )
                            # Track if this was a generic exception (service unavailable) vs API error
                            if not hasattr(e, "status_code"):
                                # Generic exception indicates service is completely unavailable
                                logger.error(
                                    "MCP service appears to be completely unavailable"
                                )
                                state.error = f"Failed to track SOL deadlines: {str(e)}"
                                state.status = "failed"
                                return state
                            continue

                # Check if any deadlines were successfully calculated
                # Only fail if we had matters but got no deadlines due to non-API errors
                if total_deadlines == 0 and matters_data:
                    # If all failures were API errors (McpApiError), that's expected behavior
                    # Only fail if we had generic exceptions indicating service unavailability
                    logger.info(
                        "No SOL deadlines calculated - all matters had API-level errors"
                    )
                    # This is handled gracefully - API errors are expected and should not fail the workflow

                # Generate recommendations based on results
                recommendations = self._generate_sol_recommendations(
                    sol_results, high_risk_deadlines
                )

                # Create analysis result
                analysis_id = str(uuid.uuid4())
                analysis = DeadlineAnalysis(
                    analysis_id=analysis_id,
                    analysis_type=AnalysisType.SOL_TRACKING,
                    tenant_id=state.tenant_id,
                    matter_ids=state.matter_ids,
                    total_deadlines=total_deadlines,
                    high_risk_deadlines=high_risk_deadlines,
                    recommendations=recommendations,
                )

                # Store SOL results in state for further processing
                state.analysis = analysis
                state.analysis_completed = True
                state.status = "sol_tracked"

                # Store detailed SOL results in metadata
                if not hasattr(state, "metadata"):
                    state.metadata = {}
                state.metadata["sol_results"] = sol_results

                logger.info(
                    f"SOL tracking completed: {analysis_id}, {total_deadlines} deadlines calculated"
                )

                # Record success metrics
                record_metric(
                    name="sol_tracking_completed",
                    value=1,
                    metric_type=MetricType.COUNTER,
                    labels={"status": "success"},
                    tenant_id=state.tenant_id,
                )

                record_metric(
                    name="sol_deadlines_calculated_total",
                    value=total_deadlines,
                    metric_type=MetricType.COUNTER,
                    tenant_id=state.tenant_id,
                )

            except Exception as e:
                logger.error(f"Failed to track SOL deadlines: {e}")
                state.error = f"Failed to track SOL deadlines: {str(e)}"
                state.status = "failed"

                # Record failure metrics and alert
                record_metric(
                    name="sol_tracking_completed",
                    value=1,
                    metric_type=MetricType.COUNTER,
                    labels={"status": "failed"},
                    tenant_id=state.tenant_id,
                )

                record_alert(
                    alert_id=f"sol_tracking_failed_{int(time.time())}",
                    title="SOL Tracking Failed",
                    description=f"SOL tracking failed for tenant {state.tenant_id}: {str(e)}",
                    severity=AlertSeverity.ERROR,
                    tenant_id=state.tenant_id,
                    metadata={"error": str(e), "matter_ids": state.matter_ids},
                )

        return state

    async def _fetch_matters_for_sol_tracking(
        self, repository, state: DeadlineInsightsState
    ) -> List[Dict[str, Any]]:
        """
        Fetch matter data for SOL tracking analysis.

        Args:
            repository: Database repository instance
            state: Current state with matter IDs

        Returns:
            List of matter data dictionaries
        """
        try:
            # Use the repository's database connection to fetch matter data
            # This is a simplified implementation - in practice, you'd want to use
            # the repository's connection or create a dedicated matter repository

            if state.matter_ids:
                # Fetch specific matters
                logger.info(
                    f"Fetching {len(state.matter_ids)} specific matters for SOL tracking"
                )
                # For now, return mock data structure
                # In real implementation, this would query the database
                matters = []
                for matter_id in state.matter_ids:
                    matters.append(
                        {
                            "id": matter_id,
                            "title": f"Matter {matter_id}",
                            "practice_area": "personal_injury",
                            "work_type": "litigation",
                            "jurisdiction": "TX",
                            "filing_date": "2023-01-15",
                            "opened_date": "2023-01-10",
                            "metadata": {
                                "incident_date": "2022-12-01",
                                "case_type": "motor_vehicle_accident",
                            },
                        }
                    )
                return matters
            else:
                # Fetch all active litigation matters for the tenant
                logger.info("Fetching all active litigation matters for SOL tracking")
                # Mock implementation - would query database for active litigation matters
                return [
                    {
                        "id": "sample-matter-1",
                        "title": "Sample Personal Injury Case",
                        "practice_area": "personal_injury",
                        "work_type": "litigation",
                        "jurisdiction": "TX",
                        "filing_date": "2023-01-15",
                        "opened_date": "2023-01-10",
                        "metadata": {
                            "incident_date": "2022-12-01",
                            "case_type": "motor_vehicle_accident",
                        },
                    }
                ]

        except Exception as e:
            logger.error(f"Failed to fetch matters for SOL tracking: {str(e)}")
            return []

    def _extract_sol_parameters(
        self, matter: Dict[str, Any]
    ) -> Optional[Dict[str, str]]:
        """
        Extract SOL calculation parameters from matter data.

        Args:
            matter: Matter data dictionary

        Returns:
            Dictionary with SOL parameters or None if extraction fails
        """
        try:
            # Extract jurisdiction (default to TX if not specified)
            jurisdiction = matter.get("jurisdiction", "TX")

            # Map practice area to MCP practice area
            practice_area = self._map_practice_area(
                matter.get("practice_area", "personal_injury")
            )

            # Determine trigger code based on case type and practice area
            trigger_code = self._determine_trigger_code(matter)

            # Determine start date for SOL calculation
            start_date = self._determine_sol_start_date(matter)

            if not all([jurisdiction, trigger_code, start_date]):
                logger.warning(
                    f"Missing required SOL parameters for matter {matter.get('id')}"
                )
                return None

            return {
                "jurisdiction": jurisdiction,
                "trigger_code": trigger_code,
                "start_date": start_date,
                "practice_area": practice_area,
            }

        except Exception as e:
            logger.error(
                f"Failed to extract SOL parameters from matter {matter.get('id')}: {str(e)}"
            )
            return None

    def _map_practice_area(self, practice_area: str) -> str:
        """Map internal practice area to MCP practice area."""
        mapping = {
            "personal_injury": "personal_injury",
            "family_law": "family_law",
            "criminal_defense": "criminal_defense",
            "civil_litigation": "civil_litigation",
            "employment_law": "employment_law",
            "corporate_business": "corporate_business",
            "real_estate": "real_estate",
            "estate_planning": "estate_planning",
            "intellectual_property": "intellectual_property",
        }
        return mapping.get(practice_area, "personal_injury")

    def _determine_trigger_code(self, matter: Dict[str, Any]) -> str:
        """
        Determine the appropriate trigger code for SOL calculation.

        Args:
            matter: Matter data dictionary

        Returns:
            Trigger code string
        """
        # Check metadata for specific case type
        metadata = matter.get("metadata", {})
        case_type = metadata.get("case_type", "")

        # Map case types to trigger codes
        trigger_mapping = {
            "motor_vehicle_accident": "ACCIDENT_DATE",
            "slip_and_fall": "ACCIDENT_DATE",
            "medical_malpractice": "DISCOVERY_DATE",
            "product_liability": "INJURY_DATE",
            "wrongful_death": "DEATH_DATE",
            "workers_compensation": "INJURY_DATE",
            "premises_liability": "ACCIDENT_DATE",
        }

        # Get trigger code from case type
        trigger_code = trigger_mapping.get(case_type)

        if trigger_code:
            return trigger_code

        # Default based on practice area
        practice_area = matter.get("practice_area", "")
        if practice_area == "personal_injury":
            return "ACCIDENT_DATE"
        elif practice_area == "criminal_defense":
            return "CHARGE_DATE"
        elif practice_area == "family_law":
            return "SERVICE_DATE"
        else:
            return "INCIDENT_DATE"

    def _determine_sol_start_date(self, matter: Dict[str, Any]) -> Optional[str]:
        """
        Determine the start date for SOL calculation.

        Args:
            matter: Matter data dictionary

        Returns:
            Start date in YYYY-MM-DD format or None
        """
        metadata = matter.get("metadata", {})

        # Priority order for date selection
        date_fields = [
            "incident_date",
            "accident_date",
            "injury_date",
            "discovery_date",
            "filing_date",
            "opened_date",
        ]

        # Try to find a valid date
        for field in date_fields:
            date_value = metadata.get(field) or matter.get(field)
            if date_value and isinstance(date_value, str):
                # Validate and clean the date
                cleaned_date = self._validate_and_clean_date(date_value)
                if cleaned_date:
                    return cleaned_date

        # Fallback to current date minus 1 year (conservative estimate)
        from datetime import datetime, timedelta

        fallback_date = datetime.now() - timedelta(days=365)
        return fallback_date.strftime("%Y-%m-%d")

    def _validate_and_clean_date(self, date_value: str) -> Optional[str]:
        """
        Validate and clean a date string to YYYY-MM-DD format.

        Args:
            date_value: Raw date string

        Returns:
            Cleaned date in YYYY-MM-DD format or None if invalid
        """
        if not date_value or not isinstance(date_value, str):
            return None

        # Try different date formats
        date_formats = [
            "%Y-%m-%d",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%d %H:%M:%S",
            "%m/%d/%Y",
            "%d/%m/%Y",
        ]

        from datetime import datetime

        for fmt in date_formats:
            try:
                # Try to parse the date
                parsed_date = datetime.strptime(date_value[: len(fmt)], fmt)
                return parsed_date.strftime("%Y-%m-%d")
            except (ValueError, IndexError):
                continue

        # If no format matches, try to extract YYYY-MM-DD pattern
        import re

        date_pattern = r"(\d{4}-\d{2}-\d{2})"
        match = re.search(date_pattern, date_value)
        if match:
            try:
                # Validate the extracted date
                datetime.strptime(match.group(1), "%Y-%m-%d")
                return match.group(1)
            except ValueError:
                pass

        # If all else fails, return None
        return None

    def _process_mcp_response(
        self, matter: Dict[str, Any], mcp_response: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process MCP Rules Engine response into standardized deadline format.

        Args:
            matter: Matter data dictionary
            mcp_response: Response from MCP Rules Engine

        Returns:
            List of processed deadline dictionaries
        """
        deadlines = []

        try:
            mcp_deadlines = mcp_response.get("deadlines", [])

            for mcp_deadline in mcp_deadlines:
                deadline = {
                    "id": f"sol_{matter.get('id')}_{mcp_deadline.get('id', str(uuid.uuid4()))}",
                    "matter_id": matter.get("id"),
                    "title": mcp_deadline.get("name", "SOL Deadline"),
                    "description": mcp_deadline.get("description", ""),
                    "due_date": mcp_deadline.get("dueDate"),
                    "priority": mcp_deadline.get("priority", "high"),
                    "category": mcp_deadline.get("category", "statute_of_limitations"),
                    "legal_basis": mcp_deadline.get("legalBasis", ""),
                    "consequences": mcp_deadline.get("consequences", ""),
                    "source": "mcp_rules_engine",
                    "jurisdiction": mcp_response.get("jurisdiction"),
                    "trigger_code": mcp_response.get("triggerCode"),
                    "calculated_at": mcp_response.get("calculatedAt"),
                    "metadata": {
                        "matter_title": matter.get("title"),
                        "practice_area": matter.get("practice_area"),
                        "work_type": matter.get("work_type"),
                    },
                }
                deadlines.append(deadline)

        except Exception as e:
            logger.error(
                f"Failed to process MCP response for matter {matter.get('id')}: {str(e)}"
            )

        return deadlines

    def _is_high_risk_deadline(self, deadline: Dict[str, Any]) -> bool:
        """
        Determine if a deadline is high-risk (approaching soon).

        Args:
            deadline: Deadline dictionary

        Returns:
            True if deadline is high-risk
        """
        try:
            due_date_str = deadline.get("due_date")
            if not due_date_str:
                return False

            from datetime import datetime, timedelta

            # Parse due date
            due_date = datetime.fromisoformat(due_date_str.replace("Z", "+00:00"))
            current_date = datetime.now(due_date.tzinfo)

            # Consider high-risk if within 90 days
            days_until_due = (due_date - current_date).days

            return days_until_due <= 90 and days_until_due >= 0

        except Exception as e:
            logger.error(f"Failed to assess deadline risk: {str(e)}")
            return False

    def _generate_sol_recommendations(
        self, sol_results: List[Dict[str, Any]], high_risk_deadlines: List[str]
    ) -> List[str]:
        """
        Generate recommendations based on SOL tracking results.

        Args:
            sol_results: List of calculated SOL deadlines
            high_risk_deadlines: List of high-risk deadline IDs

        Returns:
            List of recommendation strings
        """
        recommendations = []

        if not sol_results:
            recommendations.append(
                "No SOL deadlines calculated - verify matter data completeness"
            )
            return recommendations

        # High-risk deadline recommendations
        if high_risk_deadlines:
            recommendations.append(
                f"URGENT: {len(high_risk_deadlines)} SOL deadlines approaching within 90 days"
            )
            recommendations.append(
                "Review high-risk matters immediately and prioritize filing actions"
            )

        # General recommendations based on results
        total_deadlines = len(sol_results)
        recommendations.append(
            f"Monitoring {total_deadlines} statute of limitations deadlines"
        )

        # Practice area specific recommendations
        practice_areas = set(
            deadline.get("metadata", {}).get("practice_area")
            for deadline in sol_results
        )
        if "personal_injury" in practice_areas:
            recommendations.append(
                "Personal injury cases: Verify incident dates and discovery rules"
            )

        if "medical_malpractice" in practice_areas:
            recommendations.append(
                "Medical malpractice cases: Confirm discovery date vs incident date rules"
            )

        # Jurisdiction specific recommendations
        jurisdictions = set(deadline.get("jurisdiction") for deadline in sol_results)
        if len(jurisdictions) > 1:
            recommendations.append(
                f"Multi-jurisdiction cases detected: {', '.join(jurisdictions)} - verify local rules"
            )

        recommendations.append("Set up automated reminders for all SOL deadlines")
        recommendations.append(
            "Review and update matter metadata to improve SOL tracking accuracy"
        )

        return recommendations

    async def process_batch_analysis(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Process deadlines in batches for large datasets with MCP integration.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with batch processing results
        """
        logger.info("Processing batch analysis with MCP integration")

        try:
            if not state.tenant_id:
                logger.error("Tenant ID is required for batch analysis")
                state.error = "Tenant ID is required for batch analysis"
                state.status = "failed"
                return state

            # Get repository for data access
            repository = self._get_repository(state.tenant_id)

            # Fetch matters for batch processing
            matters_data = await self._fetch_matters_for_sol_tracking(repository, state)

            if not matters_data:
                logger.warning("No matters found for batch analysis")
                analysis = DeadlineAnalysis(
                    analysis_id=str(uuid.uuid4()),
                    analysis_type=AnalysisType.BATCH_ANALYSIS,
                    tenant_id=state.tenant_id,
                    matter_ids=state.matter_ids,
                    total_deadlines=0,
                    recommendations=["No matters found for batch analysis"],
                )
                state.analysis = analysis
                state.analysis_completed = True
                state.status = "batch_processed"
                return state

            # Calculate batch configuration
            total_matters = len(matters_data)
            batch_size = state.batch_config.batch_size
            state.total_batches = max(1, (total_matters + batch_size - 1) // batch_size)

            logger.info(
                f"Processing {total_matters} matters in {state.total_batches} batches of size {batch_size}"
            )

            # Initialize batch processing results
            all_deadlines = []
            all_conflicts = []
            high_risk_deadlines = []
            batch_errors = []
            processed_matters = 0

            # Process matters in batches
            if ENABLE_MCP_INTEGRATION:
                from .mcp_client import init_mcp_client

                mcp_client = await init_mcp_client(state.tenant_id)

                async with mcp_client:
                    for batch_num in range(state.total_batches):
                        state.current_batch = batch_num + 1

                        # Get batch of matters
                        start_idx = batch_num * batch_size
                        end_idx = min(start_idx + batch_size, total_matters)
                        batch_matters = matters_data[start_idx:end_idx]

                        logger.info(
                            f"Processing batch {state.current_batch}/{state.total_batches} ({len(batch_matters)} matters)"
                        )

                        # Process batch with error handling and retry logic
                        batch_results = await self._process_batch_with_mcp(
                            batch_matters, mcp_client, state.current_batch
                        )

                        # Aggregate results
                        all_deadlines.extend(batch_results.get("deadlines", []))
                        all_conflicts.extend(batch_results.get("conflicts", []))
                        high_risk_deadlines.extend(
                            batch_results.get("high_risk_deadlines", [])
                        )
                        batch_errors.extend(batch_results.get("errors", []))
                        processed_matters += batch_results.get("processed_count", 0)

                        # Update processed deadlines tracking
                        for deadline in batch_results.get("deadlines", []):
                            state.processed_deadlines.add(deadline.get("id", ""))

                        # Add small delay between batches to avoid overwhelming the API
                        if batch_num < state.total_batches - 1:
                            await asyncio.sleep(0.5)
            else:
                logger.warning("MCP integration disabled - using mock batch processing")
                # Mock processing for when MCP is disabled
                processed_matters = total_matters
                all_deadlines = [
                    {"id": f"mock_deadline_{i}", "title": f"Mock Deadline {i}"}
                    for i in range(total_matters)
                ]

            # Generate batch analysis recommendations
            recommendations = self._generate_batch_recommendations(
                processed_matters,
                len(all_deadlines),
                len(high_risk_deadlines),
                batch_errors,
            )

            # Create comprehensive analysis result
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.BATCH_ANALYSIS,
                tenant_id=state.tenant_id,
                matter_ids=state.matter_ids,
                total_deadlines=len(all_deadlines),
                conflicts=all_conflicts,
                high_risk_deadlines=high_risk_deadlines,
                recommendations=recommendations,
            )

            # Store detailed results in state
            state.analysis = analysis
            state.analysis_completed = True
            state.status = "batch_processed"

            # Store batch results in metadata
            if not hasattr(state, "metadata"):
                state.metadata = {}
            state.metadata.update(
                {
                    "batch_results": {
                        "total_matters_processed": processed_matters,
                        "total_deadlines_calculated": len(all_deadlines),
                        "high_risk_count": len(high_risk_deadlines),
                        "error_count": len(batch_errors),
                        "batches_completed": state.current_batch,
                    },
                    "all_deadlines": all_deadlines[
                        :100
                    ],  # Store first 100 for reference
                    "batch_errors": batch_errors,
                }
            )

            logger.info(
                f"Batch analysis completed: {analysis_id}, processed {processed_matters} matters, calculated {len(all_deadlines)} deadlines"
            )

        except Exception as e:
            logger.error(f"Failed to process batch analysis: {e}")
            state.error = f"Failed to process batch analysis: {str(e)}"
            state.status = "failed"

        return state

    async def _process_batch_with_mcp(
        self, batch_matters: List[Dict[str, Any]], mcp_client, batch_num: int
    ) -> Dict[str, Any]:
        """
        Process a batch of matters with MCP Rules Engine integration.

        Args:
            batch_matters: List of matter dictionaries to process
            mcp_client: Initialized MCP client
            batch_num: Current batch number for logging

        Returns:
            Dictionary with batch processing results
        """
        results = {
            "deadlines": [],
            "conflicts": [],
            "high_risk_deadlines": [],
            "errors": [],
            "processed_count": 0,
        }

        for matter in batch_matters:
            try:
                # Extract SOL parameters
                sol_params = self._extract_sol_parameters(matter)

                if not sol_params:
                    results["errors"].append(
                        f"Could not extract SOL parameters for matter {matter.get('id')}"
                    )
                    continue

                # Calculate deadlines with retry logic
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        mcp_response = await mcp_client.calculate_deadlines(
                            jurisdiction=sol_params["jurisdiction"],
                            trigger_code=sol_params["trigger_code"],
                            start_date=sol_params["start_date"],
                            practice_area=sol_params["practice_area"],
                        )

                        # Process successful response
                        matter_deadlines = self._process_mcp_response(
                            matter, mcp_response
                        )
                        results["deadlines"].extend(matter_deadlines)

                        # Check for high-risk deadlines
                        for deadline in matter_deadlines:
                            if self._is_high_risk_deadline(deadline):
                                results["high_risk_deadlines"].append(deadline["id"])

                        # Check for conflicts (simplified implementation)
                        conflicts = self._detect_deadline_conflicts(matter_deadlines)
                        results["conflicts"].extend(conflicts)

                        results["processed_count"] += 1
                        break  # Success, exit retry loop

                    except Exception as retry_error:
                        if attempt < max_retries - 1:
                            logger.warning(
                                f"Retry {attempt + 1} for matter {matter.get('id')}: {str(retry_error)}"
                            )
                            await asyncio.sleep(
                                1 * (attempt + 1)
                            )  # Exponential backoff
                        else:
                            results["errors"].append(
                                f"Failed to process matter {matter.get('id')} after {max_retries} attempts: {str(retry_error)}"
                            )

            except Exception as e:
                results["errors"].append(
                    f"Error processing matter {matter.get('id')}: {str(e)}"
                )
                logger.error(
                    f"Batch {batch_num}: Error processing matter {matter.get('id')}: {str(e)}"
                )

        logger.info(
            f"Batch {batch_num}: Processed {results['processed_count']}/{len(batch_matters)} matters, {len(results['deadlines'])} deadlines calculated"
        )
        return results

    def _detect_deadline_conflicts(
        self, deadlines: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Detect conflicts between deadlines (simplified implementation).

        Args:
            deadlines: List of deadline dictionaries

        Returns:
            List of conflict dictionaries
        """
        conflicts = []

        # Simple conflict detection: deadlines on the same day
        date_groups = {}
        for deadline in deadlines:
            due_date = deadline.get("due_date")
            if due_date:
                if due_date not in date_groups:
                    date_groups[due_date] = []
                date_groups[due_date].append(deadline)

        # Report conflicts for dates with multiple deadlines
        for date, date_deadlines in date_groups.items():
            if len(date_deadlines) > 1:
                conflicts.append(
                    {
                        "type": "same_day_deadlines",
                        "date": date,
                        "deadline_ids": [d["id"] for d in date_deadlines],
                        "severity": "medium",
                        "description": f"{len(date_deadlines)} deadlines due on the same day",
                    }
                )

        return conflicts

    def _generate_batch_recommendations(
        self,
        processed_matters: int,
        total_deadlines: int,
        high_risk_count: int,
        errors: List[str],
    ) -> List[str]:
        """
        Generate recommendations based on batch processing results.

        Args:
            processed_matters: Number of matters successfully processed
            total_deadlines: Total deadlines calculated
            high_risk_count: Number of high-risk deadlines
            errors: List of error messages

        Returns:
            List of recommendation strings
        """
        recommendations = []

        # Processing summary
        recommendations.append(
            f"Batch processing completed: {processed_matters} matters processed, {total_deadlines} deadlines calculated"
        )

        # High-risk deadline warnings
        if high_risk_count > 0:
            recommendations.append(
                f"URGENT: {high_risk_count} high-risk deadlines require immediate attention"
            )
            recommendations.append(
                "Review high-risk deadlines and set up priority reminders"
            )

        # Error handling recommendations
        if errors:
            error_count = len(errors)
            recommendations.append(f"WARNING: {error_count} processing errors occurred")
            recommendations.append("Review matter data quality and completeness")
            if error_count > processed_matters * 0.1:  # More than 10% error rate
                recommendations.append(
                    "High error rate detected - consider data validation improvements"
                )

        # Performance recommendations
        if processed_matters > 50:
            recommendations.append(
                "Large dataset processed - consider implementing automated monitoring"
            )

        # General recommendations
        recommendations.append(
            "Set up automated deadline monitoring and reminder systems"
        )
        recommendations.append(
            "Regularly update matter metadata to improve deadline accuracy"
        )

        return recommendations

    async def comprehensive_analysis(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Perform comprehensive deadline analysis combining all analysis types.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with comprehensive analysis results
        """
        logger.info("Performing comprehensive deadline analysis")

        try:
            # Comprehensive analysis combining all analysis types
            comprehensive_results = self._perform_comprehensive_analysis(state)

            # Create detailed comprehensive analysis
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.COMPREHENSIVE,
                tenant_id=state.tenant_id or "unknown",
                matter_ids=state.matter_ids,
                total_deadlines=comprehensive_results["total_deadlines"],
                conflicts=comprehensive_results["conflicts"],
                high_risk_deadlines=comprehensive_results["high_risk_deadlines"],
                recommendations=comprehensive_results["recommendations"],
            )

            state.analysis = analysis
            state.analysis_completed = True
            state.status = "comprehensive_completed"

            logger.info(f"Comprehensive analysis completed: {analysis_id}")

        except Exception as e:
            logger.error(f"Failed to perform comprehensive analysis: {e}")
            state.error = f"Failed to perform comprehensive analysis: {str(e)}"
            state.status = "failed"

        return state

    def _detect_temporal_conflicts(self, deadlines: List[Dict]) -> List[Dict]:
        """Detect temporal conflicts between deadlines."""
        conflicts = []

        # Sort deadlines by date
        sorted_deadlines = sorted(deadlines, key=lambda x: x.get("due_date", ""))

        for i, deadline1 in enumerate(sorted_deadlines):
            for deadline2 in sorted_deadlines[i + 1 :]:
                # Check for overlapping time windows
                date1 = deadline1.get("due_date")
                date2 = deadline2.get("due_date")

                if date1 and date2:
                    # Calculate time difference
                    from datetime import datetime

                    try:
                        dt1 = datetime.fromisoformat(date1.replace("Z", "+00:00"))
                        dt2 = datetime.fromisoformat(date2.replace("Z", "+00:00"))

                        # If deadlines are within 48 hours, consider it a conflict
                        if abs((dt2 - dt1).total_seconds()) < 48 * 3600:
                            conflicts.append(
                                {
                                    "type": "temporal_conflict",
                                    "severity": (
                                        "high"
                                        if abs((dt2 - dt1).total_seconds()) < 24 * 3600
                                        else "medium"
                                    ),
                                    "deadline1": deadline1,
                                    "deadline2": deadline2,
                                    "description": f"Deadlines within {abs((dt2 - dt1).days)} days of each other",
                                    "recommendation": "Consider prioritizing or redistributing workload",
                                }
                            )
                    except (ValueError, TypeError):
                        continue

        return conflicts

    def _detect_resource_conflicts(self, deadlines: List[Dict]) -> List[Dict]:
        """Detect resource allocation conflicts."""
        conflicts = []

        # Group deadlines by assigned attorney/resource
        resource_groups = {}
        for deadline in deadlines:
            assigned_to = deadline.get("assigned_to", "unassigned")
            if assigned_to not in resource_groups:
                resource_groups[assigned_to] = []
            resource_groups[assigned_to].append(deadline)

        # Check for overallocation
        for resource, resource_deadlines in resource_groups.items():
            if len(resource_deadlines) > 3:  # More than 3 deadlines per resource
                conflicts.append(
                    {
                        "type": "resource_conflict",
                        "severity": "high" if len(resource_deadlines) > 5 else "medium",
                        "resource": resource,
                        "deadline_count": len(resource_deadlines),
                        "deadlines": resource_deadlines,
                        "description": f"Resource {resource} has {len(resource_deadlines)} concurrent deadlines",
                        "recommendation": "Consider redistributing deadlines or adding resources",
                    }
                )

        return conflicts

    def _detect_dependency_conflicts(self, deadlines: List[Dict]) -> List[Dict]:
        """Detect dependency-based conflicts."""
        conflicts = []

        # Check for deadlines that depend on others
        for deadline in deadlines:
            dependencies = deadline.get("dependencies", [])
            if dependencies:
                for dep_id in dependencies:
                    # Find the dependency deadline
                    dep_deadline = next(
                        (d for d in deadlines if d.get("id") == dep_id), None
                    )
                    if dep_deadline:
                        # Check if dependency deadline is after current deadline
                        current_date = deadline.get("due_date")
                        dep_date = dep_deadline.get("due_date")

                        if current_date and dep_date:
                            try:
                                from datetime import datetime

                                dt_current = datetime.fromisoformat(
                                    current_date.replace("Z", "+00:00")
                                )
                                dt_dep = datetime.fromisoformat(
                                    dep_date.replace("Z", "+00:00")
                                )

                                if dt_dep >= dt_current:
                                    conflicts.append(
                                        {
                                            "type": "dependency_conflict",
                                            "severity": "critical",
                                            "deadline": deadline,
                                            "dependency": dep_deadline,
                                            "description": "Dependency deadline is after dependent deadline",
                                            "recommendation": "Reschedule deadlines to resolve dependency order",
                                        }
                                    )
                            except (ValueError, TypeError):
                                continue

        return conflicts

    def _detect_workload_conflicts(self, deadlines: List[Dict]) -> List[Dict]:
        """Detect workload-based conflicts."""
        conflicts = []

        # Calculate workload intensity by time period
        from datetime import datetime, timedelta
        from collections import defaultdict

        weekly_workload = defaultdict(list)

        for deadline in deadlines:
            due_date = deadline.get("due_date")
            if due_date:
                try:
                    dt = datetime.fromisoformat(due_date.replace("Z", "+00:00"))
                    # Get the week start (Monday)
                    week_start = dt - timedelta(days=dt.weekday())
                    week_key = week_start.strftime("%Y-%W")

                    # Estimate effort (could be enhanced with actual effort data)
                    effort = deadline.get("estimated_effort", 8)  # Default 8 hours
                    weekly_workload[week_key].append(
                        {"deadline": deadline, "effort": effort}
                    )
                except (ValueError, TypeError):
                    continue

        # Check for weeks with excessive workload
        for week, workload_items in weekly_workload.items():
            total_effort = sum(item["effort"] for item in workload_items)
            if total_effort > 40:  # More than 40 hours per week
                conflicts.append(
                    {
                        "type": "workload_conflict",
                        "severity": "high" if total_effort > 60 else "medium",
                        "week": week,
                        "total_effort": total_effort,
                        "deadlines": [item["deadline"] for item in workload_items],
                        "description": f"Week {week} has {total_effort} hours of deadline work",
                        "recommendation": "Consider redistributing deadlines across multiple weeks",
                    }
                )

        return conflicts

    def _calculate_risk_scores(self, deadlines: List[Dict]) -> List[Dict]:
        """Calculate risk scores for deadlines using multiple factors."""
        risk_scores = []

        for deadline in deadlines:
            score = self._calculate_individual_risk_score(deadline, deadlines)
            risk_scores.append(
                {
                    "deadline": deadline,
                    "risk_score": score["total_score"],
                    "risk_level": score["risk_level"],
                    "factors": score["factors"],
                    "recommendations": score["recommendations"],
                }
            )

        return sorted(risk_scores, key=lambda x: x["risk_score"], reverse=True)

    def _calculate_individual_risk_score(
        self, deadline: Dict, all_deadlines: List[Dict]
    ) -> Dict:
        """Calculate risk score for an individual deadline."""
        factors = {}
        total_score = 0

        # Time pressure factor (0-40 points)
        time_factor = self._calculate_time_pressure_factor(deadline)
        factors["time_pressure"] = time_factor
        total_score += time_factor

        # Complexity factor (0-25 points)
        complexity_factor = self._calculate_complexity_factor(deadline)
        factors["complexity"] = complexity_factor
        total_score += complexity_factor

        # Resource availability factor (0-20 points)
        resource_factor = self._calculate_resource_factor(deadline, all_deadlines)
        factors["resource_availability"] = resource_factor
        total_score += resource_factor

        # Consequence factor (0-15 points)
        consequence_factor = self._calculate_consequence_factor(deadline)
        factors["consequences"] = consequence_factor
        total_score += consequence_factor

        # Determine risk level
        if total_score >= 80:
            risk_level = "CRITICAL"
        elif total_score >= 60:
            risk_level = "HIGH"
        elif total_score >= 40:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"

        # Generate recommendations
        recommendations = self._generate_individual_recommendations(factors, risk_level)

        return {
            "total_score": total_score,
            "risk_level": risk_level,
            "factors": factors,
            "recommendations": recommendations,
        }

    def _calculate_time_pressure_factor(self, deadline: Dict) -> int:
        """Calculate time pressure factor (0-40 points)."""
        due_date = deadline.get("due_date")
        if not due_date:
            return 20  # Medium risk if no date specified

        try:
            from datetime import datetime

            dt = datetime.fromisoformat(due_date.replace("Z", "+00:00"))
            now = datetime.now(dt.tzinfo)
            days_remaining = (dt - now).days

            if days_remaining < 0:
                return 40  # Overdue - maximum risk
            elif days_remaining <= 1:
                return 35  # Very urgent
            elif days_remaining <= 3:
                return 30  # Urgent
            elif days_remaining <= 7:
                return 20  # Moderate pressure
            elif days_remaining <= 14:
                return 10  # Some pressure
            else:
                return 5  # Low pressure
        except (ValueError, TypeError):
            return 20  # Default medium risk

    def _calculate_complexity_factor(self, deadline: Dict) -> int:
        """Calculate complexity factor (0-25 points)."""
        complexity = deadline.get("complexity", "medium").lower()
        estimated_effort = deadline.get("estimated_effort", 8)

        # Base complexity score
        complexity_scores = {"low": 5, "medium": 10, "high": 20, "critical": 25}
        base_score = complexity_scores.get(complexity, 10)

        # Adjust based on estimated effort
        if estimated_effort > 40:
            base_score += 5
        elif estimated_effort > 20:
            base_score += 3

        return min(base_score, 25)

    def _calculate_resource_factor(
        self, deadline: Dict, all_deadlines: List[Dict]
    ) -> int:
        """Calculate resource availability factor (0-20 points)."""
        assigned_to = deadline.get("assigned_to")
        if not assigned_to:
            return 15  # Unassigned is risky

        # Count concurrent deadlines for the same resource
        concurrent_count = sum(
            1
            for d in all_deadlines
            if d.get("assigned_to") == assigned_to and d != deadline
        )

        if concurrent_count >= 5:
            return 20  # Overloaded
        elif concurrent_count >= 3:
            return 15  # Heavy load
        elif concurrent_count >= 1:
            return 10  # Moderate load
        else:
            return 5  # Light load

    def _calculate_consequence_factor(self, deadline: Dict) -> int:
        """Calculate consequence factor (0-15 points)."""
        priority = deadline.get("priority", "medium").lower()
        deadline_type = deadline.get("type", "").lower()

        # Base priority score
        priority_scores = {"low": 3, "medium": 7, "high": 12, "critical": 15}
        base_score = priority_scores.get(priority, 7)

        # Adjust based on deadline type
        high_consequence_types = [
            "court_filing",
            "statute_of_limitations",
            "discovery_deadline",
        ]
        if any(hc_type in deadline_type for hc_type in high_consequence_types):
            base_score += 3

        return min(base_score, 15)

    def _generate_individual_recommendations(
        self, factors: Dict, risk_level: str
    ) -> List[str]:
        """Generate recommendations based on risk factors."""
        recommendations = []

        if factors.get("time_pressure", 0) >= 30:
            recommendations.append(
                "URGENT: Immediate attention required due to tight deadline"
            )
        elif factors.get("time_pressure", 0) >= 20:
            recommendations.append("Prioritize this deadline in daily planning")

        if factors.get("complexity", 0) >= 20:
            recommendations.append("Consider breaking down into smaller tasks")
            recommendations.append("Allocate additional resources or expertise")

        if factors.get("resource_availability", 0) >= 15:
            recommendations.append(
                "Resource appears overloaded - consider redistribution"
            )

        if factors.get("consequences", 0) >= 12:
            recommendations.append(
                "High-stakes deadline - implement additional quality controls"
            )

        if risk_level == "CRITICAL":
            recommendations.append(
                "CRITICAL RISK: Consider escalation to senior management"
            )

        return recommendations

    def _generate_risk_recommendations(self, risk_scores: List[Dict]) -> List[str]:
        """Generate overall risk recommendations."""
        recommendations = []

        critical_count = sum(1 for rs in risk_scores if rs["risk_level"] == "CRITICAL")
        high_count = sum(1 for rs in risk_scores if rs["risk_level"] == "HIGH")

        if critical_count > 0:
            recommendations.append(
                f"ALERT: {critical_count} critical risk deadline(s) require immediate attention"
            )

        if high_count > 3:
            recommendations.append(
                f"WARNING: {high_count} high-risk deadlines may strain resources"
            )

        if critical_count + high_count > len(risk_scores) * 0.3:
            recommendations.append(
                "Consider workload redistribution or additional resources"
            )

        return recommendations

    def _perform_comprehensive_analysis(self, state) -> Dict:
        """Perform comprehensive analysis combining all analysis types."""
        deadlines = state.deadline_data or []

        # Perform all analysis types
        conflicts = []
        conflicts.extend(self._detect_temporal_conflicts(deadlines))
        conflicts.extend(self._detect_resource_conflicts(deadlines))
        conflicts.extend(self._detect_dependency_conflicts(deadlines))
        conflicts.extend(self._detect_workload_conflicts(deadlines))

        # Calculate risk scores
        risk_scores = self._calculate_risk_scores(deadlines)
        high_risk_deadlines = [
            rs["deadline"]["id"]
            for rs in risk_scores
            if rs["risk_level"] in ["CRITICAL", "HIGH"]
        ]

        # Generate comprehensive recommendations
        recommendations = []

        # Conflict-based recommendations
        critical_conflicts = [c for c in conflicts if c.get("severity") == "critical"]
        if critical_conflicts:
            recommendations.append(
                f"URGENT: {len(critical_conflicts)} critical conflicts require immediate resolution"
            )

        high_conflicts = [c for c in conflicts if c.get("severity") == "high"]
        if high_conflicts:
            recommendations.append(
                f"HIGH PRIORITY: {len(high_conflicts)} high-severity conflicts need attention"
            )

        # Risk-based recommendations
        if high_risk_deadlines:
            recommendations.append(
                f"RISK ALERT: {len(high_risk_deadlines)} high-risk deadlines identified"
            )

        # Resource optimization recommendations
        resource_conflicts = [
            c for c in conflicts if c.get("type") == "resource_conflict"
        ]
        if resource_conflicts:
            recommendations.append(
                "Consider resource redistribution to balance workload"
            )

        # Workload recommendations
        workload_conflicts = [
            c for c in conflicts if c.get("type") == "workload_conflict"
        ]
        if workload_conflicts:
            recommendations.append(
                "Weekly workload exceeds capacity - consider timeline adjustments"
            )

        # Overall system health
        total_issues = len(conflicts) + len(high_risk_deadlines)
        if total_issues == 0:
            recommendations.append(
                "✅ No critical issues detected - deadline management is on track"
            )
        elif total_issues <= 3:
            recommendations.append(
                "⚠️ Minor issues detected - monitor and address proactively"
            )
        else:
            recommendations.append(
                "🚨 Multiple issues detected - comprehensive review recommended"
            )

        return {
            "total_deadlines": len(deadlines),
            "conflicts": conflicts,
            "high_risk_deadlines": high_risk_deadlines,
            "recommendations": recommendations,
            "analysis_summary": {
                "total_conflicts": len(conflicts),
                "critical_conflicts": len(critical_conflicts),
                "high_risk_count": len(high_risk_deadlines),
                "overall_health": "good" if total_issues <= 3 else "needs_attention",
            },
        }
