"""
Deadline Insights Agent Implementation

This module provides the DeadlineInsightsAgent class, which implements the deadline insights agent
for the AiLex system following the unified architecture development guidelines.

The Deadline Insights Agent is responsible for:
1. Long-running deadline analysis and insights generation
2. Conflict detection between deadlines
3. Risk assessment and prioritization
4. Batch processing of deadline data
5. Integration with MCP Rules Engine for SOL tracking

The implementation follows LangGraph's newest patterns, including the Command API
for routing and structured output parsing for robust JSON handling.
"""

import logging
import os
from typing import Any, Dict, Optional
from datetime import datetime, timezone

# Import from the LangGraph-compatible BaseAgent
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), "../../../../"))

try:
    from shared.core.base_agent import BaseAgent, AgentConfig
except ImportError:
    # Fallback to backend agents if shared core not available
    try:
        from backend.agents.shared.core.base_agent import BaseAgent
        from backend.agents.shared.core.state import AgentConfig
    except ImportError:
        # Final fallback - create minimal base classes
        from abc import ABC, abstractmethod
        from pydantic import BaseModel

        class AgentConfig(BaseModel):
            name: str
            agent_type: str = "insights"
            description: str = ""
            version: str = "1.0.0"

        class BaseAgent(ABC):
            def __init__(self, config: AgentConfig):
                self.config = config

            @abstractmethod
            async def initialize(self, state, config=None):
                pass

            @abstractmethod
            async def execute(self, state, config=None):
                pass

            @abstractmethod
            async def cleanup(self, state, config=None):
                pass


try:
    from langgraph.graph import StateGraph, END
    from langchain_core.runnables import RunnableConfig
except ImportError:
    # Fallback for environments without LangGraph
    StateGraph = None
    END = None
    RunnableConfig = None

from .state import DeadlineInsightsState, AnalysisType
from .nodes import DeadlineInsightsNodes

# Configure logger
logger = logging.getLogger(__name__)


class DeadlineInsightsAgent(BaseAgent):
    """
    Deadline Insights Agent for the AiLex system.

    This agent is responsible for:
    1. Long-running deadline analysis and insights generation
    2. Conflict detection between deadlines
    3. Risk assessment and prioritization
    4. Batch processing of deadline data
    5. Integration with MCP Rules Engine for SOL tracking

    It serves as a specialized insights agent for comprehensive deadline management,
    complementing the interactive deadline agent with batch processing capabilities.
    """

    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the Deadline Insights Agent."""
        if config is None:
            config = AgentConfig(
                name="deadline_insights_agent",
                agent_type="insights",
                description="Deadline insights agent for long-running analysis and conflict detection",
                version="1.0.0",
            )

        super().__init__(config)
        self.nodes = DeadlineInsightsNodes()

        # Configuration from environment
        self.enabled = os.getenv("DEADLINE_INSIGHTS_ENABLED", "true").lower() == "true"
        self.batch_size = int(os.getenv("DEADLINE_BATCH_SIZE", "50"))
        self.cache_ttl = int(os.getenv("DEADLINE_ANALYSIS_CACHE_TTL", "600"))

        logger.info(f"Initialized Deadline Insights Agent (enabled: {self.enabled})")

    async def initialize(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Initialize the agent with the given state.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Initializing Deadline Insights Agent")

        try:
            # Check if agent is enabled
            if not self.enabled:
                logger.warning("Deadline Insights Agent is disabled")
                state.error = "Deadline insights agent is disabled"
                state.status = "disabled"
                return state

            # Validate tenant isolation
            if not hasattr(state, "tenant_id") or not state.tenant_id:
                logger.error("No tenant_id provided for deadline insights")
                state.error = "Tenant ID is required for deadline insights"
                state.status = "failed"
                return state

            # Set processing start time
            state.processing_start_time = datetime.now(timezone.utc)

            # Apply batch configuration from environment
            if state.batch_config.batch_size != self.batch_size:
                state.batch_config.batch_size = self.batch_size

            if state.batch_config.cache_ttl_seconds != self.cache_ttl:
                state.batch_config.cache_ttl_seconds = self.cache_ttl

            # Initialize analysis type if not set
            if not state.analysis_type:
                state.analysis_type = AnalysisType.COMPREHENSIVE

            # Mark as initialized
            state.initialized = True
            state.status = "initialized"

            logger.info(
                f"Deadline Insights Agent initialized for tenant {state.tenant_id}"
            )

        except Exception as e:
            logger.error(f"Deadline Insights Agent initialization failed: {e}")
            state.error = str(e)
            state.status = "failed"

        return state

    async def execute(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Execute the main agent logic.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Executing Deadline Insights Agent")

        try:
            # Step 1: Fetch deadline data
            if not state.data_fetched:
                state = await self.nodes.fetch_deadline_data(state, config)

            # Step 2: Perform analysis based on type
            if state.data_fetched and not state.analysis_completed:
                if state.analysis_type == AnalysisType.CONFLICT_DETECTION:
                    state = await self.nodes.detect_conflicts(state, config)
                elif state.analysis_type == AnalysisType.RISK_ASSESSMENT:
                    state = await self.nodes.assess_risks(state, config)
                elif state.analysis_type == AnalysisType.SOL_TRACKING:
                    state = await self.nodes.track_sol_deadlines(state, config)
                elif state.analysis_type == AnalysisType.BATCH_ANALYSIS:
                    state = await self.nodes.process_batch_analysis(state, config)
                else:  # COMPREHENSIVE
                    state = await self.nodes.comprehensive_analysis(state, config)

            # Update status
            if state.analysis_completed and not state.error:
                state.status = "completed"
                state.processing_end_time = datetime.now(timezone.utc)

                # Calculate processing time
                if state.processing_start_time:
                    delta = state.processing_end_time - state.processing_start_time
                    state.processing_time_ms = delta.total_seconds() * 1000
            elif state.error:
                state.status = "failed"

        except Exception as e:
            logger.error(f"Deadline Insights Agent execution failed: {e}")
            state.error = str(e)
            state.status = "failed"

        return state

    async def cleanup(
        self, state: DeadlineInsightsState, config: Any = None
    ) -> DeadlineInsightsState:
        """
        Cleanup resources and finalize state.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        try:
            logger.info("Cleaning up Deadline Insights Agent")

            # Cleanup logic here
            state.cleanup_completed = True

            # Log final state
            if state.analysis:
                logger.info(f"Analysis completed: {state.analysis.analysis_id}")
                logger.info(
                    f"Total deadlines analyzed: {state.analysis.total_deadlines}"
                )
                logger.info(f"Conflicts detected: {len(state.analysis.conflicts)}")
                logger.info(f"Processing time: {state.processing_time_ms}ms")

            # Log performance metrics
            if state.cache_hits > 0 or state.cache_misses > 0:
                cache_hit_rate = (
                    state.cache_hits / (state.cache_hits + state.cache_misses) * 100
                )
                logger.info(f"Cache hit rate: {cache_hit_rate:.1f}%")

        except Exception as e:
            logger.error(f"Deadline Insights Agent cleanup failed: {e}")
            # Don't fail the entire workflow on cleanup errors

        return state

    def create_graph(self):
        """
        Create the LangGraph workflow.

        Returns:
            Compiled StateGraph or fallback implementation
        """
        if StateGraph is None:
            # Fallback for environments without LangGraph
            logger.warning("LangGraph not available, using fallback implementation")
            return self._create_fallback_graph()

        workflow = StateGraph(DeadlineInsightsState)

        # Add nodes
        workflow.add_node("initialize", self.initialize)
        workflow.add_node("execute", self.execute)
        workflow.add_node("cleanup", self.cleanup)

        # Define edges
        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "execute")
        workflow.add_edge("execute", "cleanup")
        workflow.add_edge("cleanup", END)

        return workflow.compile()

    def _create_fallback_graph(self):
        """
        Create a fallback graph implementation.

        Returns:
            Simple callable that mimics LangGraph behavior
        """

        async def fallback_invoke(state, config=None):
            """Fallback invoke method."""
            # Convert dict to DeadlineInsightsState if needed
            if isinstance(state, dict):
                state = DeadlineInsightsState(**state)

            # Run the workflow manually
            state = await self.initialize(state, config or {})
            state = await self.execute(state, config or {})
            state = await self.cleanup(state, config or {})

            return state

        # Return an object that mimics LangGraph's interface
        class FallbackGraph:
            def __init__(self, invoke_func):
                self.ainvoke = invoke_func
                self.invoke = invoke_func  # For sync calls

        return FallbackGraph(fallback_invoke)
