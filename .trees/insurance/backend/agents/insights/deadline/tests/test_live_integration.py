"""
Live Integration Tests for Python MCP Client

This module contains live integration tests that connect to the real MCP Rules Engine
to validate the Python implementation works correctly with the production service.

These tests are designed to run only in CI/staging environments and use real
secrets from Secret Manager to test the complete integration flow.
"""

import os
import pytest
import asyncio
from unittest.mock import patch, Mock
from datetime import datetime, timedelta

from backend.agents.insights.deadline.mcp_client import (
    Mcp<PERSON>lient,
    McpApiError,
    init_mcp_client,
    get_mcp_api_key,
)
from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    BatchProcessingConfig,
)

# Skip all tests in this module if not in CI staging environment
IS_CI = os.getenv("CI") == "true"
IS_STAGING = os.getenv("NODE_ENV") == "staging" or os.getenv("APP_ENV") == "staging"
SKIP_TESTS = not IS_CI or not IS_STAGING

# Test configuration
MCP_RULES_BASE = os.getenv("MCP_RULES_BASE", "https://rules.ailexlaw.com")
TEST_TENANT_ID = "pilot-smith"  # Use pilot tenant for testing
TEST_SECRET_NAME = f"projects/texas-laws-personalinjury/secrets/mcp-key-{TEST_TENANT_ID}/versions/latest"


@pytest.mark.skipif(
    SKIP_TESTS, reason="Live integration tests only run in CI staging environment"
)
class TestLiveMcpIntegration:
    """Live integration tests with real MCP Rules Engine."""

    @pytest.fixture(scope="class")
    async def mcp_client(self):
        """Create MCP client with real API key for testing."""
        if SKIP_TESTS:
            pytest.skip("Live integration tests only run in CI staging environment")

        try:
            # Mock the Secret Manager to use test tenant
            with patch(
                "backend.agents.insights.deadline.mcp_client.MCP_API_KEY_SECRET",
                TEST_SECRET_NAME,
            ):
                client = await init_mcp_client(TEST_TENANT_ID)
                yield client
        except Exception as e:
            pytest.skip(f"Failed to initialize MCP client: {str(e)}")

    @pytest.mark.asyncio
    async def test_health_check_live(self, mcp_client):
        """Test health check against live MCP service."""
        async with mcp_client:
            try:
                health_response = await mcp_client.health_check()

                assert health_response is not None
                # Health check might return different formats, so we're flexible
                assert isinstance(health_response, dict)

                print(f"✅ Health check successful: {health_response}")

            except Exception as e:
                # Health check endpoint might not be available, log but don't fail
                print(f"⚠️ Health check failed (endpoint may not exist): {str(e)}")

    @pytest.mark.asyncio
    async def test_calculate_deadlines_personal_injury_live(self, mcp_client):
        """Test deadline calculation for personal injury case with live service."""
        test_params = {
            "jurisdiction": "TX",
            "trigger_code": "ACCIDENT_DATE",
            "start_date": "2022-01-15",
            "practice_area": "personal_injury",
        }

        print(f"Testing live MCP service with parameters: {test_params}")

        async with mcp_client:
            response = await mcp_client.calculate_deadlines(
                jurisdiction=test_params["jurisdiction"],
                trigger_code=test_params["trigger_code"],
                start_date=test_params["start_date"],
                practice_area=test_params["practice_area"],
            )

            # Validate response structure
            assert response is not None
            assert isinstance(response, dict)

            # Check for deadlines in response
            if "deadlines" in response:
                deadlines = response["deadlines"]
                assert isinstance(deadlines, list)

                if len(deadlines) > 0:
                    # Validate first deadline structure
                    first_deadline = deadlines[0]
                    assert isinstance(first_deadline, dict)

                    # Common deadline fields (flexible validation)
                    expected_fields = ["id", "name", "dueDate"]
                    for field in expected_fields:
                        if field in first_deadline:
                            assert first_deadline[field] is not None

                print(
                    f"✅ Live test passed! Retrieved {len(deadlines)} deadline(s) from production service"
                )
            else:
                print("⚠️ No deadlines field in response, but request succeeded")

            print(f"Full response: {response}")

    @pytest.mark.asyncio
    async def test_calculate_deadlines_different_jurisdictions(self, mcp_client):
        """Test deadline calculation for different jurisdictions."""
        test_cases = [
            {
                "jurisdiction": "TX",
                "trigger_code": "SERVICE_OF_PROCESS",
                "start_date": "2023-01-15",
                "practice_area": "personal_injury",
            },
            {
                "jurisdiction": "CA",
                "trigger_code": "ACCIDENT_DATE",
                "start_date": "2023-02-01",
                "practice_area": "personal_injury",
            },
        ]

        async with mcp_client:
            for i, test_case in enumerate(test_cases):
                print(
                    f"Testing jurisdiction {test_case['jurisdiction']} (test {i+1}/{len(test_cases)})"
                )

                try:
                    response = await mcp_client.calculate_deadlines(
                        jurisdiction=test_case["jurisdiction"],
                        trigger_code=test_case["trigger_code"],
                        start_date=test_case["start_date"],
                        practice_area=test_case["practice_area"],
                    )

                    assert response is not None
                    print(f"✅ {test_case['jurisdiction']} jurisdiction test passed")

                except Exception as e:
                    print(
                        f"⚠️ {test_case['jurisdiction']} jurisdiction test failed: {str(e)}"
                    )
                    # Don't fail the test if specific jurisdictions aren't supported
                    continue

    @pytest.mark.asyncio
    async def test_error_handling_invalid_jurisdiction(self, mcp_client):
        """Test error handling with invalid jurisdiction."""
        invalid_params = {
            "jurisdiction": "INVALID_STATE",
            "trigger_code": "ACCIDENT_DATE",
            "start_date": "2023-01-15",
            "practice_area": "personal_injury",
        }

        async with mcp_client:
            try:
                response = await mcp_client.calculate_deadlines(
                    jurisdiction=invalid_params["jurisdiction"],
                    trigger_code=invalid_params["trigger_code"],
                    start_date=invalid_params["start_date"],
                    practice_area=invalid_params["practice_area"],
                )

                # If no error is thrown, the service might handle invalid jurisdictions gracefully
                print(f"⚠️ Invalid jurisdiction handled gracefully: {response}")

            except McpApiError as e:
                # Expected behavior - service should return an error for invalid jurisdiction
                assert e.status_code >= 400
                print(
                    f"✅ Error handling test passed - got expected error: {e.message}"
                )

            except Exception as e:
                print(f"⚠️ Unexpected error type: {type(e).__name__}: {str(e)}")


@pytest.mark.skipif(
    SKIP_TESTS, reason="Live integration tests only run in CI staging environment"
)
class TestLiveDeadlineInsightsIntegration:
    """Live integration tests for the complete Deadline Insights workflow."""

    @pytest.fixture
    def agent(self):
        """Create DeadlineInsightsAgent for testing."""
        return DeadlineInsightsAgent()

    @pytest.fixture
    def mock_matters_data(self):
        """Create realistic test matter data."""
        return [
            {
                "id": "test-matter-1",
                "title": "Motor Vehicle Accident - Live Test",
                "practice_area": "personal_injury",
                "work_type": "litigation",
                "jurisdiction": "TX",
                "filing_date": "2023-01-15",
                "opened_date": "2023-01-10",
                "metadata": {
                    "incident_date": "2022-12-01",
                    "case_type": "motor_vehicle_accident",
                    "client_name": "Test Client",
                },
            }
        ]

    @pytest.mark.asyncio
    async def test_live_sol_tracking_workflow(self, agent, mock_matters_data):
        """Test complete SOL tracking workflow with live MCP service."""
        state = DeadlineInsightsState(
            tenant_id=TEST_TENANT_ID,
            matter_ids=["test-matter-1"],
            analysis_type=AnalysisType.SOL_TRACKING,
        )

        # Mock the matter data fetching to use our test data
        with patch.object(agent.nodes, "_get_repository") as mock_repo:
            with patch.object(
                agent.nodes, "_fetch_matters_for_sol_tracking"
            ) as mock_fetch:
                with patch(
                    "backend.agents.insights.deadline.mcp_client.MCP_API_KEY_SECRET",
                    TEST_SECRET_NAME,
                ):
                    mock_fetch.return_value = mock_matters_data

                    # Execute SOL tracking with live MCP service
                    result_state = await agent.nodes.track_sol_deadlines(state)

                    # Validate results
                    assert result_state.status in ["sol_tracked", "failed"]

                    if result_state.status == "sol_tracked":
                        assert result_state.analysis_completed is True
                        assert result_state.analysis is not None
                        assert (
                            result_state.analysis.analysis_type
                            == AnalysisType.SOL_TRACKING
                        )

                        print(f"✅ Live SOL tracking workflow completed successfully")
                        print(
                            f"Analysis: {result_state.analysis.total_deadlines} deadlines calculated"
                        )
                        print(
                            f"Recommendations: {len(result_state.analysis.recommendations)}"
                        )

                        # Check for SOL results in metadata
                        if (
                            hasattr(result_state, "metadata")
                            and "sol_results" in result_state.metadata
                        ):
                            sol_results = result_state.metadata["sol_results"]
                            print(
                                f"SOL Results: {len(sol_results)} deadlines processed"
                            )
                    else:
                        print(f"⚠️ SOL tracking failed: {result_state.error}")
                        # Don't fail the test - log the error for investigation

    @pytest.mark.asyncio
    async def test_live_batch_processing(self, agent, mock_matters_data):
        """Test batch processing with live MCP service."""
        # Create multiple test matters for batch processing
        batch_matters = mock_matters_data * 3  # Duplicate for batch testing
        for i, matter in enumerate(batch_matters):
            matter["id"] = f"test-matter-batch-{i+1}"
            matter["title"] = f"Batch Test Matter {i+1}"

        state = DeadlineInsightsState(
            tenant_id=TEST_TENANT_ID,
            analysis_type=AnalysisType.BATCH_ANALYSIS,
            batch_config=BatchProcessingConfig(batch_size=2),
        )

        with patch.object(agent.nodes, "_get_repository") as mock_repo:
            with patch.object(
                agent.nodes, "_fetch_matters_for_sol_tracking"
            ) as mock_fetch:
                with patch(
                    "backend.agents.insights.deadline.mcp_client.MCP_API_KEY_SECRET",
                    TEST_SECRET_NAME,
                ):
                    mock_fetch.return_value = batch_matters

                    # Execute batch processing with live MCP service
                    result_state = await agent.nodes.process_batch_analysis(state)

                    # Validate results
                    assert result_state.status in ["batch_processed", "failed"]

                    if result_state.status == "batch_processed":
                        assert result_state.analysis_completed is True
                        assert result_state.analysis is not None

                        print(f"✅ Live batch processing completed successfully")
                        print(f"Total batches: {result_state.total_batches}")
                        print(
                            f"Analysis: {result_state.analysis.total_deadlines} deadlines calculated"
                        )

                        # Check batch metadata
                        if (
                            hasattr(result_state, "metadata")
                            and "batch_results" in result_state.metadata
                        ):
                            batch_results = result_state.metadata["batch_results"]
                            print(f"Batch Results: {batch_results}")
                    else:
                        print(f"⚠️ Batch processing failed: {result_state.error}")


if __name__ == "__main__":
    # Allow running tests directly for debugging
    if not SKIP_TESTS:
        pytest.main([__file__, "-v", "-s"])
    else:
        print("Live integration tests are skipped - not in CI staging environment")
        print("To run these tests, set CI=true and NODE_ENV=staging")
