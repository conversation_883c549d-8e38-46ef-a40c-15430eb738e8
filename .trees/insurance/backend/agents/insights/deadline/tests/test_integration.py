"""
Integration Tests for Deadline Insights Agent Database Integration

This module contains integration tests that verify the database integration
components work together correctly for Task 2 implementation.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone

from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    ConflictSeverity,
    DeadlineConflict,
)
from backend.agents.insights.deadline.database import DeadlineRepository


class TestDatabaseIntegration:
    """Test database integration functionality."""

    @pytest.fixture
    def agent(self):
        """Create a DeadlineInsightsAgent instance for testing."""
        return DeadlineInsightsAgent()

    @pytest.fixture
    def basic_state(self):
        """Create a basic state for testing."""
        return DeadlineInsightsState(
            tenant_id="test_tenant_123",
            matter_ids=["matter_1", "matter_2"],
            analysis_type=AnalysisType.COMPREHENSIVE,
        )

    @pytest.fixture
    def mock_deadline_data(self):
        """Create mock deadline data."""
        return [
            {
                "id": "deadline_1",
                "description": "File motion to dismiss",
                "date": "2024-07-15",
                "priority": "high",
                "tenant_id": "test_tenant_123",
                "document_id": "matter_1",
                "jurisdiction": "TX",
                "source": "Court Order",
                "legal_basis": "Rule 12(b)(6)",
                "consequences": ["Case dismissal risk"],
            },
            {
                "id": "deadline_2",
                "description": "Discovery deadline",
                "date": "2024-07-15",
                "priority": "critical",
                "tenant_id": "test_tenant_123",
                "document_id": "matter_2",
                "jurisdiction": "TX",
                "source": "Scheduling Order",
                "legal_basis": "Local Rule 16.1",
                "consequences": ["Discovery sanctions"],
            },
            {
                "id": "deadline_3",
                "description": "Summary judgment motion",
                "date": "2024-08-01",
                "priority": "medium",
                "tenant_id": "test_tenant_123",
                "document_id": "matter_1",
                "jurisdiction": "TX",
                "source": "Case Management Order",
                "legal_basis": "Rule 56",
                "consequences": ["Waiver of motion right"],
            },
        ]

    def test_repository_initialization_in_nodes(self, agent):
        """Test that nodes can initialize repository correctly."""
        # Test repository creation
        repo = agent.nodes._get_repository("test_tenant_123")

        assert isinstance(repo, DeadlineRepository)
        assert repo.tenant_id == "test_tenant_123"
        assert repo.use_service_role is True

        # Test repository caching
        repo2 = agent.nodes._get_repository("test_tenant_123")
        assert repo is repo2  # Should be the same instance

        # Test different tenant gets different repository
        repo3 = agent.nodes._get_repository("different_tenant")
        assert repo3 is not repo
        assert repo3.tenant_id == "different_tenant"

    @pytest.mark.asyncio
    async def test_fetch_deadline_data_success(
        self, agent, basic_state, mock_deadline_data
    ):
        """Test successful deadline data fetching."""
        # Mock the repository method
        with patch.object(agent.nodes, "_get_repository") as mock_get_repo:
            mock_repo = Mock()
            mock_repo.get_deadlines_by_matter_ids = AsyncMock(
                return_value=mock_deadline_data
            )
            mock_get_repo.return_value = mock_repo

            # Execute the fetch_deadline_data node
            result_state = await agent.nodes.fetch_deadline_data(basic_state)

            # Verify state was updated correctly
            assert result_state.data_fetched is True
            assert result_state.status == "data_fetched"
            assert len(result_state.deadline_data) == 3
            assert result_state.deadline_data == mock_deadline_data
            assert result_state.cache_misses == 1
            assert result_state.error is None

            # Verify repository was called correctly
            mock_get_repo.assert_called_once_with("test_tenant_123")
            mock_repo.get_deadlines_by_matter_ids.assert_called_once_with(
                matter_ids=["matter_1", "matter_2"],
                limit=basic_state.batch_config.batch_size,
                include_metadata=True,
            )

    @pytest.mark.asyncio
    async def test_fetch_deadline_data_no_tenant_id(self, agent):
        """Test fetch deadline data with missing tenant ID."""
        state = DeadlineInsightsState(
            matter_ids=["matter_1"], analysis_type=AnalysisType.COMPREHENSIVE
        )
        # tenant_id is None

        result_state = await agent.nodes.fetch_deadline_data(state)

        assert result_state.data_fetched is False
        assert result_state.error == "Tenant ID is required for deadline data fetching"
        assert result_state.status != "data_fetched"

    @pytest.mark.asyncio
    async def test_fetch_deadline_data_database_error(self, agent, basic_state):
        """Test fetch deadline data with database error."""
        # Mock the repository to raise an exception
        with patch.object(agent.nodes, "_get_repository") as mock_get_repo:
            mock_repo = Mock()
            mock_repo.get_deadlines_by_matter_ids = AsyncMock(
                side_effect=Exception("Database connection failed")
            )
            mock_get_repo.return_value = mock_repo

            result_state = await agent.nodes.fetch_deadline_data(basic_state)

            assert result_state.data_fetched is False
            assert result_state.status == "failed"
            assert (
                "Failed to fetch deadline data: Database connection failed"
                in result_state.error
            )

    @pytest.mark.asyncio
    async def test_fetch_deadline_data_all_deadlines(self, agent, mock_deadline_data):
        """Test fetching all deadlines when no matter IDs provided."""
        state = DeadlineInsightsState(
            tenant_id="test_tenant_123",
            matter_ids=[],  # Empty matter IDs
            analysis_type=AnalysisType.COMPREHENSIVE,
        )

        # Mock the repository method
        with patch.object(agent.nodes, "_get_repository") as mock_get_repo:
            mock_repo = Mock()
            mock_repo.get_all_deadlines = AsyncMock(return_value=mock_deadline_data)
            mock_get_repo.return_value = mock_repo

            result_state = await agent.nodes.fetch_deadline_data(state)

            assert result_state.data_fetched is True
            assert result_state.status == "data_fetched"
            assert len(result_state.deadline_data) == 3

            # Verify get_all_deadlines was called instead of get_deadlines_by_matter_ids
            mock_repo.get_all_deadlines.assert_called_once_with(
                limit=state.batch_config.batch_size,
                priority_filter=None,
                date_range=None,
            )

    @pytest.mark.asyncio
    async def test_conflict_detection_with_fetched_data(
        self, agent, basic_state, mock_deadline_data
    ):
        """Test conflict detection using fetched deadline data."""
        # Set up state with fetched data
        basic_state.data_fetched = True
        basic_state.deadline_data = mock_deadline_data
        basic_state.analysis_type = AnalysisType.CONFLICT_DETECTION

        result_state = await agent.nodes.detect_conflicts(basic_state)

        # Verify analysis was completed
        assert result_state.analysis_completed is True
        assert result_state.analysis is not None
        assert result_state.analysis.analysis_type == AnalysisType.CONFLICT_DETECTION
        assert result_state.analysis.total_deadlines == 3

        # Verify conflict detection found the date conflict
        # (deadline_1 and deadline_2 both have date '2024-07-15' and high/critical priority)
        assert len(result_state.conflicts) == 1
        conflict = result_state.conflicts[0]
        assert isinstance(conflict, DeadlineConflict)
        assert conflict.severity == ConflictSeverity.HIGH
        assert len(conflict.deadline_ids) == 2
        assert "deadline_1" in conflict.deadline_ids
        assert "deadline_2" in conflict.deadline_ids
        assert "2024-07-15" in conflict.description

    @pytest.mark.asyncio
    async def test_conflict_detection_no_data(self, agent, basic_state):
        """Test conflict detection with no fetched data."""
        # State without fetched data
        basic_state.data_fetched = False
        basic_state.deadline_data = []

        result_state = await agent.nodes.detect_conflicts(basic_state)

        assert result_state.analysis_completed is False
        assert result_state.error == "No deadline data available for conflict detection"

    @pytest.mark.asyncio
    async def test_full_workflow_with_database_integration(
        self, agent, basic_state, mock_deadline_data
    ):
        """Test the full agent workflow with database integration."""
        # Mock the repository for the entire workflow
        with patch.object(agent.nodes, "_get_repository") as mock_get_repo:
            mock_repo = Mock()
            mock_repo.get_deadlines_by_matter_ids = AsyncMock(
                return_value=mock_deadline_data
            )
            mock_get_repo.return_value = mock_repo

            # Initialize the agent
            initialized_state = await agent.initialize(basic_state)
            assert initialized_state.initialized is True
            assert initialized_state.status == "initialized"

            # Execute the agent (which includes data fetching and analysis)
            executed_state = await agent.execute(initialized_state)
            assert executed_state.status == "completed"
            assert executed_state.data_fetched is True
            assert executed_state.analysis_completed is True
            assert len(executed_state.deadline_data) == 3

            # Cleanup
            final_state = await agent.cleanup(executed_state)
            assert final_state.cleanup_completed is True

            # Verify repository was used correctly
            mock_get_repo.assert_called_with("test_tenant_123")
            mock_repo.get_deadlines_by_matter_ids.assert_called_once()

    def test_state_model_deadline_data_field(self):
        """Test that the state model correctly handles deadline_data field."""
        state = DeadlineInsightsState(
            tenant_id="test_tenant",
            deadline_data=[
                {"id": "test_1", "description": "Test deadline"},
                {"id": "test_2", "description": "Another deadline"},
            ],
        )

        assert len(state.deadline_data) == 2
        assert state.deadline_data[0]["id"] == "test_1"
        assert state.deadline_data[1]["description"] == "Another deadline"

        # Test serialization
        state_dict = state.model_dump()
        assert "deadline_data" in state_dict
        assert len(state_dict["deadline_data"]) == 2
