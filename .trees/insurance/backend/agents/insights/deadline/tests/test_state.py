"""
Tests for Deadline Insights Agent State Models

This module contains comprehensive tests for the state models used by the
Deadline Insights Agent, including validation, serialization, and edge cases.
"""

import pytest
from datetime import datetime, timezone
from typing import List, Set
from pydantic import ValidationError

from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    ConflictSeverity,
    DeadlineConflict,
    DeadlineAnalysis,
    BatchProcessingConfig,
    ANALYZE_DEADLINES_FN_SCHEMA,
)


class TestAnalysisType:
    """Test AnalysisType enum."""

    def test_analysis_type_values(self):
        """Test that all analysis types have correct values."""
        assert AnalysisType.CONFLICT_DETECTION == "conflict_detection"
        assert AnalysisType.RISK_ASSESSMENT == "risk_assessment"
        assert AnalysisType.SOL_TRACKING == "sol_tracking"
        assert AnalysisType.BATCH_ANALYSIS == "batch_analysis"
        assert AnalysisType.COMPREHENSIVE == "comprehensive"

    def test_analysis_type_membership(self):
        """Test analysis type membership."""
        valid_types = [
            "conflict_detection",
            "risk_assessment",
            "sol_tracking",
            "batch_analysis",
            "comprehensive",
        ]

        for type_value in valid_types:
            assert type_value in [e.value for e in AnalysisType]


class TestConflictSeverity:
    """Test ConflictSeverity enum."""

    def test_conflict_severity_values(self):
        """Test that all severity levels have correct values."""
        assert ConflictSeverity.LOW == "low"
        assert ConflictSeverity.MEDIUM == "medium"
        assert ConflictSeverity.HIGH == "high"
        assert ConflictSeverity.CRITICAL == "critical"


class TestDeadlineConflict:
    """Test DeadlineConflict model."""

    def test_create_deadline_conflict(self):
        """Test creating a deadline conflict."""
        conflict = DeadlineConflict(
            id="conflict_123",
            deadline_ids=["deadline_1", "deadline_2"],
            severity=ConflictSeverity.HIGH,
            description="Overlapping court dates",
            suggested_resolution="Reschedule one of the hearings",
        )

        assert conflict.id == "conflict_123"
        assert conflict.deadline_ids == ["deadline_1", "deadline_2"]
        assert conflict.severity == ConflictSeverity.HIGH
        assert conflict.description == "Overlapping court dates"
        assert conflict.suggested_resolution == "Reschedule one of the hearings"
        assert isinstance(conflict.detected_at, datetime)

    def test_deadline_conflict_without_resolution(self):
        """Test creating a deadline conflict without suggested resolution."""
        conflict = DeadlineConflict(
            id="conflict_456",
            deadline_ids=["deadline_3"],
            severity=ConflictSeverity.LOW,
            description="Minor scheduling issue",
        )

        assert conflict.suggested_resolution is None

    def test_deadline_conflict_serialization(self):
        """Test deadline conflict JSON serialization."""
        conflict = DeadlineConflict(
            id="conflict_789",
            deadline_ids=["deadline_4", "deadline_5"],
            severity=ConflictSeverity.CRITICAL,
            description="Critical conflict",
        )

        # Test that it can be serialized to dict (datetime objects remain as datetime)
        conflict_dict = conflict.dict()
        assert "detected_at" in conflict_dict
        assert isinstance(
            conflict_dict["detected_at"], datetime
        )  # Raw dict has datetime objects

        # Test that JSON serialization applies encoders correctly
        import json

        conflict_json = conflict.json()
        conflict_from_json = json.loads(conflict_json)
        assert isinstance(
            conflict_from_json["detected_at"], str
        )  # JSON has ISO format strings


class TestDeadlineAnalysis:
    """Test DeadlineAnalysis model."""

    def test_create_deadline_analysis(self):
        """Test creating a deadline analysis."""
        analysis = DeadlineAnalysis(
            analysis_id="analysis_123",
            analysis_type=AnalysisType.CONFLICT_DETECTION,
            tenant_id="tenant_456",
            matter_ids=["matter_1", "matter_2"],
            total_deadlines=10,
            high_risk_deadlines=["deadline_1", "deadline_2"],
            recommendations=[
                "Review high-risk deadlines",
                "Schedule conflict resolution",
            ],
        )

        assert analysis.analysis_id == "analysis_123"
        assert analysis.analysis_type == AnalysisType.CONFLICT_DETECTION
        assert analysis.tenant_id == "tenant_456"
        assert analysis.matter_ids == ["matter_1", "matter_2"]
        assert analysis.total_deadlines == 10
        assert analysis.high_risk_deadlines == ["deadline_1", "deadline_2"]
        assert len(analysis.recommendations) == 2
        assert isinstance(analysis.started_at, datetime)
        assert analysis.completed_at is None

    def test_deadline_analysis_completion(self):
        """Test marking deadline analysis as completed."""
        analysis = DeadlineAnalysis(
            analysis_id="analysis_456",
            analysis_type=AnalysisType.COMPREHENSIVE,
            tenant_id="tenant_789",
        )

        # Mark as completed
        completion_time = datetime.now(timezone.utc)
        analysis.completed_at = completion_time
        analysis.processing_time_ms = 1500.0

        assert analysis.completed_at == completion_time
        assert analysis.processing_time_ms == 1500.0


class TestBatchProcessingConfig:
    """Test BatchProcessingConfig model."""

    def test_default_batch_config(self):
        """Test default batch processing configuration."""
        config = BatchProcessingConfig()

        assert config.batch_size == 50
        assert config.max_concurrent_batches == 3
        assert config.timeout_seconds == 300
        assert config.enable_caching is True
        assert config.cache_ttl_seconds == 600

    def test_custom_batch_config(self):
        """Test custom batch processing configuration."""
        config = BatchProcessingConfig(
            batch_size=100,
            max_concurrent_batches=5,
            timeout_seconds=600,
            enable_caching=False,
            cache_ttl_seconds=1200,
        )

        assert config.batch_size == 100
        assert config.max_concurrent_batches == 5
        assert config.timeout_seconds == 600
        assert config.enable_caching is False
        assert config.cache_ttl_seconds == 1200

    def test_batch_size_validation(self):
        """Test batch size validation."""
        # Valid batch sizes
        config1 = BatchProcessingConfig(batch_size=1)
        assert config1.batch_size == 1

        config2 = BatchProcessingConfig(batch_size=1000)
        assert config2.batch_size == 1000

        # Invalid batch sizes should raise validation error
        with pytest.raises(ValidationError):
            BatchProcessingConfig(batch_size=0)

        with pytest.raises(ValidationError):
            BatchProcessingConfig(batch_size=1001)


class TestDeadlineInsightsState:
    """Test DeadlineInsightsState model."""

    def test_create_minimal_state(self):
        """Test creating state with minimal required fields."""
        state = DeadlineInsightsState()

        # Check defaults
        assert state.analysis_type == AnalysisType.COMPREHENSIVE
        assert isinstance(state.batch_config, BatchProcessingConfig)
        assert state.matter_ids == []
        assert state.deadline_ids == []
        assert state.current_batch == 0
        assert state.total_batches == 0
        assert isinstance(state.processed_deadlines, set)
        assert isinstance(state.failed_deadlines, set)
        assert state.conflicts == []
        assert state.status == "pending"
        assert state.initialized is False
        assert state.data_fetched is False
        assert state.analysis_completed is False
        assert state.cleanup_completed is False

    def test_create_full_state(self):
        """Test creating state with all fields."""
        batch_config = BatchProcessingConfig(batch_size=25)

        state = DeadlineInsightsState(
            tenant_id="tenant_123",
            analysis_type=AnalysisType.CONFLICT_DETECTION,
            batch_config=batch_config,
            matter_ids=["matter_1", "matter_2"],
            deadline_ids=["deadline_1", "deadline_2", "deadline_3"],
            date_range_start=datetime(2025, 1, 1, tzinfo=timezone.utc),
            date_range_end=datetime(2025, 12, 31, tzinfo=timezone.utc),
            current_batch=2,
            total_batches=5,
            processed_deadlines={"deadline_1", "deadline_2"},
            failed_deadlines={"deadline_3"},
            status="processing",
        )

        assert state.tenant_id == "tenant_123"
        assert state.analysis_type == AnalysisType.CONFLICT_DETECTION
        assert state.batch_config.batch_size == 25
        assert state.matter_ids == ["matter_1", "matter_2"]
        assert len(state.deadline_ids) == 3
        assert state.current_batch == 2
        assert state.total_batches == 5
        assert "deadline_1" in state.processed_deadlines
        assert "deadline_3" in state.failed_deadlines
        assert state.status == "processing"

    def test_state_serialization(self):
        """Test state serialization to dict."""
        state = DeadlineInsightsState(
            tenant_id="tenant_456",
            matter_ids=["matter_1"],
            processed_deadlines={"deadline_1", "deadline_2"},
        )

        # Test that dict() returns raw Python objects (sets remain as sets)
        state_dict = state.dict()
        assert isinstance(state_dict["processed_deadlines"], set)
        assert state_dict["processed_deadlines"] == {"deadline_1", "deadline_2"}

        # Test that JSON serialization applies encoders correctly
        import json

        state_json = state.json()
        state_from_json = json.loads(state_json)
        assert isinstance(state_from_json["processed_deadlines"], list)
        assert set(state_from_json["processed_deadlines"]) == {
            "deadline_1",
            "deadline_2",
        }

    def test_state_with_analysis(self):
        """Test state with analysis results."""
        analysis = DeadlineAnalysis(
            analysis_id="analysis_789",
            analysis_type=AnalysisType.RISK_ASSESSMENT,
            tenant_id="tenant_123",
        )

        conflict = DeadlineConflict(
            id="conflict_123",
            deadline_ids=["deadline_1", "deadline_2"],
            severity=ConflictSeverity.HIGH,
            description="Test conflict",
        )

        state = DeadlineInsightsState(analysis=analysis, conflicts=[conflict])

        assert state.analysis.analysis_id == "analysis_789"
        assert len(state.conflicts) == 1
        assert state.conflicts[0].severity == ConflictSeverity.HIGH


class TestFunctionSchema:
    """Test function calling schema."""

    def test_analyze_deadlines_schema(self):
        """Test the analyze deadlines function schema."""
        schema = ANALYZE_DEADLINES_FN_SCHEMA

        assert schema["name"] == "analyze_deadlines"
        assert "description" in schema
        assert "parameters" in schema

        params = schema["parameters"]
        assert params["type"] == "object"
        assert "properties" in params
        assert "required" in params

        # Check required fields
        assert "analysis_type" in params["required"]

        # Check analysis_type enum values
        analysis_type_prop = params["properties"]["analysis_type"]
        assert analysis_type_prop["type"] == "string"
        assert set(analysis_type_prop["enum"]) == {e.value for e in AnalysisType}
