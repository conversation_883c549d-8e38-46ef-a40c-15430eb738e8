"""
Integration Tests for SOL Tracking Workflow

This module contains end-to-end integration tests for the complete SOL tracking
workflow including MCP integration, batch processing, and result generation.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    BatchProcessingConfig,
)


class TestSolTrackingWorkflow:
    """Test complete SOL tracking workflow."""

    @pytest.fixture
    def agent(self):
        """Create DeadlineInsightsAgent instance for testing."""
        return DeadlineInsightsAgent()

    @pytest.fixture
    def sol_state(self):
        """Create SOL tracking state for testing."""
        return DeadlineInsightsState(
            tenant_id="test-tenant-123",
            matter_ids=["matter-1", "matter-2"],
            analysis_type=AnalysisType.SOL_TRACKING,
        )

    @pytest.fixture
    def batch_state(self):
        """Create batch processing state for testing."""
        return DeadlineInsightsState(
            tenant_id="test-tenant-123",
            matter_ids=["matter-1", "matter-2", "matter-3", "matter-4"],
            analysis_type=AnalysisType.BATCH_ANALYSIS,
            batch_config=BatchProcessingConfig(batch_size=2),
        )

    @pytest.fixture
    def mock_mcp_response(self):
        """Create mock MCP response for testing."""
        return {
            "deadlines": [
                {
                    "id": "sol_deadline_1",
                    "name": "Personal Injury Statute of Limitations",
                    "dueDate": "2024-12-01T00:00:00Z",
                    "priority": "high",
                    "category": "statute_of_limitations",
                    "description": "Two-year statute of limitations for personal injury claims",
                    "legalBasis": "Texas Civil Practice and Remedies Code § 16.003",
                    "consequences": "Claim will be time-barred if not filed by deadline",
                }
            ],
            "jurisdiction": "TX",
            "triggerCode": "ACCIDENT_DATE",
            "startDate": "2022-12-01",
            "practiceArea": "personal_injury",
            "calculatedAt": "2023-01-15T10:00:00Z",
        }

    @pytest.fixture
    def mock_matters_data(self):
        """Create mock matters data for testing."""
        return [
            {
                "id": "matter-1",
                "title": "Motor Vehicle Accident - Smith v. Jones",
                "practice_area": "personal_injury",
                "work_type": "litigation",
                "jurisdiction": "TX",
                "filing_date": "2023-01-15",
                "opened_date": "2023-01-10",
                "metadata": {
                    "incident_date": "2022-12-01",
                    "case_type": "motor_vehicle_accident",
                    "client_name": "John Smith",
                },
            },
            {
                "id": "matter-2",
                "title": "Slip and Fall - Johnson v. Store Corp",
                "practice_area": "personal_injury",
                "work_type": "litigation",
                "jurisdiction": "TX",
                "filing_date": "2023-02-01",
                "opened_date": "2023-01-25",
                "metadata": {
                    "incident_date": "2022-11-15",
                    "case_type": "slip_and_fall",
                    "client_name": "Jane Johnson",
                },
            },
        ]

    @pytest.mark.asyncio
    async def test_complete_sol_tracking_workflow(
        self, agent, sol_state, mock_mcp_response, mock_matters_data
    ):
        """Test complete SOL tracking workflow from start to finish."""

        # Mock the MCP client and its methods
        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines.return_value = mock_mcp_response
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None

        # Mock the repository and data fetching
        mock_repository = Mock()

        with patch(
            "backend.agents.insights.deadline.nodes.ENABLE_MCP_INTEGRATION", True
        ):
            with patch(
                "backend.agents.insights.deadline.mcp_client.init_mcp_client",
                new_callable=AsyncMock,
            ) as mock_init:
                with patch.object(
                    agent.nodes, "_get_repository", return_value=mock_repository
                ):
                    with patch.object(
                        agent.nodes,
                        "_fetch_matters_for_sol_tracking",
                        new_callable=AsyncMock,
                    ) as mock_fetch:
                        mock_init.return_value = mock_mcp_client
                        mock_fetch.return_value = mock_matters_data

                        # Execute SOL tracking
                        result_state = await agent.nodes.track_sol_deadlines(sol_state)

                        # Verify successful execution
                        assert result_state.status == "sol_tracked"
                        assert result_state.analysis_completed is True
                        assert result_state.error is None

                        # Verify analysis results
                        analysis = result_state.analysis
                        assert analysis is not None
                        assert analysis.analysis_type == AnalysisType.SOL_TRACKING
                        assert analysis.tenant_id == "test-tenant-123"
                        assert analysis.total_deadlines == 2  # One deadline per matter
                        assert len(analysis.recommendations) > 0

                        # Verify SOL results in metadata
                        assert hasattr(result_state, "metadata")
                        assert "sol_results" in result_state.metadata
                        sol_results = result_state.metadata["sol_results"]
                        assert len(sol_results) == 2

                        # Verify deadline structure
                        deadline = sol_results[0]
                        assert deadline["matter_id"] == "matter-1"
                        assert (
                            deadline["title"]
                            == "Personal Injury Statute of Limitations"
                        )
                        assert deadline["source"] == "mcp_rules_engine"
                        assert deadline["jurisdiction"] == "TX"

                        # Verify MCP client was called correctly
                        assert mock_mcp_client.calculate_deadlines.call_count == 2
                        mock_mcp_client.calculate_deadlines.assert_any_call(
                            jurisdiction="TX",
                            trigger_code="ACCIDENT_DATE",
                            start_date="2022-12-01",
                            practice_area="personal_injury",
                        )

    @pytest.mark.asyncio
    async def test_batch_processing_workflow(
        self, agent, batch_state, mock_mcp_response, mock_matters_data
    ):
        """Test complete batch processing workflow."""

        # Extend mock data for batch testing
        extended_matters = mock_matters_data + [
            {
                "id": "matter-3",
                "title": "Medical Malpractice Case",
                "practice_area": "personal_injury",
                "work_type": "litigation",
                "jurisdiction": "TX",
                "metadata": {
                    "incident_date": "2022-10-01",
                    "case_type": "medical_malpractice",
                },
            },
            {
                "id": "matter-4",
                "title": "Product Liability Case",
                "practice_area": "personal_injury",
                "work_type": "litigation",
                "jurisdiction": "CA",
                "metadata": {
                    "incident_date": "2022-09-01",
                    "case_type": "product_liability",
                },
            },
        ]

        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines.return_value = mock_mcp_response
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None

        mock_repository = Mock()

        with patch(
            "backend.agents.insights.deadline.nodes.ENABLE_MCP_INTEGRATION", True
        ):
            with patch(
                "backend.agents.insights.deadline.mcp_client.init_mcp_client",
                new_callable=AsyncMock,
            ) as mock_init:
                with patch.object(
                    agent.nodes, "_get_repository", return_value=mock_repository
                ):
                    with patch.object(
                        agent.nodes,
                        "_fetch_matters_for_sol_tracking",
                        new_callable=AsyncMock,
                    ) as mock_fetch:
                        mock_init.return_value = mock_mcp_client
                        mock_fetch.return_value = extended_matters

                        # Execute batch processing
                        result_state = await agent.nodes.process_batch_analysis(
                            batch_state
                        )

                        # Verify successful execution
                        assert result_state.status == "batch_processed"
                        assert result_state.analysis_completed is True
                        assert result_state.error is None

                        # Verify batch configuration
                        assert (
                            result_state.total_batches == 2
                        )  # 4 matters / 2 batch_size
                        assert result_state.current_batch == 2  # Completed both batches

                        # Verify analysis results
                        analysis = result_state.analysis
                        assert analysis is not None
                        assert analysis.analysis_type == AnalysisType.BATCH_ANALYSIS
                        assert analysis.total_deadlines == 4  # One deadline per matter

                        # Verify batch metadata
                        assert "batch_results" in result_state.metadata
                        batch_results = result_state.metadata["batch_results"]
                        assert batch_results["total_matters_processed"] == 4
                        assert batch_results["total_deadlines_calculated"] == 4
                        assert batch_results["batches_completed"] == 2

                        # Verify MCP client was called for each matter
                        assert mock_mcp_client.calculate_deadlines.call_count == 4

    @pytest.mark.asyncio
    async def test_workflow_with_high_risk_deadlines(
        self, agent, sol_state, mock_matters_data
    ):
        """Test workflow with high-risk deadlines detection."""

        # Create mock response with high-risk deadline (within 90 days)
        high_risk_date = (datetime.now() + timedelta(days=30)).isoformat()
        high_risk_response = {
            "deadlines": [
                {
                    "id": "urgent_deadline",
                    "name": "Urgent SOL Deadline",
                    "dueDate": high_risk_date,
                    "priority": "critical",
                    "category": "statute_of_limitations",
                }
            ],
            "jurisdiction": "TX",
            "triggerCode": "ACCIDENT_DATE",
        }

        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines.return_value = high_risk_response
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None

        with patch(
            "backend.agents.insights.deadline.nodes.ENABLE_MCP_INTEGRATION", True
        ):
            with patch(
                "backend.agents.insights.deadline.mcp_client.init_mcp_client",
                new_callable=AsyncMock,
            ) as mock_init:
                with patch.object(agent.nodes, "_get_repository"):
                    with patch.object(
                        agent.nodes,
                        "_fetch_matters_for_sol_tracking",
                        new_callable=AsyncMock,
                    ) as mock_fetch:
                        mock_init.return_value = mock_mcp_client
                        mock_fetch.return_value = mock_matters_data

                        result_state = await agent.nodes.track_sol_deadlines(sol_state)

                        # Verify high-risk deadlines were detected
                        analysis = result_state.analysis
                        assert len(analysis.high_risk_deadlines) > 0

                        # Verify urgent recommendations
                        recommendations = analysis.recommendations
                        assert any("URGENT" in rec for rec in recommendations)
                        assert any(
                            "approaching within 90 days" in rec
                            for rec in recommendations
                        )

    @pytest.mark.asyncio
    async def test_workflow_error_handling(self, agent, sol_state, mock_matters_data):
        """Test workflow error handling and recovery."""

        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines.side_effect = Exception(
            "MCP service unavailable"
        )
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None

        with patch(
            "backend.agents.insights.deadline.nodes.ENABLE_MCP_INTEGRATION", True
        ):
            with patch(
                "backend.agents.insights.deadline.mcp_client.init_mcp_client",
                new_callable=AsyncMock,
            ) as mock_init:
                with patch.object(agent.nodes, "_get_repository"):
                    with patch.object(
                        agent.nodes,
                        "_fetch_matters_for_sol_tracking",
                        new_callable=AsyncMock,
                    ) as mock_fetch:
                        mock_init.return_value = mock_mcp_client
                        mock_fetch.return_value = mock_matters_data

                        result_state = await agent.nodes.track_sol_deadlines(sol_state)

                        # Verify error was handled gracefully
                        assert result_state.status == "failed"
                        assert result_state.error is not None
                        assert "Failed to track SOL deadlines" in result_state.error
