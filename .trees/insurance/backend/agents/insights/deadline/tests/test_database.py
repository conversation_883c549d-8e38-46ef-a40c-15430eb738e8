"""
Tests for Deadline Insights Agent Database Integration

This module contains comprehensive tests for the database integration components
of the Deadline Insights Agent, including repository operations and data fetching.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone

from backend.agents.insights.deadline.database import DeadlineRepository


class TestDeadlineRepository:
    """Test DeadlineRepository class."""

    @pytest.fixture
    def repository(self):
        """Create a DeadlineRepository instance for testing."""
        return DeadlineRepository(tenant_id="test_tenant_123")

    @pytest.fixture
    def mock_supabase_client(self):
        """Create a mock Supabase client."""
        mock_client = Mock()
        mock_schema = Mock()
        mock_table = Mock()
        mock_query = Mock()

        # Set up the chain: client.schema().from_().select()...
        mock_client.schema.return_value = mock_schema
        mock_schema.from_.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        mock_query.in_.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.order.return_value = mock_query
        mock_query.gte.return_value = mock_query
        mock_query.lte.return_value = mock_query
        mock_query.single.return_value = mock_query

        return mock_client, mock_query

    def test_repository_initialization(self, repository):
        """Test repository initialization."""
        assert repository.tenant_id == "test_tenant_123"
        assert repository.use_service_role is True
        assert repository._client is None

    @patch(
        "backend.agents.insights.deadline.database.deadline_repository.create_client"
    )
    def test_client_property(self, mock_create_client, repository):
        """Test client property initialization."""
        mock_client = Mock()
        mock_create_client.return_value = mock_client

        # Mock environment variables
        with patch.dict(
            "os.environ",
            {
                "SUPABASE_URL": "https://test.supabase.co",
                "SUPABASE_SERVICE_KEY": "test-service-key",
            },
        ):
            client = repository.client

            assert client == mock_client
            mock_create_client.assert_called_once_with(
                "https://test.supabase.co", "test-service-key"
            )

    @pytest.mark.asyncio
    async def test_get_deadlines_by_matter_ids_success(
        self, repository, mock_supabase_client
    ):
        """Test successful deadline fetching by matter IDs."""
        mock_client, mock_query = mock_supabase_client

        # Mock successful response
        mock_result = Mock()
        mock_result.data = [
            {
                "id": "deadline_1",
                "description": "Test deadline 1",
                "priority": "high",
                "tenant_id": "test_tenant_123",
            },
            {
                "id": "deadline_2",
                "description": "Test deadline 2",
                "priority": "medium",
                "tenant_id": "test_tenant_123",
            },
        ]
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadlines_by_matter_ids(
                ["matter_1", "matter_2"]
            )

            assert len(result) == 2
            assert result[0]["id"] == "deadline_1"
            assert result[1]["id"] == "deadline_2"

            # Verify query was built correctly
            mock_client.schema.assert_called_with("tenants")
            mock_query.eq.assert_any_call("tenant_id", "test_tenant_123")
            mock_query.in_.assert_called_with("document_id", ["matter_1", "matter_2"])

    @pytest.mark.asyncio
    async def test_get_deadlines_by_matter_ids_empty_result(
        self, repository, mock_supabase_client
    ):
        """Test deadline fetching with empty result."""
        mock_client, mock_query = mock_supabase_client

        # Mock empty response
        mock_result = Mock()
        mock_result.data = None
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadlines_by_matter_ids(["matter_1"])

            assert result == []

    @pytest.mark.asyncio
    async def test_get_deadlines_by_matter_ids_error(
        self, repository, mock_supabase_client
    ):
        """Test deadline fetching with database error."""
        mock_client, mock_query = mock_supabase_client

        # Mock database error
        mock_query.execute.side_effect = Exception("Database connection failed")

        with patch.object(repository, "client", mock_client):
            with pytest.raises(Exception, match="Database connection failed"):
                await repository.get_deadlines_by_matter_ids(["matter_1"])

    @pytest.mark.asyncio
    async def test_get_all_deadlines_with_filters(
        self, repository, mock_supabase_client
    ):
        """Test getting all deadlines with filters."""
        mock_client, mock_query = mock_supabase_client

        # Mock successful response
        mock_result = Mock()
        mock_result.data = [{"id": "deadline_1", "priority": "high"}]
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_all_deadlines(
                limit=10,
                priority_filter=["high", "critical"],
                date_range={"start": "2024-01-01", "end": "2024-12-31"},
            )

            assert len(result) == 1
            assert result[0]["id"] == "deadline_1"

            # Verify filters were applied
            mock_query.in_.assert_called_with("priority", ["high", "critical"])
            mock_query.gte.assert_called_with("date", "2024-01-01")
            mock_query.lte.assert_called_with("date", "2024-12-31")
            mock_query.limit.assert_called_with(10)

    @pytest.mark.asyncio
    async def test_get_deadline_by_id_success(self, repository, mock_supabase_client):
        """Test successful deadline fetching by ID."""
        mock_client, mock_query = mock_supabase_client

        # Mock successful response
        mock_result = Mock()
        mock_result.data = {
            "id": "deadline_1",
            "description": "Test deadline",
            "tenant_id": "test_tenant_123",
        }
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadline_by_id("deadline_1")

            assert result["id"] == "deadline_1"
            assert result["description"] == "Test deadline"

            # Verify single record query
            mock_query.single.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_deadline_by_id_not_found(self, repository, mock_supabase_client):
        """Test deadline fetching by ID when not found."""
        mock_client, mock_query = mock_supabase_client

        # Mock not found response
        mock_result = Mock()
        mock_result.data = None
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadline_by_id("nonexistent_id")

            assert result is None

    @pytest.mark.asyncio
    async def test_get_deadlines_count(self, repository, mock_supabase_client):
        """Test getting deadlines count."""
        mock_client, mock_query = mock_supabase_client

        # Mock count response
        mock_result = Mock()
        mock_result.count = 42
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadlines_count()

            assert result == 42

            # Verify count query - need to access the table mock, not the query mock
            mock_schema = mock_client.schema.return_value
            mock_table = mock_schema.from_.return_value
            mock_table.select.assert_called_with("id", count="exact")

    @pytest.mark.asyncio
    async def test_get_deadlines_by_priority(self, repository, mock_supabase_client):
        """Test getting deadlines by priority."""
        mock_client, mock_query = mock_supabase_client

        # Mock priority response
        mock_result = Mock()
        mock_result.data = [
            {"id": "deadline_1", "priority": "critical"},
            {"id": "deadline_2", "priority": "critical"},
        ]
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadlines_by_priority("critical")

            assert len(result) == 2
            assert all(d["priority"] == "critical" for d in result)

            # Verify priority filter
            mock_query.eq.assert_any_call("priority", "critical")

    @pytest.mark.asyncio
    async def test_get_deadlines_by_date_range(self, repository, mock_supabase_client):
        """Test getting deadlines by date range."""
        mock_client, mock_query = mock_supabase_client

        # Mock date range response
        mock_result = Mock()
        mock_result.data = [{"id": "deadline_1", "date": "2024-06-15"}]
        mock_query.execute.return_value = mock_result

        with patch.object(repository, "client", mock_client):
            result = await repository.get_deadlines_by_date_range(
                "2024-01-01", "2024-12-31"
            )

            assert len(result) == 1
            assert result[0]["date"] == "2024-06-15"

            # Verify date range filters
            mock_query.gte.assert_called_with("date", "2024-01-01")
            mock_query.lte.assert_called_with("date", "2024-12-31")

    @pytest.mark.asyncio
    async def test_get_deadlines_statistics(self, repository):
        """Test getting deadline statistics."""
        # Mock the individual methods that statistics calls
        with patch.object(repository, "get_deadlines_count", return_value=10):
            with patch.object(
                repository, "get_deadlines_by_priority"
            ) as mock_by_priority:
                # Mock priority counts
                mock_by_priority.side_effect = [
                    [{"id": "1"}, {"id": "2"}],  # critical: 2
                    [{"id": "3"}],  # high: 1
                    [{"id": "4"}, {"id": "5"}, {"id": "6"}],  # medium: 3
                    [{"id": "7"}, {"id": "8"}, {"id": "9"}, {"id": "10"}],  # low: 4
                ]

                result = await repository.get_deadlines_statistics()

                assert result["total_deadlines"] == 10
                assert result["priority_breakdown"]["critical"] == 2
                assert result["priority_breakdown"]["high"] == 1
                assert result["priority_breakdown"]["medium"] == 3
                assert result["priority_breakdown"]["low"] == 4
                assert result["tenant_id"] == "test_tenant_123"
                assert "generated_at" in result
