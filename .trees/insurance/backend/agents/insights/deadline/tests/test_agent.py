"""
Tests for Deadline Insights Agent

This module contains comprehensive tests for the DeadlineInsightsAgent class,
including lifecycle management, workflow execution, and error handling.
"""

import pytest
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone

from backend.agents.insights.deadline.agent import DeadlineInsightsAgent
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    DeadlineAnalysis,
    BatchProcessingConfig,
)


class TestDeadlineInsightsAgent:
    """Test DeadlineInsightsAgent class."""

    @pytest.fixture
    def agent(self):
        """Create a DeadlineInsightsAgent instance for testing."""
        return DeadlineInsightsAgent()

    @pytest.fixture
    def basic_state(self):
        """Create a basic state for testing."""
        return DeadlineInsightsState(
            tenant_id="test_tenant_123",
            matter_ids=["matter_1", "matter_2"],
            analysis_type=AnalysisType.COMPREHENSIVE,
        )

    def test_agent_initialization(self, agent):
        """Test agent initialization with default config."""
        assert agent.config.name == "deadline_insights_agent"
        assert agent.config.agent_type == "insights"
        assert agent.config.description.startswith("Deadline insights agent")
        assert agent.config.version == "1.0.0"
        assert hasattr(agent, "nodes")
        assert agent.enabled is True  # Default value
        assert agent.batch_size == 50  # Default value
        assert agent.cache_ttl == 600  # Default value

    def test_agent_initialization_with_custom_config(self):
        """Test agent initialization with custom config."""
        from shared.core.base_agent import AgentConfig

        custom_config = AgentConfig(
            name="custom_deadline_agent",
            description="Custom deadline agent",
            version="2.0.0",
        )

        agent = DeadlineInsightsAgent(config=custom_config)
        assert agent.config.name == "custom_deadline_agent"
        assert agent.config.version == "2.0.0"

    @patch.dict(
        os.environ,
        {
            "DEADLINE_INSIGHTS_ENABLED": "false",
            "DEADLINE_BATCH_SIZE": "100",
            "DEADLINE_ANALYSIS_CACHE_TTL": "1200",
        },
    )
    def test_agent_initialization_with_env_vars(self):
        """Test agent initialization with environment variables."""
        agent = DeadlineInsightsAgent()
        assert agent.enabled is False
        assert agent.batch_size == 100
        assert agent.cache_ttl == 1200

    @pytest.mark.asyncio
    async def test_initialize_success(self, agent, basic_state):
        """Test successful agent initialization."""
        result = await agent.initialize(basic_state)

        assert result.initialized is True
        assert result.status == "initialized"
        assert result.error is None
        assert result.processing_start_time is not None
        assert result.batch_config.batch_size == agent.batch_size
        assert result.batch_config.cache_ttl_seconds == agent.cache_ttl

    @pytest.mark.asyncio
    async def test_initialize_disabled_agent(self, basic_state):
        """Test initialization when agent is disabled."""
        with patch.dict(os.environ, {"DEADLINE_INSIGHTS_ENABLED": "false"}):
            agent = DeadlineInsightsAgent()
            result = await agent.initialize(basic_state)

            assert result.status == "disabled"
            assert result.error == "Deadline insights agent is disabled"
            assert result.initialized is False

    @pytest.mark.asyncio
    async def test_initialize_missing_tenant_id(self, agent):
        """Test initialization with missing tenant ID."""
        state = DeadlineInsightsState()  # No tenant_id
        result = await agent.initialize(state)

        assert result.status == "failed"
        assert "Tenant ID is required" in result.error
        assert result.initialized is False

    @pytest.mark.asyncio
    async def test_initialize_exception_handling(self, agent, basic_state):
        """Test initialization exception handling."""
        # Mock datetime.now to raise an exception during initialization
        with patch("backend.agents.insights.deadline.agent.datetime") as mock_datetime:
            mock_datetime.now.side_effect = Exception("Test exception")

            result = await agent.initialize(basic_state)

            assert result.status == "failed"
            assert "Test exception" in result.error
            assert result.initialized is False

    @pytest.mark.asyncio
    async def test_execute_conflict_detection(self, agent, basic_state):
        """Test execute with conflict detection analysis."""
        basic_state.analysis_type = AnalysisType.CONFLICT_DETECTION
        basic_state.initialized = True

        # Mock the nodes
        agent.nodes.fetch_deadline_data = AsyncMock(return_value=basic_state)
        agent.nodes.detect_conflicts = AsyncMock(return_value=basic_state)

        # Set up state for execution (not yet completed)
        basic_state.data_fetched = False  # Will trigger fetch_deadline_data
        basic_state.analysis_completed = False  # Will trigger detect_conflicts
        basic_state.processing_start_time = datetime.now(timezone.utc)

        # Configure mocks to update state appropriately
        def mock_fetch_data(state, config=None):
            state.data_fetched = True
            return state

        def mock_detect_conflicts(state, config=None):
            state.analysis_completed = True
            return state

        agent.nodes.fetch_deadline_data = AsyncMock(side_effect=mock_fetch_data)
        agent.nodes.detect_conflicts = AsyncMock(side_effect=mock_detect_conflicts)

        result = await agent.execute(basic_state)

        assert result.status == "completed"
        assert result.processing_end_time is not None
        assert result.processing_time_ms is not None

        # Verify nodes were called
        agent.nodes.fetch_deadline_data.assert_called_once()
        agent.nodes.detect_conflicts.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_risk_assessment(self, agent, basic_state):
        """Test execute with risk assessment analysis."""
        basic_state.analysis_type = AnalysisType.RISK_ASSESSMENT
        basic_state.initialized = True
        basic_state.data_fetched = True  # Skip data fetching
        basic_state.analysis_completed = False  # Will trigger assess_risks
        basic_state.processing_start_time = datetime.now(timezone.utc)

        # Configure mock to update state appropriately
        def mock_assess_risks(state, config=None):
            state.analysis_completed = True
            return state

        agent.nodes.assess_risks = AsyncMock(side_effect=mock_assess_risks)

        result = await agent.execute(basic_state)

        assert result.status == "completed"
        agent.nodes.assess_risks.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_sol_tracking(self, agent, basic_state):
        """Test execute with SOL tracking analysis."""
        basic_state.analysis_type = AnalysisType.SOL_TRACKING
        basic_state.initialized = True
        basic_state.data_fetched = True  # Skip data fetching
        basic_state.analysis_completed = False  # Will trigger track_sol_deadlines
        basic_state.processing_start_time = datetime.now(timezone.utc)

        # Configure mock to update state appropriately
        def mock_track_sol(state, config=None):
            state.analysis_completed = True
            return state

        agent.nodes.track_sol_deadlines = AsyncMock(side_effect=mock_track_sol)

        result = await agent.execute(basic_state)

        assert result.status == "completed"
        agent.nodes.track_sol_deadlines.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_batch_analysis(self, agent, basic_state):
        """Test execute with batch analysis."""
        basic_state.analysis_type = AnalysisType.BATCH_ANALYSIS
        basic_state.initialized = True
        basic_state.data_fetched = True  # Skip data fetching
        basic_state.analysis_completed = False  # Will trigger process_batch_analysis
        basic_state.processing_start_time = datetime.now(timezone.utc)

        # Configure mock to update state appropriately
        def mock_batch_analysis(state, config=None):
            state.analysis_completed = True
            return state

        agent.nodes.process_batch_analysis = AsyncMock(side_effect=mock_batch_analysis)

        result = await agent.execute(basic_state)

        assert result.status == "completed"
        agent.nodes.process_batch_analysis.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_comprehensive_analysis(self, agent, basic_state):
        """Test execute with comprehensive analysis."""
        basic_state.analysis_type = AnalysisType.COMPREHENSIVE
        basic_state.initialized = True
        basic_state.data_fetched = True  # Skip data fetching
        basic_state.analysis_completed = False  # Will trigger comprehensive_analysis
        basic_state.processing_start_time = datetime.now(timezone.utc)

        # Configure mock to update state appropriately
        def mock_comprehensive(state, config=None):
            state.analysis_completed = True
            return state

        agent.nodes.comprehensive_analysis = AsyncMock(side_effect=mock_comprehensive)

        result = await agent.execute(basic_state)

        assert result.status == "completed"
        agent.nodes.comprehensive_analysis.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_with_error(self, agent, basic_state):
        """Test execute with error handling."""
        basic_state.initialized = True

        # Mock the nodes to raise an exception
        agent.nodes.fetch_deadline_data = AsyncMock(side_effect=Exception("Test error"))

        result = await agent.execute(basic_state)

        assert result.status == "failed"
        assert "Test error" in result.error

    @pytest.mark.asyncio
    async def test_cleanup_success(self, agent, basic_state):
        """Test successful cleanup."""
        # Set up state with analysis results
        analysis = DeadlineAnalysis(
            analysis_id="test_analysis",
            analysis_type=AnalysisType.COMPREHENSIVE,
            tenant_id="test_tenant",
            total_deadlines=10,
        )
        basic_state.analysis = analysis
        basic_state.processing_time_ms = 1500.0
        basic_state.cache_hits = 5
        basic_state.cache_misses = 2

        result = await agent.cleanup(basic_state)

        assert result.cleanup_completed is True

    @pytest.mark.asyncio
    async def test_cleanup_with_exception(self, agent, basic_state):
        """Test cleanup with exception handling."""
        # This should not fail the entire workflow
        with patch("backend.agents.insights.deadline.agent.logger") as mock_logger:
            mock_logger.info.side_effect = Exception("Cleanup error")

            result = await agent.cleanup(basic_state)

            # Cleanup should handle errors gracefully and not raise exceptions
            # The cleanup_completed flag may be False due to the exception, but the method should return
            assert result is not None
            assert isinstance(result, type(basic_state))

    def test_create_graph_with_langgraph(self, agent):
        """Test graph creation with LangGraph available."""
        graph = agent.create_graph()

        # Should return a compiled graph or fallback
        assert graph is not None
        assert hasattr(graph, "ainvoke") or hasattr(graph, "invoke")

    def test_create_fallback_graph(self, agent):
        """Test fallback graph creation."""
        fallback_graph = agent._create_fallback_graph()

        assert fallback_graph is not None
        assert hasattr(fallback_graph, "ainvoke")
        assert hasattr(fallback_graph, "invoke")

    @pytest.mark.asyncio
    async def test_fallback_graph_execution(self, agent, basic_state):
        """Test fallback graph execution."""
        fallback_graph = agent._create_fallback_graph()

        # Mock the agent methods
        agent.initialize = AsyncMock(return_value=basic_state)
        agent.execute = AsyncMock(return_value=basic_state)
        agent.cleanup = AsyncMock(return_value=basic_state)

        result = await fallback_graph.ainvoke(basic_state)

        assert result is not None
        agent.initialize.assert_called_once()
        agent.execute.assert_called_once()
        agent.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_fallback_graph_with_dict_state(self, agent):
        """Test fallback graph with dictionary state input."""
        fallback_graph = agent._create_fallback_graph()

        # Mock the agent methods
        agent.initialize = AsyncMock(return_value=DeadlineInsightsState())
        agent.execute = AsyncMock(return_value=DeadlineInsightsState())
        agent.cleanup = AsyncMock(return_value=DeadlineInsightsState())

        # Test with dict input
        dict_state = {
            "tenant_id": "test_tenant",
            "matter_ids": ["matter_1"],
            "analysis_type": "comprehensive",
        }

        result = await fallback_graph.ainvoke(dict_state)

        assert result is not None
        agent.initialize.assert_called_once()
        agent.execute.assert_called_once()
        agent.cleanup.assert_called_once()


class TestAgentIntegration:
    """Test agent integration scenarios."""

    @pytest.mark.asyncio
    async def test_full_workflow_execution(self):
        """Test full workflow execution from start to finish."""
        agent = DeadlineInsightsAgent()

        state = DeadlineInsightsState(
            tenant_id="integration_test_tenant",
            matter_ids=["matter_1", "matter_2"],
            analysis_type=AnalysisType.COMPREHENSIVE,
        )

        # Execute full workflow
        state = await agent.initialize(state)
        assert state.initialized is True

        state = await agent.execute(state)
        # Note: Since we don't have real data, the execute will complete with placeholder results

        state = await agent.cleanup(state)
        assert state.cleanup_completed is True

    @pytest.mark.asyncio
    async def test_workflow_with_graph(self):
        """Test workflow execution using the graph interface."""
        agent = DeadlineInsightsAgent()
        graph = agent.create_graph()

        initial_state = DeadlineInsightsState(
            tenant_id="graph_test_tenant",
            matter_ids=["matter_1"],
            analysis_type=AnalysisType.CONFLICT_DETECTION,
        )

        # Execute using graph
        result = await graph.ainvoke(initial_state)

        assert result is not None
        # The exact state depends on the implementation, but it should complete
