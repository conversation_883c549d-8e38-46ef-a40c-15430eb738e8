"""
Template Repository for Document Agent

This module provides unified access to both global templates (public.legal_templates)
and tenant-specific templates (tenants.authored_document_templates) with proper
priority handling and tenant isolation.

Key Features:
- Dual-template architecture support
- Template priority handling (tenant > global)
- Proper RLS compliance and tenant isolation
- Support for different content formats (JSONB vs TEXT)
- Advanced filtering and search capabilities

Usage:
    from backend.agents.insights.document.database import TemplateRepository

    repo = TemplateRepository(tenant_id="tenant-123")
    template = await repo.get_best_template("demand_letter", "personal_injury")
"""

import logging
from typing import Any, Dict, List, Optional, Union
from uuid import UUID
import os
from supabase import create_client, Client

from ..state import TemplateInfo, TemplateSource, DocumentType, PracticeArea

logger = logging.getLogger(__name__)


class TemplateRepository:
    """
    Repository for accessing both global and tenant-specific templates.

    Provides unified access to templates with proper priority handling,
    tenant isolation, and support for different content formats.
    """

    def __init__(self, tenant_id: str, use_service_role: bool = True):
        """
        Initialize the template repository.

        Args:
            tenant_id: Tenant ID for isolation
            use_service_role: Whether to use service role credentials
        """
        self.tenant_id = tenant_id
        self.use_service_role = use_service_role
        self._client: Optional[Client] = None

    @property
    def client(self) -> Client:
        """Get the Supabase client."""
        if self._client is None:
            supabase_url = os.getenv("SUPABASE_URL")
            if self.use_service_role:
                supabase_key = os.getenv(
                    "SUPABASE_SERVICE_KEY", os.getenv("SUPABASE_KEY")
                )
            else:
                supabase_key = os.getenv("SUPABASE_ANON_KEY")

            if not supabase_url or not supabase_key:
                raise ValueError("Missing Supabase configuration")

            self._client = create_client(supabase_url, supabase_key)

        return self._client

    async def get_best_template(
        self,
        document_type: Union[str, DocumentType],
        practice_area: Union[str, PracticeArea],
        state: Optional[str] = None,
        template_id: Optional[str] = None,
    ) -> Optional[TemplateInfo]:
        """
        Get the best template based on priority: tenant > global > dynamic.

        Args:
            document_type: Type of document
            practice_area: Practice area
            state: State jurisdiction (optional)
            template_id: Specific template ID (optional)

        Returns:
            Best available template or None
        """
        logger.info(f"Finding best template for {document_type} in {practice_area}")

        # Convert enums to strings if needed
        doc_type_str = (
            document_type.value
            if isinstance(document_type, DocumentType)
            else document_type
        )
        practice_area_str = (
            practice_area.value
            if isinstance(practice_area, PracticeArea)
            else practice_area
        )

        # If specific template ID provided, try to get it
        if template_id:
            template = await self.get_template_by_id(template_id)
            if template:
                return template

        # Try tenant-specific template first
        tenant_template = await self._get_tenant_template(
            doc_type_str, practice_area_str, state
        )
        if tenant_template:
            return tenant_template

        # Fall back to global template
        global_template = await self._get_global_template(
            doc_type_str, practice_area_str, state
        )
        if global_template:
            return global_template

        logger.warning(f"No template found for {doc_type_str} in {practice_area_str}")
        return None

    async def get_template_by_id(self, template_id: str) -> Optional[TemplateInfo]:
        """
        Get a specific template by ID from either source.

        Args:
            template_id: Template ID

        Returns:
            Template if found, None otherwise
        """
        # Try tenant template first
        tenant_template = await self._get_tenant_template_by_id(template_id)
        if tenant_template:
            return tenant_template

        # Try global template
        global_template = await self._get_global_template_by_id(template_id)
        if global_template:
            return global_template

        return None

    async def _get_tenant_template(
        self, document_type: str, practice_area: str, state: Optional[str] = None
    ) -> Optional[TemplateInfo]:
        """Get tenant-specific template."""
        try:
            query = (
                self.client.table("authored_document_templates")
                .select("*")
                .eq("tenant_id", self.tenant_id)
                .eq("category", document_type)
                .eq("is_active", True)
            )

            response = query.execute()

            if response.data:
                # Take the first active template
                template_data = response.data[0]
                return self._map_tenant_template(template_data)

        except Exception as e:
            logger.error(f"Error fetching tenant template: {e}")

        return None

    async def _get_global_template(
        self, document_type: str, practice_area: str, state: Optional[str] = None
    ) -> Optional[TemplateInfo]:
        """Get global template."""
        try:
            query = (
                self.client.table("legal_templates")
                .select("*")
                .eq("document_type", document_type)
                .eq("practice_area", practice_area)
                .eq("is_active", True)
            )

            if state:
                query = query.eq("state", state)

            response = query.execute()

            if response.data:
                # Take the first active template
                template_data = response.data[0]
                return self._map_global_template(template_data)

        except Exception as e:
            logger.error(f"Error fetching global template: {e}")

        return None

    async def _get_tenant_template_by_id(
        self, template_id: str
    ) -> Optional[TemplateInfo]:
        """Get tenant template by ID."""
        try:
            response = (
                self.client.table("authored_document_templates")
                .select("*")
                .eq("id", template_id)
                .eq("tenant_id", self.tenant_id)
                .single()
                .execute()
            )

            if response.data:
                return self._map_tenant_template(response.data)

        except Exception as e:
            logger.error(f"Error fetching tenant template by ID: {e}")

        return None

    async def _get_global_template_by_id(
        self, template_id: str
    ) -> Optional[TemplateInfo]:
        """Get global template by ID."""
        try:
            response = (
                self.client.table("legal_templates")
                .select("*")
                .eq("id", template_id)
                .single()
                .execute()
            )

            if response.data:
                return self._map_global_template(response.data)

        except Exception as e:
            logger.error(f"Error fetching global template by ID: {e}")

        return None

    def _map_tenant_template(self, data: Dict[str, Any]) -> TemplateInfo:
        """Map tenant template data to TemplateInfo."""
        return TemplateInfo(
            id=data["id"],
            name=data["title"],
            source=TemplateSource.TENANT,
            content=data["content"],  # TEXT format
            variables=data.get("variables", {}),
            metadata=data.get("metadata", {}),
        )

    def _map_global_template(self, data: Dict[str, Any]) -> TemplateInfo:
        """Map global template data to TemplateInfo."""
        return TemplateInfo(
            id=data["id"],
            name=data["name"],
            source=TemplateSource.GLOBAL,
            content=data["content"],  # JSONB format
            variables=data.get("variables", {}),
            metadata={
                "practice_area": data.get("practice_area"),
                "state": data.get("state"),
                "category": data.get("category"),
                "subcategory": data.get("subcategory"),
                "template_engine": data.get("template_engine"),
                "allows_conditional_blocks": data.get("allows_conditional_blocks"),
                "allows_loops": data.get("allows_loops"),
            },
        )

    async def get_templates_by_practice_area(
        self,
        practice_area: Union[str, PracticeArea],
        document_type: Optional[Union[str, DocumentType]] = None,
        state: Optional[str] = None,
        include_global: bool = True,
    ) -> List[TemplateInfo]:
        """
        Get all templates for a practice area.

        Args:
            practice_area: Practice area to filter by
            document_type: Optional document type filter
            state: Optional state filter
            include_global: Whether to include global templates

        Returns:
            List of available templates
        """
        templates = []

        # Convert enums to strings if needed
        practice_area_str = (
            practice_area.value
            if isinstance(practice_area, PracticeArea)
            else practice_area
        )
        doc_type_str = (
            document_type.value
            if isinstance(document_type, DocumentType)
            else document_type
        )

        # Get tenant templates
        tenant_templates = await self._get_tenant_templates(
            practice_area_str, doc_type_str, state
        )
        templates.extend(tenant_templates)

        # Get global templates if requested
        if include_global:
            global_templates = await self._get_global_templates(
                practice_area_str, doc_type_str, state
            )
            templates.extend(global_templates)

        return templates

    async def _get_tenant_templates(
        self,
        practice_area: str,
        document_type: Optional[str] = None,
        state: Optional[str] = None,
    ) -> List[TemplateInfo]:
        """Get all tenant templates for criteria."""
        try:
            query = (
                self.client.table("authored_document_templates")
                .select("*")
                .eq("tenant_id", self.tenant_id)
                .eq("is_active", True)
            )

            if document_type:
                query = query.eq("category", document_type)

            response = query.execute()

            return [self._map_tenant_template(data) for data in response.data or []]

        except Exception as e:
            logger.error(f"Error fetching tenant templates: {e}")
            return []

    async def _get_global_templates(
        self,
        practice_area: str,
        document_type: Optional[str] = None,
        state: Optional[str] = None,
    ) -> List[TemplateInfo]:
        """Get all global templates for criteria."""
        try:
            query = (
                self.client.table("legal_templates")
                .select("*")
                .eq("practice_area", practice_area)
                .eq("is_active", True)
            )

            if document_type:
                query = query.eq("document_type", document_type)

            if state:
                query = query.eq("state", state)

            response = query.execute()

            return [self._map_global_template(data) for data in response.data or []]

        except Exception as e:
            logger.error(f"Error fetching global templates: {e}")
            return []
