"""
Template Engine for Document Agent

This module provides template processing and rendering capabilities for both
global templates (JSONB format with advanced features) and tenant-specific
templates (TEXT format with simple substitution).

Key Features:
- Dual-format support (JSONB vs TEXT)
- Advanced template engines (Jinja2, simple substitution)
- Conditional blocks and loops for global templates
- Variable validation and substitution
- Error handling and validation

Usage:
    from backend.agents.insights.document.template_engine import TemplateEngine

    engine = TemplateEngine()
    result = await engine.render_template(template_info, variables)
"""

import logging
import re
from typing import Any, Dict, List, Optional, Union
from jinja2 import Environment, BaseLoader, TemplateError, select_autoescape
import json

from .state import TemplateInfo, TemplateSource

logger = logging.getLogger(__name__)


class TemplateEngine:
    """
    Template processing engine supporting multiple formats and engines.

    Handles both global templates (JSONB with advanced features) and
    tenant templates (TEXT with simple substitution).
    """

    def __init__(self):
        """Initialize the template engine."""
        # Initialize Jinja2 environment for advanced templates
        self.jinja_env = Environment(
            loader=BaseLoader(),
            autoescape=select_autoescape(["html", "xml"]),
            trim_blocks=True,
            lstrip_blocks=True,
        )

        # Add custom filters
        self.jinja_env.filters["currency"] = self._format_currency
        self.jinja_env.filters["date"] = self._format_date
        self.jinja_env.filters["upper"] = str.upper
        self.jinja_env.filters["lower"] = str.lower
        self.jinja_env.filters["title"] = str.title

    async def render_template(
        self, template_info: TemplateInfo, variables: Dict[str, Any]
    ) -> str:
        """
        Render a template with the provided variables.

        Args:
            template_info: Template information
            variables: Variables to substitute

        Returns:
            Rendered template content

        Raises:
            TemplateError: If template rendering fails
        """
        logger.info(
            f"Rendering template {template_info.id} from {template_info.source}"
        )

        try:
            if template_info.source == TemplateSource.GLOBAL:
                return await self._render_global_template(template_info, variables)
            elif template_info.source == TemplateSource.TENANT:
                return await self._render_tenant_template(template_info, variables)
            else:
                raise ValueError(f"Unsupported template source: {template_info.source}")

        except Exception as e:
            logger.error(f"Error rendering template {template_info.id}: {e}")
            raise TemplateError(f"Failed to render template: {e}")

    async def _render_global_template(
        self, template_info: TemplateInfo, variables: Dict[str, Any]
    ) -> str:
        """Render global template with advanced features."""
        content = template_info.content

        # Handle JSONB content format
        if isinstance(content, dict):
            # Extract template content from JSONB structure
            template_content = content.get("content", "")
            if isinstance(template_content, dict):
                # If content is structured, join sections
                template_content = self._join_template_sections(template_content)
        else:
            template_content = str(content)

        # Get template engine type from metadata
        template_engine = template_info.metadata.get("template_engine", "simple")

        if template_engine == "jinja" or template_engine == "jinja2":
            return await self._render_jinja_template(template_content, variables)
        else:
            return await self._render_simple_template(template_content, variables)

    async def _render_tenant_template(
        self, template_info: TemplateInfo, variables: Dict[str, Any]
    ) -> str:
        """Render tenant template with simple substitution."""
        # Tenant templates are stored as TEXT
        template_content = str(template_info.content)
        return await self._render_simple_template(template_content, variables)

    async def _render_jinja_template(
        self, template_content: str, variables: Dict[str, Any]
    ) -> str:
        """Render template using Jinja2 engine."""
        try:
            template = self.jinja_env.from_string(template_content)
            return template.render(**variables)
        except TemplateError as e:
            logger.error(f"Jinja2 template error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Jinja2 rendering: {e}")
            raise TemplateError(f"Jinja2 rendering failed: {e}")

    async def _render_simple_template(
        self, template_content: str, variables: Dict[str, Any]
    ) -> str:
        """Render template using simple variable substitution."""
        try:
            # Replace {{variable}} patterns
            def replace_variable(match):
                var_name = match.group(1).strip()
                return str(variables.get(var_name, f"{{{{ {var_name} }}}}"))

            # Pattern for {{variable_name}}
            pattern = r"\{\{\s*([^}]+)\s*\}\}"
            result = re.sub(pattern, replace_variable, template_content)

            return result

        except Exception as e:
            logger.error(f"Simple template rendering error: {e}")
            raise TemplateError(f"Simple rendering failed: {e}")

    def _join_template_sections(self, content_dict: Dict[str, Any]) -> str:
        """Join structured template sections into a single string."""
        if "sections" in content_dict:
            sections = content_dict["sections"]
            if isinstance(sections, list):
                return "\n\n".join(
                    section.get("content", "")
                    for section in sections
                    if isinstance(section, dict)
                )
            elif isinstance(sections, dict):
                return "\n\n".join(sections.values())

        # Fallback: join all string values
        return "\n\n".join(
            str(value) for value in content_dict.values() if isinstance(value, str)
        )

    def validate_variables(
        self, template_info: TemplateInfo, variables: Dict[str, Any]
    ) -> List[str]:
        """
        Validate that all required variables are provided.

        Args:
            template_info: Template information
            variables: Provided variables

        Returns:
            List of validation errors
        """
        errors = []

        # Get required variables from template
        template_variables = template_info.variables

        if isinstance(template_variables, dict):
            required_vars = template_variables.get("required", [])

            for var_name in required_vars:
                if var_name not in variables or variables[var_name] is None:
                    errors.append(f"Required variable '{var_name}' is missing")
                elif (
                    isinstance(variables[var_name], str)
                    and not variables[var_name].strip()
                ):
                    errors.append(f"Required variable '{var_name}' is empty")

        return errors

    def extract_variables_from_template(self, template_content: str) -> List[str]:
        """
        Extract variable names from template content.

        Args:
            template_content: Template content string

        Returns:
            List of variable names found in template
        """
        # Pattern for {{variable_name}}
        pattern = r"\{\{\s*([^}]+)\s*\}\}"
        matches = re.findall(pattern, template_content)

        # Clean up variable names (remove filters, etc.)
        variables = []
        for match in matches:
            # Split on | to handle Jinja2 filters
            var_name = match.split("|")[0].strip()
            # Split on . to handle object properties
            var_name = var_name.split(".")[0].strip()
            if var_name and var_name not in variables:
                variables.append(var_name)

        return variables

    def _format_currency(self, value: Union[str, int, float]) -> str:
        """Format value as currency."""
        try:
            amount = float(value)
            return f"${amount:,.2f}"
        except (ValueError, TypeError):
            return str(value)

    def _format_date(self, value: str, format_str: str = "%B %d, %Y") -> str:
        """Format date string."""
        try:
            from datetime import datetime

            if isinstance(value, str):
                # Try to parse common date formats
                for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y"]:
                    try:
                        date_obj = datetime.strptime(value, fmt)
                        return date_obj.strftime(format_str)
                    except ValueError:
                        continue
            return str(value)
        except Exception:
            return str(value)
