#!/usr/bin/env python3
"""
Document Agent Implementation Validation

This script validates the Document Agent implementation by checking:
- File structure completeness
- Import syntax correctness
- Class definitions and methods
- Documentation completeness

Run this script to verify the implementation is complete and correct.
"""

import os
import ast
import sys
from pathlib import Path
from typing import List, Dict, Any


class ImplementationValidator:
    """Validates the Document Agent implementation."""

    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.success_count = 0
        self.total_checks = 0

    def validate(self) -> bool:
        """Run all validation checks."""
        print("🔍 Validating Document Agent Implementation")
        print("=" * 50)

        # File structure validation
        self._validate_file_structure()

        # Python syntax validation
        self._validate_python_syntax()

        # Class and method validation
        self._validate_classes_and_methods()

        # Documentation validation
        self._validate_documentation()

        # Print results
        self._print_results()

        return len(self.errors) == 0

    def _validate_file_structure(self):
        """Validate that all required files exist."""
        print("\n📁 Validating file structure...")

        required_files = [
            "__init__.py",
            "README.md",
            "IMPLEMENTATION_SUMMARY.md",
            "agent.py",
            "state.py",
            "template_manager.py",
            "template_engine.py",
            "nodes.py",
            "database/__init__.py",
            "database/template_repository.py",
            "tests/__init__.py",
            "tests/test_agent.py",
            "tests/test_template_engine.py",
            "tests/test_integration.py",
            "examples/basic_usage.py",
        ]

        for file_path in required_files:
            full_path = self.base_path / file_path
            self.total_checks += 1

            if full_path.exists():
                print(f"  ✅ {file_path}")
                self.success_count += 1
            else:
                print(f"  ❌ {file_path} - MISSING")
                self.errors.append(f"Missing required file: {file_path}")

    def _validate_python_syntax(self):
        """Validate Python syntax in all .py files."""
        print("\n🐍 Validating Python syntax...")

        python_files = list(self.base_path.rglob("*.py"))

        for py_file in python_files:
            self.total_checks += 1

            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # Parse the AST to check syntax
                ast.parse(content)
                print(f"  ✅ {py_file.relative_to(self.base_path)}")
                self.success_count += 1

            except SyntaxError as e:
                print(f"  ❌ {py_file.relative_to(self.base_path)} - Syntax Error: {e}")
                self.errors.append(
                    f"Syntax error in {py_file.relative_to(self.base_path)}: {e}"
                )
            except Exception as e:
                print(f"  ⚠️  {py_file.relative_to(self.base_path)} - Warning: {e}")
                self.warnings.append(
                    f"Warning in {py_file.relative_to(self.base_path)}: {e}"
                )

    def _validate_classes_and_methods(self):
        """Validate that required classes and methods exist."""
        print("\n🏗️  Validating classes and methods...")

        validations = [
            {
                "file": "agent.py",
                "class": "DocumentAgent",
                "methods": [
                    "__init__",
                    "_build_graph",
                    "initialize",
                    "execute",
                    "cleanup",
                ],
            },
            {"file": "state.py", "class": "DocumentState", "methods": []},
            {
                "file": "template_manager.py",
                "class": "TemplateManager",
                "methods": ["__init__", "generate_document", "select_template"],
            },
            {
                "file": "template_engine.py",
                "class": "TemplateEngine",
                "methods": ["__init__", "render_template", "validate_variables"],
            },
            {
                "file": "database/template_repository.py",
                "class": "TemplateRepository",
                "methods": ["__init__", "get_best_template", "get_template_by_id"],
            },
        ]

        for validation in validations:
            file_path = self.base_path / validation["file"]
            self.total_checks += 1

            if not file_path.exists():
                print(f"  ❌ {validation['file']} - File not found")
                self.errors.append(f"File not found: {validation['file']}")
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                tree = ast.parse(content)

                # Find the class
                class_found = False
                for node in ast.walk(tree):
                    if (
                        isinstance(node, ast.ClassDef)
                        and node.name == validation["class"]
                    ):
                        class_found = True

                        # Check methods
                        method_names = [
                            n.name for n in node.body if isinstance(n, ast.FunctionDef)
                        ]

                        missing_methods = []
                        for required_method in validation["methods"]:
                            if required_method not in method_names:
                                missing_methods.append(required_method)

                        if missing_methods:
                            print(
                                f"  ⚠️  {validation['file']} - {validation['class']} missing methods: {missing_methods}"
                            )
                            self.warnings.append(
                                f"{validation['class']} missing methods: {missing_methods}"
                            )
                        else:
                            print(f"  ✅ {validation['file']} - {validation['class']}")
                            self.success_count += 1
                        break

                if not class_found:
                    print(
                        f"  ❌ {validation['file']} - Class {validation['class']} not found"
                    )
                    self.errors.append(
                        f"Class {validation['class']} not found in {validation['file']}"
                    )

            except Exception as e:
                print(f"  ❌ {validation['file']} - Error: {e}")
                self.errors.append(f"Error validating {validation['file']}: {e}")

    def _validate_documentation(self):
        """Validate documentation completeness."""
        print("\n📚 Validating documentation...")

        doc_files = [
            ("README.md", ["Overview", "Usage", "Architecture"]),
            (
                "IMPLEMENTATION_SUMMARY.md",
                ["Implementation Status", "Key Features", "File Structure"],
            ),
        ]

        for doc_file, required_sections in doc_files:
            file_path = self.base_path / doc_file
            self.total_checks += 1

            if not file_path.exists():
                print(f"  ❌ {doc_file} - Missing")
                self.errors.append(f"Missing documentation: {doc_file}")
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                missing_sections = []
                for section in required_sections:
                    if section.lower() not in content.lower():
                        missing_sections.append(section)

                if missing_sections:
                    print(f"  ⚠️  {doc_file} - Missing sections: {missing_sections}")
                    self.warnings.append(
                        f"{doc_file} missing sections: {missing_sections}"
                    )
                else:
                    print(f"  ✅ {doc_file}")
                    self.success_count += 1

            except Exception as e:
                print(f"  ❌ {doc_file} - Error: {e}")
                self.errors.append(f"Error validating {doc_file}: {e}")

    def _print_results(self):
        """Print validation results."""
        print("\n" + "=" * 50)
        print("📊 Validation Results")
        print("=" * 50)

        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")

        if self.warnings:
            print(f"⚠️  Warnings: {len(self.warnings)}")
            for warning in self.warnings:
                print(f"   - {warning}")

        if self.errors:
            print(f"❌ Errors: {len(self.errors)}")
            for error in self.errors:
                print(f"   - {error}")
        else:
            print("🎉 No errors found!")

        # Overall status
        if len(self.errors) == 0:
            print("\n🎯 VALIDATION PASSED - Implementation is complete and correct!")
        else:
            print("\n💥 VALIDATION FAILED - Please fix the errors above.")

        # Success rate
        success_rate = (
            (self.success_count / self.total_checks) * 100
            if self.total_checks > 0
            else 0
        )
        print(f"📈 Success rate: {success_rate:.1f}%")


def main():
    """Main validation function."""
    # Get the directory of this script
    script_dir = Path(__file__).parent

    # Validate the implementation
    validator = ImplementationValidator(script_dir)
    success = validator.validate()

    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
