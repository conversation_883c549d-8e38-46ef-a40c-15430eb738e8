"""
Tests for Document Agent

This module contains tests for the main DocumentAgent class,
testing the complete document generation workflow.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from backend.agents.shared.core.state import Agent<PERSON>tatus, AgentExecutionContext
from backend.agents.insights.document.agent import DocumentAgent
from backend.agents.insights.document.state import (
    DocumentState,
    DocumentRequest,
    DocumentType,
    PracticeArea,
    TemplateInfo,
    TemplateSource,
    DocumentOutput,
)


@pytest.fixture
def execution_context():
    """Create test execution context."""
    return AgentExecutionContext(
        tenant_id="test-tenant-123",
        user_id="test-user-456",
        agent_type="document",
        created_at=datetime.now().isoformat(),
    )


@pytest.fixture
def document_request():
    """Create test document request."""
    return DocumentRequest(
        document_type=DocumentType.DEMAND_LETTER,
        practice_area=PracticeArea.PERSONAL_INJURY,
        matter_id="matter-789",
        variables={
            "client_name": "<PERSON>",
            "incident_date": "2024-01-15",
            "defendant_name": "ABC Insurance",
            "total_damages": "50000",
        },
    )


@pytest.fixture
def document_state(execution_context, document_request):
    """Create test document state."""
    return DocumentState(
        execution_context=execution_context,
        document_request=document_request,
        status=AgentStatus.PENDING,
    )


@pytest.fixture
def mock_template():
    """Create mock template."""
    return TemplateInfo(
        id="template-123",
        name="Demand Letter Template",
        source=TemplateSource.GLOBAL,
        content="Dear {{defendant_name}}, This letter demands payment of ${{total_damages}} for {{client_name}}.",
        variables={
            "required": ["client_name", "defendant_name", "total_damages"],
            "optional": [],
        },
        metadata={"template_engine": "simple"},
    )


@pytest.fixture
def mock_document_output(mock_template):
    """Create mock document output."""
    return DocumentOutput(
        content="Dear ABC Insurance, This letter demands payment of $50000 for John Doe.",
        title="Demand Letter - John Doe (2024-01-15)",
        template_used=mock_template,
        variables_used={
            "client_name": "John Doe",
            "defendant_name": "ABC Insurance",
            "total_damages": "50000",
        },
        metadata={"generated_at": datetime.now().isoformat()},
    )


class TestDocumentAgent:
    """Test cases for DocumentAgent."""

    def test_agent_initialization(self):
        """Test agent initialization."""
        agent = DocumentAgent()

        assert agent.agent_type == "document"
        assert agent.agent_name == "DocumentAgent"
        assert agent.graph is not None

    def test_agent_initialization_with_config(self):
        """Test agent initialization with config."""
        config = {"max_execution_time": 300}
        agent = DocumentAgent(config=config)

        assert agent.config == config

    async def test_initialize(self, document_state):
        """Test agent initialization."""
        agent = DocumentAgent()

        result = await agent.initialize(document_state)

        assert result.status == AgentStatus.INITIALIZING
        assert result.execution_context is not None

    async def test_initialize_missing_context(self):
        """Test initialization with missing context."""
        agent = DocumentAgent()
        state = DocumentState(execution_context=None, status=AgentStatus.PENDING)

        with pytest.raises(ValueError, match="Missing execution context"):
            await agent.initialize(state)

    async def test_execute_success(
        self, document_state, mock_template, mock_document_output
    ):
        """Test successful document generation."""
        agent = DocumentAgent()

        # Mock the graph execution to return successful result
        async def mock_graph_invoke(state, config=None):
            return {
                "selected_template": mock_template,
                "template_selection_complete": True,
                "template_variables": {"client_name": "John Doe"},
                "variable_collection_complete": True,
                "generated_document": mock_document_output,
                "document_generation_complete": True,
                "validation_errors": [],
                "quality_score": 1.0,
                "errors": [],
                "status": AgentStatus.COMPLETED,
            }

        # Patch the graph's ainvoke method
        with patch.object(agent.graph, "ainvoke", side_effect=mock_graph_invoke):
            result = await agent.execute(document_state)

        assert result.status == AgentStatus.COMPLETED
        assert result.generated_document is not None

    async def test_execute_with_errors(self, document_state):
        """Test execution with errors."""
        agent = DocumentAgent()

        # Create state with errors
        document_state.errors = ["Test error"]

        result = await agent.execute(document_state)

        assert result.status == AgentStatus.FAILED
        assert "Test error" in result.errors

    async def test_cleanup(self, document_state, mock_document_output):
        """Test cleanup method."""
        agent = DocumentAgent()

        # Set generated document
        document_state.generated_document = mock_document_output
        document_state.quality_score = 0.9

        result = await agent.cleanup(document_state)

        assert result is not None
        assert result.generated_document == mock_document_output

    @patch("backend.agents.insights.document.template_manager.TemplateManager")
    @pytest.mark.asyncio
    async def test_generate_document_from_request(
        self,
        mock_manager_class,
        execution_context,
        document_request,
        mock_document_output,
    ):
        """Test convenience method for document generation."""
        # Mock template manager
        mock_manager = AsyncMock()
        mock_manager.generate_document.return_value = (mock_document_output, [])
        mock_manager_class.return_value = mock_manager

        agent = DocumentAgent()

        # Mock the run method that the production code calls
        mock_state = DocumentState(
            execution_context=execution_context,
            document_request=document_request,
            status=AgentStatus.COMPLETED,
            generated_document=mock_document_output,
        )

        # Create an async mock for the execute method
        mock_execute = AsyncMock(return_value=mock_state)
        agent.execute = mock_execute

        result = await agent.generate_document_from_request(
            document_request, execution_context
        )

        assert result.generated_document == mock_document_output
        mock_execute.assert_called_once()

    @patch("backend.agents.insights.document.template_manager.TemplateManager")
    async def test_preview_template(self, mock_manager_class):
        """Test template preview functionality."""
        # Mock template manager
        mock_manager = AsyncMock()
        mock_manager.preview_template.return_value = ("Preview content", [])
        mock_manager_class.return_value = mock_manager

        agent = DocumentAgent()

        result = await agent.preview_template(
            template_id="template-123",
            variables={"client_name": "John Doe"},
            tenant_id="tenant-123",
        )

        assert result["content"] == "Preview content"
        assert result["errors"] == []
        assert result["success"] is True

    def test_should_continue_after_template_selection(
        self, document_state, mock_template
    ):
        """Test template selection continuation logic."""
        agent = DocumentAgent()

        # Test continue case
        document_state.template_selection_complete = True
        document_state.selected_template = mock_template

        result = agent._should_continue_after_template_selection(document_state)
        assert result == "continue"

        # Test end case
        document_state.template_selection_complete = False

        result = agent._should_continue_after_template_selection(document_state)
        assert result == "end"

    def test_should_continue_after_variable_collection(self, document_state):
        """Test variable collection continuation logic."""
        agent = DocumentAgent()

        # Test continue case
        document_state.variable_collection_complete = True
        document_state.validation_errors = []

        result = agent._should_continue_after_variable_collection(document_state)
        assert result == "continue"

        # Test end case with errors
        document_state.validation_errors = ["Error"]

        result = agent._should_continue_after_variable_collection(document_state)
        assert result == "end"

    def test_should_continue_after_generation(
        self, document_state, mock_document_output
    ):
        """Test generation continuation logic."""
        agent = DocumentAgent()

        # Test continue case
        document_state.document_generation_complete = True
        document_state.generated_document = mock_document_output

        result = agent._should_continue_after_generation(document_state)
        assert result == "continue"

        # Test end case
        document_state.document_generation_complete = False

        result = agent._should_continue_after_generation(document_state)
        assert result == "end"
