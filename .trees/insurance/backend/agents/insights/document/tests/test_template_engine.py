"""
Tests for Template Engine

This module contains tests for the TemplateEngine class,
testing template rendering for both global and tenant templates.
"""

import pytest
from jinja2 import TemplateError
from unittest.mock import patch

from backend.agents.insights.document.template_engine import TemplateEngine
from backend.agents.insights.document.state import TemplateInfo, TemplateSource


class TestTemplateEngine:
    """Test cases for TemplateEngine."""

    def test_initialization(self):
        """Test engine initialization."""
        engine = TemplateEngine()

        assert engine.jinja_env is not None
        assert "currency" in engine.jinja_env.filters
        assert "date" in engine.jinja_env.filters

    async def test_render_simple_template(self):
        """Test simple template rendering."""
        engine = TemplateEngine()

        template_info = TemplateInfo(
            id="test-1",
            name="Test Template",
            source=TemplateSource.TENANT,
            content="Hello {{name}}, your balance is {{amount}}.",
            variables={"required": ["name", "amount"]},
            metadata={},
        )

        variables = {"name": "<PERSON>", "amount": "1000"}

        result = await engine.render_template(template_info, variables)

        assert result == "Hello John Doe, your balance is 1000."

    async def test_render_jinja_template(self):
        """Test Jinja2 template rendering."""
        engine = TemplateEngine()

        template_content = """
        Dear {{client_name}},
        
        {% if total_damages > 10000 %}
        This is a significant claim of ${{total_damages|currency}}.
        {% else %}
        This is a claim of ${{total_damages|currency}}.
        {% endif %}
        
        Sincerely,
        {{attorney_name}}
        """

        template_info = TemplateInfo(
            id="test-2",
            name="Jinja Template",
            source=TemplateSource.GLOBAL,
            content=template_content,
            variables={"required": ["client_name", "total_damages", "attorney_name"]},
            metadata={"template_engine": "jinja"},
        )

        variables = {
            "client_name": "Jane Smith",
            "total_damages": 15000,
            "attorney_name": "John Attorney",
        }

        result = await engine.render_template(template_info, variables)

        assert "Jane Smith" in result
        assert "significant claim" in result
        assert "$15,000.00" in result
        assert "John Attorney" in result

    async def test_render_global_template_jsonb(self):
        """Test rendering global template with JSONB content."""
        engine = TemplateEngine()

        jsonb_content = {
            "content": "Dear {{client_name}}, Amount: {{amount}}",
            "sections": [
                {"title": "Header", "content": "Legal Document"},
                {"title": "Body", "content": "Client: {{client_name}}"},
            ],
        }

        template_info = TemplateInfo(
            id="test-3",
            name="JSONB Template",
            source=TemplateSource.GLOBAL,
            content=jsonb_content,
            variables={"required": ["client_name", "amount"]},
            metadata={"template_engine": "simple"},
        )

        variables = {"client_name": "Bob Johnson", "amount": "5000"}

        result = await engine.render_template(template_info, variables)

        assert "Bob Johnson" in result
        assert "5000" in result

    async def test_render_template_with_missing_variables(self):
        """Test rendering with missing variables."""
        engine = TemplateEngine()

        template_info = TemplateInfo(
            id="test-4",
            name="Missing Vars Template",
            source=TemplateSource.TENANT,
            content="Hello {{name}}, your balance is {{missing_var}}.",
            variables={"required": ["name", "missing_var"]},
            metadata={},
        )

        variables = {"name": "John Doe"}  # missing_var not provided

        result = await engine.render_template(template_info, variables)

        # Should keep placeholder for missing variable
        assert "John Doe" in result
        assert "{{ missing_var }}" in result

    async def test_render_invalid_jinja_template(self):
        """Test rendering invalid Jinja2 template."""
        engine = TemplateEngine()

        template_info = TemplateInfo(
            id="test-5",
            name="Invalid Jinja",
            source=TemplateSource.GLOBAL,
            content="Hello {{name}, invalid syntax",  # Missing closing brace
            variables={"required": ["name"]},
            metadata={"template_engine": "jinja"},
        )

        variables = {"name": "John Doe"}

        with pytest.raises(TemplateError):
            await engine.render_template(template_info, variables)

    def test_validate_variables_success(self):
        """Test successful variable validation."""
        engine = TemplateEngine()

        template_info = TemplateInfo(
            id="test-6",
            name="Validation Test",
            source=TemplateSource.TENANT,
            content="Test content",
            variables={"required": ["name", "email"], "optional": ["phone"]},
            metadata={},
        )

        variables = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "************",
        }

        errors = engine.validate_variables(template_info, variables)

        assert len(errors) == 0

    def test_validate_variables_missing_required(self):
        """Test validation with missing required variables."""
        engine = TemplateEngine()

        template_info = TemplateInfo(
            id="test-7",
            name="Missing Required",
            source=TemplateSource.TENANT,
            content="Test content",
            variables={"required": ["name", "email"], "optional": ["phone"]},
            metadata={},
        )

        variables = {"name": "John Doe"}  # Missing email

        errors = engine.validate_variables(template_info, variables)

        assert len(errors) == 1
        assert "email" in errors[0]
        assert "missing" in errors[0].lower()

    def test_validate_variables_empty_required(self):
        """Test validation with empty required variables."""
        engine = TemplateEngine()

        template_info = TemplateInfo(
            id="test-8",
            name="Empty Required",
            source=TemplateSource.TENANT,
            content="Test content",
            variables={"required": ["name", "email"], "optional": ["phone"]},
            metadata={},
        )

        variables = {"name": "", "email": "<EMAIL>"}  # Empty name

        errors = engine.validate_variables(template_info, variables)

        assert len(errors) == 1
        assert "name" in errors[0]
        assert "empty" in errors[0].lower()

    def test_extract_variables_from_template(self):
        """Test variable extraction from template content."""
        engine = TemplateEngine()

        template_content = """
        Dear {{client_name}},
        
        Your case {{case_number}} has been updated.
        The amount is {{total_amount|currency}}.
        
        Contact: {{attorney.name}}
        """

        variables = engine.extract_variables_from_template(template_content)

        expected_vars = ["client_name", "case_number", "total_amount", "attorney"]
        assert all(var in variables for var in expected_vars)

    def test_format_currency_filter(self):
        """Test currency formatting filter."""
        engine = TemplateEngine()

        assert engine._format_currency(1000) == "$1,000.00"
        assert engine._format_currency(1000.50) == "$1,000.50"
        assert engine._format_currency("1500") == "$1,500.00"
        assert engine._format_currency("invalid") == "invalid"

    def test_format_date_filter(self):
        """Test date formatting filter."""
        engine = TemplateEngine()

        # Test various date formats
        assert "January" in engine._format_date("2024-01-15")
        assert "2024" in engine._format_date("2024-01-15")

        # Test invalid date
        result = engine._format_date("invalid-date")
        assert result == "invalid-date"

    def test_join_template_sections(self):
        """Test joining template sections."""
        engine = TemplateEngine()

        content_dict = {
            "sections": [
                {"content": "Section 1 content"},
                {"content": "Section 2 content"},
            ]
        }

        result = engine._join_template_sections(content_dict)

        assert "Section 1 content" in result
        assert "Section 2 content" in result
        assert "\n\n" in result  # Sections should be joined with double newlines

    def test_join_template_sections_dict(self):
        """Test joining template sections from dict."""
        engine = TemplateEngine()

        content_dict = {
            "sections": {
                "header": "Header content",
                "body": "Body content",
                "footer": "Footer content",
            }
        }

        result = engine._join_template_sections(content_dict)

        assert "Header content" in result
        assert "Body content" in result
        assert "Footer content" in result

    @pytest.mark.asyncio
    async def test_unsupported_template_source(self):
        """Test handling of unsupported template source."""
        engine = TemplateEngine()

        # Create a valid template info first
        template_info = TemplateInfo(
            id="test-9",
            name="Test Source",
            source=TemplateSource.GLOBAL,
            content="Test content",
            variables={},
            metadata={},
        )

        variables = {}

        # Mock the source to be an unsupported value after creation
        with patch.object(template_info, "source", "unsupported"):
            with pytest.raises(TemplateError, match="Failed to render template"):
                await engine.render_template(template_info, variables)
