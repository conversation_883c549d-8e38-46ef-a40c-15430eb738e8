"""
Document Agent Nodes

This module implements the LangGraph nodes for the Document Agent,
providing the core document generation workflow steps.

Key Nodes:
- select_template: Template selection with priority handling
- collect_variables: Variable collection and validation
- generate_document: Document generation and rendering
- validate_output: Document validation and quality checks

Usage:
    from backend.agents.insights.document.nodes import (
        select_template, collect_variables, generate_document
    )
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from backend.agents.shared.core.state import AgentStatus
from .state import DocumentState, DocumentRequest, TemplateInfo
from .template_manager import TemplateManager

logger = logging.getLogger(__name__)


async def select_template(
    state: DocumentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Select the best template for document generation.

    This node implements the template selection logic with priority handling:
    1. Tenant-specific template (highest priority)
    2. Global template (fallback)
    3. Dynamic generation (last resort)

    Args:
        state: Current document state
        config: Optional configuration

    Returns:
        Updated state with selected template
    """
    logger.info("Selecting template for document generation")

    try:
        # Validate execution context
        if not state.execution_context:
            raise ValueError("Missing execution context")

        tenant_id = state.execution_context.tenant_id

        # Validate document request
        if not state.document_request:
            raise ValueError("Missing document request")

        request = state.document_request

        # Initialize template manager
        template_manager = TemplateManager(tenant_id)

        # Select template
        selected_template = await template_manager.select_template(request)

        if selected_template:
            logger.info(
                f"Selected template: {selected_template.id} from {selected_template.source}"
            )

            # Get all available templates for context
            available_templates = await template_manager.get_available_templates(
                practice_area=request.practice_area, document_type=request.document_type
            )

            return {
                "selected_template": selected_template,
                "available_templates": available_templates,
                "template_selection_complete": True,
                "status": AgentStatus.EXECUTING,
            }
        else:
            logger.warning(
                f"No template found for {request.document_type} in {request.practice_area}"
            )
            return {
                "errors": state.errors
                + [
                    f"No template found for {request.document_type} in {request.practice_area}"
                ],
                "template_selection_complete": False,
                "status": AgentStatus.FAILED,
            }

    except Exception as e:
        logger.error(f"Error in template selection: {e}")
        return {
            "errors": state.errors + [f"Template selection failed: {str(e)}"],
            "template_selection_complete": False,
            "status": AgentStatus.FAILED,
        }


async def collect_variables(
    state: DocumentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Collect and validate template variables.

    This node handles variable collection, validation, and preparation
    for template rendering.

    Args:
        state: Current document state
        config: Optional configuration

    Returns:
        Updated state with collected variables
    """
    logger.info("Collecting template variables")

    try:
        # Validate prerequisites
        if not state.selected_template:
            raise ValueError("No template selected")

        if not state.document_request:
            raise ValueError("Missing document request")

        template = state.selected_template
        request = state.document_request

        # Initialize template manager
        if state.execution_context is None:
            raise ValueError("Execution context is required for variable collection")
        tenant_id = state.execution_context.tenant_id
        template_manager = TemplateManager(tenant_id)

        # Validate provided variables
        validation_errors = await template_manager.validate_template_variables(
            template.id, request.variables
        )

        if validation_errors:
            logger.warning(f"Variable validation errors: {validation_errors}")
            return {
                "validation_errors": validation_errors,
                "template_variables": request.variables,
                "variable_collection_complete": False,
                "status": AgentStatus.FAILED,
            }

        # Collect variables from request
        collected_variables = dict(request.variables)

        # Add system variables
        if state.execution_context is not None:
            collected_variables.update(
                {
                    "tenant_id": state.execution_context.tenant_id,
                    "user_id": state.execution_context.user_id,
                    "generation_date": getattr(
                        state.execution_context, "created_at", datetime.now()
                    ),
                    "agent_version": state.execution_context.agent_version,
                }
            )

        logger.info(f"Collected {len(collected_variables)} variables")

        return {
            "template_variables": collected_variables,
            "variable_collection_complete": True,
            "validation_errors": [],
            "status": AgentStatus.EXECUTING,
        }

    except Exception as e:
        logger.error(f"Error in variable collection: {e}")
        return {
            "errors": state.errors + [f"Variable collection failed: {str(e)}"],
            "variable_collection_complete": False,
            "status": AgentStatus.FAILED,
        }


async def generate_document(
    state: DocumentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Generate the final document.

    This node handles the actual document generation using the selected
    template and collected variables.

    Args:
        state: Current document state
        config: Optional configuration

    Returns:
        Updated state with generated document
    """
    logger.info("Generating document")

    try:
        # Validate prerequisites
        if not state.selected_template:
            raise ValueError("No template selected")

        if not state.template_variables:
            raise ValueError("No variables collected")

        if not state.document_request:
            raise ValueError("Missing document request")

        # Initialize template manager
        if state.execution_context is None:
            raise ValueError("Execution context is required for document generation")
        tenant_id = state.execution_context.tenant_id
        template_manager = TemplateManager(tenant_id)

        # Generate document
        document_output, generation_errors = await template_manager.generate_document(
            state.document_request
        )

        if generation_errors:
            logger.warning(f"Document generation errors: {generation_errors}")
            return {
                "errors": state.errors + generation_errors,
                "generated_document": document_output,
                "document_generation_complete": False,
                "status": AgentStatus.FAILED,
            }

        logger.info(f"Successfully generated document: {document_output.title}")

        return {
            "generated_document": document_output,
            "document_generation_complete": True,
            "status": AgentStatus.COMPLETED,
        }

    except Exception as e:
        logger.error(f"Error in document generation: {e}")
        return {
            "errors": state.errors + [f"Document generation failed: {str(e)}"],
            "document_generation_complete": False,
            "status": AgentStatus.FAILED,
        }


async def validate_output(
    state: DocumentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Validate the generated document.

    This node performs quality checks and validation on the generated
    document to ensure it meets requirements.

    Args:
        state: Current document state
        config: Optional configuration

    Returns:
        Updated state with validation results
    """
    logger.info("Validating generated document")

    try:
        if not state.generated_document:
            raise ValueError("No document to validate")

        document = state.generated_document
        validation_errors = []
        quality_score = 1.0

        # Basic content validation
        if not document.content or not document.content.strip():
            validation_errors.append("Document content is empty")
            quality_score -= 0.5

        # Title validation
        if not document.title or not document.title.strip():
            validation_errors.append("Document title is missing")
            quality_score -= 0.2

        # Content length validation
        if len(document.content) < 50:
            validation_errors.append("Document content is too short")
            quality_score -= 0.2

        # Variable substitution validation
        if "{{" in document.content and "}}" in document.content:
            validation_errors.append("Unsubstituted variables found in document")
            quality_score -= 0.3

        # Ensure quality score is not negative
        quality_score = max(0.0, quality_score)

        if validation_errors:
            logger.warning(f"Document validation issues: {validation_errors}")
        else:
            logger.info(
                f"Document validation passed with quality score: {quality_score}"
            )

        return {
            "validation_errors": validation_errors,
            "quality_score": quality_score,
            "status": (
                AgentStatus.COMPLETED if not validation_errors else AgentStatus.FAILED
            ),
        }

    except Exception as e:
        logger.error(f"Error in document validation: {e}")
        return {
            "errors": state.errors + [f"Document validation failed: {str(e)}"],
            "validation_errors": [f"Validation error: {str(e)}"],
            "quality_score": 0.0,
            "status": AgentStatus.FAILED,
        }
