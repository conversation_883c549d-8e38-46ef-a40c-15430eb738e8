"""
Template Manager for Document Agent

This module provides high-level template management functionality, coordinating
between the template repository and template engine to provide a unified
interface for template operations.

Key Features:
- Template selection with priority handling
- Variable collection and validation
- Template rendering coordination
- Caching and performance optimization
- Error handling and fallback strategies

Usage:
    from backend.agents.insights.document.template_manager import TemplateManager

    manager = TemplateManager(tenant_id="tenant-123")
    result = await manager.generate_document(request)
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

from .database import TemplateRepository
from .template_engine import TemplateEngine
from .state import (
    DocumentRequest,
    TemplateInfo,
    DocumentOutput,
    DocumentType,
    PracticeArea,
    TemplateSource,
)

logger = logging.getLogger(__name__)


class TemplateManager:
    """
    High-level template management coordinator.

    Provides unified interface for template operations, coordinating
    between repository access and template rendering.
    """

    def __init__(self, tenant_id: str):
        """
        Initialize the template manager.

        Args:
            tenant_id: Tenant ID for isolation
        """
        self.tenant_id = tenant_id
        self.repository = TemplateRepository(tenant_id)
        self.engine = TemplateEngine()
        self._template_cache: Dict[str, TemplateInfo] = {}

    async def generate_document(
        self, request: DocumentRequest
    ) -> Tuple[DocumentOutput, List[str]]:
        """
        Generate a document from a request.

        Args:
            request: Document generation request

        Returns:
            Tuple of (generated document, validation errors)
        """
        logger.info(
            f"Generating document: {request.document_type} for {request.practice_area}"
        )

        errors = []

        try:
            # Select the best template
            template = await self.select_template(request)
            if not template:
                errors.append(
                    f"No template found for {request.document_type} in {request.practice_area}"
                )
                return self._create_error_output(request, errors), errors

            # Validate variables
            validation_errors = self.engine.validate_variables(
                template, request.variables
            )
            if validation_errors:
                errors.extend(validation_errors)
                return self._create_error_output(request, errors), errors

            # Render the template
            content = await self.engine.render_template(template, request.variables)

            # Create document output
            output = DocumentOutput(
                content=content,
                title=self._generate_title(request, template),
                template_used=template,
                variables_used=request.variables,
                metadata={
                    "generated_at": datetime.now().isoformat(),
                    "tenant_id": self.tenant_id,
                    "template_source": template.source.value,
                    **request.metadata,
                },
            )

            logger.info(f"Successfully generated document using template {template.id}")
            return output, errors

        except Exception as e:
            logger.error(f"Error generating document: {e}")
            errors.append(f"Document generation failed: {str(e)}")
            return self._create_error_output(request, errors), errors

    async def select_template(self, request: DocumentRequest) -> Optional[TemplateInfo]:
        """
        Select the best template for a request.

        Args:
            request: Document generation request

        Returns:
            Selected template or None
        """
        # Check cache first
        cache_key = self._get_cache_key(request)
        if cache_key in self._template_cache:
            return self._template_cache[cache_key]

        # Get template from repository
        template = await self.repository.get_best_template(
            document_type=request.document_type,
            practice_area=request.practice_area,
            template_id=request.template_id,
        )

        # Cache the result
        if template:
            self._template_cache[cache_key] = template

        return template

    async def get_available_templates(
        self, practice_area: PracticeArea, document_type: Optional[DocumentType] = None
    ) -> List[TemplateInfo]:
        """
        Get all available templates for criteria.

        Args:
            practice_area: Practice area to filter by
            document_type: Optional document type filter

        Returns:
            List of available templates
        """
        return await self.repository.get_templates_by_practice_area(
            practice_area=practice_area,
            document_type=document_type,
            include_global=True,
        )

    async def validate_template_variables(
        self, template_id: str, variables: Dict[str, Any]
    ) -> List[str]:
        """
        Validate variables for a specific template.

        Args:
            template_id: Template ID
            variables: Variables to validate

        Returns:
            List of validation errors
        """
        template = await self.repository.get_template_by_id(template_id)
        if not template:
            return [f"Template {template_id} not found"]

        return self.engine.validate_variables(template, variables)

    async def preview_template(
        self, template_id: str, variables: Dict[str, Any]
    ) -> Tuple[str, List[str]]:
        """
        Preview a template with variables.

        Args:
            template_id: Template ID
            variables: Variables for preview

        Returns:
            Tuple of (preview content, errors)
        """
        errors = []

        try:
            template = await self.repository.get_template_by_id(template_id)
            if not template:
                errors.append(f"Template {template_id} not found")
                return "", errors

            # Validate variables (but don't fail on missing ones for preview)
            validation_errors = self.engine.validate_variables(template, variables)
            if validation_errors:
                errors.extend(validation_errors)

            # Render with available variables
            content = await self.engine.render_template(template, variables)
            return content, errors

        except Exception as e:
            logger.error(f"Error previewing template: {e}")
            errors.append(f"Preview failed: {str(e)}")
            return "", errors

    def get_template_variables(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get variable information for a template.

        Args:
            template_id: Template ID

        Returns:
            Template variables or None
        """
        # Check cache first
        for template in self._template_cache.values():
            if template.id == template_id:
                return template.variables

        return None

    def _get_cache_key(self, request: DocumentRequest) -> str:
        """Generate cache key for template request."""
        return f"{request.document_type}:{request.practice_area}:{request.template_id or 'auto'}"

    def _generate_title(self, request: DocumentRequest, template: TemplateInfo) -> str:
        """Generate document title."""
        base_title = template.name

        # Add client name if available
        if "client_name" in request.variables:
            base_title = f"{base_title} - {request.variables['client_name']}"

        # Add date
        date_str = datetime.now().strftime("%Y-%m-%d")
        return f"{base_title} ({date_str})"

    def _create_error_output(
        self, request: DocumentRequest, errors: List[str]
    ) -> DocumentOutput:
        """Create error output for failed generation."""
        return DocumentOutput(
            content=f"Document generation failed:\n\n"
            + "\n".join(f"- {error}" for error in errors),
            title=f"Error - {request.document_type}",
            template_used=TemplateInfo(
                id="error",
                name="Error Template",
                source=TemplateSource.DYNAMIC,
                content="Error occurred during generation",
                variables={},
                metadata={},
            ),
            variables_used=request.variables,
            metadata={
                "error": True,
                "errors": errors,
                "generated_at": datetime.now().isoformat(),
            },
        )

    def clear_cache(self):
        """Clear the template cache."""
        self._template_cache.clear()
        logger.info("Template cache cleared")
