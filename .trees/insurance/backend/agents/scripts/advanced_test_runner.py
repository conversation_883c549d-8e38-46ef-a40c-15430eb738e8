#!/usr/bin/env python3
"""
Advanced Test Runner for Agent System

This script provides comprehensive test execution capabilities including
load testing, chaos engineering, performance monitoring, and advanced reporting.
"""

import argparse
import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import yaml

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from backend.agents.shared.testing import (
        LoadTester,
        LoadTestConfig,
        ChaosEngineer,
        ChaosConfig,
        PerformanceTester,
        TestDataManager,
    )
except ImportError as e:
    print(f"Warning: Could not import testing utilities: {e}")
    print("Running in basic mode without advanced features")


class AdvancedTestRunner:
    """Advanced test runner with comprehensive testing capabilities."""

    def __init__(self, config_file: Optional[str] = None):
        """Initialize the test runner with configuration."""
        self.config_file = config_file or "test_data/test_config.yaml"
        self.config = self._load_config()
        self.results = {}
        self.start_time = None
        self.end_time = None

        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger(__name__)

        # Initialize test data manager
        self.test_data_manager = TestDataManager("test_data")

    def _load_config(self) -> Dict:
        """Load test configuration from YAML file."""
        config_path = Path(__file__).parent.parent / self.config_file

        if config_path.exists():
            with open(config_path, "r") as f:
                return yaml.safe_load(f)
        else:
            self.logger.warning(f"Config file not found: {config_path}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """Get default configuration if config file is not found."""
        return {
            "performance": {
                "response_times": {"unit_tests": 0.1, "integration_tests": 2.0},
                "memory_limits": {"unit_tests": 50, "integration_tests": 200},
                "throughput": {"agent_execution": 10},
            },
            "logging": {
                "levels": {"root": "INFO", "tests": "DEBUG"},
                "console_output": True,
            },
        }

    def _setup_logging(self):
        """Setup logging configuration."""
        log_config = self.config.get("logging", {})

        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, log_config.get("levels", {}).get("root", "INFO")),
            format=log_config.get(
                "format", "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
            ),
            datefmt=log_config.get("date_format", "%Y-%m-%d %H:%M:%S"),
        )

        # Configure file logging if enabled
        if log_config.get("file_output", False):
            log_file = log_config.get("log_file", "test_execution.log")
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(
                logging.Formatter(
                    log_config.get(
                        "format", "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
                    )
                )
            )
            logging.getLogger().addHandler(file_handler)

    async def run_unit_tests(self) -> Dict:
        """Run unit tests with performance monitoring."""
        self.logger.info("Starting unit tests...")

        # Use pytest to run unit tests
        import subprocess

        cmd = [
            "python",
            "-m",
            "pytest",
            "tests/unit/",
            "-v",
            "--tb=short",
            "--json-report",
            "--json-report-file=test_reports/unit_tests.json",
        ]

        # Add coverage if configured
        if (
            self.config.get("ci_cd", {})
            .get("coverage", {})
            .get("minimum_line_coverage")
        ):
            cmd.extend(
                ["--cov=backend/agents", "--cov-report=json:test_reports/coverage.json"]
            )

        start_time = time.time()
        result = subprocess.run(
            cmd, capture_output=True, text=True, cwd=Path(__file__).parent.parent
        )
        duration = time.time() - start_time

        # Parse results
        test_results = {
            "type": "unit_tests",
            "duration": duration,
            "exit_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "success": result.returncode == 0,
        }

        # Load detailed results if available
        results_file = Path(__file__).parent.parent / "test_reports" / "unit_tests.json"
        if results_file.exists():
            with open(results_file, "r") as f:
                detailed_results = json.load(f)
                test_results["detailed_results"] = detailed_results

        self.logger.info(
            f"Unit tests completed in {duration:.2f}s - {'PASSED' if test_results['success'] else 'FAILED'}"
        )
        return test_results

    async def run_integration_tests(self) -> Dict:
        """Run integration tests with advanced monitoring."""
        self.logger.info("Starting integration tests...")

        import subprocess

        cmd = [
            "python",
            "-m",
            "pytest",
            "tests/integration/",
            "-v",
            "--tb=short",
            "-m",
            "integration",
            "--json-report",
            "--json-report-file=test_reports/integration_tests.json",
        ]

        start_time = time.time()
        result = subprocess.run(
            cmd, capture_output=True, text=True, cwd=Path(__file__).parent.parent
        )
        duration = time.time() - start_time

        test_results = {
            "type": "integration_tests",
            "duration": duration,
            "exit_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "success": result.returncode == 0,
        }

        self.logger.info(
            f"Integration tests completed in {duration:.2f}s - {'PASSED' if test_results['success'] else 'FAILED'}"
        )
        return test_results

    async def run_performance_tests(self) -> Dict:
        """Run performance tests with load testing and chaos engineering."""
        self.logger.info("Starting performance tests...")

        performance_results = {
            "type": "performance_tests",
            "load_tests": [],
            "chaos_tests": [],
            "benchmark_tests": [],
        }

        # Load test configuration
        perf_config = self.config.get("performance", {})
        load_config = perf_config.get("load_testing", {})

        # Run load tests if configured
        if load_config.get("enabled", True):
            load_test_config = LoadTestConfig(
                concurrent_users=load_config.get("default_concurrent_users", 10),
                requests_per_user=load_config.get("default_requests_per_user", 5),
                ramp_up_time=load_config.get("ramp_up_time", 2.0),
            )

            load_tester = LoadTester(load_test_config)

            # Example load test
            async def sample_operation():
                await asyncio.sleep(0.1)  # Simulate work
                return {"status": "completed"}

            load_results = await load_tester.run_load_test(sample_operation)
            performance_results["load_tests"].append(load_results)

        # Run chaos tests if configured
        chaos_config = perf_config.get("chaos_engineering", {})
        if chaos_config.get("enabled", False):
            chaos_engineer_config = ChaosConfig(
                failure_rate=chaos_config.get("default_failure_rate", 0.1),
                latency_injection=chaos_config.get("latency_injection", {}).get(
                    "enabled", True
                ),
            )

            chaos_engineer = ChaosEngineer(chaos_engineer_config)

            # Example chaos test
            async with chaos_engineer.inject_chaos():
                chaos_results = {"chaos_injected": True, "test_completed": True}
                performance_results["chaos_tests"].append(chaos_results)

        # Run pytest performance tests
        import subprocess

        cmd = [
            "python",
            "-m",
            "pytest",
            "tests/performance/",
            "-v",
            "--tb=short",
            "-m",
            "performance",
            "--json-report",
            "--json-report-file=test_reports/performance_tests.json",
        ]

        start_time = time.time()
        result = subprocess.run(
            cmd, capture_output=True, text=True, cwd=Path(__file__).parent.parent
        )
        duration = time.time() - start_time

        performance_results.update(
            {
                "duration": duration,
                "exit_code": result.returncode,
                "success": result.returncode == 0,
            }
        )

        self.logger.info(
            f"Performance tests completed in {duration:.2f}s - {'PASSED' if performance_results['success'] else 'FAILED'}"
        )
        return performance_results

    async def run_e2e_tests(self) -> Dict:
        """Run end-to-end tests."""
        self.logger.info("Starting end-to-end tests...")

        import subprocess

        cmd = [
            "python",
            "-m",
            "pytest",
            "tests/e2e/",
            "-v",
            "--tb=short",
            "-m",
            "e2e",
            "--json-report",
            "--json-report-file=test_reports/e2e_tests.json",
        ]

        start_time = time.time()
        result = subprocess.run(
            cmd, capture_output=True, text=True, cwd=Path(__file__).parent.parent
        )
        duration = time.time() - start_time

        test_results = {
            "type": "e2e_tests",
            "duration": duration,
            "exit_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "success": result.returncode == 0,
        }

        self.logger.info(
            f"E2E tests completed in {duration:.2f}s - {'PASSED' if test_results['success'] else 'FAILED'}"
        )
        return test_results

    async def run_all_tests(self) -> Dict:
        """Run all test suites."""
        self.logger.info("Starting comprehensive test execution...")
        self.start_time = datetime.utcnow()

        all_results = {
            "start_time": self.start_time.isoformat(),
            "test_suites": {},
            "summary": {},
        }

        # Run test suites
        test_suites = [
            ("unit", self.run_unit_tests),
            ("integration", self.run_integration_tests),
            ("performance", self.run_performance_tests),
            ("e2e", self.run_e2e_tests),
        ]

        for suite_name, suite_func in test_suites:
            try:
                self.logger.info(f"Executing {suite_name} test suite...")
                suite_results = await suite_func()
                all_results["test_suites"][suite_name] = suite_results
            except Exception as e:
                self.logger.error(f"Error running {suite_name} tests: {e}")
                all_results["test_suites"][suite_name] = {
                    "type": suite_name,
                    "success": False,
                    "error": str(e),
                }

        self.end_time = datetime.utcnow()
        all_results["end_time"] = self.end_time.isoformat()
        all_results["total_duration"] = (
            self.end_time - self.start_time
        ).total_seconds()

        # Generate summary
        all_results["summary"] = self._generate_summary(all_results["test_suites"])

        # Save results
        self._save_results(all_results)

        return all_results

    def _generate_summary(self, test_suites: Dict) -> Dict:
        """Generate test execution summary."""
        summary = {
            "total_suites": len(test_suites),
            "passed_suites": 0,
            "failed_suites": 0,
            "total_duration": 0,
            "overall_success": True,
        }

        for suite_name, suite_results in test_suites.items():
            if suite_results.get("success", False):
                summary["passed_suites"] += 1
            else:
                summary["failed_suites"] += 1
                summary["overall_success"] = False

            summary["total_duration"] += suite_results.get("duration", 0)

        return summary

    def _save_results(self, results: Dict):
        """Save test results to file."""
        results_dir = Path(__file__).parent.parent / "test_reports"
        results_dir.mkdir(exist_ok=True)

        results_file = (
            results_dir
            / f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )

        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        self.logger.info(f"Test results saved to: {results_file}")

    def print_summary(self, results: Dict):
        """Print test execution summary."""
        summary = results.get("summary", {})

        print("\n" + "=" * 60)
        print("TEST EXECUTION SUMMARY")
        print("=" * 60)
        print(f"Total Duration: {summary.get('total_duration', 0):.2f} seconds")
        print(f"Test Suites: {summary.get('total_suites', 0)}")
        print(f"Passed: {summary.get('passed_suites', 0)}")
        print(f"Failed: {summary.get('failed_suites', 0)}")
        print(
            f"Overall Result: {'PASSED' if summary.get('overall_success', False) else 'FAILED'}"
        )
        print("=" * 60)

        # Print suite details
        for suite_name, suite_results in results.get("test_suites", {}).items():
            status = "PASSED" if suite_results.get("success", False) else "FAILED"
            duration = suite_results.get("duration", 0)
            print(f"{suite_name.upper()}: {status} ({duration:.2f}s)")

        print("=" * 60)


async def main():
    """Main entry point for the advanced test runner."""
    parser = argparse.ArgumentParser(
        description="Advanced Test Runner for Agent System"
    )
    parser.add_argument("--config", help="Path to test configuration file")
    parser.add_argument(
        "--suite",
        choices=["unit", "integration", "performance", "e2e", "all"],
        default="all",
        help="Test suite to run",
    )
    parser.add_argument("--output", help="Output file for results")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    # Initialize test runner
    runner = AdvancedTestRunner(args.config)

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Run tests based on suite selection
    if args.suite == "unit":
        results = await runner.run_unit_tests()
    elif args.suite == "integration":
        results = await runner.run_integration_tests()
    elif args.suite == "performance":
        results = await runner.run_performance_tests()
    elif args.suite == "e2e":
        results = await runner.run_e2e_tests()
    else:  # all
        results = await runner.run_all_tests()

    # Print summary
    if args.suite == "all":
        runner.print_summary(results)
    else:
        print(
            f"\n{args.suite.upper()} Tests: {'PASSED' if results.get('success', False) else 'FAILED'}"
        )
        print(f"Duration: {results.get('duration', 0):.2f} seconds")

    # Save to custom output file if specified
    if args.output:
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2, default=str)
        print(f"Results saved to: {args.output}")

    # Exit with appropriate code
    if args.suite == "all":
        sys.exit(0 if results.get("summary", {}).get("overall_success", False) else 1)
    else:
        sys.exit(0 if results.get("success", False) else 1)


if __name__ == "__main__":
    asyncio.run(main())
