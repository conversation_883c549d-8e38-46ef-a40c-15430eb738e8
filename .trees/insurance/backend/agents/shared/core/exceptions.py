"""
Standard Exception Classes for Agent System

This module defines a comprehensive set of exception classes for the agent system,
providing structured error handling with proper categorization and context.

Key Features:
- Hierarchical exception structure
- Error categorization for monitoring
- Context preservation for debugging
- Tenant isolation error handling
- Performance and timeout errors
- Security and authorization errors

Usage:
    from backend.agents.shared.core.exceptions import (
        AgentExecutionError,
        AgentValidationError,
        AgentAuthorizationError
    )

    try:
        # Agent operation
        pass
    except AgentValidationError as e:
        logger.error(f"Validation failed: {e}")
        # Handle validation error
    except AgentExecutionError as e:
        logger.error(f"Execution failed: {e}")
        # Handle execution error
"""

from typing import Optional, Dict, Any
from enum import Enum


class ErrorCategory(str, Enum):
    """Standard error categories for monitoring and alerting."""

    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    CONFIGURATION = "configuration"
    EXECUTION = "execution"
    TIMEOUT = "timeout"
    RESOURCE = "resource"
    NETWORK = "network"
    STATE = "state"
    SECURITY = "security"


class BaseAgentError(Exception):
    """
    Base exception class for all agent errors.

    Provides common functionality for error categorization, context preservation,
    and structured error handling across the agent system.
    """

    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.EXECUTION,
        context: Optional[Dict[str, Any]] = None,
        tenant_id: Optional[str] = None,
        agent_type: Optional[str] = None,
        original_error: Optional[Exception] = None,
    ):
        """
        Initialize the base agent error.

        Args:
            message: Human-readable error message
            category: Error category for monitoring
            context: Additional context information
            tenant_id: Tenant ID for isolation tracking
            agent_type: Agent type where error occurred
            original_error: Original exception if this is a wrapper
        """
        super().__init__(message)
        self.message = message
        self.category = category
        self.context = context or {}
        self.tenant_id = tenant_id
        self.agent_type = agent_type
        self.original_error = original_error

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/monitoring."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "category": self.category.value,
            "context": self.context,
            "tenant_id": self.tenant_id,
            "agent_type": self.agent_type,
            "original_error": str(self.original_error) if self.original_error else None,
        }


class AgentExecutionError(BaseAgentError):
    """Raised when agent execution fails."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.EXECUTION, **kwargs)


class AgentConfigurationError(BaseAgentError):
    """Raised when agent configuration is invalid."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.CONFIGURATION, **kwargs)


class AgentValidationError(BaseAgentError):
    """Raised when input validation fails."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.VALIDATION, **kwargs)


class AgentAuthenticationError(BaseAgentError):
    """Raised when authentication fails."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.AUTHENTICATION, **kwargs)


class AgentAuthorizationError(BaseAgentError):
    """Raised when authorization fails."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.AUTHORIZATION, **kwargs)


class AgentTimeoutError(BaseAgentError):
    """Raised when agent execution times out."""

    def __init__(self, message: str, timeout_seconds: Optional[float] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.TIMEOUT, **kwargs)
        self.timeout_seconds = timeout_seconds


class AgentStateError(BaseAgentError):
    """Raised when agent state operations fail."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.STATE, **kwargs)


class AgentResourceError(BaseAgentError):
    """Raised when resource allocation or access fails."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.RESOURCE, **kwargs)


class AgentNetworkError(BaseAgentError):
    """Raised when network operations fail."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.NETWORK, **kwargs)


class AgentSecurityError(BaseAgentError):
    """Raised when security violations are detected."""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.SECURITY, **kwargs)


# Legacy compatibility - map old exception names to new ones
AgentError = BaseAgentError
AgentInitializationError = AgentConfigurationError
AgentCleanupError = AgentExecutionError


__all__ = [
    # Enums
    "ErrorCategory",
    # Base exception
    "BaseAgentError",
    # Specific exceptions
    "AgentExecutionError",
    "AgentConfigurationError",
    "AgentValidationError",
    "AgentAuthenticationError",
    "AgentAuthorizationError",
    "AgentTimeoutError",
    "AgentStateError",
    "AgentResourceError",
    "AgentNetworkError",
    "AgentSecurityError",
    # Legacy compatibility
    "AgentError",
    "AgentInitializationError",
    "AgentCleanupError",
]
