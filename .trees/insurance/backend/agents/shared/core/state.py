"""
Enhanced State Management for Agent System

This module provides comprehensive state management for the agent system,
including validation, tenant isolation, error handling, and performance tracking.

Key Features:
- Enhanced state classes with validation
- Tenant isolation and security
- Performance and execution tracking
- Error context preservation
- State transition management
- Memory and context handling

Usage:
    from backend.agents.shared.core.state import (
        BaseLangGraphState,
        AgentStatus,
        AgentExecutionContext
    )

    # Create execution context
    context = AgentExecutionContext(
        tenant_id="tenant-123",
        user_id="user-456",
        agent_type="research"
    )

    # Use in state
    state = BaseLangGraphState(
        messages=[],
        execution_context=context,
        status=AgentStatus.INITIALIZING
    )
"""

from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field, field_validator
from datetime import datetime, timezone
from enum import Enum
import uuid

# Import LangGraph types
try:
    from langchain_core.messages import BaseMessage
    from langgraph.graph.message import add_messages
    from typing_extensions import Annotated, TypedDict
except ImportError:
    # Fallback for type checking
    BaseMessage = Any

    def add_messages(x):
        return x

    def Annotated(x, y):
        return x

    class TypedDict(dict):
        pass


class AgentStatus(str, Enum):
    """Standard agent execution statuses."""

    PENDING = "pending"
    INITIALIZING = "initializing"
    EXECUTING = "executing"
    WAITING_FOR_INPUT = "waiting_for_input"
    PROCESSING_TOOLS = "processing_tools"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class ErrorCategory(str, Enum):
    """Standard error categories."""

    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    CONFIGURATION = "configuration"
    EXECUTION = "execution"
    TIMEOUT = "timeout"
    RESOURCE = "resource"
    NETWORK = "network"
    STATE = "state"
    SECURITY = "security"


class AgentExecutionContext(BaseModel):
    """
    Execution context for agent operations.

    Provides tenant isolation, user context, and execution tracking
    for all agent operations.
    """

    # Identity and isolation
    tenant_id: str = Field(..., description="Tenant ID for isolation")
    user_id: str = Field(..., description="User ID for authorization")
    thread_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Thread ID for conversation",
    )

    # Agent information
    agent_type: str = Field(..., description="Type of agent executing")
    agent_name: Optional[str] = Field(None, description="Name of agent instance")
    agent_version: str = Field(default="1.0.0", description="Agent version")

    # Execution tracking
    execution_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="Unique execution ID"
    )
    started_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Execution start time",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update time",
    )

    # Performance tracking
    max_execution_time: Optional[int] = Field(
        None, description="Maximum execution time in seconds"
    )
    iteration_count: int = Field(default=0, description="Current iteration count")
    max_iterations: int = Field(default=10, description="Maximum iterations allowed")

    # Security and authorization
    user_role: Optional[str] = Field(None, description="User role for authorization")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    security_context: Dict[str, Any] = Field(
        default_factory=dict, description="Security context"
    )

    @field_validator("tenant_id", "user_id", "agent_type")
    @classmethod
    def validate_required_fields(cls, v):
        """Validate required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Required field cannot be empty")
        return v.strip()

    @field_validator("max_execution_time")
    @classmethod
    def validate_execution_time(cls, v):
        """Validate execution time is positive."""
        if v is not None and v <= 0:
            raise ValueError("Max execution time must be positive")
        return v

    @field_validator("max_iterations")
    @classmethod
    def validate_max_iterations(cls, v):
        """Validate max iterations is positive."""
        if v <= 0:
            raise ValueError("Max iterations must be positive")
        return v

    def update_timestamp(self):
        """Update the last updated timestamp."""
        self.updated_at = datetime.now(timezone.utc)

    def increment_iteration(self):
        """Increment the iteration count and update timestamp."""
        self.iteration_count += 1
        self.update_timestamp()

    def is_timeout_exceeded(self) -> bool:
        """Check if execution time has exceeded the maximum."""
        if self.max_execution_time is None:
            return False

        elapsed = (datetime.now(timezone.utc) - self.started_at).total_seconds()
        return elapsed > self.max_execution_time

    def is_iteration_limit_exceeded(self) -> bool:
        """Check if iteration limit has been exceeded."""
        return self.iteration_count >= self.max_iterations

    def get_elapsed_time(self) -> float:
        """Get elapsed execution time in seconds."""
        return (datetime.now(timezone.utc) - self.started_at).total_seconds()


class AgentErrorContext(BaseModel):
    """Context information for agent errors."""

    error_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="Unique error ID"
    )
    category: ErrorCategory = Field(..., description="Error category")
    occurred_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Error occurrence time",
    )

    # Error details
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    stack_trace: Optional[str] = Field(None, description="Stack trace if available")

    # Context
    agent_state: Optional[Dict[str, Any]] = Field(
        None, description="Agent state when error occurred"
    )
    user_input: Optional[str] = Field(None, description="User input that caused error")
    execution_context: Optional[Dict[str, Any]] = Field(
        None, description="Execution context"
    )

    # Recovery information
    is_recoverable: bool = Field(
        default=False, description="Whether error is recoverable"
    )
    recovery_suggestions: List[str] = Field(
        default_factory=list, description="Recovery suggestions"
    )


class BaseLangGraphState(BaseModel):
    """
    Enhanced base state for LangGraph agents.

    This is the foundation for all agent states in the system, providing
    comprehensive state management with validation, tenant isolation,
    and execution tracking.
    """

    # Core LangGraph fields
    messages: List[Any] = Field(default_factory=list, description="List of messages")

    # Execution context and tracking
    execution_context: Optional[AgentExecutionContext] = Field(
        None, description="Execution context"
    )
    status: AgentStatus = Field(default=AgentStatus.PENDING, description="Agent status")

    # Memory and context
    memory: Dict[str, Any] = Field(default_factory=dict, description="Agent memory")
    context: Dict[str, Any] = Field(default_factory=dict, description="Agent context")

    # Error handling
    errors: List[AgentErrorContext] = Field(
        default_factory=list, description="Error list"
    )
    last_error: Optional[AgentErrorContext] = Field(None, description="Last error")

    # Tool and action tracking
    tool_calls: List[Dict[str, Any]] = Field(
        default_factory=list, description="Tool calls"
    )
    pending_actions: List[Dict[str, Any]] = Field(
        default_factory=list, description="Pending actions"
    )
    completed_actions: List[Dict[str, Any]] = Field(
        default_factory=list, description="Completed actions"
    )

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadata")

    # Next action
    next: Optional[str] = Field(None, description="Next action")

    class Config:
        """Pydantic configuration."""

        arbitrary_types_allowed = True


# Utility functions for state management
def create_execution_context(
    tenant_id: str, user_id: str, agent_type: str, **kwargs
) -> AgentExecutionContext:
    """
    Create a new execution context.

    Args:
        tenant_id: Tenant ID for isolation
        user_id: User ID for authorization
        agent_type: Type of agent
        **kwargs: Additional context fields

    Returns:
        New execution context
    """
    return AgentExecutionContext(
        tenant_id=tenant_id, user_id=user_id, agent_type=agent_type, **kwargs
    )


def create_error_context(
    category: ErrorCategory, error_type: str, error_message: str, **kwargs
) -> AgentErrorContext:
    """
    Create a new error context.

    Args:
        category: Error category
        error_type: Type of error
        error_message: Error message
        **kwargs: Additional error context fields

    Returns:
        New error context
    """
    return AgentErrorContext(
        category=category, error_type=error_type, error_message=error_message, **kwargs
    )


__all__ = [
    # Enums
    "AgentStatus",
    "ErrorCategory",
    # Models
    "AgentExecutionContext",
    "AgentErrorContext",
    "BaseLangGraphState",
    # Utility functions
    "create_execution_context",
    "create_error_context",
]
