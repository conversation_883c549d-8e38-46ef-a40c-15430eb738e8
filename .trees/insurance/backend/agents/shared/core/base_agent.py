"""
Enhanced Base Agent Class for LangGraph Agents

This module defines the enhanced BaseAgent class, which serves as the foundation for all
LangGraph agents in the system. It provides comprehensive lifecycle hooks, state
management, tool registration, execution functionality, and monitoring.

Key Features:
- Enhanced lifecycle hooks (initialize, execute, cleanup)
- Comprehensive state management with validation
- Tool registration and execution with error handling
- Tenant isolation and security
- Performance monitoring and metrics
- Error handling and recovery
- Configuration management
- Async/await support throughout

Usage:
    from backend.agents.shared.core.base_agent import BaseAgent
    from backend.agents.shared.core.state import AgentStatus

    class MyAgent(BaseAgent):
        async def initialize(self, state, config):
            # Initialize agent-specific state
            state["status"] = AgentStatus.INITIALIZING
            return state

        async def execute(self, state, config):
            # Execute agent-specific logic
            state["status"] = AgentStatus.EXECUTING
            # ... agent logic ...
            state["status"] = AgentStatus.COMPLETED
            return state

        async def cleanup(self, state, config):
            # Cleanup agent-specific resources
            return state
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Type, List, Callable
from datetime import datetime, timezone
import logging
import uuid
import asyncio
import time

# LangGraph imports - these are required dependencies
from langgraph.graph import StateGraph
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

# Local imports
from .state import (
    BaseLangGraphState,
    AgentStatus,
    AgentExecutionContext,
    AgentErrorContext,
    ErrorCategory,
    create_execution_context,
    create_error_context,
)
from .exceptions import (
    BaseAgentError,
    AgentExecutionError,
    AgentConfigurationError,
    AgentValidationError,
    AgentAuthorizationError,
    AgentTimeoutError,
    AgentStateError,
)

# Import utilities with fallbacks
try:
    from ..utils.validation import validate_tenant_access, validate_agent_config
    from ..utils.logging import StructuredLogger
    from ..utils.monitoring import AgentMetrics
    from ..utils.error_handling import ErrorHandler
except ImportError:
    # Fallback implementations for testing
    def validate_tenant_access(tenant_id, user_id):
        return True

    def validate_agent_config(config):
        pass

    class StructuredLogger:
        def __init__(self, name):
            self.name = name

        def info(self, msg, **kwargs):
            print(f"INFO: {msg}")

        def error(self, msg, **kwargs):
            print(f"ERROR: {msg}")

        def warning(self, msg, **kwargs):
            print(f"WARNING: {msg}")

    class AgentMetrics:
        def __init__(self, name, type):
            pass

        def record_execution_start(self):
            pass

        def record_execution_success(self, time):
            pass

        def record_execution_failure(self, time, error):
            pass

        def record_error(self, type, msg):
            pass

        def get_metrics(self):
            return {}

    class ErrorHandler:
        def __init__(self, name):
            pass


# Set up logging
logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    Enhanced base class for all LangGraph agents.

    This class provides comprehensive lifecycle hooks, state management, tool registration,
    execution functionality, monitoring, and error handling for LangGraph agents.

    Key Features:
    - Lifecycle management with proper error handling
    - State validation and tenant isolation
    - Performance monitoring and metrics
    - Tool registration and execution
    - Configuration management
    - Security and authorization
    """

    def __init__(
        self,
        agent_type: str,
        agent_name: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        """
        Initialize the enhanced base agent.

        Args:
            agent_type: Type of agent (e.g., "research", "intake", "document")
            agent_name: Name of agent instance (defaults to class name)
            config: Agent configuration dictionary
            **kwargs: Additional initialization parameters
        """
        # Basic properties
        self.agent_type = agent_type
        self.agent_name = agent_name or self.__class__.__name__
        self.config = config or {}

        # Validation
        validate_agent_config(self.config)

        # Initialize components
        self.logger = StructuredLogger(self.agent_name)
        self.metrics = AgentMetrics(self.agent_name, self.agent_type)
        self.error_handler = ErrorHandler(self.agent_name)

        # State and execution tracking
        self._current_execution_context: Optional[AgentExecutionContext] = None
        self._tools: Dict[str, Callable] = {}
        self._middleware: List[Callable] = []

        # Performance tracking
        self._execution_start_time: Optional[float] = None
        self._iteration_count: int = 0

        # Register default tools and middleware
        self._register_default_tools()
        self._register_default_middleware()

        self.logger.info(f"Initialized {self.agent_type} agent: {self.agent_name}")

    @property
    def current_execution_context(self) -> Optional[AgentExecutionContext]:
        """Get the current execution context."""
        return self._current_execution_context

    @property
    def is_executing(self) -> bool:
        """Check if agent is currently executing."""
        return self._current_execution_context is not None

    def _register_default_tools(self):
        """Register default tools available to all agents."""
        # This will be implemented based on existing tool system
        pass

    def _register_default_middleware(self):
        """Register default middleware for all agents."""
        # Add tenant validation middleware
        self._middleware.append(self._validate_tenant_middleware)
        # Add performance tracking middleware
        self._middleware.append(self._performance_tracking_middleware)
        # Add error handling middleware
        self._middleware.append(self._error_handling_middleware)

    async def _validate_tenant_middleware(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Middleware to validate tenant access."""
        execution_context = state.get("execution_context")
        if execution_context:
            tenant_id = execution_context.tenant_id
            user_id = execution_context.user_id

            if not validate_tenant_access(tenant_id, user_id):
                raise AgentAuthorizationError(
                    f"User {user_id} does not have access to tenant {tenant_id}",
                    tenant_id=tenant_id,
                    agent_type=self.agent_type,
                )

        return state

    async def _performance_tracking_middleware(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Middleware to track performance metrics."""
        execution_context = state.get("execution_context")
        if execution_context:
            # Update iteration count
            execution_context.increment_iteration()

            # Check for timeout
            if execution_context.is_timeout_exceeded():
                raise AgentTimeoutError(
                    f"Agent execution exceeded maximum time: {execution_context.max_execution_time}s",
                    timeout_seconds=execution_context.max_execution_time,
                    tenant_id=execution_context.tenant_id,
                    agent_type=self.agent_type,
                )

            # Check for iteration limit
            if execution_context.is_iteration_limit_exceeded():
                raise AgentExecutionError(
                    f"Agent exceeded maximum iterations: {execution_context.max_iterations}",
                    tenant_id=execution_context.tenant_id,
                    agent_type=self.agent_type,
                )

        return state

    async def _error_handling_middleware(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Middleware to handle errors and update state."""
        try:
            return state
        except BaseAgentError as e:
            # Add error to state
            error_context = create_error_context(
                category=e.category,
                error_type=e.__class__.__name__,
                error_message=str(e),
                agent_state=state,
                execution_context=e.to_dict(),
            )

            if "errors" not in state:
                state["errors"] = []
            state["errors"].append(error_context)
            state["last_error"] = error_context
            state["status"] = AgentStatus.FAILED

            # Log error
            self.logger.error(f"Agent error: {e}", extra=e.to_dict())

            # Record metrics
            self.metrics.record_error(e.category.value, str(e))

            raise

    def create_execution_context(
        self, tenant_id: str, user_id: str, thread_id: Optional[str] = None, **kwargs
    ) -> AgentExecutionContext:
        """
        Create a new execution context for this agent.

        Args:
            tenant_id: Tenant ID for isolation
            user_id: User ID for authorization
            thread_id: Thread ID (optional, will be generated if not provided)
            **kwargs: Additional context fields

        Returns:
            New execution context
        """
        return create_execution_context(
            tenant_id=tenant_id,
            user_id=user_id,
            agent_type=self.agent_type,
            agent_name=self.agent_name,
            thread_id=thread_id or str(uuid.uuid4()),
            **kwargs,
        )

    def create_initial_state(
        self,
        execution_context: AgentExecutionContext,
        messages: Optional[List[BaseMessage]] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Create initial state for agent execution.

        Args:
            execution_context: Execution context
            messages: Initial messages (optional)
            **kwargs: Additional state fields

        Returns:
            Initial state dictionary
        """
        return {
            "messages": messages or [],
            "execution_context": execution_context,
            "status": AgentStatus.PENDING,
            "memory": {},
            "context": {},
            "errors": [],
            "last_error": None,
            "tool_calls": [],
            "pending_actions": [],
            "completed_actions": [],
            "metadata": {},
            "next": None,
            **kwargs,
        }

    async def _apply_middleware(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Apply all registered middleware to the state."""
        for middleware in self._middleware:
            state = await middleware(state, config)
        return state

    @abstractmethod
    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Initialize the agent.

        This method is called before the agent executes. It should set up
        any agent-specific state and prepare for execution.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        pass

    @abstractmethod
    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Execute the agent.

        This method is called to execute the agent's main logic.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        pass

    @abstractmethod
    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Clean up the agent.

        This method is called after the agent executes. It should clean up
        any agent-specific resources.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        pass

    async def invoke(
        self, input_data: Dict[str, Any], config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """
        Invoke the agent with comprehensive lifecycle management.

        This method is the main entry point for agent execution. It manages
        the complete lifecycle including initialization, execution, cleanup,
        error handling, and performance tracking.

        Args:
            input_data: Input data for the agent
            config: Runnable configuration

        Returns:
            Agent execution result
        """
        config = config or {}
        configurable = config.get("configurable", {})

        # Extract required context information
        tenant_id = configurable.get("tenant_id")
        user_id = configurable.get("user_id")
        thread_id = configurable.get("thread_id")

        if not tenant_id or not user_id:
            raise AgentValidationError(
                "tenant_id and user_id are required in config.configurable",
                agent_type=self.agent_type,
            )

        # Create execution context
        execution_context = self.create_execution_context(
            tenant_id=tenant_id, user_id=user_id, thread_id=thread_id
        )

        # Set current execution context
        self._current_execution_context = execution_context

        try:
            # Start performance tracking
            start_time = time.time()
            self.metrics.record_execution_start()

            # Create initial state
            state = self.create_initial_state(
                execution_context=execution_context, **input_data
            )

            # Apply middleware
            state = await self._apply_middleware(state, config)

            # Execute lifecycle hooks
            state["status"] = AgentStatus.INITIALIZING
            state = await self.initialize(state, config)

            state["status"] = AgentStatus.EXECUTING
            state = await self.execute(state, config)

            state["status"] = AgentStatus.COMPLETED
            state = await self.cleanup(state, config)

            # Record success metrics
            execution_time = time.time() - start_time
            self.metrics.record_execution_success(execution_time)

            self.logger.info(
                f"Agent execution completed successfully",
                extra={
                    "execution_time": execution_time,
                    "iterations": execution_context.iteration_count,
                    "tenant_id": tenant_id,
                    "agent_type": self.agent_type,
                },
            )

            return state

        except BaseAgentError as e:
            # Handle agent-specific errors
            execution_time = time.time() - start_time
            self.metrics.record_execution_failure(execution_time, e.category.value)

            self.logger.error(
                f"Agent execution failed: {e}",
                extra={
                    "execution_time": execution_time,
                    "error_category": e.category.value,
                    "tenant_id": tenant_id,
                    "agent_type": self.agent_type,
                },
            )

            raise

        except Exception as e:
            # Handle unexpected errors
            execution_time = time.time() - start_time
            self.metrics.record_execution_failure(execution_time, "unexpected")

            self.logger.error(
                f"Unexpected error in agent execution: {e}",
                extra={
                    "execution_time": execution_time,
                    "error_type": e.__class__.__name__,
                    "tenant_id": tenant_id,
                    "agent_type": self.agent_type,
                },
            )

            # Wrap in agent error
            raise AgentExecutionError(
                f"Unexpected error: {str(e)}",
                tenant_id=tenant_id,
                agent_type=self.agent_type,
                original_error=e,
            )

        finally:
            # Clear current execution context
            self._current_execution_context = None

    async def __call__(
        self, state: Dict[str, Any], config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """
        Make the agent callable for LangGraph.

        This method allows the agent to be used directly in a LangGraph StateGraph.
        It executes the agent's lifecycle hooks in sequence with proper error handling.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        config = config or {}

        try:
            # Apply middleware
            state = await self._apply_middleware(state, config)

            # Execute lifecycle hooks
            if state.get("status") != AgentStatus.INITIALIZING:
                state["status"] = AgentStatus.INITIALIZING
                state = await self.initialize(state, config)

            state["status"] = AgentStatus.EXECUTING
            state = await self.execute(state, config)

            state["status"] = AgentStatus.COMPLETED
            state = await self.cleanup(state, config)

            return state

        except BaseAgentError:
            # Re-raise agent errors as-is
            raise

        except Exception as e:
            # Wrap unexpected errors
            execution_context = state.get("execution_context")
            tenant_id = execution_context.tenant_id if execution_context else None

            raise AgentExecutionError(
                f"Unexpected error in agent call: {str(e)}",
                tenant_id=tenant_id,
                agent_type=self.agent_type,
                original_error=e,
            )

    def create_graph(self) -> StateGraph:
        """
        Create a LangGraph StateGraph for this agent.

        Returns:
            Configured StateGraph
        """
        from typing import Dict, Any, TypedDict

        # Create a simple state schema that works with LangGraph 0.2.50
        class SimpleAgentState(TypedDict):
            messages: list
            tenant_id: str
            user_id: str
            thread_id: str
            status: str

        # Create the graph with the simple state schema
        graph = StateGraph(SimpleAgentState)

        # Add the agent as a node
        graph.add_node(self.agent_name, self)

        # Set entry point
        graph.set_entry_point(self.agent_name)

        # Set finish point
        graph.set_finish_point(self.agent_name)

        return graph.compile()

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get current metrics for this agent.

        Returns:
            Dictionary of metrics
        """
        return self.metrics.get_metrics()

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get health status for this agent.

        Returns:
            Dictionary of health information
        """
        return {
            "agent_name": self.agent_name,
            "agent_type": self.agent_type,
            "is_executing": self.is_executing,
            "current_execution_context": (
                self._current_execution_context.dict()
                if self._current_execution_context
                else None
            ),
            "metrics": self.get_metrics(),
            "config": self.config,
        }


__all__ = [
    "BaseAgent",
]
