"""
Tests for BaseAgent Class

This module tests the enhanced BaseAgent class functionality,
including lifecycle management, error handling, and performance tracking.
"""

import unittest
import asyncio
from unittest.mock import patch, MagicMock
from typing import Dict, Any

from ..core.base_agent import BaseAgent
from ..core.state import AgentStatus, AgentExecutionContext
from ..core.exceptions import (
    AgentExecutionError,
    AgentValidationError,
    AgentAuthorizationError,
    AgentTimeoutError,
)
from ..testing.base_test import BaseAgentTest
from ..testing.fixtures import create_test_context, create_test_state
from ..testing.mocks import MockAgent

# Temporarily remove the shim import to test the original error


class TestAgent(BaseAgent):
    """Test agent implementation for testing."""

    def __init__(self, **kwargs):
        super().__init__(agent_type="test", **kwargs)
        self.initialize_called = False
        self.execute_called = False
        self.cleanup_called = False

    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        self.initialize_called = True
        state["initialized"] = True
        return state

    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        self.execute_called = True
        state["executed"] = True
        return state

    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        self.cleanup_called = True
        state["cleaned_up"] = True
        return state


class TestFailingAgent(BaseAgent):
    """Test agent that fails during execution."""

    def __init__(self, **kwargs):
        super().__init__(agent_type="test_failing", **kwargs)

    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        return state

    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        raise AgentExecutionError("Test execution failure")

    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        return state


class TestBaseAgent(BaseAgentTest, unittest.TestCase):
    """Test cases for BaseAgent class."""

    def setUp(self):
        super().setUp()
        self.agent = TestAgent()
        self.test_tenant_id = "test-tenant-123"
        self.test_user_id = "test-user-456"

    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.agent_type, "test")
        self.assertEqual(self.agent.agent_name, "TestAgent")
        self.assertIsNotNone(self.agent.logger)
        self.assertIsNotNone(self.agent.metrics)
        self.assertIsNotNone(self.agent.error_handler)

    def test_create_execution_context(self):
        """Test execution context creation."""
        context = self.agent.create_execution_context(
            tenant_id=self.test_tenant_id, user_id=self.test_user_id
        )

        self.assertIsInstance(context, AgentExecutionContext)
        self.assertEqual(context.tenant_id, self.test_tenant_id)
        self.assertEqual(context.user_id, self.test_user_id)
        self.assertEqual(context.agent_type, "test")
        self.assertEqual(context.agent_name, "TestAgent")

    def test_create_initial_state(self):
        """Test initial state creation."""
        context = self.agent.create_execution_context(
            tenant_id=self.test_tenant_id, user_id=self.test_user_id
        )

        state = self.agent.create_initial_state(context)

        # Assert that state is a dictionary with expected keys
        self.assertIsInstance(state, dict)
        self.assertEqual(state["status"], AgentStatus.PENDING)
        self.assertEqual(state["execution_context"], context)

    async def test_agent_lifecycle(self):
        """Test complete agent lifecycle."""
        # Test agent lifecycle by invoking it and checking lifecycle methods were called
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        # Reset lifecycle flags
        self.agent.initialize_called = False
        self.agent.execute_called = False
        self.agent.cleanup_called = False

        # Invoke agent to trigger lifecycle
        await self.agent.invoke(input_data, config)

        # Verify lifecycle methods were called
        self.assertTrue(self.agent.initialize_called)
        self.assertTrue(self.agent.execute_called)
        self.assertTrue(self.agent.cleanup_called)

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    def test_agent_lifecycle_sync(self, mock_validate):
        """Test agent lifecycle synchronously."""
        # Mock tenant validation to pass so we can test lifecycle
        mock_validate.return_value = True
        asyncio.run(self.test_agent_lifecycle())

    async def test_agent_invoke(self):
        """Test agent invoke method."""
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        result = await self.agent.invoke(input_data, config)

        # Assert that result is a dictionary with expected success indicators
        self.assertIsInstance(result, dict)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    def test_agent_invoke_sync(self, mock_validate):
        """Test agent invoke method synchronously."""
        # Mock tenant validation to pass so we can test invoke method
        mock_validate.return_value = True
        asyncio.run(self.test_agent_invoke())

    async def test_agent_call(self):
        """Test agent __call__ method."""
        # Create test state as dictionary (as expected by agent.__call__)
        state = {
            "messages": [],
            "tenant_id": self.test_tenant_id,
            "user_id": self.test_user_id,
        }
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        result = await self.agent(state, config)

        # Assert that result is a dictionary with expected keys
        self.assertIsInstance(result, dict)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))

    def test_agent_call_sync(self):
        """Test agent __call__ method synchronously."""
        asyncio.run(self.test_agent_call())

    async def test_agent_validation_error(self):
        """Test agent validation error handling."""
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                # Missing tenant_id and user_id
            }
        }

        with self.assertRaises(AgentValidationError):
            await self.agent.invoke(input_data, config)

    def test_agent_validation_error_sync(self):
        """Test agent validation error handling synchronously."""
        asyncio.run(self.test_agent_validation_error())

    async def test_agent_execution_error(self):
        """Test agent execution error handling."""
        failing_agent = TestFailingAgent()

        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        with self.assertRaises(AgentExecutionError):
            await failing_agent.invoke(input_data, config)

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    def test_agent_execution_error_sync(self, mock_validate):
        """Test agent execution error handling synchronously."""
        # Mock tenant validation to pass so we can test execution error
        mock_validate.return_value = True
        asyncio.run(self.test_agent_execution_error())

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    async def test_tenant_validation_middleware(self, mock_validate):
        """Test tenant validation middleware."""
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        # Test successful validation
        mock_validate.return_value = True

        result = await self.agent.invoke(input_data, config)
        # Assert that result is a dictionary with expected success indicators
        self.assertIsInstance(result, dict)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))

        # Test failed validation
        mock_validate.return_value = False

        with self.assertRaises(AgentAuthorizationError):
            await self.agent.invoke(input_data, config)

    def test_tenant_validation_middleware_sync(self):
        """Test tenant validation middleware synchronously."""
        asyncio.run(self.test_tenant_validation_middleware())

    async def test_timeout_middleware(self):
        """Test timeout middleware."""
        # Create agent with very short timeout
        from datetime import datetime, timezone, timedelta

        context = AgentExecutionContext(
            tenant_id=self.test_tenant_id,
            user_id=self.test_user_id,
            agent_type="test",
            agent_name="TestAgent",
            max_execution_time=1,  # 1 second timeout
            started_at=datetime.now(timezone.utc)
            - timedelta(seconds=2),  # already exceeded
        )

        # Create test state as dictionary
        state = {"messages": [], "execution_context": context, "status": "pending"}

        # Add delay to agent execution
        class SlowAgent(TestAgent):
            async def execute(self, state, config):
                await asyncio.sleep(
                    2.0
                )  # 2 second delay (longer than 1 second timeout)
                return await super().execute(state, config)

        slow_agent = SlowAgent()

        with self.assertRaises(AgentTimeoutError):
            await slow_agent(state, {})

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    def test_timeout_middleware_sync(self, mock_validate):
        """Test timeout middleware synchronously."""
        # Mock tenant validation to pass so we can test timeout middleware
        mock_validate.return_value = True
        asyncio.run(self.test_timeout_middleware())

    def test_create_graph(self):
        """Test graph creation."""
        graph = self.agent.create_graph()

        self.assertIsNotNone(graph)
        # Additional graph validation would go here

    def test_get_metrics(self):
        """Test metrics retrieval."""
        metrics = self.agent.get_metrics()

        self.assertIsInstance(metrics, dict)
        self.assertIn("agent_name", metrics)
        self.assertIn("agent_type", metrics)

    def test_get_health_status(self):
        """Test health status retrieval."""
        health = self.agent.get_health_status()

        self.assertIsInstance(health, dict)
        self.assertIn("agent_name", health)
        self.assertIn("agent_type", health)
        self.assertIn("is_executing", health)
        self.assertIn("metrics", health)

    def test_agent_configuration(self):
        """Test agent configuration validation."""
        # Test basic agent configuration
        self.assertIsNotNone(self.agent.config)
        self.assertEqual(self.agent.agent_type, "test")
        self.assertEqual(self.agent.agent_name, "TestAgent")

    def test_tenant_isolation(self):
        """Test tenant isolation enforcement."""
        # This test would verify that agents properly isolate tenant data
        # Implementation depends on specific tenant isolation requirements
        pass

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    def test_error_handling(self, mock_validate):
        """Test error handling capabilities."""
        # Mock tenant validation to pass so we can test error handling
        mock_validate.return_value = True

        # Test that errors are properly caught and handled
        failing_agent = TestFailingAgent()

        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        try:
            asyncio.run(failing_agent.invoke(input_data, config))
            self.fail("Expected AgentExecutionError")
        except AgentExecutionError:
            pass  # Expected

    @patch("backend.agents.shared.core.base_agent.validate_tenant_access")
    def test_performance(self, mock_validate):
        """Test performance characteristics."""
        # Mock tenant validation to pass so we can test performance
        mock_validate.return_value = True

        # Run performance test
        import time

        start_time = time.time()

        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }

        asyncio.run(self.agent.invoke(input_data, config))
        execution_time = time.time() - start_time

        # Assert reasonable execution time (should be under 1 second)
        self.assertLess(
            execution_time,
            1.0,
            f"Agent execution took {execution_time:.3f}s, expected < 1.0s",
        )


class TestMockAgent(BaseAgentTest, unittest.TestCase):
    """Test cases for MockAgent."""

    def setUp(self):
        super().setUp()
        self.mock_agent = MockAgent()

    def test_mock_agent_basic_functionality(self):
        """Test basic mock agent functionality."""
        result = self.run_agent_test_sync(self.mock_agent)

        self.assert_agent_success(result)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))

    def test_mock_agent_call_history(self):
        """Test mock agent call history tracking."""
        self.run_agent_test_sync(self.mock_agent)

        history = self.mock_agent.get_call_history()

        self.assertEqual(len(history["initialize"]), 1)
        self.assertEqual(len(history["execute"]), 1)
        self.assertEqual(len(history["cleanup"]), 1)

    def test_mock_agent_failure_simulation(self):
        """Test mock agent failure simulation."""
        self.mock_agent.set_should_fail(True, "Test failure")

        with self.assertRaises(AgentExecutionError):
            self.run_agent_test_sync(self.mock_agent)

    def test_mock_agent_delay_simulation(self):
        """Test mock agent delay simulation."""
        self.mock_agent.set_execution_delay(0.1)  # 100ms delay

        import time

        start_time = time.time()
        self.run_agent_test_sync(self.mock_agent)
        execution_time = time.time() - start_time

        # Should take at least 300ms (100ms * 3 lifecycle methods)
        self.assertGreaterEqual(execution_time, 0.3)


if __name__ == "__main__":
    unittest.main()
