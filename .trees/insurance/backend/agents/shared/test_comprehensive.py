#!/usr/bin/env python3
"""
Comprehensive test suite for the shared agent infrastructure.

This script runs a complete validation of all shared components including
integration tests, performance tests, and edge case handling.
"""

import asyncio
import sys
import traceback
import time
from typing import Dict, Any
import pytest

# Mark all tests in this module as integration tests
pytestmark = pytest.mark.integration

# Add the current directory to Python path
sys.path.insert(0, ".")

try:
    # Core imports
    from backend.agents.shared.core.base_agent import BaseAgent
    from backend.agents.shared.core.state import (
        AgentStatus,
        AgentExecutionContext,
        create_execution_context,
    )
    from backend.agents.shared.core.exceptions import (
        AgentExecutionError,
        AgentValidationError,
        AgentAuthorizationError,
        AgentTimeoutError,
    )

    # Utility imports
    from utils.validation import validate_tenant_access, validate_agent_config
    from backend.utils.logging import StructuredLogger
    from utils.monitoring import AgentMetrics, PerformanceTracker
    from utils.error_handling import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorRecoveryStrategy

    # Testing imports
    from testing.base_test import BaseAgentTest
    from testing.fixtures import create_test_context, create_test_state, sample_messages
    from testing.mocks import <PERSON>ckA<PERSON>, MockLLM, MockTool

    print("✓ Successfully imported all shared infrastructure modules")
except ImportError as e:
    print(f"✗ Failed to import modules: {e}")
    traceback.print_exc()
    # Don't exit during test collection
    # sys.exit(1)


class ComprehensiveTestAgent(BaseAgent):
    """Comprehensive test agent with all features."""

    def __init__(self):
        super().__init__(
            agent_type="comprehensive_test", agent_name="ComprehensiveTestAgent"
        )
        self.operations_performed = []

    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        """Initialize with comprehensive setup."""
        self.operations_performed.append("initialize")

        # Validate input
        if not state.get("execution_context"):
            raise AgentValidationError("Missing execution context")

        # Set up agent state
        state["initialized"] = True
        state["initialization_time"] = time.time()

        # Log initialization
        self.logger.info(
            "Agent initialized",
            extra={
                "tenant_id": state["execution_context"].tenant_id,
                "user_id": state["execution_context"].user_id,
            },
        )

        return state

    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        """Execute with comprehensive processing."""
        self.operations_performed.append("execute")

        # Simulate processing time
        await asyncio.sleep(0.05)

        # Process messages
        messages = state.get("messages", [])
        processed_messages = []

        for message in messages:
            processed_message = {
                "original": message,
                "processed": True,
                "timestamp": time.time(),
            }
            processed_messages.append(processed_message)

        # Update state
        state["executed"] = True
        state["processed_messages"] = processed_messages
        state["execution_time"] = time.time()
        state["result"] = {
            "status": "success",
            "messages_processed": len(processed_messages),
            "operations": self.operations_performed.copy(),
        }

        # Log execution
        self.logger.info(
            "Agent executed successfully",
            extra={
                "messages_processed": len(processed_messages),
                "execution_time": state["execution_time"]
                - state.get("initialization_time", 0),
            },
        )

        return state

    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        """Cleanup with comprehensive teardown."""
        self.operations_performed.append("cleanup")

        # Perform cleanup operations
        state["cleaned_up"] = True
        state["cleanup_time"] = time.time()

        # Log cleanup
        self.logger.info(
            "Agent cleanup completed",
            extra={"total_operations": len(self.operations_performed)},
        )

        return state


async def test_comprehensive_agent_functionality():
    """Test comprehensive agent functionality."""
    print("\n--- Testing Comprehensive Agent Functionality ---")

    try:
        agent = ComprehensiveTestAgent()

        # Create test data
        context = create_test_context(
            tenant_id="comp-test-tenant",
            user_id="comp-test-user",
            agent_type="comprehensive_test",
        )

        messages = sample_messages("research")

        input_data = {"messages": messages, "test_data": {"key": "value"}}

        config = {
            "configurable": {
                "tenant_id": context.tenant_id,
                "user_id": context.user_id,
                "thread_id": context.thread_id,
            }
        }

        # Execute agent
        result = await agent.invoke(input_data, config)

        # Validate results
        assert result["status"] == AgentStatus.COMPLETED
        assert result["initialized"] == True
        assert result["executed"] == True
        assert result["cleaned_up"] == True
        assert "result" in result
        assert result["result"]["status"] == "success"
        assert len(result["processed_messages"]) == len(messages)

        # Validate operations
        expected_operations = ["initialize", "execute", "cleanup"]
        assert agent.operations_performed == expected_operations

        print("✓ Comprehensive agent functionality test passed")
        return True

    except Exception as e:
        print(f"✗ Comprehensive agent test failed: {e}")
        traceback.print_exc()
        return False


async def test_error_recovery_scenarios():
    """Test various error recovery scenarios."""
    print("\n--- Testing Error Recovery Scenarios ---")

    try:
        # Test timeout recovery
        class TimeoutAgent(BaseAgent):
            def __init__(self):
                super().__init__(agent_type="timeout_test")

            async def initialize(self, state, config):
                return state

            async def execute(self, state, config):
                # Simulate long operation
                await asyncio.sleep(0.2)
                return state

            async def cleanup(self, state, config):
                return state

        timeout_agent = TimeoutAgent()

        # Create context with very short timeout
        timeout_context = create_test_context(max_execution_time=0.1)
        timeout_state = create_test_state(timeout_context)

        try:
            await timeout_agent(timeout_state, {})
            print("✗ Expected timeout error but none occurred")
            return False
        except AgentTimeoutError:
            print("✓ Timeout error properly handled")

        # Test validation error recovery
        try:
            await timeout_agent.invoke({}, {})  # Missing required config
            print("✗ Expected validation error but none occurred")
            return False
        except AgentValidationError:
            print("✓ Validation error properly handled")

        # Test error handler recovery
        error_handler = ErrorHandler("test_recovery")

        def failing_operation():
            raise Exception("Simulated failure")

        def recovery_operation():
            return "recovered"

        recovery_result = error_handler.handle_error(
            Exception("Test error"),
            {"operation": recovery_operation},
            ErrorRecoveryStrategy.RETRY,
        )

        assert recovery_result.success == True
        assert recovery_result.final_result == "recovered"
        print("✓ Error recovery strategy works")

        return True

    except Exception as e:
        print(f"✗ Error recovery test failed: {e}")
        traceback.print_exc()
        return False


async def test_performance_characteristics():
    """Test performance characteristics."""
    print("\n--- Testing Performance Characteristics ---")

    try:
        agent = ComprehensiveTestAgent()

        # Performance test configuration
        num_iterations = 10
        max_avg_time = 0.2  # 200ms average

        execution_times = []

        for i in range(num_iterations):
            context = create_test_context(
                tenant_id=f"perf-tenant-{i}", user_id=f"perf-user-{i}"
            )

            input_data = {"messages": sample_messages("simple"), "iteration": i}

            config = {
                "configurable": {
                    "tenant_id": context.tenant_id,
                    "user_id": context.user_id,
                }
            }

            start_time = time.time()
            result = await agent.invoke(input_data, config)
            execution_time = time.time() - start_time

            execution_times.append(execution_time)

            # Validate result
            assert result["status"] == AgentStatus.COMPLETED

        # Calculate performance metrics
        avg_time = sum(execution_times) / len(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)

        print(f"✓ Performance test completed:")
        print(f"  - Iterations: {num_iterations}")
        print(f"  - Average time: {avg_time:.3f}s")
        print(f"  - Min time: {min_time:.3f}s")
        print(f"  - Max time: {max_time:.3f}s")

        # Validate performance
        if avg_time > max_avg_time:
            print(
                f"✗ Average execution time {avg_time:.3f}s exceeds limit {max_avg_time}s"
            )
            return False

        print("✓ Performance characteristics are acceptable")
        return True

    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        traceback.print_exc()
        return False


async def test_concurrent_execution():
    """Test concurrent agent execution."""
    print("\n--- Testing Concurrent Execution ---")

    try:
        # Create multiple agents
        agents = [ComprehensiveTestAgent() for _ in range(5)]

        async def run_agent(agent, agent_id):
            """Run a single agent."""
            context = create_test_context(
                tenant_id=f"concurrent-tenant-{agent_id}",
                user_id=f"concurrent-user-{agent_id}",
            )

            input_data = {"messages": sample_messages("intake"), "agent_id": agent_id}

            config = {
                "configurable": {
                    "tenant_id": context.tenant_id,
                    "user_id": context.user_id,
                }
            }

            result = await agent.invoke(input_data, config)
            return result

        # Run agents concurrently
        start_time = time.time()
        tasks = [run_agent(agent, i) for i, agent in enumerate(agents)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time

        # Validate results
        for i, result in enumerate(results):
            assert result["status"] == AgentStatus.COMPLETED
            assert result["result"]["status"] == "success"

        print(f"✓ Concurrent execution test completed:")
        print(f"  - Agents: {len(agents)}")
        print(f"  - Total time: {total_time:.3f}s")
        print(f"  - Average per agent: {total_time/len(agents):.3f}s")

        return True

    except Exception as e:
        print(f"✗ Concurrent execution test failed: {e}")
        traceback.print_exc()
        return False


async def test_mock_integration():
    """Test integration with mock objects."""
    print("\n--- Testing Mock Integration ---")

    try:
        # Test mock agent
        mock_agent = MockAgent()
        mock_agent.set_execution_delay(0.05)

        result = await mock_agent.invoke(
            {"test": "data"},
            {"configurable": {"tenant_id": "mock-tenant", "user_id": "mock-user"}},
        )

        assert result["status"] == AgentStatus.COMPLETED
        assert result["mock_result"] == "Mock execution completed"

        # Verify call history
        history = mock_agent.get_call_history()
        assert len(history["initialize"]) == 1
        assert len(history["execute"]) == 1
        assert len(history["cleanup"]) == 1

        print("✓ Mock agent integration works")

        # Test mock LLM
        mock_llm = MockLLM()
        mock_llm.set_response("Mock LLM response")

        response = await mock_llm.generate("Test prompt")
        assert response == "Mock LLM response"
        assert mock_llm.get_call_count() == 1

        print("✓ Mock LLM integration works")

        # Test mock tool
        mock_tool = MockTool("test_tool", {"result": "mock_tool_result"})

        tool_result = await mock_tool.execute("test_input")
        assert tool_result["result"] == "mock_tool_result"
        assert mock_tool.get_call_count() == 1

        print("✓ Mock tool integration works")

        return True

    except Exception as e:
        print(f"✗ Mock integration test failed: {e}")
        traceback.print_exc()
        return False


async def main():
    """Run comprehensive test suite."""
    print("🧪 Starting Comprehensive Shared Infrastructure Tests")
    print("=" * 60)

    tests = [
        test_comprehensive_agent_functionality,
        test_error_recovery_scenarios,
        test_performance_characteristics,
        test_concurrent_execution,
        test_mock_integration,
    ]

    results = []
    start_time = time.time()

    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            traceback.print_exc()
            results.append(False)

    total_time = time.time() - start_time

    print("\n" + "=" * 60)
    print("🏁 Comprehensive Test Results Summary")
    print("=" * 60)

    passed = sum(results)
    total = len(results)

    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Total execution time: {total_time:.2f}s")

    if passed == total:
        print("🎉 All comprehensive tests passed!")
        print("✅ Shared agent infrastructure is fully functional and ready for use.")
        return 0
    else:
        print("❌ Some comprehensive tests failed.")
        print("🔧 Please review and fix the issues before proceeding.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
