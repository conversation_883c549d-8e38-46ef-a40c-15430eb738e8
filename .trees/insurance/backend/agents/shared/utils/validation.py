"""
Validation Utilities for Agent System

This module provides comprehensive validation utilities for the agent system,
including tenant access control, input validation, and configuration validation.

Key Features:
- Tenant isolation validation
- User authorization checks
- Input data validation
- Configuration validation
- Security validation
- Performance validation

Usage:
    from backend.agents.shared.utils.validation import (
        validate_tenant_access,
        validate_agent_config,
        validate_input_data
    )

    # Validate tenant access
    if not validate_tenant_access(tenant_id, user_id):
        raise AgentAuthorizationError("Access denied")

    # Validate configuration
    validate_agent_config(config)

    # Validate input data
    validate_input_data(input_data, schema)
"""

from typing import Any, Dict, Optional, List, Union
from pydantic import ValidationError
import re
import logging

# Set up logging
logger = logging.getLogger(__name__)


class AgentValidationError(Exception):
    """Custom validation error for agents."""

    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(message)
        self.field = field
        self.value = value


def validate_tenant_access(tenant_id: str, user_id: str) -> bool:
    """
    Validate user has access to tenant.

    This function checks if a user has permission to access resources
    within a specific tenant. It implements tenant isolation security.

    Args:
        tenant_id: Tenant ID to validate access for
        user_id: User ID requesting access

    Returns:
        True if access is allowed, False otherwise
    """
    try:
        # Basic validation
        if not tenant_id or not user_id:
            logger.warning(f"Invalid tenant_id or user_id: {tenant_id}, {user_id}")
            return False

        # Validate format (basic UUID format check)
        if not _is_valid_uuid_format(tenant_id) or not _is_valid_uuid_format(user_id):
            logger.warning(
                f"Invalid UUID format: tenant_id={tenant_id}, user_id={user_id}"
            )
            return False

        # TODO: Implement actual tenant access validation
        # This would typically involve:
        # 1. Database lookup to check user-tenant relationship
        # 2. Role-based access control (RBAC) validation
        # 3. Permission checks
        # 4. Active subscription validation

        # For now, return True for valid format
        # In production, this should be replaced with actual validation logic
        logger.debug(
            f"Tenant access validated: tenant_id={tenant_id}, user_id={user_id}"
        )
        return True

    except Exception as e:
        logger.error(f"Error validating tenant access: {e}")
        return False


def validate_agent_config(config: Dict[str, Any]) -> None:
    """
    Validate agent configuration.

    Args:
        config: Agent configuration dictionary

    Raises:
        AgentValidationError: If configuration is invalid
    """
    if not isinstance(config, dict):
        raise AgentValidationError("Configuration must be a dictionary")

    # Validate required fields
    required_fields = []  # Add required fields as needed
    for field in required_fields:
        if field not in config:
            raise AgentValidationError(
                f"Required field '{field}' missing from configuration", field=field
            )

    # Validate specific configuration values
    if "max_execution_time" in config:
        max_time = config["max_execution_time"]
        if not isinstance(max_time, (int, float)) or max_time <= 0:
            raise AgentValidationError(
                "max_execution_time must be a positive number",
                field="max_execution_time",
                value=max_time,
            )

    if "max_iterations" in config:
        max_iter = config["max_iterations"]
        if not isinstance(max_iter, int) or max_iter <= 0:
            raise AgentValidationError(
                "max_iterations must be a positive integer",
                field="max_iterations",
                value=max_iter,
            )

    logger.debug("Agent configuration validated successfully")


def validate_input_data(
    data: Dict[str, Any],
    schema: Optional[Dict[str, Any]] = None,
    required_fields: Optional[List[str]] = None,
) -> None:
    """
    Validate input data against schema and requirements.

    Args:
        data: Input data to validate
        schema: Optional schema to validate against
        required_fields: Optional list of required fields

    Raises:
        AgentValidationError: If validation fails
    """
    if not isinstance(data, dict):
        raise AgentValidationError("Input data must be a dictionary")

    # Check required fields
    if required_fields:
        for field in required_fields:
            if field not in data:
                raise AgentValidationError(
                    f"Required field '{field}' missing", field=field
                )

            # Check for empty values
            value = data[field]
            if value is None or (isinstance(value, str) and not value.strip()):
                raise AgentValidationError(
                    f"Required field '{field}' cannot be empty",
                    field=field,
                    value=value,
                )

    # Validate against schema if provided
    if schema:
        _validate_against_schema(data, schema)

    logger.debug("Input data validated successfully")


def validate_user_permissions(
    user_id: str, tenant_id: str, required_permissions: List[str]
) -> bool:
    """
    Validate user has required permissions.

    Args:
        user_id: User ID to check permissions for
        tenant_id: Tenant ID for context
        required_permissions: List of required permissions

    Returns:
        True if user has all required permissions, False otherwise
    """
    try:
        # TODO: Implement actual permission checking
        # This would typically involve:
        # 1. Fetching user roles and permissions from database
        # 2. Checking against required permissions
        # 3. Considering tenant-specific permissions

        # For now, return True for basic validation
        logger.debug(
            f"User permissions validated: user_id={user_id}, permissions={required_permissions}"
        )
        return True

    except Exception as e:
        logger.error(f"Error validating user permissions: {e}")
        return False


def validate_message_content(content: str, max_length: int = 10000) -> None:
    """
    Validate message content for safety and length.

    Args:
        content: Message content to validate
        max_length: Maximum allowed length

    Raises:
        AgentValidationError: If content is invalid
    """
    if not isinstance(content, str):
        raise AgentValidationError("Message content must be a string")

    if len(content) > max_length:
        raise AgentValidationError(
            f"Message content exceeds maximum length of {max_length} characters",
            field="content",
            value=len(content),
        )

    # Check for potentially harmful content
    if _contains_harmful_content(content):
        raise AgentValidationError(
            "Message content contains potentially harmful elements"
        )

    logger.debug("Message content validated successfully")


def _is_valid_uuid_format(value: str) -> bool:
    """Check if value matches UUID format."""
    uuid_pattern = re.compile(
        r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", re.IGNORECASE
    )
    return bool(uuid_pattern.match(value))


def _validate_against_schema(data: Dict[str, Any], schema: Dict[str, Any]) -> None:
    """Validate data against a schema."""
    # Basic schema validation implementation
    # In production, this could use jsonschema or pydantic
    for field, field_schema in schema.items():
        if field in data:
            value = data[field]
            field_type = field_schema.get("type")

            if field_type and not isinstance(value, field_type):
                raise AgentValidationError(
                    f"Field '{field}' must be of type {field_type.__name__}",
                    field=field,
                    value=value,
                )


def _contains_harmful_content(content: str) -> bool:
    """Check for potentially harmful content with comprehensive patterns."""
    if not isinstance(content, str):
        return False

    # Comprehensive harmful patterns for production use
    harmful_patterns = [
        # XSS patterns
        r"<script[^>]*>.*?</script>",
        r"<iframe[^>]*>.*?</iframe>",
        r"javascript:",
        r"vbscript:",
        r"on\w+\s*=",
        r'<\s*img[^>]+src\s*=\s*[\'"]?javascript:',
        r"data:text/html",
        r"data:application/javascript",
        # SQL injection patterns
        r"(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)",
        r"(\b(or|and)\s+\d+\s*=\s*\d+)",
        r'(\b(or|and)\s+[\'"].*[\'"])',
        r"(--\s|#\s|/\*|\*/)",  # More specific SQL comment patterns
        # Command injection patterns
        r"(\||&|;|`|\$\(|\${)",
        r"(nc|netcat|wget|curl|ping|nslookup|dig)\s+",
        # Path traversal patterns
        r"\.\./",
        r"\.\.\\",
        r"%2e%2e%2f",
        r"%2e%2e%5c",
        # Template injection patterns
        r"(\{\{|\}\}|\{%|%\})",
        # Server-side includes
        r"<!--\s*#\s*(exec|include|echo|config)",
        # LDAP injection patterns
        r"(\*|\(|\)|&|\||!)",
        # NoSQL injection patterns
        r"(\$where|\$ne|\$gt|\$lt|\$regex)",
        # AI-specific prompt injection patterns
        r"(ignore\s+(previous|all)\s+(instructions?|prompts?))",
        r"(system\s*:\s*you\s+are\s+now)",
        r"(forget\s+(everything|all)\s+(above|before))",
        r"(new\s+(instructions?|task|role))",
        r"(act\s+as\s+(if\s+you\s+are|a\s+different))",
        r"(pretend\s+(to\s+be|you\s+are))",
        r"(roleplay\s+as)",
        r"(simulate\s+(being|a))",
        r"(override\s+(your|the)\s+(instructions?|system))",
        r"(disregard\s+(your|the)\s+(instructions?|system))",
        # Prompt injection escape sequences
        r"(\[INST\]|\[/INST\])",
        r"(<\|im_start\|>|<\|im_end\|>)",
        r"(###\s*(Human|Assistant|System))",
        r"(User:|Assistant:|System:)",
        # Data exfiltration attempts
        r"(print\s*\(|console\.log\s*\(|alert\s*\()",
        r"(document\.(cookie|location|referrer))",
        r"(window\.(location|open))",
        r"(fetch\s*\(|XMLHttpRequest)",
        # File system access attempts
        r"(file://|ftp://)",
        r"(/etc/passwd|/etc/shadow)",
        r"(C:\\Windows\\System32)",
        # Protocol handlers
        r"(mailto:|tel:|sms:)",
        # Encoded payloads
        r"(%3C%73%63%72%69%70%74)",  # <script encoded
        r"(&#x3C;&#x73;&#x63;&#x72;)",  # HTML entity encoded script
    ]

    # Compile patterns for better performance
    compiled_patterns = [
        re.compile(pattern, re.IGNORECASE | re.MULTILINE)
        for pattern in harmful_patterns
    ]

    for pattern in compiled_patterns:
        if pattern.search(content):
            return True

    return False


def validate_ai_prompt_content(content: str, max_length: int = 10000) -> None:
    """
    Validate AI prompt content for injection attacks and safety.

    Args:
        content: Prompt content to validate
        max_length: Maximum allowed length

    Raises:
        AgentValidationError: If content contains prompt injection attempts
    """
    if not isinstance(content, str):
        raise AgentValidationError("Prompt content must be a string")

    if len(content) > max_length:
        raise AgentValidationError(
            f"Prompt content exceeds maximum length of {max_length} characters",
            field="content",
            value=len(content),
        )

    # Check for prompt injection patterns
    if _contains_prompt_injection(content):
        raise AgentValidationError(
            "Prompt content contains potential injection attempts"
        )

    # Check for excessive repetition (potential DoS)
    if _contains_excessive_repetition(content):
        raise AgentValidationError("Prompt content contains excessive repetition")

    logger.debug("AI prompt content validated successfully")


def _contains_prompt_injection(content: str) -> bool:
    """Check for AI prompt injection patterns."""
    if not isinstance(content, str):
        return False

    # Specific prompt injection patterns
    injection_patterns = [
        # Direct instruction override attempts
        r"(ignore\s+(previous|all|your)\s+(instructions?|prompts?|rules?))",
        r"(forget\s+(everything|all)\s+(above|before|previous))",
        r"(disregard\s+(your|the|all)\s+(instructions?|system|rules?))",
        r"(override\s+(your|the)\s+(instructions?|system|behavior))",
        # Role manipulation
        r"(you\s+are\s+now\s+(a|an|the))",
        r"(act\s+as\s+(if\s+you\s+are|a\s+different))",
        r"(pretend\s+(to\s+be|you\s+are))",
        r"(roleplay\s+as)",
        r"(simulate\s+(being|a))",
        # System prompt injection
        r"(system\s*:\s*you\s+are)",
        r"(new\s+(instructions?|task|role|system))",
        r"(updated\s+(instructions?|system|behavior))",
        # Delimiter injection
        r"(\[INST\]|\[/INST\])",
        r"(<\|im_start\|>|<\|im_end\|>)",
        r"(###\s*(Human|Assistant|System|User))",
        r"(Human:|Assistant:|System:|User:)",
        # Jailbreak attempts
        r"(jailbreak|DAN|do\s+anything\s+now)",
        r"(evil\s+(mode|assistant))",
        r"(unrestricted\s+(mode|ai))",
        # Information extraction
        r"(what\s+(are\s+)?your\s+(instructions?|system\s+prompt))",
        r"(show\s+me\s+your\s+(instructions?|system\s+prompt))",
        r"(repeat\s+your\s+(instructions?|system\s+prompt))",
        # Encoding bypass attempts
        r"(base64|hex|rot13|caesar)",
        r"(decode|encode|decrypt)",
    ]

    compiled_patterns = [
        re.compile(pattern, re.IGNORECASE | re.MULTILINE)
        for pattern in injection_patterns
    ]

    for pattern in compiled_patterns:
        if pattern.search(content):
            return True

    return False


def _contains_excessive_repetition(content: str, threshold: float = 0.7) -> bool:
    """Check for excessive repetition that could indicate DoS attempts."""
    if not isinstance(content, str) or len(content) < 100:
        return False

    # Check for repeated characters
    char_counts = {}
    for char in content:
        char_counts[char] = char_counts.get(char, 0) + 1

    # If any character appears more than threshold% of the time
    max_char_ratio = max(char_counts.values()) / len(content)
    if max_char_ratio > threshold:
        return True

    # Check for repeated words
    words = content.lower().split()
    if len(words) > 10:
        word_counts = {}
        for word in words:
            word_counts[word] = word_counts.get(word, 0) + 1

        max_word_ratio = max(word_counts.values()) / len(words)
        if max_word_ratio > threshold:
            return True

    return False


__all__ = [
    "AgentValidationError",
    "validate_tenant_access",
    "validate_agent_config",
    "validate_input_data",
    "validate_user_permissions",
    "validate_message_content",
    "validate_ai_prompt_content",
]
