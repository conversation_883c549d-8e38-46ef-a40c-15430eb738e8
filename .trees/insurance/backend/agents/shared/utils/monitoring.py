"""
Monitoring and Metrics Utilities for Agent System

This module provides comprehensive monitoring and metrics collection for the agent system,
including performance tracking, health monitoring, and operational metrics.

Key Features:
- Performance metrics collection
- Health status monitoring
- Resource usage tracking
- Error rate monitoring
- Execution time tracking
- Throughput metrics
- Custom metrics support

Usage:
    from backend.agents.shared.utils.monitoring import (
        AgentMetrics,
        PerformanceTracker,
        HealthChecker
    )

    # Create metrics collector
    metrics = AgentMetrics("my_agent", "research")
    metrics.record_execution_start()
    metrics.record_execution_success(1.5)

    # Track performance
    tracker = PerformanceTracker("my_agent")
    with tracker.time_operation("search"):
        # Perform operation
        pass

    # Check health
    health_checker = HealthChecker("my_agent")
    status = health_checker.get_health_status()
"""

from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
import time
import threading
import statistics
from dataclasses import dataclass, field
from enum import Enum


class HealthStatus(str, Enum):
    """Health status levels."""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class MetricType(str, Enum):
    """Types of metrics."""

    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricValue:
    """Individual metric value with timestamp."""

    value: Union[int, float]
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """Performance metrics for an operation."""

    operation_name: str
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    total_execution_time: float = 0.0
    min_execution_time: Optional[float] = None
    max_execution_time: Optional[float] = None
    recent_execution_times: deque = field(default_factory=lambda: deque(maxlen=100))

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_executions == 0:
            return 0.0
        return (self.successful_executions / self.total_executions) * 100

    @property
    def average_execution_time(self) -> float:
        """Calculate average execution time."""
        if self.successful_executions == 0:
            return 0.0
        return self.total_execution_time / self.successful_executions

    @property
    def recent_average_execution_time(self) -> float:
        """Calculate recent average execution time."""
        if not self.recent_execution_times:
            return 0.0
        return statistics.mean(self.recent_execution_times)

    def record_execution(self, execution_time: float, success: bool):
        """Record an execution."""
        self.total_executions += 1

        if success:
            self.successful_executions += 1
            self.total_execution_time += execution_time
            self.recent_execution_times.append(execution_time)

            if (
                self.min_execution_time is None
                or execution_time < self.min_execution_time
            ):
                self.min_execution_time = execution_time

            if (
                self.max_execution_time is None
                or execution_time > self.max_execution_time
            ):
                self.max_execution_time = execution_time
        else:
            self.failed_executions += 1


class AgentMetrics:
    """Comprehensive metrics collection for agents."""

    def __init__(self, agent_name: str, agent_type: str):
        self.agent_name = agent_name
        self.agent_type = agent_type
        self.start_time = datetime.now(timezone.utc)

        # Metrics storage
        self.metrics: Dict[str, List[MetricValue]] = defaultdict(list)
        self.performance_metrics: Dict[str, PerformanceMetrics] = defaultdict(
            lambda: PerformanceMetrics(operation_name="default")
        )

        # Execution tracking
        self.current_executions: Dict[str, float] = {}
        self.error_counts: Dict[str, int] = defaultdict(int)

        # Thread safety
        self._lock = threading.Lock()

    def record_metric(
        self,
        name: str,
        value: Union[int, float],
        metric_type: MetricType = MetricType.GAUGE,
        labels: Optional[Dict[str, str]] = None,
    ):
        """Record a custom metric."""
        with self._lock:
            metric_value = MetricValue(value=value, labels=labels or {})
            self.metrics[name].append(metric_value)

            # Keep only recent metrics (last 1000 values)
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]

    def record_execution_start(self, execution_id: Optional[str] = None) -> str:
        """Record the start of an execution."""
        execution_id = execution_id or f"exec_{int(time.time() * 1000)}"

        with self._lock:
            self.current_executions[execution_id] = time.time()

        self.record_metric("executions_started", 1, MetricType.COUNTER)
        return execution_id

    def record_execution_success(
        self,
        execution_time: Optional[float] = None,
        execution_id: Optional[str] = None,
        operation_name: str = "default",
    ):
        """Record a successful execution."""
        if execution_time is None and execution_id:
            with self._lock:
                start_time = self.current_executions.pop(execution_id, time.time())
                execution_time = time.time() - start_time
        elif execution_time is None:
            execution_time = 0.0

        with self._lock:
            self.performance_metrics[operation_name].record_execution(
                execution_time, True
            )

        self.record_metric("executions_completed", 1, MetricType.COUNTER)
        self.record_metric("execution_time", execution_time, MetricType.TIMER)

    def record_execution_failure(
        self,
        execution_time: Optional[float] = None,
        error_type: str = "unknown",
        execution_id: Optional[str] = None,
        operation_name: str = "default",
    ):
        """Record a failed execution."""
        if execution_time is None and execution_id:
            with self._lock:
                start_time = self.current_executions.pop(execution_id, time.time())
                execution_time = time.time() - start_time
        elif execution_time is None:
            execution_time = 0.0

        with self._lock:
            self.performance_metrics[operation_name].record_execution(
                execution_time, False
            )
            self.error_counts[error_type] += 1

        self.record_metric("executions_failed", 1, MetricType.COUNTER)
        self.record_metric("execution_time", execution_time, MetricType.TIMER)

    def record_error(self, error_type: str, error_message: str):
        """Record an error occurrence."""
        with self._lock:
            self.error_counts[error_type] += 1

        self.record_metric("errors", 1, MetricType.COUNTER, {"error_type": error_type})

    def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics."""
        with self._lock:
            uptime = (datetime.now(timezone.utc) - self.start_time).total_seconds()

            # Calculate aggregate metrics
            total_executions = sum(
                pm.total_executions for pm in self.performance_metrics.values()
            )
            total_successful = sum(
                pm.successful_executions for pm in self.performance_metrics.values()
            )
            total_failed = sum(
                pm.failed_executions for pm in self.performance_metrics.values()
            )

            overall_success_rate = (
                (total_successful / total_executions * 100)
                if total_executions > 0
                else 0.0
            )

            return {
                "agent_name": self.agent_name,
                "agent_type": self.agent_type,
                "uptime_seconds": uptime,
                "total_executions": total_executions,
                "successful_executions": total_successful,
                "failed_executions": total_failed,
                "success_rate_percent": overall_success_rate,
                "error_counts": dict(self.error_counts),
                "performance_metrics": {
                    name: {
                        "total_executions": pm.total_executions,
                        "success_rate": pm.success_rate,
                        "average_execution_time": pm.average_execution_time,
                        "recent_average_execution_time": pm.recent_average_execution_time,
                        "min_execution_time": pm.min_execution_time,
                        "max_execution_time": pm.max_execution_time,
                    }
                    for name, pm in self.performance_metrics.items()
                },
                "current_executions": len(self.current_executions),
            }


class PerformanceTracker:
    """Performance tracking utilities."""

    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.operation_times: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.Lock()

    def time_operation(self, operation_name: str):
        """Context manager for timing operations."""
        return OperationTimer(operation_name, self)

    def record_operation_time(self, operation_name: str, duration: float):
        """Record operation execution time."""
        with self._lock:
            self.operation_times[operation_name].append(duration)

            # Keep only recent times (last 100)
            if len(self.operation_times[operation_name]) > 100:
                self.operation_times[operation_name] = self.operation_times[
                    operation_name
                ][-100:]

    def get_operation_stats(self, operation_name: str) -> Dict[str, float]:
        """Get statistics for an operation."""
        with self._lock:
            times = self.operation_times.get(operation_name, [])

            if not times:
                return {"count": 0}

            return {
                "count": len(times),
                "average": statistics.mean(times),
                "median": statistics.median(times),
                "min": min(times),
                "max": max(times),
                "std_dev": statistics.stdev(times) if len(times) > 1 else 0.0,
            }

    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """Get statistics for all operations."""
        return {
            operation: self.get_operation_stats(operation)
            for operation in self.operation_times.keys()
        }


class OperationTimer:
    """Context manager for timing operations."""

    def __init__(self, operation_name: str, tracker: PerformanceTracker):
        self.operation_name = operation_name
        self.tracker = tracker
        self.start_time: Optional[float] = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.tracker.record_operation_time(self.operation_name, duration)


class HealthChecker:
    """Health monitoring for agents."""

    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.health_checks: Dict[str, Callable] = {}
        self.last_check_time: Optional[datetime] = None
        self.last_status = HealthStatus.UNKNOWN

    def register_health_check(self, name: str, check_function: Callable):
        """Register a health check function."""
        self.health_checks[name] = check_function

    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status."""
        self.last_check_time = datetime.now(timezone.utc)

        check_results = {}
        overall_status = HealthStatus.HEALTHY

        for name, check_function in self.health_checks.items():
            try:
                result = check_function()
                check_results[name] = {
                    "status": HealthStatus.HEALTHY,
                    "result": result,
                    "error": None,
                }
            except Exception as e:
                check_results[name] = {
                    "status": HealthStatus.CRITICAL,
                    "result": None,
                    "error": str(e),
                }
                overall_status = HealthStatus.CRITICAL

        self.last_status = overall_status

        return {
            "agent_name": self.agent_name,
            "overall_status": overall_status.value,
            "check_time": self.last_check_time.isoformat(),
            "checks": check_results,
        }

    def is_healthy(self) -> bool:
        """Check if agent is healthy."""
        status = self.get_health_status()
        return status["overall_status"] == HealthStatus.HEALTHY.value


__all__ = [
    "HealthStatus",
    "MetricType",
    "MetricValue",
    "PerformanceMetrics",
    "AgentMetrics",
    "PerformanceTracker",
    "OperationTimer",
    "HealthChecker",
]
