"""
Structured Logging Utilities for Agent System

This module provides structured logging capabilities for the agent system,
including performance logging, security event logging, and agent-specific logging.

Key Features:
- Structured logging with JSON output
- Agent-specific loggers with context
- Performance event logging
- Security event logging
- Log correlation and tracing
- Configurable log levels and formats

Usage:
    from backend.agents.shared.utils.logging import (
        StructuredLogger,
        get_logger,
        log_security_event,
        log_performance_event
    )

    # Create structured logger
    logger = StructuredLogger("my_agent")
    logger.info("Agent started", extra={"tenant_id": "123"})

    # Log security event
    log_security_event("unauthorized_access", {"user_id": "456"})

    # Log performance event
    log_performance_event("execution_time", 1.5, {"agent_type": "research"})
"""

import logging
import json
import time
from typing import Dict, Any, Optional, Union
from datetime import datetime, timezone
import uuid
import sys
import traceback

# Set up base logger configuration
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)


class StructuredLogger:
    """Structured logging for agents with context preservation."""

    def __init__(self, agent_name: str, base_context: Optional[Dict[str, Any]] = None):
        """
        Initialize structured logger.

        Args:
            agent_name: Name of the agent
            base_context: Base context to include in all log messages
        """
        self.agent_name = agent_name
        self.base_context = base_context or {}
        self.logger = logging.getLogger(f"agent.{agent_name}")

        # Add structured formatter
        handler = logging.StreamHandler()
        formatter = StructuredFormatter()
        handler.setFormatter(formatter)

        # Clear existing handlers and add our structured handler
        self.logger.handlers.clear()
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def _create_log_record(
        self,
        level: str,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        exc_info: Optional[Exception] = None,
    ) -> Dict[str, Any]:
        """Create structured log record."""
        record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": level,
            "agent_name": self.agent_name,
            "message": message,
            "log_id": str(uuid.uuid4()),
        }

        # Add base context
        record.update(self.base_context)

        # Add extra context
        if extra:
            record.update(extra)

        # Add exception information
        if exc_info:
            record["exception"] = {
                "type": type(exc_info).__name__,
                "message": str(exc_info),
                "traceback": traceback.format_exc(),
            }

        return record

    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log debug message."""
        record = self._create_log_record("DEBUG", message, extra)
        self.logger.debug(json.dumps(record))

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log info message."""
        record = self._create_log_record("INFO", message, extra)
        self.logger.info(json.dumps(record))

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log warning message."""
        record = self._create_log_record("WARNING", message, extra)
        self.logger.warning(json.dumps(record))

    def error(
        self,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        exc_info: Optional[Exception] = None,
    ):
        """Log error message."""
        record = self._create_log_record("ERROR", message, extra, exc_info)
        self.logger.error(json.dumps(record))

    def critical(
        self,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        exc_info: Optional[Exception] = None,
    ):
        """Log critical message."""
        record = self._create_log_record("CRITICAL", message, extra, exc_info)
        self.logger.critical(json.dumps(record))

    def with_context(self, **context) -> "StructuredLogger":
        """Create new logger with additional context."""
        new_context = {**self.base_context, **context}
        return StructuredLogger(self.agent_name, new_context)


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""

    def format(self, record):
        """Format log record as JSON."""
        try:
            # If the message is already JSON, return as-is
            if record.getMessage().startswith("{"):
                return record.getMessage()

            # Otherwise, create structured record
            log_record = {
                "timestamp": datetime.fromtimestamp(
                    record.created, timezone.utc
                ).isoformat(),
                "level": record.levelname,
                "logger": record.name,
                "message": record.getMessage(),
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno,
            }

            # Add exception info if present
            if record.exc_info:
                log_record["exception"] = {
                    "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                    "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                    "traceback": (
                        self.formatException(record.exc_info)
                        if record.exc_info
                        else None
                    ),
                }

            return json.dumps(log_record)

        except Exception as e:
            # Fallback to standard formatting if JSON formatting fails
            return f"LOG_FORMAT_ERROR: {str(e)} | Original: {record.getMessage()}"


def get_logger(name: str, context: Optional[Dict[str, Any]] = None) -> StructuredLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name
        context: Base context for the logger

    Returns:
        Structured logger instance
    """
    return StructuredLogger(name, context)


def log_security_event(
    event_type: str, context: Dict[str, Any], severity: str = "WARNING"
) -> None:
    """
    Log security-related events.

    Args:
        event_type: Type of security event
        context: Event context information
        severity: Log severity level
    """
    security_logger = get_logger("security")

    security_context = {
        "event_type": "security",
        "security_event_type": event_type,
        "severity": severity,
        **context,
    }

    message = f"Security event: {event_type}"

    if severity == "CRITICAL":
        security_logger.critical(message, extra=security_context)
    elif severity == "ERROR":
        security_logger.error(message, extra=security_context)
    elif severity == "WARNING":
        security_logger.warning(message, extra=security_context)
    else:
        security_logger.info(message, extra=security_context)


def log_performance_event(
    metric_name: str,
    value: Union[float, int],
    context: Optional[Dict[str, Any]] = None,
    unit: str = "seconds",
) -> None:
    """
    Log performance-related events.

    Args:
        metric_name: Name of the performance metric
        value: Metric value
        context: Additional context information
        unit: Unit of measurement
    """
    performance_logger = get_logger("performance")

    performance_context = {
        "event_type": "performance",
        "metric_name": metric_name,
        "metric_value": value,
        "metric_unit": unit,
        **(context or {}),
    }

    message = f"Performance metric: {metric_name} = {value} {unit}"
    performance_logger.info(message, extra=performance_context)


def log_agent_lifecycle_event(
    agent_name: str, event: str, context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log agent lifecycle events.

    Args:
        agent_name: Name of the agent
        event: Lifecycle event (e.g., "started", "completed", "failed")
        context: Additional context information
    """
    lifecycle_logger = get_logger("lifecycle")

    lifecycle_context = {
        "event_type": "lifecycle",
        "agent_name": agent_name,
        "lifecycle_event": event,
        **(context or {}),
    }

    message = f"Agent lifecycle: {agent_name} {event}"
    lifecycle_logger.info(message, extra=lifecycle_context)


def log_tenant_event(
    tenant_id: str, event_type: str, context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log tenant-specific events.

    Args:
        tenant_id: Tenant ID
        event_type: Type of tenant event
        context: Additional context information
    """
    tenant_logger = get_logger("tenant")

    tenant_context = {
        "event_type": "tenant",
        "tenant_id": tenant_id,
        "tenant_event_type": event_type,
        **(context or {}),
    }

    message = f"Tenant event: {event_type} for tenant {tenant_id}"
    tenant_logger.info(message, extra=tenant_context)


class PerformanceTimer:
    """Context manager for timing operations."""

    def __init__(self, operation_name: str, logger: Optional[StructuredLogger] = None):
        self.operation_name = operation_name
        self.logger = logger or get_logger("performance")
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time

        context = {
            "operation_name": self.operation_name,
            "duration_seconds": duration,
            "success": exc_type is None,
        }

        if exc_type:
            context["error_type"] = exc_type.__name__
            context["error_message"] = str(exc_val)

        log_performance_event(f"{self.operation_name}_duration", duration, context)


__all__ = [
    "StructuredLogger",
    "StructuredFormatter",
    "get_logger",
    "log_security_event",
    "log_performance_event",
    "log_agent_lifecycle_event",
    "log_tenant_event",
    "PerformanceTimer",
]
