"""
Performance testing utilities for agent testing.

This module provides tools for measuring and validating the performance
of agents, including execution time, memory usage, and throughput metrics.
"""

import asyncio
import gc
import psutil
import time
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional
from datetime import datetime, timezone

import pytest


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""

    execution_time: float = 0.0
    memory_usage_mb: float = 0.0
    peak_memory_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    operations_per_second: float = 0.0
    success_rate: float = 0.0
    error_count: int = 0
    total_operations: int = 0
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    end_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "execution_time": self.execution_time,
            "memory_usage_mb": self.memory_usage_mb,
            "peak_memory_mb": self.peak_memory_mb,
            "cpu_usage_percent": self.cpu_usage_percent,
            "operations_per_second": self.operations_per_second,
            "success_rate": self.success_rate,
            "error_count": self.error_count,
            "total_operations": self.total_operations,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "metadata": self.metadata,
        }


class PerformanceTester:
    """
    Performance testing utility for agents.

    This class provides methods for measuring various performance metrics
    during agent execution and validation.
    """

    def __init__(self, thresholds: Optional[Dict[str, float]] = None):
        """
        Initialize performance tester.

        Args:
            thresholds: Performance thresholds for validation
        """
        self.thresholds = thresholds or {
            "max_execution_time": 5.0,  # seconds
            "max_memory_usage": 100.0,  # MB
            "min_operations_per_second": 10.0,
            "min_success_rate": 0.95,
            "max_cpu_usage": 80.0,  # percent
        }
        self.process = psutil.Process()

    def measure_execution_time(self, func: Callable) -> Callable:
        """
        Decorator to measure execution time of a function.

        Args:
            func: Function to measure

        Returns:
            Decorated function
        """
        if asyncio.iscoroutinefunction(func):

            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                result = await func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time

                # Store metrics in result if it's a dict
                if isinstance(result, dict):
                    result["_performance_metrics"] = {"execution_time": execution_time}

                return result

            return async_wrapper
        else:

            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time

                # Store metrics in result if it's a dict
                if isinstance(result, dict):
                    result["_performance_metrics"] = {"execution_time": execution_time}

                return result

            return sync_wrapper

    def get_memory_usage(self) -> float:
        """
        Get current memory usage in MB.

        Returns:
            Memory usage in megabytes
        """
        memory_info = self.process.memory_info()
        return memory_info.rss / 1024 / 1024  # Convert to MB

    def get_cpu_usage(self) -> float:
        """
        Get current CPU usage percentage.

        Returns:
            CPU usage percentage
        """
        return self.process.cpu_percent()

    async def measure_async_operation(
        self, operation: Callable, *args, **kwargs
    ) -> tuple[Any, PerformanceMetrics]:
        """
        Measure performance of an async operation.

        Args:
            operation: Async operation to measure
            *args: Operation arguments
            **kwargs: Operation keyword arguments

        Returns:
            Tuple of (result, metrics)
        """
        metrics = PerformanceMetrics()

        # Force garbage collection before measurement
        gc.collect()

        # Record initial state
        initial_memory = self.get_memory_usage()
        start_time = time.time()

        try:
            # Execute operation
            result = await operation(*args, **kwargs)

            # Record success
            metrics.total_operations = 1
            metrics.error_count = 0
            metrics.success_rate = 1.0

        except Exception as e:
            # Record error
            metrics.total_operations = 1
            metrics.error_count = 1
            metrics.success_rate = 0.0
            result = {"error": str(e)}

        # Record final state
        end_time = time.time()
        final_memory = self.get_memory_usage()

        metrics.execution_time = end_time - start_time
        metrics.memory_usage_mb = final_memory - initial_memory
        # Report peak as peak delta rather than absolute RSS to keep tests stable
        metrics.peak_memory_mb = max(final_memory - initial_memory, 0)
        metrics.cpu_usage_percent = self.get_cpu_usage()
        metrics.end_time = datetime.now(timezone.utc)

        if metrics.execution_time > 0:
            metrics.operations_per_second = 1.0 / metrics.execution_time

        return result, metrics

    async def measure_batch_operations(
        self,
        operation: Callable,
        batch_size: int = 10,
        operation_args: Optional[List[tuple]] = None,
    ) -> tuple[List[Any], PerformanceMetrics]:
        """
        Measure performance of batch operations.

        Args:
            operation: Operation to execute
            batch_size: Number of operations to execute
            operation_args: List of argument tuples for each operation

        Returns:
            Tuple of (results, metrics)
        """
        metrics = PerformanceMetrics()
        results = []

        # Force garbage collection before measurement
        gc.collect()

        # Record initial state
        initial_memory = self.get_memory_usage()
        start_time = time.time()

        # Execute batch operations
        for i in range(batch_size):
            try:
                if operation_args and i < len(operation_args):
                    args, kwargs = (
                        operation_args[i]
                        if isinstance(operation_args[i], tuple)
                        and len(operation_args[i]) == 2
                        else (operation_args[i], {})
                    )
                    result = await operation(*args, **kwargs)
                else:
                    result = await operation()

                results.append(result)

            except Exception as e:
                metrics.error_count += 1
                results.append({"error": str(e)})

        # Record final state
        end_time = time.time()
        final_memory = self.get_memory_usage()

        metrics.execution_time = end_time - start_time
        metrics.memory_usage_mb = final_memory - initial_memory
        metrics.peak_memory_mb = max(final_memory - initial_memory, 0)
        metrics.cpu_usage_percent = self.get_cpu_usage()
        metrics.total_operations = batch_size
        metrics.success_rate = (batch_size - metrics.error_count) / batch_size
        metrics.end_time = datetime.now(timezone.utc)

        if metrics.execution_time > 0:
            metrics.operations_per_second = batch_size / metrics.execution_time

        return results, metrics

    async def measure_concurrent_operations(
        self, operation: Callable, concurrency_level: int = 5, operation_count: int = 20
    ) -> tuple[List[Any], PerformanceMetrics]:
        """
        Measure performance of concurrent operations.

        Args:
            operation: Operation to execute
            concurrency_level: Number of concurrent operations
            operation_count: Total number of operations

        Returns:
            Tuple of (results, metrics)
        """
        metrics = PerformanceMetrics()

        # Force garbage collection before measurement
        gc.collect()

        # Record initial state
        initial_memory = self.get_memory_usage()
        start_time = time.time()

        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(concurrency_level)

        async def limited_operation():
            async with semaphore:
                try:
                    return await operation()
                except Exception as e:
                    return {"error": str(e)}

        # Execute concurrent operations
        tasks = [limited_operation() for _ in range(operation_count)]
        results = await asyncio.gather(*tasks)

        # Record final state
        end_time = time.time()
        final_memory = self.get_memory_usage()

        # Calculate metrics
        error_count = sum(
            1 for result in results if isinstance(result, dict) and "error" in result
        )

        metrics.execution_time = end_time - start_time
        metrics.memory_usage_mb = final_memory - initial_memory
        metrics.peak_memory_mb = max(final_memory - initial_memory, 0)
        metrics.cpu_usage_percent = self.get_cpu_usage()
        metrics.total_operations = operation_count
        metrics.error_count = error_count
        metrics.success_rate = (operation_count - error_count) / operation_count
        metrics.end_time = datetime.now(timezone.utc)

        if metrics.execution_time > 0:
            metrics.operations_per_second = operation_count / metrics.execution_time

        return results, metrics

    def validate_performance(self, metrics: PerformanceMetrics) -> Dict[str, bool]:
        """
        Validate performance metrics against thresholds.

        Args:
            metrics: Performance metrics to validate

        Returns:
            Dictionary of validation results
        """
        validation_results = {}

        # Check execution time
        if "max_execution_time" in self.thresholds:
            validation_results["execution_time_ok"] = (
                metrics.execution_time <= self.thresholds["max_execution_time"]
            )

        # Check memory usage
        if "max_memory_usage" in self.thresholds:
            validation_results["memory_usage_ok"] = (
                metrics.memory_usage_mb <= self.thresholds["max_memory_usage"]
            )

        # Check operations per second
        if "min_operations_per_second" in self.thresholds:
            validation_results["throughput_ok"] = (
                metrics.operations_per_second
                >= self.thresholds["min_operations_per_second"]
            )

        # Check success rate
        if "min_success_rate" in self.thresholds:
            validation_results["success_rate_ok"] = (
                metrics.success_rate >= self.thresholds["min_success_rate"]
            )

        # Check CPU usage
        if "max_cpu_usage" in self.thresholds:
            validation_results["cpu_usage_ok"] = (
                metrics.cpu_usage_percent <= self.thresholds["max_cpu_usage"]
            )

        return validation_results

    def assert_performance_acceptable(self, metrics: PerformanceMetrics):
        """
        Assert that performance metrics meet all thresholds.

        Args:
            metrics: Performance metrics to validate

        Raises:
            AssertionError: If any performance threshold is not met
        """
        validation_results = self.validate_performance(metrics)

        for check_name, passed in validation_results.items():
            assert (
                passed
            ), f"Performance check failed: {check_name}. Metrics: {metrics.to_dict()}"


# Pytest fixtures for performance testing
@pytest.fixture
def performance_tester():
    """Create a performance tester instance."""
    return PerformanceTester()


@pytest.fixture
def strict_performance_tester():
    """Create a performance tester with strict thresholds."""
    strict_thresholds = {
        "max_execution_time": 1.0,  # 1 second
        "max_memory_usage": 50.0,  # 50 MB
        "min_operations_per_second": 50.0,
        "min_success_rate": 0.99,
        "max_cpu_usage": 50.0,
    }
    return PerformanceTester(strict_thresholds)


@pytest.fixture
def relaxed_performance_tester():
    """Create a performance tester with relaxed thresholds."""
    relaxed_thresholds = {
        "max_execution_time": 10.0,  # 10 seconds
        "max_memory_usage": 500.0,  # 500 MB
        "min_operations_per_second": 1.0,
        "min_success_rate": 0.80,
        "max_cpu_usage": 95.0,
    }
    return PerformanceTester(relaxed_thresholds)
