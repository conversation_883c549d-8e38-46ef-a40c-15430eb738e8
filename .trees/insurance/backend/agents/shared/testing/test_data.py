"""
Test data management for agent testing.

This module provides utilities for managing test data, including loading
test datasets, creating test scenarios, and managing test data lifecycle.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import yaml


class TestDataManager:
    """
    Manage test data for agent testing.

    This class provides utilities for loading, saving, and managing
    test data used in agent testing scenarios.
    """

    def __init__(self, data_dir: str = "test_data"):
        """
        Initialize test data manager.

        Args:
            data_dir: Directory containing test data files
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self._cache = {}

    def load_json_data(self, filename: str) -> Dict[str, Any]:
        """
        Load JSON test data from file.

        Args:
            filename: Name of JSON file (without extension)

        Returns:
            Loaded JSON data
        """
        cache_key = f"json_{filename}"
        if cache_key in self._cache:
            return self._cache[cache_key]

        file_path = self.data_dir / f"{filename}.json"

        if not file_path.exists():
            # Create default data if file doesn't exist
            default_data = self._create_default_data(filename)
            self.save_json_data(filename, default_data)
            return default_data

        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        self._cache[cache_key] = data
        return data

    def save_json_data(self, filename: str, data: Dict[str, Any]):
        """
        Save JSON test data to file.

        Args:
            filename: Name of JSON file (without extension)
            data: Data to save
        """
        file_path = self.data_dir / f"{filename}.json"

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        # Update cache
        cache_key = f"json_{filename}"
        self._cache[cache_key] = data

    def load_yaml_data(self, filename: str) -> Dict[str, Any]:
        """
        Load YAML test data from file.

        Args:
            filename: Name of YAML file (without extension)

        Returns:
            Loaded YAML data
        """
        cache_key = f"yaml_{filename}"
        if cache_key in self._cache:
            return self._cache[cache_key]

        file_path = self.data_dir / f"{filename}.yaml"

        if not file_path.exists():
            # Create default data if file doesn't exist
            default_data = self._create_default_data(filename)
            self.save_yaml_data(filename, default_data)
            return default_data

        with open(file_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f)

        self._cache[cache_key] = data
        return data

    def save_yaml_data(self, filename: str, data: Dict[str, Any]):
        """
        Save YAML test data to file.

        Args:
            filename: Name of YAML file (without extension)
            data: Data to save
        """
        file_path = self.data_dir / f"{filename}.yaml"

        with open(file_path, "w", encoding="utf-8") as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)

        # Update cache
        cache_key = f"yaml_{filename}"
        self._cache[cache_key] = data

    def load_text_data(self, filename: str) -> str:
        """
        Load text test data from file.

        Args:
            filename: Name of text file (with extension)

        Returns:
            File content as string
        """
        cache_key = f"text_{filename}"
        if cache_key in self._cache:
            return self._cache[cache_key]

        file_path = self.data_dir / filename

        if not file_path.exists():
            return ""

        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        self._cache[cache_key] = content
        return content

    def save_text_data(self, filename: str, content: str):
        """
        Save text test data to file.

        Args:
            filename: Name of text file (with extension)
            content: Content to save
        """
        file_path = self.data_dir / filename

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)

        # Update cache
        cache_key = f"text_{filename}"
        self._cache[cache_key] = content

    def get_test_scenarios(self, scenario_type: str) -> List[Dict[str, Any]]:
        """
        Get test scenarios for a specific type.

        Args:
            scenario_type: Type of scenarios to load

        Returns:
            List of test scenarios
        """
        scenarios_data = self.load_json_data("scenarios")
        return scenarios_data.get(scenario_type, [])

    def add_test_scenario(self, scenario_type: str, scenario: Dict[str, Any]):
        """
        Add a new test scenario.

        Args:
            scenario_type: Type of scenario
            scenario: Scenario data
        """
        scenarios_data = self.load_json_data("scenarios")

        if scenario_type not in scenarios_data:
            scenarios_data[scenario_type] = []

        scenarios_data[scenario_type].append(scenario)
        self.save_json_data("scenarios", scenarios_data)

    def get_mock_responses(self, service_name: str) -> Dict[str, Any]:
        """
        Get mock responses for a service.

        Args:
            service_name: Name of the service

        Returns:
            Mock responses dictionary
        """
        mock_data = self.load_json_data("mock_responses")
        return mock_data.get(service_name, {})

    def set_mock_response(self, service_name: str, endpoint: str, response: Any):
        """
        Set a mock response for a service endpoint.

        Args:
            service_name: Name of the service
            endpoint: Endpoint name
            response: Mock response data
        """
        mock_data = self.load_json_data("mock_responses")

        if service_name not in mock_data:
            mock_data[service_name] = {}

        mock_data[service_name][endpoint] = response
        self.save_json_data("mock_responses", mock_data)

    def get_test_configurations(self) -> Dict[str, Any]:
        """
        Get test configurations.

        Returns:
            Test configurations dictionary
        """
        return self.load_yaml_data("test_config")

    def clear_cache(self):
        """Clear the data cache."""
        self._cache.clear()

    def _create_default_data(self, filename: str) -> Dict[str, Any]:
        """
        Create default test data based on filename.

        Args:
            filename: Name of the data file

        Returns:
            Default data structure
        """
        if filename == "scenarios":
            return {
                "agent_execution": [
                    {
                        "name": "basic_execution",
                        "description": "Basic agent execution test",
                        "input_state": {
                            "messages": [{"role": "user", "content": "Hello"}],
                            "tenant_id": "test-tenant",
                            "user_id": "test-user",
                        },
                        "expected_output": {
                            "message_count": 2,
                            "last_message_role": "assistant",
                        },
                    }
                ],
                "error_handling": [
                    {
                        "name": "network_error",
                        "description": "Test network error handling",
                        "error_type": "NetworkError",
                        "error_message": "Connection failed",
                    }
                ],
            }

        elif filename == "mock_responses":
            return {
                "openai": {
                    "chat_completion": {
                        "choices": [
                            {
                                "message": {
                                    "content": "Test response",
                                    "role": "assistant",
                                }
                            }
                        ]
                    }
                },
                "supabase": {
                    "get_user": {"id": "test-user-id", "email": "<EMAIL>"}
                },
            }

        elif filename == "test_config":
            return {
                "performance": {
                    "max_execution_time": 5.0,
                    "max_memory_usage": 100.0,
                    "min_success_rate": 0.95,
                },
                "database": {"test_db_url": "sqlite:///:memory:", "pool_size": 5},
                "external_apis": {"timeout": 30.0, "retry_attempts": 3},
            }

        else:
            return {}

    def create_test_dataset(
        self,
        name: str,
        data: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        Create a test dataset with metadata.

        Args:
            name: Dataset name
            data: Dataset records
            metadata: Optional metadata
        """
        dataset = {
            "name": name,
            "created_at": "2024-01-01T00:00:00Z",
            "metadata": metadata or {},
            "data": data,
        }

        self.save_json_data(f"dataset_{name}", dataset)

    def load_test_dataset(self, name: str) -> Dict[str, Any]:
        """
        Load a test dataset.

        Args:
            name: Dataset name

        Returns:
            Dataset dictionary
        """
        return self.load_json_data(f"dataset_{name}")

    def list_available_data(self) -> Dict[str, List[str]]:
        """
        List all available test data files.

        Returns:
            Dictionary of file types and their available files
        """
        available_data = {"json": [], "yaml": [], "text": []}

        for file_path in self.data_dir.iterdir():
            if file_path.is_file():
                if file_path.suffix == ".json":
                    available_data["json"].append(file_path.stem)
                elif file_path.suffix in [".yaml", ".yml"]:
                    available_data["yaml"].append(file_path.stem)
                else:
                    available_data["text"].append(file_path.name)

        return available_data
