#!/usr/bin/env python3
"""
Test script for shared agent utilities.

This script tests the utility modules including validation, logging, monitoring, and error handling.
"""

import asyncio
import sys
import traceback
import time
from typing import Dict, Any
import pytest

# Mark all tests in this module as integration tests
pytestmark = pytest.mark.integration

# Add the current directory to Python path
sys.path.insert(0, ".")

try:
    from utils.validation import (
        validate_tenant_access,
        validate_agent_config,
        validate_input_data,
        AgentValidationError,
    )
    from backend.utils.logging import (
        StructuredLogger,
        log_security_event,
        log_performance_event,
    )
    from utils.monitoring import AgentMetrics, PerformanceTracker, HealthChecker
    from utils.error_handling import <PERSON>rror<PERSON>andler, ErrorRecoveryStrategy

    print("✓ Successfully imported utility modules")
except ImportError as e:
    print(f"✗ Failed to import utility modules: {e}")
    traceback.print_exc()
    # Don't exit during test collection
    # sys.exit(1)


async def test_validation():
    """Test validation utilities."""
    print("\n--- Testing Validation Utilities ---")

    try:
        # Test tenant access validation
        result = validate_tenant_access("tenant-123", "user-456")
        assert isinstance(result, bool)
        print("✓ Tenant access validation works")

        # Test agent config validation
        valid_config = {"max_execution_time": 30, "max_iterations": 10}
        validate_agent_config(valid_config)
        print("✓ Agent config validation works")

        # Test invalid config
        try:
            invalid_config = {"max_execution_time": -1}
            validate_agent_config(invalid_config)
            print("✗ Should have raised validation error")
            return False
        except AgentValidationError:
            print("✓ Invalid config properly rejected")

        # Test input data validation
        valid_data = {"messages": ["test"], "user_id": "123"}
        validate_input_data(valid_data, required_fields=["messages"])
        print("✓ Input data validation works")

        # Test missing required field
        try:
            invalid_data = {"user_id": "123"}
            validate_input_data(invalid_data, required_fields=["messages"])
            print("✗ Should have raised validation error")
            return False
        except AgentValidationError:
            print("✓ Missing required field properly detected")

        return True

    except Exception as e:
        print(f"✗ Validation test failed: {e}")
        traceback.print_exc()
        return False


async def test_logging():
    """Test logging utilities."""
    print("\n--- Testing Logging Utilities ---")

    try:
        # Test structured logger
        logger = StructuredLogger("test_agent")
        logger.info("Test info message", extra={"test_key": "test_value"})
        logger.warning("Test warning message")
        logger.error("Test error message")
        print("✓ Structured logger works")

        # Test security event logging
        log_security_event("test_event", {"user_id": "123"})
        print("✓ Security event logging works")

        # Test performance event logging
        log_performance_event("test_metric", 1.5, {"agent_type": "test"})
        print("✓ Performance event logging works")

        return True

    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        traceback.print_exc()
        return False


async def test_monitoring():
    """Test monitoring utilities."""
    print("\n--- Testing Monitoring Utilities ---")

    try:
        # Test agent metrics
        metrics = AgentMetrics("test_agent", "test")

        execution_id = metrics.record_execution_start()
        assert execution_id is not None
        print("✓ Execution start recording works")

        metrics.record_execution_success(1.5)
        print("✓ Execution success recording works")

        metrics.record_execution_failure(2.0, "test_error")
        print("✓ Execution failure recording works")

        metrics.record_error("validation", "Test error message")
        print("✓ Error recording works")

        metrics_data = metrics.get_metrics()
        assert isinstance(metrics_data, dict)
        assert "agent_name" in metrics_data
        print("✓ Metrics retrieval works")

        # Test performance tracker
        tracker = PerformanceTracker("test_agent")

        with tracker.time_operation("test_operation"):
            await asyncio.sleep(0.1)  # Simulate work

        stats = tracker.get_operation_stats("test_operation")
        assert stats["count"] == 1
        assert stats["average"] >= 0.1
        print("✓ Performance tracking works")

        # Test health checker
        health_checker = HealthChecker("test_agent")

        def test_health_check():
            return {"status": "healthy"}

        health_checker.register_health_check("test_check", test_health_check)

        health_status = health_checker.get_health_status()
        assert isinstance(health_status, dict)
        assert "overall_status" in health_status
        print("✓ Health checking works")

        return True

    except Exception as e:
        print(f"✗ Monitoring test failed: {e}")
        traceback.print_exc()
        return False


async def test_error_handling():
    """Test error handling utilities."""
    print("\n--- Testing Error Handling Utilities ---")

    try:
        # Test error handler
        error_handler = ErrorHandler("test_agent")

        # Test successful operation (no error)
        def successful_operation():
            return "success"

        context = {"operation": successful_operation}

        # Simulate an error and recovery
        test_error = Exception("Test error")
        recovery_result = error_handler.handle_error(
            test_error, context, ErrorRecoveryStrategy.RETRY
        )

        assert recovery_result.strategy_used == ErrorRecoveryStrategy.RETRY
        print("✓ Error handling with retry strategy works")

        # Test fallback strategy
        def fallback_operation():
            return "fallback_result"

        fallback_context = {"fallback_operation": fallback_operation}

        fallback_result = error_handler.handle_error(
            test_error, fallback_context, ErrorRecoveryStrategy.FALLBACK
        )

        assert fallback_result.strategy_used == ErrorRecoveryStrategy.FALLBACK
        print("✓ Error handling with fallback strategy works")

        # Test error statistics
        stats = error_handler.get_error_statistics()
        assert isinstance(stats, dict)
        assert "agent_name" in stats
        print("✓ Error statistics collection works")

        return True

    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        traceback.print_exc()
        return False


async def test_integration():
    """Test integration between utilities."""
    print("\n--- Testing Utility Integration ---")

    try:
        # Create integrated test scenario
        logger = StructuredLogger("integration_test")
        metrics = AgentMetrics("integration_test", "test")
        error_handler = ErrorHandler("integration_test")

        # Simulate agent operation with monitoring
        execution_id = metrics.record_execution_start()

        start_time = time.time()

        try:
            # Simulate some work
            await asyncio.sleep(0.1)

            # Log progress
            logger.info("Operation in progress", extra={"execution_id": execution_id})

            # Simulate successful completion
            execution_time = time.time() - start_time
            metrics.record_execution_success(execution_time)

            logger.info(
                "Operation completed successfully",
                extra={"execution_id": execution_id, "execution_time": execution_time},
            )

            print("✓ Integrated monitoring and logging works")

        except Exception as e:
            # Handle error with recovery
            execution_time = time.time() - start_time
            metrics.record_execution_failure(execution_time, "integration_error")

            logger.error(
                "Operation failed",
                extra={"execution_id": execution_id, "error": str(e)},
            )

            recovery_result = error_handler.handle_error(e, {})
            print(f"✓ Integrated error handling works: {recovery_result.strategy_used}")

        # Verify metrics were collected
        final_metrics = metrics.get_metrics()
        assert final_metrics["total_executions"] > 0
        print("✓ Metrics integration works")

        return True

    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        traceback.print_exc()
        return False


async def main():
    """Run all utility tests."""
    print("🧪 Starting Utility Tests")
    print("=" * 50)

    tests = [
        test_validation,
        test_logging,
        test_monitoring,
        test_error_handling,
        test_integration,
    ]

    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            traceback.print_exc()
            results.append(False)

    print("\n" + "=" * 50)
    print("🏁 Utility Test Results Summary")
    print("=" * 50)

    passed = sum(results)
    total = len(results)

    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All utility tests passed! Utilities are working correctly.")
        return 0
    else:
        print("❌ Some utility tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
