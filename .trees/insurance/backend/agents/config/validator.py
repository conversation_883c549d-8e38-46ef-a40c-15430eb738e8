"""
Configuration Validator

This module provides validation for agent configurations, ensuring that
configurations are valid, complete, and follow best practices.
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from pydantic import ValidationError

from .models import (
    AgentConfigModel,
    NodeConfigModel,
    PromptConfigModel,
    ModelConfigModel,
    AgentType,
    NodeType,
)

logger = logging.getLogger(__name__)


class ValidationResult:
    """Result of configuration validation."""

    def __init__(self):
        self.is_valid = True
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.suggestions: List[str] = []

    def add_error(self, message: str) -> None:
        """Add validation error."""
        self.errors.append(message)
        self.is_valid = False

    def add_warning(self, message: str) -> None:
        """Add validation warning."""
        self.warnings.append(message)

    def add_suggestion(self, message: str) -> None:
        """Add validation suggestion."""
        self.suggestions.append(message)

    def __bool__(self) -> bool:
        """Return True if validation passed."""
        return self.is_valid


class ConfigValidator:
    """
    Validates agent configurations for correctness and best practices.

    Performs validation on:
    - Required fields and data types
    - Configuration consistency
    - Best practices compliance
    - Security considerations
    """

    def __init__(self):
        """Initialize the validator."""
        self.valid_providers = {
            "openai",
            "anthropic",
            "google",
            "cohere",
            "mistral",
            "groq",
        }

        self.valid_model_patterns = {
            "openai": r"^openai/(gpt-[34]|gpt-[34]\.5|gpt-4o|text-davinci|text-curie)",
            "anthropic": r"^anthropic/(claude-[23]|claude-instant)",
            "google": r"^google/(gemini|palm)",
            "cohere": r"^cohere/(command|embed)",
            "mistral": r"^mistral/(mistral|mixtral)",
            "groq": r"^groq/",
        }

    def validate_config(self, config: AgentConfigModel) -> ValidationResult:
        """
        Validate a complete agent configuration.

        Args:
            config: Agent configuration to validate

        Returns:
            Validation result with errors, warnings, and suggestions
        """
        result = ValidationResult()

        try:
            # Basic Pydantic validation
            config.dict()  # This will raise ValidationError if invalid

            # Custom validation rules
            self._validate_basic_fields(config, result)
            self._validate_model_config(
                config.llm_config, result, f"Agent {config.name}"
            )
            self._validate_prompt_config(
                config.prompt_config, result, f"Agent {config.name}"
            )
            self._validate_nodes(config.nodes, result)
            self._validate_capabilities(config, result)
            self._validate_consistency(config, result)
            self._validate_security(config, result)
            self._validate_best_practices(config, result)

        except ValidationError as e:
            for error in e.errors():
                result.add_error(f"Pydantic validation error: {error}")
        except Exception as e:
            result.add_error(f"Unexpected validation error: {e}")

        return result

    def _validate_basic_fields(
        self, config: AgentConfigModel, result: ValidationResult
    ) -> None:
        """Validate basic required fields."""
        # Name validation
        if not config.name:
            result.add_error("Agent name is required")
        elif not re.match(r"^[a-z][a-z0-9_]*$", config.name):
            result.add_error(
                "Agent name must be lowercase, start with letter, and contain only letters, numbers, and underscores"
            )

        # Display name validation
        if not config.display_name:
            result.add_warning("Display name is recommended for better UX")

        # Description validation
        if not config.description:
            result.add_warning("Description is recommended for documentation")
        elif len(config.description) < 10:
            result.add_suggestion("Consider providing a more detailed description")

        # Agent type validation
        if config.agent_type not in [t.value for t in AgentType]:
            result.add_error(f"Invalid agent type: {config.agent_type}")

        # Version validation
        if not re.match(r"^\d+\.\d+\.\d+$", config.version):
            result.add_warning(
                "Version should follow semantic versioning (e.g., 1.0.0)"
            )

    def _validate_model_config(
        self,
        model_config: Optional[ModelConfigModel],
        result: ValidationResult,
        context: str,
    ) -> None:
        """Validate model configuration."""
        if not model_config:
            result.add_warning(f"{context}: No model configuration specified")
            return

        # Provider validation
        provider = model_config.provider.lower()
        if provider not in self.valid_providers:
            result.add_error(
                f"{context}: Invalid provider '{provider}'. Valid providers: {', '.join(self.valid_providers)}"
            )

        # Model name validation
        model_name = model_config.model_name
        if "/" not in model_name:
            result.add_error(
                f"{context}: Model name must be in format 'provider/model'"
            )
        else:
            model_provider = model_name.split("/")[0].lower()
            if model_provider != provider:
                result.add_error(
                    f"{context}: Model provider '{model_provider}' doesn't match config provider '{provider}'"
                )

            # Validate model name pattern
            if provider in self.valid_model_patterns:
                pattern = self.valid_model_patterns[provider]
                if not re.match(pattern, model_name, re.IGNORECASE):
                    result.add_warning(
                        f"{context}: Model name '{model_name}' may not be valid for provider '{provider}'"
                    )

        # Temperature validation
        if not (0.0 <= model_config.temperature <= 2.0):
            result.add_error(f"{context}: Temperature must be between 0.0 and 2.0")
        elif model_config.temperature > 1.0:
            result.add_suggestion(
                f"{context}: High temperature ({model_config.temperature}) may produce inconsistent results"
            )

        # Token limits
        if model_config.max_tokens and model_config.max_tokens > 32000:
            result.add_warning(
                f"{context}: Very high max_tokens ({model_config.max_tokens}) may be expensive"
            )

    def _validate_prompt_config(
        self,
        prompt_config: Optional[PromptConfigModel],
        result: ValidationResult,
        context: str,
    ) -> None:
        """Validate prompt configuration."""
        if not prompt_config:
            result.add_suggestion(
                f"{context}: Consider adding a system prompt for better performance"
            )
            return

        # Key validation
        if not prompt_config.key:
            result.add_error(f"{context}: Prompt key is required")
        elif not re.match(r"^[a-z][a-z0-9_]*$", prompt_config.key):
            result.add_error(
                f"{context}: Prompt key must be lowercase and contain only letters, numbers, and underscores"
            )

        # Content validation
        if not prompt_config.content:
            result.add_error(f"{context}: Prompt content is required")
        elif len(prompt_config.content) < 20:
            result.add_suggestion(
                f"{context}: Prompt content seems very short, consider adding more context"
            )
        elif len(prompt_config.content) > 8000:
            result.add_warning(
                f"{context}: Very long prompt ({len(prompt_config.content)} chars) may hit token limits"
            )

        # Check for common prompt issues
        content_lower = prompt_config.content.lower()
        if "you are" not in content_lower and "your role" not in content_lower:
            result.add_suggestion(
                f"{context}: Consider starting prompt with role definition (e.g., 'You are...')"
            )

        if "{" in prompt_config.content and "}" in prompt_config.content:
            # Check for template variables
            variables = re.findall(r"\{([^}]+)\}", prompt_config.content)
            if variables:
                result.add_suggestion(
                    f"{context}: Prompt contains template variables: {', '.join(variables)}"
                )

    def _validate_nodes(
        self, nodes: Dict[str, NodeConfigModel], result: ValidationResult
    ) -> None:
        """Validate node configurations."""
        if not nodes:
            result.add_suggestion("Consider adding nodes for multi-step workflows")
            return

        node_names = set()
        for node_name, node_config in nodes.items():
            # Check for duplicate names
            if node_name in node_names:
                result.add_error(f"Duplicate node name: {node_name}")
            node_names.add(node_name)

            # Validate individual node
            self._validate_node(node_config, result)

    def _validate_node(self, node: NodeConfigModel, result: ValidationResult) -> None:
        """Validate individual node configuration."""
        context = f"Node {node.name}"

        # Name validation
        if not re.match(r"^[a-z][a-z0-9_]*$", node.name):
            result.add_error(
                f"{context}: Node name must be lowercase and contain only letters, numbers, and underscores"
            )

        # Node type validation
        if node.node_type not in [t.value for t in NodeType]:
            result.add_error(f"{context}: Invalid node type '{node.node_type}'")

        # Validate nested configurations
        if node.llm_config:
            self._validate_model_config(node.llm_config, result, context)

        if node.prompt_config:
            self._validate_prompt_config(node.prompt_config, result, context)

        # Inheritance validation
        if not node.inherit_model_from_agent and not node.llm_config:
            result.add_warning(
                f"{context}: Node doesn't inherit model from agent but has no model config"
            )

        if not node.inherit_prompt_from_agent and not node.prompt_config:
            result.add_warning(
                f"{context}: Node doesn't inherit prompt from agent but has no prompt config"
            )

    def _validate_capabilities(
        self, config: AgentConfigModel, result: ValidationResult
    ) -> None:
        """Validate agent capabilities."""
        if not config.capabilities:
            result.add_suggestion(
                "Consider specifying agent capabilities for better documentation"
            )
            return

        # Check for reasonable capabilities
        common_capabilities = {
            "legal_research",
            "document_generation",
            "client_intake",
            "task_management",
            "calendar_management",
            "conflict_checking",
            "deadline_tracking",
            "case_analysis",
        }

        unknown_capabilities = set(config.capabilities) - common_capabilities
        if unknown_capabilities:
            result.add_suggestion(
                f"Unknown capabilities (consider documenting): {', '.join(unknown_capabilities)}"
            )

    def _validate_consistency(
        self, config: AgentConfigModel, result: ValidationResult
    ) -> None:
        """Validate configuration consistency."""
        # Check agent type vs capabilities consistency
        if config.agent_type == AgentType.INTERACTIVE:
            if "long_running_analysis" in config.capabilities:
                result.add_warning(
                    "Interactive agent shouldn't have long-running capabilities"
                )

        elif config.agent_type == AgentType.INSIGHTS:
            if "real_time_chat" in config.capabilities:
                result.add_warning(
                    "Insights agent shouldn't have real-time chat capabilities"
                )

        # Check nodes consistency
        for node_name, node in config.nodes.items():
            if node.node_type == NodeType.ROUTER and len(config.nodes) == 1:
                result.add_suggestion(
                    f"Router node '{node_name}' exists alone - consider adding processing nodes"
                )

    def _validate_security(
        self, config: AgentConfigModel, result: ValidationResult
    ) -> None:
        """Validate security considerations."""
        # Check for sensitive information in prompts
        if config.prompt_config and config.prompt_config.content:
            content_lower = config.prompt_config.content.lower()
            sensitive_patterns = [
                "password",
                "api_key",
                "secret",
                "token",
                "credential",
            ]

            for pattern in sensitive_patterns:
                if pattern in content_lower:
                    result.add_error(
                        f"Prompt contains potentially sensitive information: '{pattern}'"
                    )

        # Check node prompts
        for node_name, node in config.nodes.items():
            if node.prompt_config and node.prompt_config.content:
                content_lower = node.prompt_config.content.lower()
                for pattern in sensitive_patterns:
                    if pattern in content_lower:
                        result.add_error(
                            f"Node '{node_name}' prompt contains potentially sensitive information: '{pattern}'"
                        )

        # Check for overly permissive configurations
        if config.llm_config and config.llm_config.temperature > 1.5:
            result.add_warning(
                "High temperature may produce unpredictable outputs in production"
            )

    def _validate_best_practices(
        self, config: AgentConfigModel, result: ValidationResult
    ) -> None:
        """Validate against best practices."""
        # Documentation best practices
        if not config.description or len(config.description) < 50:
            result.add_suggestion(
                "Provide detailed description for better maintainability"
            )

        # Configuration best practices
        if not config.llm_config and not any(
            node.llm_config for node in config.nodes.values()
        ):
            result.add_warning(
                "No model configuration found - agent may not function properly"
            )

        # Testing best practices
        if not config.test_cases:
            result.add_suggestion("Consider adding test cases for quality assurance")

        # Versioning best practices
        if config.version == "1.0.0" and not config.is_experimental:
            result.add_suggestion(
                "Consider using pre-release version (e.g., 0.1.0) for new agents"
            )

        # Performance best practices
        if (
            config.llm_config
            and config.llm_config.max_tokens
            and config.llm_config.max_tokens > 8000
        ):
            result.add_suggestion(
                "Consider if high max_tokens is necessary for performance and cost"
            )


def validate_agent_config(config: AgentConfigModel) -> ValidationResult:
    """
    Validate agent configuration.

    Args:
        config: Agent configuration to validate

    Returns:
        Validation result
    """
    validator = ConfigValidator()
    return validator.validate_config(config)
