"""
Agent Testing and Evaluation Framework

This module provides comprehensive testing and evaluation capabilities
for agents, including test case management, sandbox environments,
and evaluation metrics.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from .models import (
    AgentConfigModel,
    TestCaseModel,
    EvaluationResultModel,
    NodeConfigModel,
)

logger = logging.getLogger(__name__)


class TestStatus(str, Enum):
    """Test execution status."""

    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    TIMEOUT = "timeout"


class EvaluationMetric(str, Enum):
    """Evaluation metrics."""

    ACCURACY = "accuracy"
    RESPONSE_TIME = "response_time"
    TOKEN_EFFICIENCY = "token_efficiency"
    RELEVANCE = "relevance"
    COHERENCE = "coherence"
    SAFETY = "safety"


@dataclass
class TestExecution:
    """Represents a test execution."""

    test_case: TestCaseModel
    status: TestStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    result: Optional[EvaluationResultModel] = None
    error: Optional[str] = None


class SandboxEnvironment:
    """
    Sandbox environment for safe agent testing.

    Provides isolated execution environment with:
    - Resource limits
    - Network isolation
    - Safe tool execution
    - Monitoring and logging
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize sandbox environment."""
        self.config = config or {}
        self.max_execution_time = self.config.get("max_execution_time", 60)
        self.max_memory_mb = self.config.get("max_memory_mb", 512)
        self.enable_network = self.config.get("enable_network", False)
        self.allowed_tools = self.config.get("allowed_tools", [])

        self._active_executions: Dict[str, TestExecution] = {}

    async def execute_test(
        self,
        agent_config: AgentConfigModel,
        test_case: TestCaseModel,
        node_name: Optional[str] = None,
    ) -> EvaluationResultModel:
        """
        Execute a test case in the sandbox environment.

        Args:
            agent_config: Agent configuration
            test_case: Test case to execute
            node_name: Specific node to test (optional)

        Returns:
            Evaluation result
        """
        execution_id = f"{test_case.id}_{int(time.time())}"
        execution = TestExecution(
            test_case=test_case, status=TestStatus.RUNNING, start_time=datetime.utcnow()
        )

        self._active_executions[execution_id] = execution

        try:
            # Execute with timeout
            result = await asyncio.wait_for(
                self._execute_test_internal(agent_config, test_case, node_name),
                timeout=test_case.timeout_seconds,
            )

            execution.status = (
                TestStatus.PASSED if result.success else TestStatus.FAILED
            )
            execution.result = result

        except asyncio.TimeoutError:
            execution.status = TestStatus.TIMEOUT
            execution.error = (
                f"Test timed out after {test_case.timeout_seconds} seconds"
            )
            result = EvaluationResultModel(
                test_case_id=test_case.id,
                agent_name=agent_config.name,
                node_name=node_name,
                success=False,
                execution_time_ms=test_case.timeout_seconds * 1000,
                error_message="Test timed out",
                error_type="timeout",
                environment="sandbox",
            )

        except Exception as e:
            execution.status = TestStatus.FAILED
            execution.error = str(e)
            result = EvaluationResultModel(
                test_case_id=test_case.id,
                agent_name=agent_config.name,
                node_name=node_name,
                success=False,
                execution_time_ms=0,
                error_message=str(e),
                error_type=type(e).__name__,
                environment="sandbox",
            )

        finally:
            execution.end_time = datetime.utcnow()
            del self._active_executions[execution_id]

        return result

    async def _execute_test_internal(
        self,
        agent_config: AgentConfigModel,
        test_case: TestCaseModel,
        node_name: Optional[str],
    ) -> EvaluationResultModel:
        """Internal test execution logic."""
        start_time = time.time()

        try:
            # Mock agent execution for now
            # In a real implementation, this would:
            # 1. Create agent instance with config
            # 2. Execute the test input
            # 3. Compare with expected output
            # 4. Calculate metrics

            # Simulate execution time
            await asyncio.sleep(0.1)

            # Mock successful execution
            execution_time_ms = int((time.time() - start_time) * 1000)

            result = EvaluationResultModel(
                test_case_id=test_case.id,
                agent_name=agent_config.name,
                node_name=node_name,
                success=True,
                score=0.95,  # Mock score
                output={"mock": "response"},
                execution_time_ms=execution_time_ms,
                token_usage={"input": 50, "output": 30},
                environment="sandbox",
            )

            return result

        except Exception as e:
            execution_time_ms = int((time.time() - start_time) * 1000)
            return EvaluationResultModel(
                test_case_id=test_case.id,
                agent_name=agent_config.name,
                node_name=node_name,
                success=False,
                execution_time_ms=execution_time_ms,
                error_message=str(e),
                error_type=type(e).__name__,
                environment="sandbox",
            )

    def get_active_executions(self) -> Dict[str, TestExecution]:
        """Get currently active test executions."""
        return self._active_executions.copy()

    def cleanup(self) -> None:
        """Clean up sandbox resources."""
        # Cancel any active executions
        for execution in self._active_executions.values():
            execution.status = TestStatus.SKIPPED
            execution.end_time = datetime.utcnow()

        self._active_executions.clear()


class TestSuite:
    """
    Collection of test cases for an agent or system.

    Manages test case organization, execution, and reporting.
    """

    def __init__(self, name: str, description: str = ""):
        """Initialize test suite."""
        self.name = name
        self.description = description
        self.test_cases: Dict[str, TestCaseModel] = {}
        self.results: Dict[str, EvaluationResultModel] = {}

    def add_test_case(self, test_case: TestCaseModel) -> None:
        """Add a test case to the suite."""
        self.test_cases[test_case.id] = test_case

    def remove_test_case(self, test_case_id: str) -> None:
        """Remove a test case from the suite."""
        if test_case_id in self.test_cases:
            del self.test_cases[test_case_id]
        if test_case_id in self.results:
            del self.results[test_case_id]

    def get_test_cases(self, tags: Optional[List[str]] = None) -> List[TestCaseModel]:
        """Get test cases, optionally filtered by tags."""
        test_cases = list(self.test_cases.values())

        if tags:
            test_cases = [
                tc for tc in test_cases if any(tag in tc.tags for tag in tags)
            ]

        return test_cases

    async def run_all_tests(
        self,
        agent_config: AgentConfigModel,
        sandbox: Optional[SandboxEnvironment] = None,
    ) -> Dict[str, EvaluationResultModel]:
        """Run all test cases in the suite."""
        if sandbox is None:
            sandbox = SandboxEnvironment()

        results = {}

        for test_case in self.test_cases.values():
            if test_case.is_enabled:
                try:
                    result = await sandbox.execute_test(agent_config, test_case)
                    results[test_case.id] = result
                    self.results[test_case.id] = result
                except Exception as e:
                    logger.error(f"Failed to execute test {test_case.id}: {e}")

        return results

    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of test results."""
        if not self.results:
            return {"total": 0, "passed": 0, "failed": 0, "success_rate": 0.0}

        total = len(self.results)
        passed = sum(1 for r in self.results.values() if r.success)
        failed = total - passed
        success_rate = passed / total if total > 0 else 0.0

        return {
            "total": total,
            "passed": passed,
            "failed": failed,
            "success_rate": success_rate,
            "avg_execution_time": sum(
                r.execution_time_ms for r in self.results.values()
            )
            / total,
            "avg_score": sum(r.score or 0 for r in self.results.values()) / total,
        }


class EvaluationFramework:
    """
    Comprehensive evaluation framework for agents.

    Provides automated testing, benchmarking, and quality assessment.
    """

    def __init__(self):
        """Initialize evaluation framework."""
        self.test_suites: Dict[str, TestSuite] = {}
        self.sandbox = SandboxEnvironment()
        self.evaluators: Dict[EvaluationMetric, Callable] = {}

        # Register default evaluators
        self._register_default_evaluators()

    def _register_default_evaluators(self) -> None:
        """Register default evaluation functions."""
        self.evaluators[EvaluationMetric.RESPONSE_TIME] = self._evaluate_response_time
        self.evaluators[EvaluationMetric.TOKEN_EFFICIENCY] = (
            self._evaluate_token_efficiency
        )
        self.evaluators[EvaluationMetric.ACCURACY] = self._evaluate_accuracy

    def _evaluate_response_time(self, result: EvaluationResultModel) -> float:
        """Evaluate response time performance."""
        # Score based on execution time (lower is better)
        max_acceptable_time = 5000  # 5 seconds
        if result.execution_time_ms <= max_acceptable_time:
            return 1.0 - (result.execution_time_ms / max_acceptable_time) * 0.5
        else:
            return 0.5 * (max_acceptable_time / result.execution_time_ms)

    def _evaluate_token_efficiency(self, result: EvaluationResultModel) -> float:
        """Evaluate token usage efficiency."""
        if not result.token_usage:
            return 0.5  # Neutral score if no token data

        total_tokens = result.token_usage.get("input", 0) + result.token_usage.get(
            "output", 0
        )

        # Score based on token efficiency (fewer tokens for same quality is better)
        if total_tokens <= 100:
            return 1.0
        elif total_tokens <= 500:
            return 0.8
        elif total_tokens <= 1000:
            return 0.6
        else:
            return 0.4

    def _evaluate_accuracy(self, result: EvaluationResultModel) -> float:
        """Evaluate accuracy based on success and score."""
        if not result.success:
            return 0.0

        return result.score or 0.5

    def create_test_suite(self, name: str, description: str = "") -> TestSuite:
        """Create a new test suite."""
        suite = TestSuite(name, description)
        self.test_suites[name] = suite
        return suite

    def get_test_suite(self, name: str) -> Optional[TestSuite]:
        """Get a test suite by name."""
        return self.test_suites.get(name)

    async def evaluate_agent(
        self, agent_config: AgentConfigModel, test_suite_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Comprehensive evaluation of an agent.

        Args:
            agent_config: Agent configuration to evaluate
            test_suite_name: Specific test suite to use (optional)

        Returns:
            Evaluation report
        """
        if test_suite_name:
            suites = (
                [self.test_suites[test_suite_name]]
                if test_suite_name in self.test_suites
                else []
            )
        else:
            # Use all relevant test suites
            suites = [suite for suite in self.test_suites.values()]

        evaluation_report = {
            "agent_name": agent_config.name,
            "evaluation_time": datetime.utcnow().isoformat(),
            "test_suites": {},
            "overall_metrics": {},
            "recommendations": [],
        }

        all_results = []

        for suite in suites:
            suite_results = await suite.run_all_tests(agent_config, self.sandbox)
            suite_summary = suite.get_test_summary()

            evaluation_report["test_suites"][suite.name] = {
                "summary": suite_summary,
                "results": {
                    test_id: result.dict() for test_id, result in suite_results.items()
                },
            }

            all_results.extend(suite_results.values())

        # Calculate overall metrics
        if all_results:
            evaluation_report["overall_metrics"] = self._calculate_overall_metrics(
                all_results
            )
            evaluation_report["recommendations"] = self._generate_recommendations(
                agent_config, all_results
            )

        return evaluation_report

    def _calculate_overall_metrics(
        self, results: List[EvaluationResultModel]
    ) -> Dict[str, float]:
        """Calculate overall evaluation metrics."""
        if not results:
            return {}

        metrics = {}

        for metric, evaluator in self.evaluators.items():
            scores = [evaluator(result) for result in results]
            metrics[metric.value] = sum(scores) / len(scores)

        # Overall success rate
        metrics["success_rate"] = sum(1 for r in results if r.success) / len(results)

        # Average execution time
        metrics["avg_execution_time_ms"] = sum(
            r.execution_time_ms for r in results
        ) / len(results)

        return metrics

    def _generate_recommendations(
        self, agent_config: AgentConfigModel, results: List[EvaluationResultModel]
    ) -> List[str]:
        """Generate recommendations based on evaluation results."""
        recommendations = []

        # Performance recommendations
        avg_time = sum(r.execution_time_ms for r in results) / len(results)
        if avg_time > 3000:  # 3 seconds
            recommendations.append("Consider optimizing for faster response times")

        # Success rate recommendations
        success_rate = sum(1 for r in results if r.success) / len(results)
        if success_rate < 0.8:
            recommendations.append(
                "Success rate is below 80% - review test failures and improve agent logic"
            )

        # Token efficiency recommendations
        if any(r.token_usage and sum(r.token_usage.values()) > 1000 for r in results):
            recommendations.append(
                "High token usage detected - consider prompt optimization"
            )

        # Configuration recommendations
        if not agent_config.test_cases:
            recommendations.append(
                "Add test cases to agent configuration for continuous validation"
            )

        return recommendations


class AgentTester:
    """
    High-level interface for agent testing.

    Provides simple methods for testing agents with built-in
    test cases and evaluation.
    """

    def __init__(self):
        """Initialize agent tester."""
        self.evaluation_framework = EvaluationFramework()
        self.sandbox = SandboxEnvironment()

    async def quick_test(
        self,
        agent_config: AgentConfigModel,
        test_input: Dict[str, Any],
        expected_output: Optional[Dict[str, Any]] = None,
    ) -> EvaluationResultModel:
        """
        Perform a quick test of an agent.

        Args:
            agent_config: Agent configuration
            test_input: Test input data
            expected_output: Expected output (optional)

        Returns:
            Evaluation result
        """
        # Create a temporary test case
        test_case = TestCaseModel(
            id=f"quick_test_{int(time.time())}",
            name="Quick Test",
            description="Ad-hoc quick test",
            agent_name=agent_config.name,
            input_data=test_input,
            expected_output=expected_output,
            success_criteria=["Agent responds without error"],
            timeout_seconds=30,
        )

        return await self.sandbox.execute_test(agent_config, test_case)

    async def benchmark_agent(self, agent_config: AgentConfigModel) -> Dict[str, Any]:
        """
        Run comprehensive benchmark tests for an agent.

        Args:
            agent_config: Agent configuration to benchmark

        Returns:
            Benchmark report
        """
        return await self.evaluation_framework.evaluate_agent(agent_config)

    def create_test_suite_for_agent(self, agent_config: AgentConfigModel) -> TestSuite:
        """Create a default test suite for an agent."""
        suite_name = f"{agent_config.name}_default_tests"
        suite = self.evaluation_framework.create_test_suite(
            suite_name, f"Default test suite for {agent_config.display_name}"
        )

        # Add basic test cases based on agent capabilities
        self._add_default_test_cases(suite, agent_config)

        return suite

    def _add_default_test_cases(
        self, suite: TestSuite, agent_config: AgentConfigModel
    ) -> None:
        """Add default test cases based on agent capabilities."""
        # Basic functionality test
        basic_test = TestCaseModel(
            id=f"{agent_config.name}_basic_test",
            name="Basic Functionality Test",
            description="Test basic agent functionality",
            agent_name=agent_config.name,
            input_data={"message": "Hello, can you help me?"},
            success_criteria=["Agent responds", "Response is coherent"],
            timeout_seconds=30,
            tags=["basic", "functionality"],
        )
        suite.add_test_case(basic_test)

        # Capability-specific tests
        for capability in agent_config.capabilities:
            capability_test = TestCaseModel(
                id=f"{agent_config.name}_{capability}_test",
                name=f"{capability.title()} Test",
                description=f"Test {capability} capability",
                agent_name=agent_config.name,
                input_data={"capability": capability, "test": True},
                success_criteria=[f"Agent demonstrates {capability} capability"],
                timeout_seconds=45,
                tags=["capability", capability],
            )
            suite.add_test_case(capability_test)
