"""
Configuration Loader

This module handles loading agent configurations from various sources:
- YAML/JSON configuration files
- Database (superadmin configurations)
- Environment variables
- Default configurations
"""

import os
import json
import yaml
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from datetime import datetime

from .models import (
    AgentConfigModel,
    NodeConfigModel,
    PromptConfigModel,
    ModelConfigModel,
)

logger = logging.getLogger(__name__)


class ConfigLoader:
    """
    Loads agent configurations from multiple sources.

    Supports loading from:
    - YAML configuration files
    - JSON configuration files
    - Database (superadmin settings)
    - Environment variables
    """

    def __init__(self):
        """Initialize the configuration loader."""
        self.config_dir = Path(__file__).parent
        self.agents_config_dir = self.config_dir / "agents"
        self.environments_config_dir = self.config_dir / "environments"

        # Ensure directories exist
        self.agents_config_dir.mkdir(exist_ok=True)
        self.environments_config_dir.mkdir(exist_ok=True)

    def load_all_configs(self) -> Dict[str, AgentConfigModel]:
        """
        Load all agent configurations from files.

        Returns:
            Dictionary of agent configurations keyed by agent name
        """
        configs = {}

        # Load from agents directory
        for config_file in self.agents_config_dir.glob("*.yaml"):
            try:
                agent_config = self.load_agent_config_file(config_file)
                if agent_config:
                    configs[agent_config.name] = agent_config
            except Exception as e:
                logger.error(f"Failed to load config from {config_file}: {e}")

        # Also check for JSON files
        for config_file in self.agents_config_dir.glob("*.json"):
            try:
                agent_config = self.load_agent_config_file(config_file)
                if agent_config:
                    configs[agent_config.name] = agent_config
            except Exception as e:
                logger.error(f"Failed to load config from {config_file}: {e}")

        logger.info(f"Loaded {len(configs)} agent configurations from files")
        return configs

    def load_agent_config_file(self, config_file: Path) -> Optional[AgentConfigModel]:
        """
        Load agent configuration from a single file.

        Args:
            config_file: Path to configuration file

        Returns:
            Agent configuration or None if failed
        """
        try:
            with open(config_file, "r") as f:
                if config_file.suffix.lower() == ".yaml":
                    data = yaml.safe_load(f)
                else:
                    data = json.load(f)

            # Convert to AgentConfigModel
            return self._dict_to_agent_config(data)

        except Exception as e:
            logger.error(f"Failed to load config file {config_file}: {e}")
            return None

    def _dict_to_agent_config(self, data: Dict[str, Any]) -> AgentConfigModel:
        """
        Convert dictionary data to AgentConfigModel.

        Args:
            data: Configuration data dictionary

        Returns:
            Agent configuration model
        """
        # Handle nested objects
        if "prompt_config" in data and data["prompt_config"]:
            data["prompt_config"] = PromptConfigModel(**data["prompt_config"])

        if "llm_config" in data and data["llm_config"]:
            data["llm_config"] = ModelConfigModel(**data["llm_config"])

        # Handle nodes
        if "nodes" in data and data["nodes"]:
            nodes = {}
            for node_name, node_data in data["nodes"].items():
                # Handle nested node objects
                if "prompt_config" in node_data and node_data["prompt_config"]:
                    node_data["prompt_config"] = PromptConfigModel(
                        **node_data["prompt_config"]
                    )

                if "llm_config" in node_data and node_data["llm_config"]:
                    node_data["llm_config"] = ModelConfigModel(
                        **node_data["llm_config"]
                    )

                nodes[node_name] = NodeConfigModel(**node_data)

            data["nodes"] = nodes

        return AgentConfigModel(**data)

    def save_agent_config(
        self, config: AgentConfigModel, file_format: str = "yaml"
    ) -> bool:
        """
        Save agent configuration to file.

        Args:
            config: Agent configuration to save
            file_format: File format ('yaml' or 'json')

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            filename = f"{config.name}.{file_format}"
            config_file = self.agents_config_dir / filename

            # Convert to dictionary
            data = config.dict(exclude_none=True)

            # Convert datetime objects to strings
            data = self._serialize_datetime_objects(data)

            with open(config_file, "w") as f:
                if file_format == "yaml":
                    yaml.dump(data, f, default_flow_style=False, indent=2)
                else:
                    json.dump(data, f, indent=2)

            logger.info(f"Saved agent configuration: {config.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to save agent configuration {config.name}: {e}")
            return False

    def _serialize_datetime_objects(self, data: Any) -> Any:
        """Recursively serialize datetime objects to strings."""
        if isinstance(data, datetime):
            return data.isoformat()
        elif isinstance(data, dict):
            return {
                key: self._serialize_datetime_objects(value)
                for key, value in data.items()
            }
        elif isinstance(data, list):
            return [self._serialize_datetime_objects(item) for item in data]
        else:
            return data

    def load_from_database(self) -> Dict[str, Any]:
        """
        Load configurations from database (superadmin settings).

        This integrates with the existing superadmin LLM selection and prompt systems.

        Returns:
            Dictionary of database configurations
        """
        configs = {}

        try:
            # Load LLM model selections
            llm_configs = self._load_llm_selections_from_db()

            # Load prompt configurations
            prompt_configs = self._load_prompts_from_db()

            # Merge configurations
            configs = self._merge_db_configs(llm_configs, prompt_configs)

        except Exception as e:
            logger.error(f"Failed to load configurations from database: {e}")

        return configs

    def _load_llm_selections_from_db(self) -> Dict[str, Any]:
        """Load LLM model selections from database."""
        # This would integrate with the existing superadmin LLM selection system
        # For now, return empty dict - will be implemented when integrating with Supabase
        return {}

    def _load_prompts_from_db(self) -> Dict[str, Any]:
        """Load prompt configurations from database."""
        # This would integrate with the existing superadmin prompt system
        # For now, return empty dict - will be implemented when integrating with Supabase
        return {}

    def _merge_db_configs(
        self, llm_configs: Dict[str, Any], prompt_configs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Merge LLM and prompt configurations from database."""
        merged = {}

        # Process LLM configurations
        for agent_name, llm_data in llm_configs.items():
            if agent_name not in merged:
                merged[agent_name] = {}
            merged[agent_name]["llm_config"] = llm_data

        # Process prompt configurations
        for agent_name, prompt_data in prompt_configs.items():
            if agent_name not in merged:
                merged[agent_name] = {}
            merged[agent_name]["prompt_config"] = prompt_data

        return merged

    def load_environment_config(self, environment: str) -> Optional[Dict[str, Any]]:
        """
        Load environment-specific configuration.

        Args:
            environment: Environment name (dev/staging/prod)

        Returns:
            Environment configuration or None if not found
        """
        config_file = self.environments_config_dir / f"{environment}.yaml"

        if not config_file.exists():
            # Try JSON format
            config_file = self.environments_config_dir / f"{environment}.json"

        if not config_file.exists():
            logger.warning(f"Environment configuration not found: {environment}")
            return None

        try:
            with open(config_file, "r") as f:
                if config_file.suffix.lower() == ".yaml":
                    return yaml.safe_load(f)
                else:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load environment config {environment}: {e}")
            return None

    def load_from_environment_variables(self) -> Dict[str, Any]:
        """
        Load configuration overrides from environment variables.

        Environment variables should follow the pattern:
        AILEX_AGENT_{AGENT_NAME}_{SETTING}

        Returns:
            Dictionary of environment variable configurations
        """
        configs = {}

        for key, value in os.environ.items():
            if key.startswith("AILEX_AGENT_"):
                try:
                    # Parse environment variable
                    parts = key.split("_")
                    if len(parts) >= 4:
                        agent_name = parts[2].lower()
                        setting = "_".join(parts[3:]).lower()

                        if agent_name not in configs:
                            configs[agent_name] = {}

                        # Convert value to appropriate type
                        configs[agent_name][setting] = self._convert_env_value(value)

                except Exception as e:
                    logger.warning(f"Failed to parse environment variable {key}: {e}")

        return configs

    def _convert_env_value(self, value: str) -> Union[str, int, float, bool]:
        """Convert environment variable string to appropriate type."""
        # Try boolean
        if value.lower() in ("true", "false"):
            return value.lower() == "true"

        # Try integer
        try:
            return int(value)
        except ValueError:
            pass

        # Try float
        try:
            return float(value)
        except ValueError:
            pass

        # Return as string
        return value

    def create_default_configs(self) -> None:
        """Create default configuration files for all discovered agents."""
        # This would create template configuration files
        # for agents that don't have explicit configurations

        default_configs = {
            "research_agent": {
                "name": "research_agent",
                "display_name": "Research Agent",
                "description": "Legal research agent for finding relevant laws and cases",
                "agent_type": "interactive",
                "capabilities": [
                    "legal_research",
                    "document_search",
                    "citation_formatting",
                ],
                "llm_config": {
                    "provider": "openai",
                    "model_name": "openai/gpt-4",
                    "temperature": 0.3,
                },
                "nodes": {
                    "query_gen": {
                        "name": "query_gen",
                        "display_name": "Query Generation",
                        "description": "Generate optimized search queries",
                        "node_type": "generator",
                    },
                    "rerank": {
                        "name": "rerank",
                        "display_name": "Result Reranking",
                        "description": "Rerank search results by relevance",
                        "node_type": "processor",
                    },
                },
            },
            "intake_agent": {
                "name": "intake_agent",
                "display_name": "Intake Agent",
                "description": "Client intake agent for new case processing",
                "agent_type": "interactive",
                "capabilities": [
                    "client_intake",
                    "case_classification",
                    "conflict_checking",
                ],
                "llm_config": {
                    "provider": "anthropic",
                    "model_name": "anthropic/claude-3-sonnet",
                    "temperature": 0.2,
                },
            },
        }

        for agent_name, config_data in default_configs.items():
            config_file = self.agents_config_dir / f"{agent_name}.yaml"
            if not config_file.exists():
                try:
                    with open(config_file, "w") as f:
                        yaml.dump(config_data, f, default_flow_style=False, indent=2)
                    logger.info(f"Created default configuration: {agent_name}")
                except Exception as e:
                    logger.error(
                        f"Failed to create default config for {agent_name}: {e}"
                    )


# Convenience functions
def load_agent_configs() -> Dict[str, AgentConfigModel]:
    """Load all agent configurations."""
    loader = ConfigLoader()
    return loader.load_all_configs()


def save_agent_config(config: AgentConfigModel, file_format: str = "yaml") -> bool:
    """Save agent configuration to file."""
    loader = ConfigLoader()
    return loader.save_agent_config(config, file_format)
