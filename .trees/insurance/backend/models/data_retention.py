"""
Data Retention Models

Comprehensive data models for automated data retention policies,
legal holds, and compliance management.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator
from sqlalchemy import (
    Column,
    String,
    Boolean,
    DateTime,
    Text,
    JSON,
    ForeignKey,
    Integer,
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, INTERVAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class DataRegion(str, Enum):
    """Data residency regions with specific compliance requirements."""

    US = "US"
    EU = "EU"
    GLOBAL = "GLOBAL"


class RetentionBasis(str, Enum):
    """Legal basis for data retention under GDPR and CCPA."""

    CONSENT = "consent"
    CONTRACT_PERFORMANCE = "contract_performance"
    LEGAL_OBLIGATION = "legal_obligation"
    LEGITIMATE_INTEREST = "legitimate_interest"
    ATTORNEY_CLIENT_PRIVILEGE = "attorney_client_privilege"
    PROFESSIONAL_REGULATION = "professional_regulation"
    SECURITY = "security"
    BUSINESS_PURPOSE = "business_purpose"
    REGULATORY_COMPLIANCE = "regulatory_compliance"


class DataSensitivity(str, Enum):
    """Data sensitivity classification."""

    CRITICAL = "critical"  # Attorney-client privileged
    HIGH = "high"  # Personal/financial data
    MEDIUM = "medium"  # Professional/technical data
    LOW = "low"  # Analytics/usage data


class LegalHoldType(str, Enum):
    """Types of legal holds."""

    LITIGATION = "litigation"
    REGULATORY = "regulatory"
    INVESTIGATION = "investigation"
    AUDIT = "audit"
    PRESERVATION = "preservation"


class CleanupStatus(str, Enum):
    """Status of cleanup operations."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    DRY_RUN = "dry_run"


class RetentionEventType(str, Enum):
    """Types of retention events for audit logging."""

    POLICY_CREATED = "policy_created"
    POLICY_UPDATED = "policy_updated"
    POLICY_DELETED = "policy_deleted"
    CLEANUP_SCHEDULED = "cleanup_scheduled"
    CLEANUP_EXECUTED = "cleanup_executed"
    CLEANUP_FAILED = "cleanup_failed"
    LEGAL_HOLD_CREATED = "legal_hold_created"
    LEGAL_HOLD_RELEASED = "legal_hold_released"
    DATA_DELETED = "data_deleted"
    DATA_PRESERVED = "data_preserved"


# SQLAlchemy Models


class RetentionPolicy(Base):
    """Database model for data retention policies."""

    __tablename__ = "retention_policies"
    __table_args__ = {"schema": "data_retention"}

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    data_type = Column(String(100), nullable=False)
    region = Column(String(10), nullable=False)
    retention_period = Column(INTERVAL, nullable=False)
    legal_basis = Column(String(100), nullable=False)
    sensitivity_level = Column(
        String(20), nullable=False, default=DataSensitivity.MEDIUM.value
    )
    auto_delete = Column(Boolean, default=True)
    description = Column(Text)
    policy_metadata = Column(JSON, default=dict)

    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(PostgresUUID(as_uuid=True))
    updated_by = Column(PostgresUUID(as_uuid=True))

    # Relationships
    retention_events = relationship("RetentionEvent", back_populates="retention_policy")


class LegalHold(Base):
    """Database model for legal holds."""

    __tablename__ = "legal_holds"
    __table_args__ = {"schema": "data_retention"}

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    hold_name = Column(String(255), nullable=False)
    hold_type = Column(String(50), nullable=False)
    description = Column(Text)
    data_criteria = Column(JSON, nullable=False)

    # Hold management
    created_by = Column(PostgresUUID(as_uuid=True), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    released_at = Column(DateTime)
    released_by = Column(PostgresUUID(as_uuid=True))
    release_reason = Column(Text)

    # Metadata
    case_number = Column(String(100))
    matter_id = Column(PostgresUUID(as_uuid=True))
    external_reference = Column(String(255))
    hold_metadata = Column(JSON, default=dict)

    # Relationships
    retention_events = relationship("RetentionEvent", back_populates="legal_hold")


class RetentionEvent(Base):
    """Database model for retention audit events."""

    __tablename__ = "retention_events"
    __table_args__ = {"schema": "data_retention"}

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    event_type = Column(String(50), nullable=False)
    data_type = Column(String(100), nullable=False)
    data_id = Column(PostgresUUID(as_uuid=True))

    # Foreign keys
    retention_policy_id = Column(
        PostgresUUID(as_uuid=True), ForeignKey("data_retention.retention_policies.id")
    )
    legal_hold_id = Column(
        PostgresUUID(as_uuid=True), ForeignKey("data_retention.legal_holds.id")
    )

    # Event details
    event_timestamp = Column(DateTime, default=datetime.utcnow)
    user_id = Column(PostgresUUID(as_uuid=True))
    region = Column(String(10))
    event_metadata = Column(JSON, default=dict)

    # Relationships
    retention_policy = relationship(
        "RetentionPolicy", back_populates="retention_events"
    )
    legal_hold = relationship("LegalHold", back_populates="retention_events")


class CleanupJob(Base):
    """Database model for cleanup jobs."""

    __tablename__ = "cleanup_jobs"
    __table_args__ = {"schema": "data_retention"}

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    job_name = Column(String(255), nullable=False)
    data_type = Column(String(100), nullable=False)
    region = Column(String(10), nullable=False)

    # Scheduling
    scheduled_at = Column(DateTime, nullable=False)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)

    # Status and configuration
    status = Column(String(20), default=CleanupStatus.PENDING.value)
    dry_run = Column(Boolean, default=False)
    criteria = Column(JSON, nullable=False)

    # Results
    records_processed = Column(Integer, default=0)
    records_deleted = Column(Integer, default=0)
    records_skipped = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    results = Column(JSON, default=dict)

    # Audit
    created_by = Column(PostgresUUID(as_uuid=True), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)


# Pydantic Models


class RetentionPolicyCreate(BaseModel):
    """Model for creating retention policies."""

    data_type: str = Field(..., description="Type of data this policy applies to")
    region: DataRegion = Field(..., description="Data region for this policy")
    retention_period_days: int = Field(
        ..., gt=0, description="Retention period in days"
    )
    legal_basis: RetentionBasis = Field(..., description="Legal basis for retention")
    sensitivity_level: DataSensitivity = Field(
        DataSensitivity.MEDIUM, description="Data sensitivity level"
    )
    auto_delete: bool = Field(True, description="Enable automatic deletion")
    description: Optional[str] = Field(None, description="Policy description")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @validator("retention_period_days")
    def validate_retention_period(cls, v, values):
        """Validate retention period based on legal basis."""
        legal_basis = values.get("legal_basis")

        # Attorney-client privilege data should have indefinite retention
        if (
            legal_basis == RetentionBasis.ATTORNEY_CLIENT_PRIVILEGE and v < 36500
        ):  # 100 years
            raise ValueError(
                "Attorney-client privileged data requires indefinite retention"
            )

        # GDPR maximum retention periods
        if legal_basis == RetentionBasis.CONSENT and v > 2555:  # 7 years
            raise ValueError("Consent-based retention cannot exceed 7 years under GDPR")

        return v


class RetentionPolicyResponse(BaseModel):
    """Model for retention policy responses."""

    id: UUID
    data_type: str
    region: DataRegion
    retention_period_days: int
    legal_basis: RetentionBasis
    sensitivity_level: DataSensitivity
    auto_delete: bool
    description: Optional[str]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LegalHoldCreate(BaseModel):
    """Model for creating legal holds."""

    hold_name: str = Field(..., description="Name of the legal hold")
    hold_type: LegalHoldType = Field(..., description="Type of legal hold")
    description: Optional[str] = Field(None, description="Hold description")
    data_criteria: Dict[str, Any] = Field(..., description="Criteria for data to hold")
    case_number: Optional[str] = Field(None, description="Associated case number")
    matter_id: Optional[UUID] = Field(None, description="Associated matter ID")
    external_reference: Optional[str] = Field(None, description="External reference")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class LegalHoldResponse(BaseModel):
    """Model for legal hold responses."""

    id: UUID
    hold_name: str
    hold_type: LegalHoldType
    description: Optional[str]
    data_criteria: Dict[str, Any]
    case_number: Optional[str]
    matter_id: Optional[UUID]
    external_reference: Optional[str]
    created_by: UUID
    created_at: datetime
    released_at: Optional[datetime]
    released_by: Optional[UUID]
    release_reason: Optional[str]
    metadata: Dict[str, Any]

    class Config:
        from_attributes = True


class CleanupCriteria(BaseModel):
    """Model for cleanup criteria."""

    retention_date: datetime = Field(..., description="Cutoff date for retention")
    exclude_holds: bool = Field(True, description="Exclude data under legal hold")
    batch_size: int = Field(
        1000, gt=0, le=10000, description="Batch size for processing"
    )
    max_records: Optional[int] = Field(
        None, gt=0, description="Maximum records to process"
    )
    table_filters: Dict[str, Any] = Field(
        default_factory=dict, description="Additional table filters"
    )
    dry_run: bool = Field(False, description="Perform dry run without actual deletion")


class CleanupJobCreate(BaseModel):
    """Model for creating cleanup jobs."""

    job_name: str = Field(..., description="Name of the cleanup job")
    data_type: str = Field(..., description="Type of data to clean up")
    region: DataRegion = Field(..., description="Data region")
    scheduled_at: datetime = Field(..., description="When to execute the job")
    criteria: CleanupCriteria = Field(..., description="Cleanup criteria")


class CleanupResult(BaseModel):
    """Model for cleanup results."""

    job_id: UUID
    status: CleanupStatus
    records_processed: int
    records_deleted: int
    records_skipped: int
    error_count: int
    duration_seconds: float
    errors: List[str] = Field(default_factory=list)
    dry_run_results: Optional[Dict[str, Any]] = None


class RetentionReport(BaseModel):
    """Model for retention compliance reports."""

    region: DataRegion
    report_date: datetime
    total_policies: int
    compliant_data_types: int
    non_compliant_data_types: int
    active_legal_holds: int
    recent_cleanups: int
    compliance_percentage: float
    policy_violations: List[Dict[str, Any]] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)


# Data Type Registry

DATA_TYPE_REGISTRY = {
    # User Data
    "user_profiles": {
        "sensitivity": DataSensitivity.HIGH,
        "default_retention_days": 2555,  # 7 years
        "legal_basis": RetentionBasis.CONTRACT_PERFORMANCE,
        "tables": ["user_profiles", "auth.users"],
    },
    # Client Data
    "client_data": {
        "sensitivity": DataSensitivity.CRITICAL,
        "default_retention_days": 36500,  # Indefinite (100 years)
        "legal_basis": RetentionBasis.ATTORNEY_CLIENT_PRIVILEGE,
        "tables": ["tenants.clients", "tenants.matters"],
    },
    # Financial Data
    "billing_data": {
        "sensitivity": DataSensitivity.HIGH,
        "default_retention_days": 2555,  # 7 years
        "legal_basis": RetentionBasis.LEGAL_OBLIGATION,
        "tables": ["subscription_plans", "subscription_addons"],
    },
    # Technical Data
    "audit_logs": {
        "sensitivity": DataSensitivity.MEDIUM,
        "default_retention_days": 2555,  # 7 years
        "legal_basis": RetentionBasis.LEGAL_OBLIGATION,
        "tables": ["audit.log", "security.security_events"],
    },
    # Communication Data
    "support_data": {
        "sensitivity": DataSensitivity.MEDIUM,
        "default_retention_days": 1095,  # 3 years
        "legal_basis": RetentionBasis.CONTRACT_PERFORMANCE,
        "tables": ["support_tickets", "communications"],
    },
    # Analytics Data
    "usage_analytics": {
        "sensitivity": DataSensitivity.LOW,
        "default_retention_days": 365,  # 1 year
        "legal_basis": RetentionBasis.LEGITIMATE_INTEREST,
        "tables": ["usage_logs", "analytics_events"],
    },
}
