"""
Multi-Factor Authentication (MFA) Data Models

This module defines the data models for the enhanced MFA system,
integrating with Supabase native MFA while adding superadmin-specific features.
"""

from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field
from pydantic import field_validator
import uuid


class MFAMethod(str, Enum):
    """Supported MFA methods."""

    TOTP = "totp"
    PHONE = "phone"
    RECOVERY_CODE = "recovery_code"
    BACKUP_EMAIL = "backup_email"


class MFAFactorType(str, Enum):
    """Supabase MFA factor types."""

    TOTP = "totp"
    WEBAUTHN = "webauthn"
    PHONE = "phone"


class MFAFactorStatus(str, Enum):
    """Supabase MFA factor status."""

    UNVERIFIED = "unverified"
    VERIFIED = "verified"


class SecurityEventCategory(str, Enum):
    """Security event categories."""

    MFA = "mfa"
    SESSION = "session"
    SECURITY = "security"
    ADMIN = "admin"


class SecurityEventSeverity(str, Enum):
    """Security event severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class SuperadminMFAConfig(BaseModel):
    """Superadmin MFA configuration model."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    email: str

    # MFA Requirements
    mfa_required: bool = True
    mfa_enforced_at: Optional[datetime] = None

    # Backup authentication methods
    backup_email: Optional[str] = None
    backup_phone: Optional[str] = None

    # Recovery codes
    recovery_codes: List[str] = Field(default_factory=list)
    recovery_codes_generated_at: Optional[datetime] = None
    recovery_codes_used: int = 0

    # Security settings
    session_timeout_hours: int = 8
    max_failed_attempts: int = 5
    lockout_duration_minutes: int = 30

    # Audit fields
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    created_by: Optional[uuid.UUID] = None
    updated_by: Optional[uuid.UUID] = None

    @field_validator("session_timeout_hours")
    def validate_session_timeout(cls, v):
        if not 1 <= v <= 24:
            raise ValueError("Session timeout must be between 1 and 24 hours")
        return v

    @field_validator("max_failed_attempts")
    def validate_max_attempts(cls, v):
        if not 1 <= v <= 10:
            raise ValueError("Max failed attempts must be between 1 and 10")
        return v

    @field_validator("lockout_duration_minutes")
    def validate_lockout_duration(cls, v):
        if not 1 <= v <= 1440:
            raise ValueError("Lockout duration must be between 1 and 1440 minutes")
        return v


class SuperadminMFASession(BaseModel):
    """Superadmin MFA session model."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    session_token: str

    # Session details
    verified_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    last_activity_at: datetime = Field(default_factory=datetime.utcnow)

    # Security context
    ip_address: str
    user_agent: Optional[str] = None
    device_fingerprint: Optional[str] = None

    # MFA method used
    mfa_method: MFAMethod
    factor_id: Optional[uuid.UUID] = None

    # Session status
    is_active: bool = True
    revoked_at: Optional[datetime] = None
    revoked_reason: Optional[str] = None

    # Audit fields
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    @field_validator("expires_at")
    def validate_expires_at(cls, v, values):
        if "verified_at" in values and v <= values["verified_at"]:
            raise ValueError("Expiration time must be after verification time")
        return v

    def is_expired(self) -> bool:
        """Check if the session is expired."""
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """Check if the session is valid (active and not expired)."""
        return self.is_active and not self.is_expired()


class SuperadminMFAAttempt(BaseModel):
    """Superadmin MFA attempt model."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID

    # Attempt details
    method: MFAMethod
    success: bool
    failure_reason: Optional[str] = None

    # Security context
    ip_address: str
    user_agent: Optional[str] = None
    device_fingerprint: Optional[str] = None

    # Rate limiting context
    attempts_in_window: int = 1
    window_start_at: datetime = Field(default_factory=datetime.utcnow)

    # Audit fields
    attempted_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class BackupMFAToken(BaseModel):
    """Backup MFA token model for SMS/email authentication."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID

    # Token details
    token_hash: str  # SHA-256 hash of the actual token
    method: str  # 'sms' or 'email'
    destination: str  # Phone number or email address

    # Token lifecycle
    expires_at: datetime
    used_at: Optional[datetime] = None
    attempts: int = 0
    max_attempts: int = 3

    # Security context
    ip_address: str
    user_agent: Optional[str] = None

    # Audit fields
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    @field_validator("method")
    def validate_method(cls, v):
        if v not in ["sms", "email"]:
            raise ValueError('Method must be either "sms" or "email"')
        return v

    @field_validator("expires_at")
    def validate_expires_at(cls, v, values):
        if "created_at" in values and v <= values["created_at"]:
            raise ValueError("Expiration time must be after creation time")
        return v

    def is_expired(self) -> bool:
        """Check if the token is expired."""
        return datetime.utcnow() > self.expires_at

    def is_used(self) -> bool:
        """Check if the token has been used."""
        return self.used_at is not None

    def can_attempt(self) -> bool:
        """Check if more attempts are allowed."""
        return (
            self.attempts < self.max_attempts
            and not self.is_expired()
            and not self.is_used()
        )


class SecurityEvent(BaseModel):
    """Security event model for audit logging."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: Optional[uuid.UUID] = None

    # Event details
    event_type: str
    event_category: SecurityEventCategory
    severity: SecurityEventSeverity = SecurityEventSeverity.INFO

    # Event data
    description: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

    # Security context
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    device_fingerprint: Optional[str] = None

    # Audit fields
    occurred_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class MFASetupRequest(BaseModel):
    """Request model for MFA setup."""

    method: MFAMethod
    backup_email: Optional[str] = None
    backup_phone: Optional[str] = None


class MFAVerificationRequest(BaseModel):
    """Request model for MFA verification."""

    method: MFAMethod
    token: str
    factor_id: Optional[uuid.UUID] = None


class MFAStatusResponse(BaseModel):
    """Response model for MFA status."""

    mfa_required: bool
    mfa_configured: bool
    available_methods: List[MFAMethod]
    backup_methods_configured: Dict[str, bool]
    recovery_codes_available: int
    session_valid: bool
    session_expires_at: Optional[datetime] = None


class TOTPSetupResponse(BaseModel):
    """Response model for TOTP setup."""

    secret: str
    qr_code_uri: str
    backup_codes: List[str]
    setup_instructions: str


class RecoveryCodesResponse(BaseModel):
    """Response model for recovery codes."""

    recovery_codes: List[str]
    generated_at: datetime
    warning_message: str


class MFAChallengeRequest(BaseModel):
    """Request model for initiating MFA challenge."""

    method: MFAMethod
    destination: Optional[str] = None  # For SMS/email


class MFAChallengeResponse(BaseModel):
    """Response model for MFA challenge."""

    challenge_id: uuid.UUID
    method: MFAMethod
    expires_at: datetime
    message: str


class MFAValidationRequest(BaseModel):
    """Request model for MFA validation."""

    challenge_id: uuid.UUID
    token: str


class MFAValidationResponse(BaseModel):
    """Response model for MFA validation."""

    success: bool
    session_token: Optional[str] = None
    expires_at: Optional[datetime] = None
    message: str
    remaining_attempts: Optional[int] = None
