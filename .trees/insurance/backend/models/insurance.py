"""
Insurance Battle Station Models

This module defines all data models for the Insurance Battle Station functionality,
including insurance policies, carriers, demand letters, and negotiation events.
"""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator
from sqlalchemy import (
    ARRAY,
    JSON,
    CheckConstraint,
    Column,
    Date,
    DateTime,
    ForeignKey,
    Index,
    Numeric,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import DeclarativeBase, relationship
from sqlalchemy.sql import func


class Base(DeclarativeBase):
    pass


class CoverageType(str, Enum):
    """Insurance coverage types."""

    BODILY_INJURY = "BI"
    UNINSURED_MOTORIST = "UM"
    UNDERINSURED_MOTORIST = "UIM"
    MEDICAL_PAYMENTS = "MedPay"
    PERSONAL_INJURY_PROTECTION = "PIP"
    PROPERTY_DAMAGE = "PD"
    COMPREHENSIVE = "COMP"
    COLLISION = "COLL"


class CarrierTone(str, Enum):
    """Carrier communication tones for demand letters."""

    NEUTRAL = "neutral"
    FIRM = "firm"
    CONCILIATORY = "conciliatory"


class NegotiationEventType(str, Enum):
    """Types of negotiation events."""

    OFFER = "offer"
    DEMAND = "demand"
    EMAIL_IN = "email_in"
    EMAIL_OUT = "email_out"
    CALL_IN = "call_in"
    CALL_OUT = "call_out"
    FAX_IN = "fax_in"
    FAX_OUT = "fax_out"
    NOTE = "note"


# =====================
# SQLAlchemy ORM Models
# =====================


class InsurerORM(Base):
    """Insurance company/carrier ORM model."""

    __tablename__ = "insurers"

    id = Column(
        PGUUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4()
    )
    name = Column(String(255), nullable=False)
    naic_code = Column(String(10), unique=True, index=True)
    common_addresses: Column[list[str]] = Column(ARRAY(Text))
    escalation_contacts = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Relationships
    policies = relationship("InsurancePolicyORM", back_populates="insurer")
    style_profiles = relationship("CarrierStyleProfileORM", back_populates="insurer")
    demand_letters = relationship("DemandLetterORM", back_populates="insurer")

    __table_args__ = (Index("idx_insurers_name_trgm", "name"),)


class InsurancePolicyORM(Base):
    """Insurance policy ORM model."""

    __tablename__ = "insurance_policies"

    id = Column(
        PGUUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4()
    )
    matter_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    tenant_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    insurer_id = Column(PGUUID(as_uuid=True), ForeignKey("insurers.id"))
    insurer_name = Column(String(255))
    policy_number = Column(String(100), index=True)
    effective_date = Column(Date, index=True)
    expiration_date = Column(Date)
    named_insured = Column(String(255))
    dec_page_document_id = Column(PGUUID(as_uuid=True))
    insurer_naic_code = Column(String(10))
    parse_confidence = Column(Numeric(3, 2))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Relationships
    insurer = relationship("InsurerORM", back_populates="policies")
    coverages = relationship(
        "CoverageLimitORM", back_populates="policy", cascade="all, delete-orphan"
    )

    __table_args__ = (
        Index("idx_insurance_policies_matter_tenant", "matter_id", "tenant_id"),
        CheckConstraint(
            "parse_confidence >= 0 AND parse_confidence <= 1",
            name="chk_parse_confidence",
        ),
    )


class CoverageLimitORM(Base):
    """Coverage limit ORM model."""

    __tablename__ = "coverage_limits"

    id = Column(
        PGUUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4()
    )
    policy_id = Column(
        PGUUID(as_uuid=True),
        ForeignKey("insurance_policies.id", ondelete="CASCADE"),
        nullable=False,
    )
    coverage_type = Column(String(50), nullable=False, index=True)
    per_person_limit = Column(Numeric(15, 2))
    per_occurrence_limit = Column(Numeric(15, 2))
    deductible = Column(Numeric(10, 2))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    policy = relationship("InsurancePolicyORM", back_populates="coverages")

    __table_args__ = (
        CheckConstraint(
            "coverage_type IN ('BI', 'UM', 'UIM', 'MedPay', 'PIP', 'PD', 'COMP', "
            "'COLL')",
            name="chk_coverage_type",
        ),
        CheckConstraint(
            "(per_person_limit IS NULL OR per_person_limit >= 0) AND "
            "(per_occurrence_limit IS NULL OR per_occurrence_limit >= 0) AND "
            "(deductible IS NULL OR deductible >= 0)",
            name="chk_positive_limits",
        ),
    )


class CarrierStyleProfileORM(Base):
    """Carrier style profile ORM model."""

    __tablename__ = "carrier_style_profiles"

    id = Column(
        PGUUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4()
    )
    insurer_id = Column(
        PGUUID(as_uuid=True),
        ForeignKey("insurers.id", ondelete="CASCADE"),
        nullable=False,
    )
    tenant_id = Column(PGUUID(as_uuid=True))  # Optional: firm-specific overrides
    tone = Column(String(20), default="neutral")
    section_order: Column[list[str]] = Column(
        ARRAY(Text),
        default=["liability", "medical", "damages", "liens", "ask", "closing"],
    )
    required_exhibits: Column[list[str]] = Column(ARRAY(Text), default=[])
    phrase_blacklist: Column[list[str]] = Column(ARRAY(Text), default=[])
    email_subject_template = Column(String(255))
    signature_block = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Relationship
    insurer = relationship("InsurerORM", back_populates="style_profiles")

    __table_args__ = (
        CheckConstraint("tone IN ('neutral', 'firm', 'conciliatory')", name="chk_tone"),
        Index("idx_carrier_profiles_insurer", "insurer_id"),
        Index("idx_carrier_profiles_tenant", "tenant_id"),
    )


class DemandLetterORM(Base):
    """Demand letter ORM model."""

    __tablename__ = "demand_letters"

    id = Column(
        PGUUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4()
    )
    matter_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    tenant_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    insurer_id = Column(PGUUID(as_uuid=True), ForeignKey("insurers.id"))
    ask_amount = Column(Numeric(15, 2))
    tone = Column(String(20), default="neutral")
    letter_html = Column(Text)
    exhibits = Column(JSON, default=[])
    generated_at = Column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )
    sent_at = Column(DateTime(timezone=True))
    created_by = Column(PGUUID(as_uuid=True), index=True)

    # Relationship
    insurer = relationship("InsurerORM", back_populates="demand_letters")

    __table_args__ = (
        Index("idx_demand_letters_matter_tenant", "matter_id", "tenant_id"),
        CheckConstraint(
            "tone IN ('neutral', 'firm', 'conciliatory')", name="chk_demand_tone"
        ),
        CheckConstraint("ask_amount > 0", name="chk_ask_amount"),
    )


class NegotiationEventORM(Base):
    """Negotiation event ORM model."""

    __tablename__ = "negotiation_events"

    id = Column(
        PGUUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4()
    )
    matter_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    tenant_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    event_type = Column(String(20), nullable=False, index=True)
    amount = Column(Numeric(15, 2), index=True)
    note = Column(Text)
    source_ref = Column(String(255))  # message/call ID for tracking
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    z_score = Column(Numeric(5, 2))  # analytics outlier indicator
    expected_offer_low = Column(Numeric(15, 2))
    expected_offer_high = Column(Numeric(15, 2))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(PGUUID(as_uuid=True))

    __table_args__ = (
        Index("idx_negotiation_events_matter_tenant", "matter_id", "tenant_id"),
        Index(
            "idx_negotiation_events_timestamp_desc",
            "timestamp",
            postgresql_using="btree",
            postgresql_ops={"timestamp": "DESC"},
        ),
        Index(
            "idx_negotiation_events_outliers",
            "z_score",
            postgresql_where="ABS(z_score) > 2",
        ),
        CheckConstraint(
            "event_type IN ('offer','demand','email_in','email_out','"
            "call_in','call_out','fax_in','fax_out','note')",
            name="chk_event_type",
        ),
        CheckConstraint("amount IS NULL OR amount >= 0", name="chk_monetary_amount"),
    )


# ==================
# Pydantic Models
# ==================


class CoverageLimitBase(BaseModel):
    """Base coverage limit model."""

    coverage_type: CoverageType
    per_person_limit: Optional[Decimal] = Field(None, ge=0)
    per_occurrence_limit: Optional[Decimal] = Field(None, ge=0)
    deductible: Optional[Decimal] = Field(None, ge=0)


class CoverageLimitCreate(CoverageLimitBase):
    """Coverage limit creation model."""

    pass


class CoverageLimitResponse(CoverageLimitBase):
    """Coverage limit response model."""

    id: UUID
    policy_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class InsurerBase(BaseModel):
    """Base insurer model."""

    name: str = Field(..., min_length=1, max_length=255)
    naic_code: Optional[str] = Field(None, max_length=10)
    common_addresses: List[str] = Field(default_factory=list)
    escalation_contacts: Dict[str, Any] = Field(default_factory=dict)


class InsurerCreate(InsurerBase):
    """Insurer creation model."""

    pass


class InsurerUpdate(BaseModel):
    """Insurer update model."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    naic_code: Optional[str] = Field(None, max_length=10)
    common_addresses: Optional[List[str]] = None
    escalation_contacts: Optional[Dict[str, Any]] = None


class InsurerResponse(InsurerBase):
    """Insurer response model."""

    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class InsurancePolicyBase(BaseModel):
    """Base insurance policy model."""

    insurer_name: Optional[str] = Field(None, max_length=255)
    policy_number: Optional[str] = Field(None, max_length=100)
    effective_date: Optional[date] = None
    expiration_date: Optional[date] = None
    named_insured: Optional[str] = Field(None, max_length=255)
    insurer_naic_code: Optional[str] = Field(None, max_length=10)
    parse_confidence: Optional[Decimal] = Field(None, ge=0, le=1)

    @validator("expiration_date")
    def validate_date_range(
        cls, v: Optional[date], values: Dict[str, Any]
    ) -> Optional[date]:
        if v and "effective_date" in values and values["effective_date"]:
            if v <= values["effective_date"]:
                raise ValueError("Expiration date must be after effective date")
        return v


class InsurancePolicyCreate(InsurancePolicyBase):
    """Insurance policy creation model."""

    matter_id: UUID
    tenant_id: UUID
    insurer_id: Optional[UUID] = None
    dec_page_document_id: Optional[UUID] = None
    coverages: List[CoverageLimitCreate] = Field(default_factory=list)


class InsurancePolicyUpdate(BaseModel):
    """Insurance policy update model."""

    insurer_id: Optional[UUID] = None
    insurer_name: Optional[str] = Field(None, max_length=255)
    policy_number: Optional[str] = Field(None, max_length=100)
    effective_date: Optional[date] = None
    expiration_date: Optional[date] = None
    named_insured: Optional[str] = Field(None, max_length=255)
    insurer_naic_code: Optional[str] = Field(None, max_length=10)
    parse_confidence: Optional[Decimal] = Field(None, ge=0, le=1)


class InsurancePolicyResponse(InsurancePolicyBase):
    """Insurance policy response model."""

    id: UUID
    matter_id: UUID
    tenant_id: UUID
    insurer_id: Optional[UUID]
    dec_page_document_id: Optional[UUID]
    created_at: datetime
    updated_at: Optional[datetime]
    coverages: List[CoverageLimitResponse] = Field(default_factory=list)
    insurer: Optional[InsurerResponse] = None

    class Config:
        from_attributes = True


class CarrierStyleProfileBase(BaseModel):
    """Base carrier style profile model."""

    tone: CarrierTone = CarrierTone.NEUTRAL
    section_order: List[str] = Field(
        default=["liability", "medical", "damages", "liens", "ask", "closing"]
    )
    required_exhibits: List[str] = Field(default_factory=list)
    phrase_blacklist: List[str] = Field(default_factory=list)
    email_subject_template: Optional[str] = Field(None, max_length=255)
    signature_block: Optional[str] = None


class CarrierStyleProfileCreate(CarrierStyleProfileBase):
    """Carrier style profile creation model."""

    insurer_id: UUID
    tenant_id: Optional[UUID] = None  # For firm-specific overrides


class CarrierStyleProfileUpdate(BaseModel):
    """Carrier style profile update model."""

    tone: Optional[CarrierTone] = None
    section_order: Optional[List[str]] = None
    required_exhibits: Optional[List[str]] = None
    phrase_blacklist: Optional[List[str]] = None
    email_subject_template: Optional[str] = Field(None, max_length=255)
    signature_block: Optional[str] = None


class CarrierStyleProfileResponse(CarrierStyleProfileBase):
    """Carrier style profile response model."""

    id: UUID
    insurer_id: UUID
    tenant_id: Optional[UUID]
    created_at: datetime
    updated_at: Optional[datetime]
    insurer: Optional[InsurerResponse] = None

    class Config:
        from_attributes = True


class DemandLetterBase(BaseModel):
    """Base demand letter model."""

    ask_amount: Decimal = Field(..., gt=0)
    tone: CarrierTone = CarrierTone.NEUTRAL
    letter_html: Optional[str] = None
    exhibits: List[Dict[str, Any]] = Field(default_factory=list)


class DemandLetterCreate(DemandLetterBase):
    """Demand letter creation model."""

    matter_id: UUID
    tenant_id: UUID
    insurer_id: Optional[UUID] = None
    created_by: Optional[UUID] = None


class DemandLetterUpdate(BaseModel):
    """Demand letter update model."""

    ask_amount: Optional[Decimal] = Field(None, gt=0)
    tone: Optional[CarrierTone] = None
    letter_html: Optional[str] = None
    exhibits: Optional[List[Dict[str, Any]]] = None
    sent_at: Optional[datetime] = None


class DemandLetterResponse(DemandLetterBase):
    """Demand letter response model."""

    id: UUID
    matter_id: UUID
    tenant_id: UUID
    insurer_id: Optional[UUID]
    generated_at: datetime
    sent_at: Optional[datetime]
    created_by: Optional[UUID]
    insurer: Optional[InsurerResponse] = None

    class Config:
        from_attributes = True


class NegotiationEventBase(BaseModel):
    """Base negotiation event model."""

    event_type: NegotiationEventType
    amount: Optional[Decimal] = Field(None, ge=0)
    note: Optional[str] = None
    source_ref: Optional[str] = Field(None, max_length=255)
    timestamp: datetime


class NegotiationEventCreate(NegotiationEventBase):
    """Negotiation event creation model."""

    matter_id: UUID
    tenant_id: UUID
    created_by: Optional[UUID] = None


class NegotiationEventUpdate(BaseModel):
    """Negotiation event update model."""

    event_type: Optional[NegotiationEventType] = None
    amount: Optional[Decimal] = Field(None, ge=0)
    note: Optional[str] = None
    source_ref: Optional[str] = Field(None, max_length=255)
    timestamp: Optional[datetime] = None


class NegotiationEventResponse(NegotiationEventBase):
    """Negotiation event response model."""

    id: UUID
    matter_id: UUID
    tenant_id: UUID
    z_score: Optional[Decimal] = None
    expected_offer_low: Optional[Decimal] = None
    expected_offer_high: Optional[Decimal] = None
    created_at: datetime
    created_by: Optional[UUID]

    class Config:
        from_attributes = True


# ==================
# Request/Response Models
# ==================


class ParseDecPageRequest(BaseModel):
    """Request model for parsing declaration pages."""

    matter_id: UUID
    document_id: UUID


class ParseDecPageResponse(BaseModel):
    """Response model for parsed declaration pages."""

    policy: InsurancePolicyResponse
    confidence: float = Field(..., ge=0, le=1)


class GenerateDemandRequest(BaseModel):
    """Request model for generating demand letters."""

    insurer_id: UUID
    ask_amount: Decimal = Field(..., gt=0)
    tone: Optional[CarrierTone] = CarrierTone.NEUTRAL
    include_sections: List[str] = Field(
        default=["liability", "medical", "damages", "liens", "ask", "closing"]
    )


class GenerateDemandResponse(BaseModel):
    """Response model for generated demand letters."""

    demand_id: UUID
    letter_html: str
    exhibits: List[Dict[str, Any]]


# ==================
# Analytics Models
# ==================


class OfferAnalytics(BaseModel):
    """Analytics data for settlement offers."""

    z_score: Optional[float] = None
    expected_offer_low: Optional[Decimal] = None
    expected_offer_high: Optional[Decimal] = None
    is_outlier: bool = False
    historical_average: Optional[Decimal] = None
    settlement_velocity: Optional[int] = None  # Days to settle


class NegotiationSummary(BaseModel):
    """Summary of negotiation timeline for a matter."""

    matter_id: UUID
    total_events: int
    first_demand: Optional[Decimal] = None
    latest_offer: Optional[Decimal] = None
    days_in_negotiation: Optional[int] = None
    events_by_type: Dict[str, int] = Field(default_factory=dict)
    analytics: Optional[OfferAnalytics] = None
