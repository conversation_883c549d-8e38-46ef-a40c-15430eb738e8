"""
Tax-related SQLAlchemy models for PI Lawyer AI

Defines database models for tax calculations, configurations,
and VAT validation caching.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Any, Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    Boolean,
    String,
    Text,
    DateTime,
    Date,
    Numeric,
    JSON,
    CheckConstraint,
    UniqueConstraint,
    ForeignKey,
    Index,
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from backend.models.base import Base


class TaxCalculation(Base):
    """SQLAlchemy model for tax calculation audit trail."""

    __tablename__ = "tax_calculations"
    __table_args__ = (
        CheckConstraint(
            "tax_rate >= 0 AND tax_rate <= 1", name="tax_calculations_rate_check"
        ),
        CheckConstraint(
            "subtotal_amount >= 0 AND tax_amount >= 0 AND total_amount >= 0",
            name="tax_calculations_amounts_check",
        ),
        CheckConstraint(
            "customer_type IN ('B2C', 'B2B')",
            name="tax_calculations_customer_type_check",
        ),
        CheckConstraint(
            "vat_validation_status IN ('valid', 'invalid', 'pending', 'unavailable')",
            name="tax_calculations_vat_status_check",
        ),
        CheckConstraint(
            "calculation_source IN ('stripe_tax', 'manual', 'fallback')",
            name="tax_calculations_source_check",
        ),
        Index("idx_tax_calculations_subscription_id", "subscription_id"),
        Index("idx_tax_calculations_stripe_id", "stripe_calculation_id"),
        Index("idx_tax_calculations_country", "customer_country"),
        Index("idx_tax_calculations_date", "calculation_date"),
        {"schema": "tenants"},
    )

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Foreign keys
    subscription_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tenants.tenant_subscriptions.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )

    # Stripe integration
    stripe_calculation_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, unique=True
    )

    # Customer information
    customer_country: Mapped[str] = mapped_column(String(2), nullable=False)
    customer_state: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    customer_type: Mapped[str] = mapped_column(String(10), nullable=False)

    # Tax calculation details
    subtotal_amount: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    tax_amount: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    total_amount: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    tax_rate: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    tax_type: Mapped[str] = mapped_column(String(20), nullable=False)

    # VAT specific fields
    vat_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    vat_validation_status: Mapped[Optional[str]] = mapped_column(
        String(20), nullable=True
    )
    reverse_charge_applied: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )

    # Calculation metadata
    calculation_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    currency: Mapped[str] = mapped_column(String(3), nullable=False)
    tax_jurisdiction: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    calculation_source: Mapped[str] = mapped_column(
        String(20), server_default="stripe_tax", nullable=False
    )

    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    def __repr__(self) -> str:
        return f"<TaxCalculation(id={self.id}, country={self.customer_country}, tax_amount={self.tax_amount})>"


class TaxConfig(Base):
    """SQLAlchemy model for regional tax configuration."""

    __tablename__ = "tax_config"
    __table_args__ = (
        CheckConstraint(
            "tax_rate >= 0 AND tax_rate <= 1", name="tax_config_rate_check"
        ),
        CheckConstraint(
            "expires_date IS NULL OR expires_date > effective_date",
            name="tax_config_dates_check",
        ),
        CheckConstraint(
            "applies_to IN ('b2c', 'b2b', 'both')", name="tax_config_applies_to_check"
        ),
        UniqueConstraint(
            "country_code",
            "region_code",
            "tax_type",
            "applies_to",
            "effective_date",
            name="tax_config_unique_constraint",
            deferrable=True,
            initially="DEFERRED",
        ),
        Index("idx_tax_config_country", "country_code"),
        Index(
            "idx_tax_config_active", "is_active", postgresql_where="is_active = true"
        ),
        Index("idx_tax_config_effective", "effective_date", "expires_date"),
        {"schema": "tenants"},
    )

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Geographic identifiers
    country_code: Mapped[str] = mapped_column(String(2), nullable=False)
    region_code: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)

    # Tax configuration
    tax_type: Mapped[str] = mapped_column(String(50), nullable=False)
    tax_rate: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    applies_to: Mapped[str] = mapped_column(
        String(20), server_default="b2c", nullable=False
    )
    requires_vat_validation: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )
    reverse_charge_eligible: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )

    # Effective dates
    effective_date: Mapped[date] = mapped_column(Date, nullable=False)
    expires_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Configuration metadata
    stripe_tax_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    compliance_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    created_by: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=True
    )

    def __repr__(self) -> str:
        return f"<TaxConfig(id={self.id}, country={self.country_code}, type={self.tax_type}, rate={self.tax_rate})>"


class VATValidationCache(Base):
    """SQLAlchemy model for VAT validation caching."""

    __tablename__ = "vat_validation_cache"
    __table_args__ = (
        CheckConstraint(
            "validation_status IN ('valid', 'invalid', 'pending', 'unavailable')",
            name="vat_cache_status_check",
        ),
        CheckConstraint(
            "validation_source IN ('stripe', 'vies', 'manual')",
            name="vat_cache_source_check",
        ),
        UniqueConstraint(
            "vat_number", "country_code", name="vat_cache_unique_constraint"
        ),
        Index("idx_vat_cache_number", "vat_number"),
        Index("idx_vat_cache_expires", "expires_at"),
        {"schema": "tenants"},
    )

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # VAT information
    vat_number: Mapped[str] = mapped_column(String(50), nullable=False)
    country_code: Mapped[str] = mapped_column(String(2), nullable=False)
    validation_status: Mapped[str] = mapped_column(String(20), nullable=False)

    # Company information (if validation successful)
    company_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    company_address: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Validation metadata
    validation_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    validation_source: Mapped[str] = mapped_column(
        String(20), server_default="stripe", nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.text("NOW() + INTERVAL '24 hours'"),
        nullable=False,
    )

    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    def __repr__(self) -> str:
        return f"<VATValidationCache(id={self.id}, vat_number={self.vat_number}, status={self.validation_status})>"
