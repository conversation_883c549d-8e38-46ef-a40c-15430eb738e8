"""
SQLAlchemy models for the PI Lawyer AI system.

This package contains SQLAlchemy models that define the database schema
for the PI Lawyer AI system.
"""

from .base import Base
from .case import Case  # Backward compatibility alias
from .matter import Matter
from .client import Client
from .firm import Firm
from .intake_event import IntakeEvent
from .subscription import (
    SubscriptionAddon,
    SubscriptionPlan,
    TenantAddon,
    TenantSubscription,
)
from .task import Task

__all__ = [
    "Base",
    "Case",  # Backward compatibility
    "Matter",
    "Client",
    "Firm",
    "IntakeEvent",
    "Task",
    "SubscriptionPlan",
    "SubscriptionAddon",
    "TenantSubscription",
    "TenantAddon",
]
