"""
Global pytest configuration for backend tests.
"""

import os
import sys
import warnings
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Any, Dict, Optional, List

import pytest
from dotenv import load_dotenv

# Suppress deprecation warnings during tests
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=PendingDeprecationWarning)

# Add backend to Python path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

# Load test environment variables
test_env_path = backend_path / ".env.test"
if test_env_path.exists():
    load_dotenv(test_env_path)

# Set default test environment variables if not already set
test_env_defaults = {
    "SECRET_KEY": "test-secret-key-for-testing-only",
    "FEATURE_FLAGS": "test_mode",
    "DB_USER": "test",
    "DB_PASSWORD": "test",
    "DB_NAME": "test",
    "DB_HOST": "localhost",
    "DB_PORT": "5432",
    "SUPABASE_URL": "http://dummy.local",
    "SUPABASE_KEY": "dummy",
    "SUPABASE_JWT_SECRET": "dummy_jwt_secret_32_chars_minimum_length_for_security_validation",
    "OPENAI_API_KEY": "dummy",
    "PINECONE_API_KEY": "dummy",
    "PINECONE_ENVIRONMENT": "test",
    "MFA_ENCRYPTION_KEY": "dummy-mfa-key-for-testing-only",
    "CALENDLY_PAT": "dummy",
    "CALENDLY_ORG_URI": "dummy",
    "MCP_RULES_BASE": "/tmp",
    "REDIS_URL": "redis://localhost:6379",
    "GOOGLE_APPLICATION_CREDENTIALS": "/tmp/test_credentials.json",
    "GCS_BUCKET_NAME": "test-bucket",
    "CALENDLY_CLIENT_ID": "test_calendly_client_id",
    "CALENDLY_CLIENT_SECRET": "test_calendly_client_secret",
    "GOOGLE_CLIENT_ID": "test_google_client_id",
    "GOOGLE_CLIENT_SECRET": "test_google_client_secret",
    "STRIPE_SECRET_KEY": "sk_test_dummy_key",
    "STRIPE_WEBHOOK_SECRET": "whsec_test_dummy_secret",
    "RESEND_API_KEY": "re_test_dummy_key",
    "VERTEX_AI_PROJECT": "test-project",
    "VERTEX_AI_LOCATION": "us-central1",
}

for key, value in test_env_defaults.items():
    if not os.getenv(key):
        os.environ[key] = value


@pytest.fixture(scope="session")
def test_env():
    """Provide test environment configuration."""
    return {key: os.getenv(key) for key in test_env_defaults.keys()}


@pytest.fixture
def mock_db():
    """Mock database connection for unit tests."""
    mock_db = Mock()
    mock_db.execute.return_value = Mock(fetchall=lambda: [], fetchone=lambda: None)
    return mock_db


@pytest.fixture
def mock_supabase():
    """Mock Supabase client for unit tests."""
    mock_client = Mock()
    mock_client.auth.admin.get_user_by_id.return_value = Mock(
        user=Mock(id="test_user_id", email="<EMAIL>")
    )
    mock_client.auth.admin.list_users.return_value = []
    mock_client.table.return_value.select.return_value.execute.return_value = Mock(
        data=[]
    )
    mock_client.table.return_value.insert.return_value.execute.return_value = Mock(
        data=[{"id": "test_id"}]
    )
    mock_client.table.return_value.update.return_value.eq.return_value.execute.return_value = Mock(
        data=[]
    )
    mock_client.table.return_value.delete.return_value.eq.return_value.execute.return_value = Mock(
        data=[]
    )
    return mock_client


@pytest.fixture
def mock_pinecone_client():
    """Provide a mock Pinecone client for tests."""
    mock_client = Mock()
    mock_client.Index.return_value.query.return_value = {"matches": []}
    mock_client.Index.return_value.upsert.return_value = {"upserted_count": 1}
    return mock_client


@pytest.fixture
def mock_openai_client():
    """Provide a mock OpenAI client for tests."""
    mock_client = Mock()
    mock_client.chat.completions.create.return_value = Mock(
        choices=[Mock(message=Mock(content="Test response"))]
    )
    return mock_client


@pytest.fixture
def mock_redis_client():
    """Provide a mock Redis client for tests."""
    mock_client = Mock()
    mock_client.get.return_value = None
    mock_client.set.return_value = True
    mock_client.delete.return_value = 1
    mock_client.ping.return_value = True
    return mock_client


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up the test environment with all necessary mocks and configurations."""
    # Mock external services that are commonly imported
    with patch("supabase.create_client") as mock_supabase_create:
        mock_client = Mock()
        mock_supabase_create.return_value = mock_client

        # Mock Supabase client methods
        mock_client.auth.admin.get_user_by_id.return_value = Mock(
            user=Mock(id="test_user_id", email="<EMAIL>")
        )
        mock_client.auth.admin.list_users.return_value = []
        mock_client.table.return_value.select.return_value.execute.return_value = Mock(
            data=[]
        )

        with patch("redis.Redis") as mock_redis:
            mock_redis.return_value = Mock()

            with patch("openai.OpenAI") as mock_openai:
                mock_openai.return_value = Mock()

                yield
