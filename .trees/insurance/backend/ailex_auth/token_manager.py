"""
Token Manager for OAuth tokens.

This module provides functions for retrieving, storing, and revoking OAuth tokens
for various providers like Google Calendar, Microsoft Outlook, and Calendly.
"""

import logging
import os
import inspect
from datetime import datetime, timedelta
from typing import Any, Dict

import httpx

# Configure logging
logger = logging.getLogger(__name__)

# Environment variables
AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_URL", "http://localhost:8000/auth")
AUTH_SERVICE_API_KEY = os.getenv("AUTH_SERVICE_API_KEY", "")

# In-memory token cache for development/testing
# In production, this would be replaced with a proper token storage solution
_token_cache: Dict[str, Dict[str, Any]] = {}


async def get_access_token(firm_id: str, provider: str) -> str:
    """
    Get an access token for a specific provider.

    Args:
        firm_id: The firm/tenant ID
        provider: The provider ID (e.g., 'google', 'microsoft', 'calendly')

    Returns:
        str: Access token

    Raises:
        AuthenticationError: If authentication fails
    """
    # Check if we're in development mode (using in-memory cache)
    if os.getenv("APP_ENV") == "development" and _token_cache:
        cache_key = f"{firm_id}:{provider}"
        if cache_key in _token_cache:
            token_data = _token_cache[cache_key]
            # Check if token is expired
            expires_at = token_data.get("expires_at")
            if expires_at and datetime.fromisoformat(expires_at) > datetime.now():
                return token_data["access_token"]

    # In production, call the Auth Service API
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/tokens/{provider}",
                headers={
                    "Authorization": f"Bearer {AUTH_SERVICE_API_KEY}",
                    "X-Tenant-ID": firm_id,
                },
            )

            if response.status_code != 200:
                logger.error(f"Failed to get token from Auth Service: {response.text}")
                raise AuthenticationError(
                    f"Failed to get token from Auth Service: {response.status_code}"
                )

            data = response.json()
            if inspect.isawaitable(data):
                data = await data
            return data["access_token"]
    except httpx.RequestError as e:
        logger.error(f"Error connecting to Auth Service: {str(e)}")
        raise AuthenticationError(f"Error connecting to Auth Service: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting token: {str(e)}")
        raise AuthenticationError(f"Unexpected error getting token: {str(e)}")


async def store_tokens(firm_id: str, provider: str, tokens: Dict[str, Any]) -> None:
    """
    Store OAuth tokens for a provider.

    Args:
        firm_id: The firm/tenant ID
        provider: The provider ID (e.g., 'google', 'microsoft', 'calendly')
        tokens: The tokens to store

    Raises:
        AuthenticationError: If storing tokens fails
    """
    # Check if we're in development mode (using in-memory cache)
    if os.getenv("APP_ENV") == "development":
        cache_key = f"{firm_id}:{provider}"
        # Add expiration time if not present
        if "expires_at" not in tokens and "expires_in" in tokens:
            expires_in = tokens["expires_in"]
            tokens["expires_at"] = (
                datetime.now() + timedelta(seconds=expires_in)
            ).isoformat()
        _token_cache[cache_key] = tokens
        return

    # In production, call the Auth Service API
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{AUTH_SERVICE_URL}/tokens/{provider}",
                headers={
                    "Authorization": f"Bearer {AUTH_SERVICE_API_KEY}",
                    "X-Tenant-ID": firm_id,
                },
                json=tokens,
            )

            if response.status_code not in (200, 201):
                logger.error(f"Failed to store tokens in Auth Service: {response.text}")
                raise AuthenticationError(
                    f"Failed to store tokens in Auth Service: {response.status_code}"
                )
    except httpx.RequestError as e:
        logger.error(f"Error connecting to Auth Service: {str(e)}")
        raise AuthenticationError(f"Error connecting to Auth Service: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error storing tokens: {str(e)}")
        raise AuthenticationError(f"Unexpected error storing tokens: {str(e)}")


async def revoke_tokens(firm_id: str, provider: str) -> None:
    """
    Revoke OAuth tokens for a provider.

    Args:
        firm_id: The firm/tenant ID
        provider: The provider ID (e.g., 'google', 'microsoft', 'calendly')

    Raises:
        AuthenticationError: If revoking tokens fails
    """
    # Check if we're in development mode (using in-memory cache)
    if os.getenv("APP_ENV") == "development":
        cache_key = f"{firm_id}:{provider}"
        if cache_key in _token_cache:
            del _token_cache[cache_key]
        return

    # In production, call the Auth Service API
    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{AUTH_SERVICE_URL}/tokens/{provider}",
                headers={
                    "Authorization": f"Bearer {AUTH_SERVICE_API_KEY}",
                    "X-Tenant-ID": firm_id,
                },
            )

            if response.status_code not in (200, 204):
                logger.error(
                    f"Failed to revoke tokens in Auth Service: {response.text}"
                )
                raise AuthenticationError(
                    f"Failed to revoke tokens in Auth Service: {response.status_code}"
                )
    except httpx.RequestError as e:
        logger.error(f"Error connecting to Auth Service: {str(e)}")
        raise AuthenticationError(f"Error connecting to Auth Service: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error revoking tokens: {str(e)}")
        raise AuthenticationError(f"Unexpected error revoking tokens: {str(e)}")


class AuthenticationError(Exception):
    """Exception raised for authentication errors."""

    pass
