[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pi-lawyer-ai"
version = "0.1.0"
description = "PI Lawyer AI"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "Proprietary"}
authors = [
    {name = "JP Kayobotsi", email = "j<PERSON><PERSON><PERSON><PERSON>@gmail.com"}
]
dependencies = [
    "diff-cover==9.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "coverage>=7.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
    "flake8>=6.0.0",
]

# We'll use setup.py for package configuration instead of this section

[tool.pytest.ini_options]
testpaths = ["backend/agents/interactive/task_crud/tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["backend/agents/interactive/task_crud"]
omit = ["backend/agents/interactive/task_crud/tests/*"]

[tool.black]
line-length = 88
target-version = ['py39', 'py310']
include = '\.pyi?$'
exclude = '''/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = false
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = false
disallow_untyped_decorators = false
no_implicit_optional = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
show_error_codes = true
namespace_packages = true
python_executable = "python3"
disallow_subclassing_any = false
disallow_any_generics = false
mypy_path = "$MYPY_CONFIG_FILE_DIR"
incremental = true

[[tool.mypy.overrides]]
module = [
    "pytest.*",
    "langchain.*",
    "pinecone.*",
    "supabase.*"
]
ignore_missing_imports = true
follow_imports = "silent"

# Models package has been improved, keep strict checking here
[[tool.mypy.overrides]]
module = "pi_lawyer.models.*"
check_untyped_defs = true
warn_return_any = true

# API module needs more work, be lenient for now
[[tool.mypy.overrides]]
module = "pi_lawyer.api.*"
check_untyped_defs = false
disallow_any_generics = false
disallow_untyped_defs = false
warn_return_any = false

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

# Exclude problematic directories with many type issues
[[tool.mypy.overrides]]
module = [
    "backend.scripts.*",
    "backend.agents.interactive.task_crud.*",
    "backend.agents.interactive.calendar_crud.*",
    "backend.agents.interactive.research.*",
    "backend.agents.interactive.intake.*",
    "backend.agents.insights.*",
    "backend.services.*",
    "backend.agents.interactive.examples.*",
    "backend.agents.shared.testing.*",
    "backend.api.*",
    "backend.test_agent_config",
    "backend.agents.shared.utils.*",
    "backend.agents.shared.core.*",
    "backend.agents.config.*",
    "backend.agents.scripts.*",
    "backend.agents.matter_client.*",
    "backend.agents.interactive.master_router",
    "backend.agents.interactive.deadline.*",
    "backend.middleware.*",
    "backend.db.*",
    "backend.models.*",
    "backend.utils.*",
    "backend.migrations.*",
    "backend.tests.*",
    "backend.agents.shared.test_utilities",
    "backend.agents.shared.test_comprehensive"
]
ignore_errors = true

[tool.ruff]
# Enable flake8-bugbear (`B`) rules.
select = ["E", "F", "B", "I"]

# Exclude a variety of commonly ignored directories.
exclude = [
    ".git",
    ".ruff_cache",
    ".venv",
    "__pypackages__",
    "_build",
    "build",
    "dist",
    "node_modules",
    "venv",
    "langgraph-migration-env",
]

# Same as Black.
line-length = 88

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Assume Python 3.10
target-version = "py310"

[tool.ruff.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.per-file-ignores]
# Ignore imported but unused in __init__.py files
"__init__.py" = ["F401"]
# Ignore some errors in test files
"test_*.py" = ["E501"]
# Ignore B008 (function call in defaults) for FastAPI files - this is the correct pattern for FastAPI
"pi_lawyer/api/*.py" = ["B008"]
# Ignore some errors in performance test files
"perf/*.py" = ["E501"]
