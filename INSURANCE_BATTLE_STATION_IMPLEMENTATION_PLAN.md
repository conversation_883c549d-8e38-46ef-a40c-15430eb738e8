# Insurance Battle Station - Implementation Plan

## Executive Summary

After comprehensive analysis of the current codebase, the Insurance Battle Station functionality is **completely missing** from the current implementation. This document provides a complete implementation roadmap to deliver all features specified in the PRD, designed as microservices for scalability, security, and HIPAA compliance.

## Gap Analysis

### ✅ What Exists (Foundation Components)
- **Core architecture**: FastAPI backend, Next.js frontend, PostgreSQL/Supabase
- **Document management**: Basic document upload/storage via GCS
- **Matter/Case models**: Solid foundation with practice area support for Personal Injury
- **Authentication system**: JWT-based auth with tenant isolation
- **Agent framework**: LangGraph-based agent system for extensibility
- **Security framework**: Rate limiting, RBAC, compliance tracking
- **Payment system**: Stripe integration with multi-currency support

### ❌ What's Missing (Everything Insurance-Specific)

**Critical Gaps:**
1. **Insurance Policy Models**: No `InsurancePolicy`, `CoverageLimit`, `Carrier` models
2. **Dec Page Parsing**: No Google Document AI integration or parsing logic  
3. **Demand Letter Generation**: No Gemini 2.5 Pro integration for letter drafting
4. **Carrier Database**: No NAIC mapping or carrier contact management
5. **Negotiation Timeline**: No offer/demand tracking or analytics
6. **Communication Integration**: No Gmail/Graph API integration for auto-capture
7. **Settlement Analytics**: No baseline models or outlier detection
8. **Microservices Architecture**: Everything runs in monolith; no Cloud Run services

**Secondary Gaps:**
- No insurance-specific UI components
- No Document AI processor configuration
- No Vertex AI zero-retention setup
- No carrier style profiles
- No citation verification system

## Implementation Strategy

### Microservices Architecture

Following the PRD requirement for microservices deployment on Google Cloud Run:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Core AiLex API │    │  insurance-parse│    │  demand-drafter │
│  (existing)     │◄──►│  Cloud Run      │◄──►│  Cloud Run      │
│  - Matter CRUD  │    │  - Doc AI       │    │  - Gemini 2.5   │
│  - Auth/Tenants │    │  - NAIC lookup  │    │  - Citations    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲
                       ┌─────────────────┐
                       │  comms-ingest   │
                       │  Cloud Run      │
                       │  - Gmail/Graph  │
                       │  - Event logs   │
                       └─────────────────┘
```

## Detailed Implementation Plan

### Phase 1: Core Insurance Models & Database (Week 1-2)

#### 1.1 Database Schema Extension

**New Tables:**
```sql
-- Insurance Policies
CREATE TABLE insurance_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    matter_id UUID NOT NULL REFERENCES matters(id),
    tenant_id UUID NOT NULL,
    insurer_id UUID REFERENCES insurers(id),
    insurer_name VARCHAR(255),
    policy_number VARCHAR(100),
    effective_date DATE,
    expiration_date DATE,
    named_insured VARCHAR(255),
    dec_page_document_id UUID REFERENCES documents(id),
    insurer_naic_code VARCHAR(10),
    parse_confidence DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Coverage Limits
CREATE TABLE coverage_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_id UUID NOT NULL REFERENCES insurance_policies(id),
    coverage_type VARCHAR(50) NOT NULL, -- 'BI', 'UM', 'UIM', 'MedPay', 'PIP', 'PD'
    per_person_limit DECIMAL(15,2),
    per_occurrence_limit DECIMAL(15,2),
    deductible DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Insurers/Carriers
CREATE TABLE insurers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    naic_code VARCHAR(10) UNIQUE,
    common_addresses TEXT[],
    escalation_contacts JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Carrier Style Profiles  
CREATE TABLE carrier_style_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    insurer_id UUID NOT NULL REFERENCES insurers(id),
    tone VARCHAR(20) DEFAULT 'neutral', -- 'neutral', 'firm', 'conciliatory'
    section_order TEXT[] DEFAULT ARRAY['liability','medical','damages','liens','ask','closing'],
    required_exhibits TEXT[],
    phrase_blacklist TEXT[],
    email_subject_template VARCHAR(255),
    signature_block TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Demand Letters
CREATE TABLE demand_letters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    matter_id UUID NOT NULL REFERENCES matters(id),
    tenant_id UUID NOT NULL,
    insurer_id UUID REFERENCES insurers(id),
    ask_amount DECIMAL(15,2),
    tone VARCHAR(20),
    letter_html TEXT,
    exhibits JSONB,
    generated_at TIMESTAMP DEFAULT NOW(),
    sent_at TIMESTAMP,
    created_by UUID REFERENCES users(id)
);

-- Negotiation Events
CREATE TABLE negotiation_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    matter_id UUID NOT NULL REFERENCES matters(id),
    tenant_id UUID NOT NULL,
    event_type VARCHAR(20) NOT NULL, -- 'offer','demand','email_in','email_out','call_in','call_out','fax_in','fax_out','note'
    amount DECIMAL(15,2),
    note TEXT,
    source_ref VARCHAR(255), -- message/call ID
    timestamp TIMESTAMP NOT NULL,
    z_score DECIMAL(5,2), -- analytics outlier indicator
    expected_offer_low DECIMAL(15,2),
    expected_offer_high DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);
```

#### 1.2 Pydantic Models

**File: `packages/insurance-battle-station/src/models/insurance.py`**
```python
from datetime import datetime, date
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import UUID
from pydantic import BaseModel, Field

class CoverageType(str, Enum):
    BODILY_INJURY = "BI"
    UNINSURED_MOTORIST = "UM"  
    UNDERINSURED_MOTORIST = "UIM"
    MEDICAL_PAYMENTS = "MedPay"
    PERSONAL_INJURY_PROTECTION = "PIP"
    PROPERTY_DAMAGE = "PD"

class CoverageLimit(BaseModel):
    coverage_type: CoverageType
    per_person_limit: Optional[Decimal] = None
    per_occurrence_limit: Optional[Decimal] = None  
    deductible: Optional[Decimal] = None

class InsurancePolicy(BaseModel):
    id: Optional[UUID] = None
    matter_id: UUID
    tenant_id: UUID
    insurer_id: Optional[UUID] = None
    insurer_name: Optional[str] = None
    policy_number: Optional[str] = None
    effective_date: Optional[date] = None
    expiration_date: Optional[date] = None
    named_insured: Optional[str] = None
    dec_page_document_id: Optional[UUID] = None
    insurer_naic_code: Optional[str] = None
    parse_confidence: Optional[Decimal] = None
    coverages: List[CoverageLimit] = Field(default_factory=list)

class CarrierTone(str, Enum):
    NEUTRAL = "neutral"
    FIRM = "firm" 
    CONCILIATORY = "conciliatory"

class CarrierStyleProfile(BaseModel):
    insurer_id: UUID
    tone: CarrierTone = CarrierTone.NEUTRAL
    section_order: List[str] = Field(default_factory=lambda: ['liability','medical','damages','liens','ask','closing'])
    required_exhibits: List[str] = Field(default_factory=list)
    phrase_blacklist: List[str] = Field(default_factory=list)
    email_subject_template: Optional[str] = None
    signature_block: Optional[str] = None

class DemandLetter(BaseModel):
    id: Optional[UUID] = None
    matter_id: UUID
    tenant_id: UUID
    insurer_id: Optional[UUID] = None
    ask_amount: Decimal
    tone: CarrierTone = CarrierTone.NEUTRAL
    letter_html: Optional[str] = None
    exhibits: Dict[str, Any] = Field(default_factory=dict)
    
class NegotiationEventType(str, Enum):
    OFFER = "offer"
    DEMAND = "demand"
    EMAIL_IN = "email_in"
    EMAIL_OUT = "email_out"
    CALL_IN = "call_in"
    CALL_OUT = "call_out"
    FAX_IN = "fax_in"
    FAX_OUT = "fax_out"
    NOTE = "note"

class NegotiationEvent(BaseModel):
    id: Optional[UUID] = None
    matter_id: UUID
    tenant_id: UUID
    event_type: NegotiationEventType
    amount: Optional[Decimal] = None
    note: Optional[str] = None
    source_ref: Optional[str] = None
    timestamp: datetime
    z_score: Optional[Decimal] = None
    expected_offer_low: Optional[Decimal] = None
    expected_offer_high: Optional[Decimal] = None
```

### Phase 2: Microservice #1 - insurance-parse (Week 2-3)

#### 2.1 Document AI Integration Service

**File: `packages/insurance-battle-station/src/services/insurance_parse_service.py`**

**Service Structure:**
```
insurance-parse/
├── src/
│   ├── main.py              # FastAPI app entry
│   ├── services/
│   │   ├── doc_ai_client.py # Document AI integration
│   │   ├── parser.py        # Dec page parsing logic
│   │   └── naic_mapper.py   # NAIC code mapping
│   ├── models/
│   │   └── parse_models.py  # Request/response models
│   └── config/
│       └── settings.py      # Environment config
├── Dockerfile
├── cloudbuild.yaml
└── requirements.txt
```

**Key Implementation:**
```python
# src/services/doc_ai_client.py
from google.cloud import documentai
from typing import Dict, Optional, List
from decimal import Decimal
import re

class DocumentAIClient:
    def __init__(self, processor_name: str):
        self.client = documentai.DocumentProcessorServiceClient()
        self.processor_name = processor_name
    
    async def parse_dec_page(self, document_content: bytes, mime_type: str) -> Dict:
        """Parse dec page using Document AI Form Parser."""
        request = documentai.ProcessRequest(
            name=self.processor_name,
            raw_document=documentai.RawDocument(
                content=document_content,
                mime_type=mime_type
            )
        )
        
        result = self.client.process_document(request=request)
        return self._extract_insurance_data(result.document)
    
    def _extract_insurance_data(self, document) -> Dict:
        """Extract structured insurance data from Document AI response."""
        text = document.text
        
        # Extract policy number
        policy_match = re.search(r'POLICY\s*(?:NO\.?|NUMBER)\s*:?\s*([A-Z0-9\-]+)', text, re.IGNORECASE)
        policy_number = policy_match.group(1) if policy_match else None
        
        # Extract effective dates
        date_pattern = r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})'
        dates = re.findall(date_pattern, text)
        
        # Extract coverage limits  
        coverages = self._extract_coverage_limits(text)
        
        # Extract insurer name
        insurer_name = self._extract_insurer_name(text)
        
        return {
            'policy_number': policy_number,
            'effective_date': dates[0] if dates else None,
            'expiration_date': dates[1] if len(dates) > 1 else None,
            'insurer_name': insurer_name,
            'coverages': coverages,
            'parse_confidence': self._calculate_confidence(policy_number, dates, coverages)
        }
    
    def _extract_coverage_limits(self, text: str) -> List[Dict]:
        """Extract coverage types and limits."""
        coverages = []
        
        # BI coverage pattern: $100,000 / $300,000
        bi_pattern = r'BODILY\s*INJURY.*?(\$[\d,]+)\s*[\/]\s*(\$[\d,]+)'
        bi_match = re.search(bi_pattern, text, re.IGNORECASE | re.DOTALL)
        if bi_match:
            coverages.append({
                'coverage_type': 'BI',
                'per_person_limit': self._parse_amount(bi_match.group(1)),
                'per_occurrence_limit': self._parse_amount(bi_match.group(2))
            })
        
        # UM/UIM pattern  
        um_pattern = r'(?:UNINSURED|UNDERINSURED).*?(\$[\d,]+)(?:\s*[\/]\s*(\$[\d,]+))?'
        for um_match in re.finditer(um_pattern, text, re.IGNORECASE | re.DOTALL):
            coverage_type = 'UM' if 'UNINSURED' in um_match.group(0).upper() else 'UIM'
            coverages.append({
                'coverage_type': coverage_type,
                'per_person_limit': self._parse_amount(um_match.group(1)),
                'per_occurrence_limit': self._parse_amount(um_match.group(2)) if um_match.group(2) else None
            })
        
        return coverages
```

**API Endpoints:**
```python
# src/main.py
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from uuid import UUID
from typing import Optional

app = FastAPI(title="Insurance Dec Page Parser")

class ParseRequest(BaseModel):
    matter_id: UUID
    document_id: UUID

class ParseResponse(BaseModel):
    policy: InsurancePolicy
    confidence: float

@app.post("/v1/insurance/decpage:parse", response_model=ParseResponse)
async def parse_dec_page(request: ParseRequest):
    """Parse insurance dec page and extract policy information."""
    try:
        # Load document from GCS
        document_content = await load_document(request.document_id)
        
        # Parse with Document AI
        doc_ai_client = DocumentAIClient(settings.DOC_AI_PROCESSOR)
        parse_result = await doc_ai_client.parse_dec_page(document_content, 'application/pdf')
        
        # Map to structured policy
        policy = InsurancePolicy(
            matter_id=request.matter_id,
            tenant_id=get_tenant_id(),  # From auth context
            **parse_result
        )
        
        # Save to database
        saved_policy = await save_insurance_policy(policy)
        
        return ParseResponse(
            policy=saved_policy,
            confidence=parse_result['parse_confidence']
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### 2.2 Deployment Configuration

**File: `insurance-parse/cloudbuild.yaml`**
```yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/insurance-parse:$BUILD_ID', '.']
  - name: 'gcr.io/cloud-builders/docker'  
    args: ['push', 'gcr.io/$PROJECT_ID/insurance-parse:$BUILD_ID']
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'  
      - 'insurance-parse'
      - '--image=gcr.io/$PROJECT_ID/insurance-parse:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated=false'
      - '--service-account=insurance-parse@$PROJECT_ID.iam.gserviceaccount.com'
      - '--set-env-vars=DOC_AI_PROCESSOR=projects/$PROJECT_ID/locations/us/processors/PROCESSOR_ID'
```

### Phase 3: Microservice #2 - demand-drafter (Week 3-4)

#### 3.1 Gemini 2.5 Pro Integration

**Service Structure:**
```
demand-drafter/
├── src/
│   ├── main.py
│   ├── services/
│   │   ├── gemini_client.py      # Vertex AI Gemini client
│   │   ├── demand_generator.py   # Core demand logic
│   │   ├── citation_verifier.py  # Citation validation
│   │   └── carrier_profiles.py   # Style profile management
│   ├── models/
│   └── prompts/
│       ├── demand_system.txt     # System prompts
│       └── carrier_styles/       # Per-carrier prompts
└── requirements.txt
```

**Key Implementation:**
```python
# src/services/gemini_client.py
from vertexai.generative_models import GenerativeModel
import vertexai
from typing import Dict, Any
import json

class GeminiDemandDrafter:
    def __init__(self, project_id: str, location: str):
        vertexai.init(project=project_id, location=location)
        # Configure zero-data-retention mode
        self.model = GenerativeModel(
            "gemini-2.5-pro",
            system_instruction=self._load_system_prompt()
        )
    
    async def draft_demand_letter(self, 
                                  matter_facts: Dict,
                                  carrier_profile: CarrierStyleProfile,
                                  ask_amount: Decimal) -> Dict[str, Any]:
        """Generate demand letter using Gemini 2.5 Pro."""
        
        prompt_data = {
            'carrier_profile': carrier_profile.dict(),
            'matter_facts': matter_facts,
            'ask_amount': str(ask_amount),
            'required_citations': True
        }
        
        response = await self.model.generate_content_async(
            json.dumps(prompt_data),
            generation_config={
                'temperature': 0.2,
                'max_output_tokens': 8192,
            }
        )
        
        # Parse structured response
        try:
            result = json.loads(response.text)
            
            # Verify citations exist
            if not self._verify_citations(result.get('html', '')):
                raise ValueError("Generated demand lacks required citations")
                
            return result
            
        except json.JSONDecodeError:
            raise ValueError("Model returned invalid JSON response")
    
    def _verify_citations(self, html: str) -> bool:
        """Verify that factual paragraphs include page citations."""
        import re
        
        # Look for citation patterns like [Doc abc123 p.5]
        citation_pattern = r'\[Doc\s+\w+\s+p\.\d+\]'
        citations = re.findall(citation_pattern, html)
        
        # Must have at least 3 citations for a proper demand
        return len(citations) >= 3
    
    def _load_system_prompt(self) -> str:
        return """You draft personal injury demand letters for AiLex. Use Matter as the anchor object.

Rules:
- Use ONLY the provided facts and tables. Each factual statement must include a page citation like [Doc {{docId}} p.{{page}}].
- Follow the carrier style profile (tone, section order, phrase blacklist).
- Do not state policy limits unless they appear in the record.
- Return JSON: { "html": "...", "exhibits": [{"title": "...", "documentId": "...", "pages": "3-5, 7"}] }

The response must be valid JSON with HTML content that includes proper citations."""
```

**API Implementation:**
```python
# src/main.py
@app.post("/v1/matters/{matter_id}/demands:generate")
async def generate_demand(
    matter_id: UUID,
    request: GenerateDemandRequest,
    tenant_id: UUID = Depends(get_tenant_id)
):
    """Generate demand letter with carrier-specific styling."""
    
    # Load matter facts from Medical Command Center and Damages module  
    matter_facts = await load_matter_facts(matter_id, tenant_id)
    
    # Load carrier style profile
    carrier_profile = await load_carrier_profile(request.insurer_id)
    
    # Generate demand with Gemini
    drafter = GeminiDemandDrafter(settings.GCP_PROJECT, settings.VERTEX_LOCATION)
    result = await drafter.draft_demand_letter(
        matter_facts=matter_facts,
        carrier_profile=carrier_profile, 
        ask_amount=request.ask_amount
    )
    
    # Save demand letter
    demand = DemandLetter(
        matter_id=matter_id,
        tenant_id=tenant_id,
        insurer_id=request.insurer_id,
        ask_amount=request.ask_amount,
        tone=request.tone or 'neutral',
        letter_html=result['html'],
        exhibits=result['exhibits']
    )
    
    saved_demand = await save_demand_letter(demand)
    
    return GenerateDemandResponse(
        demand_id=saved_demand.id,
        letter_html=result['html'],
        exhibits=result['exhibits']
    )
```

### Phase 4: Core API Integration (Week 4-5)

#### 4.1 Insurance API Routes

**File: `backend/api/routes/insurance.py`**
```python
from fastapi import APIRouter, Depends, HTTPException, status
from uuid import UUID
from typing import List, Optional

from ..dependencies.auth import get_current_user, get_tenant_id
from ..schemas.insurance import (
    InsurancePolicyCreate, InsurancePolicyResponse,
    DemandLetterCreate, DemandLetterResponse,
    NegotiationEventCreate, NegotiationEventResponse
)
from ...services.insurance import InsuranceService

router = APIRouter(prefix="/insurance", tags=["insurance"])

@router.get("/matters/{matter_id}/policies", response_model=List[InsurancePolicyResponse])
async def get_matter_policies(
    matter_id: UUID,
    tenant_id: UUID = Depends(get_tenant_id),
    insurance_service: InsuranceService = Depends()
):
    """Get all insurance policies for a matter."""
    return await insurance_service.get_policies_by_matter(matter_id, tenant_id)

@router.post("/matters/{matter_id}/policies", response_model=InsurancePolicyResponse)
async def create_policy(
    matter_id: UUID,
    policy_data: InsurancePolicyCreate,
    tenant_id: UUID = Depends(get_tenant_id),
    insurance_service: InsuranceService = Depends()
):
    """Create new insurance policy for matter."""
    policy_data.matter_id = matter_id
    policy_data.tenant_id = tenant_id
    return await insurance_service.create_policy(policy_data)

@router.post("/decpage/parse")
async def parse_dec_page(
    matter_id: UUID,
    document_id: UUID,
    tenant_id: UUID = Depends(get_tenant_id)
):
    """Parse insurance dec page using microservice."""
    # Call insurance-parse microservice
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{settings.INSURANCE_PARSE_URL}/v1/insurance/decpage:parse",
            json={"matter_id": str(matter_id), "document_id": str(document_id)},
            headers={"Authorization": f"Bearer {get_service_token()}"}
        )
        if response.status_code != 200:
            raise HTTPException(status_code=500, detail="Dec page parsing failed")
        
        return response.json()

@router.post("/matters/{matter_id}/demands", response_model=DemandLetterResponse)  
async def generate_demand(
    matter_id: UUID,
    demand_data: DemandLetterCreate,
    tenant_id: UUID = Depends(get_tenant_id)
):
    """Generate demand letter using microservice."""
    # Call demand-drafter microservice
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{settings.DEMAND_DRAFTER_URL}/v1/matters/{matter_id}/demands:generate",
            json=demand_data.dict(),
            headers={"Authorization": f"Bearer {get_service_token()}"}
        )
        if response.status_code != 200:
            raise HTTPException(status_code=500, detail="Demand generation failed")
            
        return response.json()

@router.get("/matters/{matter_id}/negotiations", response_model=List[NegotiationEventResponse])
async def get_negotiations(
    matter_id: UUID,
    tenant_id: UUID = Depends(get_tenant_id),
    insurance_service: InsuranceService = Depends()
):
    """Get negotiation timeline for matter."""  
    return await insurance_service.get_negotiation_events(matter_id, tenant_id)

@router.post("/matters/{matter_id}/negotiations", response_model=NegotiationEventResponse)
async def create_negotiation_event(
    matter_id: UUID,
    event_data: NegotiationEventCreate,
    tenant_id: UUID = Depends(get_tenant_id),
    insurance_service: InsuranceService = Depends()
):
    """Add negotiation event (offer, demand, call, etc.)."""
    event_data.matter_id = matter_id
    event_data.tenant_id = tenant_id
    return await insurance_service.create_negotiation_event(event_data)
```

#### 4.2 Insurance Service Layer

**File: `backend/services/insurance_service.py`**
```python
from typing import List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from ..models import InsurancePolicy, NegotiationEvent, Insurer
from ..schemas.insurance import InsurancePolicyCreate, NegotiationEventCreate

class InsuranceService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_policies_by_matter(self, matter_id: UUID, tenant_id: UUID) -> List[InsurancePolicy]:
        """Get all insurance policies for a matter."""
        query = select(InsurancePolicy).where(
            and_(
                InsurancePolicy.matter_id == matter_id,
                InsurancePolicy.tenant_id == tenant_id
            )
        ).options(selectinload(InsurancePolicy.coverages))
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def create_policy(self, policy_data: InsurancePolicyCreate) -> InsurancePolicy:
        """Create new insurance policy."""
        policy = InsurancePolicy(**policy_data.dict())
        self.db.add(policy)
        await self.db.commit()
        await self.db.refresh(policy)
        return policy
    
    async def create_negotiation_event(self, event_data: NegotiationEventCreate) -> NegotiationEvent:
        """Create negotiation event and calculate analytics."""
        
        # Calculate z-score and expected range
        event_dict = event_data.dict()
        if event_data.event_type == 'offer' and event_data.amount:
            analytics = await self._calculate_offer_analytics(
                event_data.matter_id, event_data.amount
            )
            event_dict.update(analytics)
        
        event = NegotiationEvent(**event_dict)
        self.db.add(event)
        await self.db.commit()
        await self.db.refresh(event)
        return event
    
    async def _calculate_offer_analytics(self, matter_id: UUID, offer_amount: Decimal) -> Dict:
        """Calculate z-score and expected offer range using simple OLS."""
        
        # Get historical offers for this firm/venue
        historical_data = await self._get_historical_offers(matter_id)
        
        if len(historical_data) < 10:  # Need minimum data for analytics
            return {'z_score': None, 'expected_offer_low': None, 'expected_offer_high': None}
        
        # Simple regression: log(offer) ~ log(specials) + days_since_demand + venue
        X, y = self._prepare_regression_data(historical_data)
        model_result = self._simple_ols(X, y)
        
        # Calculate z-score for current offer
        predicted_log_offer = self._predict_offer(matter_id, model_result)
        z_score = (math.log(offer_amount) - predicted_log_offer) / model_result['sigma']
        
        # Calculate expected range (±1 std dev)
        expected_low = math.exp(predicted_log_offer - model_result['sigma'])
        expected_high = math.exp(predicted_log_offer + model_result['sigma'])
        
        return {
            'z_score': round(z_score, 2),
            'expected_offer_low': expected_low,
            'expected_offer_high': expected_high
        }
```

### Phase 5: Frontend Components (Week 5-6)

#### 5.1 Insurance Policy Panel

**File: `frontend/src/components/insurance/PolicyPanel.tsx`**
```tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Upload, FileText, AlertTriangle } from 'lucide-react';

interface InsurancePolicy {
  id: string;
  policyNumber?: string;
  effectiveDate?: string;
  expirationDate?: string;
  namedInsured?: string;
  insurerName?: string;
  parseConfidence?: number;
  coverages: CoverageLimit[];
}

interface CoverageLimit {
  coverageType: string;
  perPersonLimit?: number;
  perOccurrenceLimit?: number;
  deductible?: number;
}

interface PolicyPanelProps {
  matterId: string;
  policies: InsurancePolicy[];
  onUploadDecPage: () => void;
  onGenerateDemand: (policyId: string) => void;
}

export function PolicyPanel({ matterId, policies, onUploadDecPage, onGenerateDemand }: PolicyPanelProps) {
  const formatCurrency = (amount?: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      maximumFractionDigits: 0 
    }).format(amount);
  };

  const renderCoverageTable = (coverages: CoverageLimit[]) => (
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b">
            <th className="text-left p-2">Coverage</th>
            <th className="text-right p-2">Per Person</th>
            <th className="text-right p-2">Per Occurrence</th>
            <th className="text-right p-2">Deductible</th>
          </tr>
        </thead>
        <tbody>
          {coverages.map((coverage, idx) => (
            <tr key={idx} className="border-b">
              <td className="p-2 font-medium">{coverage.coverageType}</td>
              <td className="p-2 text-right">{formatCurrency(coverage.perPersonLimit)}</td>
              <td className="p-2 text-right">{formatCurrency(coverage.perOccurrenceLimit)}</td>
              <td className="p-2 text-right">{formatCurrency(coverage.deductible)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Insurance Policies</h3>
        <Button onClick={onUploadDecPage} variant="outline" size="sm">
          <Upload className="w-4 h-4 mr-2" />
          Upload Dec Page
        </Button>
      </div>

      {policies.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>No insurance policies found.</p>
            <p className="text-sm">Upload a declaration page to get started.</p>
          </CardContent>
        </Card>
      ) : (
        policies.map((policy) => (
          <Card key={policy.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-base">
                    {policy.insurerName || 'Unknown Insurer'}
                  </CardTitle>
                  <div className="text-sm text-muted-foreground space-y-1">
                    {policy.policyNumber && (
                      <p>Policy #: {policy.policyNumber}</p>
                    )}
                    {policy.namedInsured && (
                      <p>Named Insured: {policy.namedInsured}</p>
                    )}
                    {policy.effectiveDate && policy.expirationDate && (
                      <p>
                        Effective: {new Date(policy.effectiveDate).toLocaleDateString()} - 
                        {new Date(policy.expirationDate).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {policy.parseConfidence && policy.parseConfidence < 0.8 && (
                    <Badge variant="destructive" className="text-xs">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Low Confidence
                    </Badge>
                  )}
                  <Button
                    onClick={() => onGenerateDemand(policy.id)}
                    size="sm"
                    disabled={!policy.coverages.length}
                  >
                    Generate Demand
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {policy.coverages.length > 0 ? (
                renderCoverageTable(policy.coverages)
              ) : (
                <p className="text-sm text-muted-foreground">
                  No coverage information available.
                </p>
              )}
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
}
```

#### 5.2 Negotiation Timeline

**File: `frontend/src/components/insurance/NegotiationTimeline.tsx`**
```tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus, Phone, Mail, DollarSign, MessageSquare, AlertCircle } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface NegotiationEvent {
  id: string;
  eventType: string;
  amount?: number;
  note?: string;
  timestamp: string;
  zScore?: number;
  createdBy?: string;
}

interface NegotiationTimelineProps {
  matterId: string;
  events: NegotiationEvent[];
  onAddEvent: () => void;
}

export function NegotiationTimeline({ matterId, events, onAddEvent }: NegotiationTimelineProps) {
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'offer':
      case 'demand':
        return <DollarSign className="w-4 h-4" />;
      case 'email_in':
      case 'email_out':
        return <Mail className="w-4 h-4" />;
      case 'call_in':
      case 'call_out':
        return <Phone className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getEventBadgeVariant = (eventType: string) => {
    switch (eventType) {
      case 'offer':
        return 'secondary';
      case 'demand':
        return 'default';
      case 'email_in':
      case 'call_in':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return null;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Prepare chart data for offers/demands over time
  const chartData = events
    .filter(event => event.amount && ['offer', 'demand'].includes(event.eventType))
    .map(event => ({
      date: new Date(event.timestamp).toLocaleDateString(),
      amount: event.amount,
      type: event.eventType
    }));

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Negotiation Timeline</h3>
        <Button onClick={onAddEvent} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Event
        </Button>
      </div>

      {chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Offer/Demand Progression</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis tickFormatter={(value) => `$${value.toLocaleString()}`} />
                <Tooltip
                  formatter={(value, name) => [formatCurrency(value as number), name]}
                />
                <Line
                  type="monotone"
                  dataKey="amount"
                  stroke="#2563eb"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      <div className="space-y-3">
        {events.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No negotiation events yet.</p>
              <p className="text-sm">Add events to track your settlement discussions.</p>
            </CardContent>
          </Card>
        ) : (
          events.map((event) => (
            <Card key={event.id}>
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {getEventIcon(event.eventType)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant={getEventBadgeVariant(event.eventType)}>
                        {event.eventType.replace('_', ' ')}
                      </Badge>
                      {event.amount && (
                        <span className="font-semibold">
                          {formatCurrency(event.amount)}
                        </span>
                      )}
                      {event.zScore && Math.abs(event.zScore) > 2 && (
                        <Badge variant="destructive" className="text-xs">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Outlier
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">
                      {new Date(event.timestamp).toLocaleString()}
                    </p>
                    {event.note && (
                      <p className="text-sm">{event.note}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
```

#### 5.3 Demand Generator Interface

**File: `frontend/src/components/insurance/DemandGenerator.tsx`**
```tsx
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, Send, AlertTriangle } from 'lucide-react';

interface DemandGeneratorProps {
  matterId: string;
  insurers: Array<{
    id: string;
    name: string;
    styleProfile?: {
      tone: string;
      requiredExhibits: string[];
    };
  }>;
  onGenerate: (params: {
    insurerId: string;
    askAmount: number;
    tone: string;
    includeSections: string[];
  }) => Promise<{
    demandId: string;
    letterHtml: string;
    exhibits: Array<{
      title: string;
      documentId: string;
      pages: string;
    }>;
  }>;
}

export function DemandGenerator({ matterId, insurers, onGenerate }: DemandGeneratorProps) {
  const [selectedInsurer, setSelectedInsurer] = useState<string>('');
  const [askAmount, setAskAmount] = useState<string>('');
  const [tone, setTone] = useState<string>('neutral');
  const [includeSections, setIncludeSections] = useState<string[]>([
    'liability', 'medical', 'damages', 'liens', 'ask', 'closing'
  ]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedDemand, setGeneratedDemand] = useState<any>(null);

  const availableSections = [
    { id: 'liability', label: 'Liability' },
    { id: 'medical', label: 'Medical' },
    { id: 'damages', label: 'Damages' },
    { id: 'liens', label: 'Liens' },
    { id: 'ask', label: 'Demand Amount' },
    { id: 'closing', label: 'Closing' }
  ];

  const handleSectionToggle = (sectionId: string) => {
    setIncludeSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(s => s !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleGenerate = async () => {
    if (!selectedInsurer || !askAmount) return;

    setIsGenerating(true);
    try {
      const result = await onGenerate({
        insurerId: selectedInsurer,
        askAmount: parseFloat(askAmount),
        tone,
        includeSections
      });
      setGeneratedDemand(result);
    } catch (error) {
      console.error('Demand generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const selectedInsurerProfile = insurers.find(i => i.id === selectedInsurer);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Generate Demand Letter
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="insurer">Insurer</Label>
              <Select value={selectedInsurer} onValueChange={setSelectedInsurer}>
                <SelectTrigger>
                  <SelectValue placeholder="Select insurer" />
                </SelectTrigger>
                <SelectContent>
                  {insurers.map((insurer) => (
                    <SelectItem key={insurer.id} value={insurer.id}>
                      {insurer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="askAmount">Demand Amount</Label>
              <Input
                id="askAmount"
                type="number"
                placeholder="100000"
                value={askAmount}
                onChange={(e) => setAskAmount(e.target.value)}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="tone">Tone</Label>
            <Select value={tone} onValueChange={setTone}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="neutral">Neutral</SelectItem>
                <SelectItem value="firm">Firm</SelectItem>
                <SelectItem value="conciliatory">Conciliatory</SelectItem>
              </SelectContent>
            </Select>
            {selectedInsurerProfile?.styleProfile && (
              <p className="text-sm text-muted-foreground mt-1">
                Recommended tone for {selectedInsurerProfile.name}: {selectedInsurerProfile.styleProfile.tone}
              </p>
            )}
          </div>

          <div>
            <Label>Include Sections</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {availableSections.map((section) => (
                <Badge
                  key={section.id}
                  variant={includeSections.includes(section.id) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => handleSectionToggle(section.id)}
                >
                  {section.label}
                </Badge>
              ))}
            </div>
          </div>

          <Button
            onClick={handleGenerate}
            disabled={!selectedInsurer || !askAmount || isGenerating}
            className="w-full"
          >
            {isGenerating ? 'Generating...' : 'Generate Demand Letter'}
          </Button>
        </CardContent>
      </Card>

      {generatedDemand && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Generated Demand Letter</span>
              <div className="space-x-2">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </Button>
                <Button size="sm">
                  <Send className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {generatedDemand.exhibits?.length > 0 && (
              <div className="mb-4">
                <Label>Required Exhibits</Label>
                <div className="space-y-2 mt-2">
                  {generatedDemand.exhibits.map((exhibit: any, idx: number) => (
                    <div key={idx} className="flex items-center justify-between p-2 bg-muted rounded">
                      <span className="text-sm font-medium">{exhibit.title}</span>
                      <Badge variant="outline" className="text-xs">
                        Pages {exhibit.pages}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="border rounded p-4 max-h-96 overflow-y-auto">
              <div 
                dangerouslySetInnerHTML={{ __html: generatedDemand.letterHtml }}
                className="prose prose-sm max-w-none"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
```

### Phase 6: Email/Communication Integration (Week 6)

#### 6.1 Microservice #3 - comms-ingest

**Service Structure:**
```
comms-ingest/
├── src/
│   ├── main.py
│   ├── services/
│   │   ├── gmail_client.py       # Gmail API integration
│   │   ├── msgraph_client.py     # Microsoft Graph integration  
│   │   ├── email_parser.py       # Extract amounts/events from emails
│   │   └── event_processor.py    # Create NegotiationEvents
│   ├── webhooks/
│   │   ├── gmail_webhook.py      # Gmail push notifications
│   │   └── msgraph_webhook.py    # Graph change notifications
│   └── models/
└── requirements.txt
```

**Gmail Integration:**
```python
# src/services/gmail_client.py
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
import re
from decimal import Decimal
from typing import Dict, Optional

class GmailClient:
    def __init__(self, credentials: Credentials):
        self.service = build('gmail', 'v1', credentials=credentials)
    
    async def setup_watch(self, tenant_id: str, matter_id: str) -> Dict:
        """Set up Gmail watch for matter-specific label."""
        label_name = f"AiLex/Matter-{matter_id}"
        
        # Create label if not exists
        try:
            self.service.users().labels().create(
                userId='me',
                body={'name': label_name}
            ).execute()
        except:
            pass  # Label already exists
        
        # Set up push notifications
        request = {
            'topicName': f'projects/{settings.GCP_PROJECT}/topics/gmail-events',
            'labelIds': [self._get_label_id(label_name)]
        }
        
        result = self.service.users().watch(userId='me', body=request).execute()
        return result
    
    async def process_message(self, message_id: str, tenant_id: str) -> Optional[Dict]:
        """Process Gmail message and extract negotiation data."""
        message = self.service.users().messages().get(
            userId='me', 
            id=message_id,
            format='full'
        ).execute()
        
        # Extract text content
        body_text = self._extract_message_text(message)
        
        # Parse for dollar amounts and negotiation language
        event_data = self._parse_negotiation_content(body_text)
        
        if event_data:
            # Determine matter_id from message threading/labels
            matter_id = self._extract_matter_id(message)
            if matter_id:
                event_data.update({
                    'matter_id': matter_id,
                    'tenant_id': tenant_id,
                    'source_ref': message_id,
                    'timestamp': self._parse_message_date(message)
                })
                
        return event_data
    
    def _parse_negotiation_content(self, text: str) -> Optional[Dict]:
        """Extract offer/demand amounts and determine event type."""
        
        # Look for offer language
        offer_patterns = [
            r'(?:offer|settlement|propose|willing to pay)\s*.*?\$?([\d,]+)',
            r'\$?([\d,]+)\s*(?:settlement|offer|to settle)',
        ]
        
        # Look for demand language
        demand_patterns = [
            r'(?:demand|seeking|requesting)\s*.*?\$?([\d,]+)',
            r'\$?([\d,]+)\s*(?:demand|damages)',
        ]
        
        for pattern in offer_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                amount = Decimal(match.group(1).replace(',', ''))
                return {
                    'event_type': 'offer',
                    'amount': amount,
                    'note': self._extract_context(text, match)
                }
        
        for pattern in demand_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                amount = Decimal(match.group(1).replace(',', ''))
                return {
                    'event_type': 'demand',
                    'amount': amount,
                    'note': self._extract_context(text, match)
                }
        
        # No monetary amounts found, classify as general email
        if any(word in text.lower() for word in ['claim', 'accident', 'injury', 'settlement']):
            return {
                'event_type': 'email_in',
                'note': text[:500]  # First 500 chars
            }
        
        return None
```

**Webhook Handler:**
```python
# src/webhooks/gmail_webhook.py
from fastapi import APIRouter, HTTPException, Depends, Request
import json
import base64

router = APIRouter(prefix="/webhooks/gmail", tags=["webhooks"])

@router.post("/push")
async def handle_gmail_push(request: Request):
    """Handle Gmail push notification."""
    try:
        body = await request.body()
        pubsub_message = json.loads(body)
        
        # Decode Pub/Sub message
        if 'message' in pubsub_message:
            data = json.loads(
                base64.b64decode(pubsub_message['message']['data']).decode('utf-8')
            )
            
            # Extract tenant and message info
            tenant_id = data.get('tenant_id')
            message_id = data.get('historyId')  # Gmail history ID
            
            # Process the message asynchronously
            await process_gmail_message_async.delay(message_id, tenant_id)
            
        return {"status": "ok"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def process_gmail_message_async(message_id: str, tenant_id: str):
    """Background task to process Gmail message."""
    gmail_client = await get_gmail_client(tenant_id)
    event_data = await gmail_client.process_message(message_id, tenant_id)
    
    if event_data:
        # Call core API to create negotiation event
        async with httpx.AsyncClient() as client:
            await client.post(
                f"{settings.CORE_API_URL}/insurance/matters/{event_data['matter_id']}/negotiations",
                json=event_data,
                headers={"Authorization": f"Bearer {get_service_token()}"}
            )
```

### Phase 7: Testing & Quality Assurance (Week 6-7)

#### 7.1 Test Coverage Plan

**Unit Tests:**
- Insurance models validation
- Dec page parsing accuracy (gold set of 10 samples per PRD)
- Demand generation with citations
- Negotiation analytics (z-score calculation)

**Integration Tests:**
- Document AI → Policy creation flow
- Gemini 2.5 Pro → Demand letter generation
- Gmail/Graph → Event creation
- End-to-end: Upload dec page → Generate demand → Track negotiation

**Gold Set Testing:**
```python
# tests/integration/test_insurance_battle_station.py
import pytest
from decimal import Decimal

class TestInsuranceBattleStation:
    
    @pytest.mark.asyncio
    async def test_dec_page_parsing_accuracy(self):
        """Test parsing accuracy on gold set samples."""
        
        gold_samples = [
            ("I1_personal_auto_bi_30_60.pdf", {
                "expected_coverages": [
                    {"type": "BI", "per_person": 30000, "per_occurrence": 60000}
                ],
                "min_confidence": 0.9
            }),
            ("I2_commercial_auto_1m.pdf", {
                "expected_coverages": [
                    {"type": "BI", "per_person": 1000000, "per_occurrence": 1000000}
                ]
            }),
            # ... more samples
        ]
        
        correct_parses = 0
        for sample_file, expected in gold_samples:
            result = await parse_dec_page(sample_file)
            
            if self._validate_parse_result(result, expected):
                correct_parses += 1
        
        accuracy = correct_parses / len(gold_samples)
        assert accuracy >= 0.9, f"Parse accuracy {accuracy:.1%} below 90% threshold"
    
    @pytest.mark.asyncio  
    async def test_demand_generation_citations(self):
        """Ensure all demands include proper citations."""
        
        demand = await generate_demand({
            "matter_id": "test-matter",
            "insurer_id": "test-insurer", 
            "ask_amount": 100000
        })
        
        # Count citations in HTML
        citation_count = len(re.findall(r'\[Doc\s+\w+\s+p\.\d+\]', demand['letter_html']))
        assert citation_count >= 3, f"Demand has only {citation_count} citations, needs ≥3"
        
        # Verify no factual paragraphs lack citations
        factual_paragraphs = self._extract_factual_paragraphs(demand['letter_html'])
        uncited_paragraphs = [p for p in factual_paragraphs if not self._has_citation(p)]
        
        assert len(uncited_paragraphs) == 0, f"Found {len(uncited_paragraphs)} uncited paragraphs"
```

### Phase 8: Deployment & Monitoring (Week 7-8)

#### 8.1 Cloud Run Deployment

**Deploy Script:**
```bash
#!/bin/bash
# deploy_insurance_services.sh

set -e

PROJECT_ID="your-project"
REGION="us-central1"

echo "Deploying insurance-parse service..."
gcloud builds submit packages/insurance-battle-station/insurance-parse \
  --config packages/insurance-battle-station/insurance-parse/cloudbuild.yaml

echo "Deploying demand-drafter service..."  
gcloud builds submit packages/insurance-battle-station/demand-drafter \
  --config packages/insurance-battle-station/demand-drafter/cloudbuild.yaml

echo "Deploying comms-ingest service..."
gcloud builds submit packages/insurance-battle-station/comms-ingest \
  --config packages/insurance-battle-station/comms-ingest/cloudbuild.yaml

echo "Updating core API with insurance routes..."
gcloud run deploy ailex-core \
  --image gcr.io/$PROJECT_ID/ailex-core:$BUILD_ID \
  --region $REGION \
  --set-env-vars INSURANCE_PARSE_URL=https://insurance-parse-xyz.run.app \
  --set-env-vars DEMAND_DRAFTER_URL=https://demand-drafter-xyz.run.app \
  --set-env-vars COMMS_INGEST_URL=https://comms-ingest-xyz.run.app

echo "All insurance services deployed successfully!"
```

#### 8.2 Monitoring & Alerting

**Cloud Monitoring Metrics:**
```yaml
# monitoring/insurance_alerts.yaml
alertPolicy:
  displayName: "Insurance Battle Station Health"
  conditions:
    - displayName: "Dec Page Parse Failures"
      conditionThreshold:
        filter: 'resource.type="cloud_run_revision" resource.label.service_name="insurance-parse"'
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 5
        duration: 300s
    
    - displayName: "Demand Generation Errors"  
      conditionThreshold:
        filter: 'resource.type="cloud_run_revision" resource.label.service_name="demand-drafter"'
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 3
        duration: 180s
        
    - displayName: "Low Parse Confidence Rate"
      conditionThreshold:
        filter: 'metric.type="custom.googleapis.com/insurance/parse_confidence"'
        comparison: COMPARISON_LESS_THAN  
        thresholdValue: 0.8
        duration: 600s
```

## Security & Compliance Considerations

### HIPAA Compliance
- **Document AI**: HIPAA-eligible, no customer data used for training
- **Vertex AI**: Zero-data-retention mode configured
- **Data Encryption**: All PHI encrypted at rest and in transit
- **Access Logging**: All insurance data access logged with tenant + matter ID
- **Service Accounts**: Least-privilege IAM per microservice

### Rate Limiting & Cost Control
- Document AI: Max 100 parses/hour per tenant
- Gemini 2.5 Pro: Max 50 demand generations/hour per tenant  
- Gmail/Graph API: Respect provider rate limits with exponential backoff

## Success Metrics (90-Day Targets)

1. **Parse Accuracy**: ≥90% correct limit extraction on clear dec pages
2. **Parse Speed**: <5 minutes from upload → structured policy
3. **Citation Compliance**: 100% demands include doc/page anchors
4. **Outlier Accuracy**: >80% of flags match attorney judgment
5. **User Adoption**: 75% of PI matters use insurance functionality
6. **System Uptime**: 99.5% availability for all insurance services

## Risk Mitigation

### Technical Risks
- **Document AI accuracy**: Use Custom Extractor with 100-300 samples if Form Parser insufficient
- **Citation drift**: Always derive page numbers from Document AI; verifier rejects uncited content
- **Gemini hallucination**: System prompts emphasize "ONLY provided facts"; citation verifier blocks uncited content

### Business Risks  
- **Email privacy**: Use read-only scopes, label/folder scoping, minimal header storage
- **NAIC data accuracy**: Seed from public listings, allow firm overrides
- **Settlement privilege**: All negotiation events marked attorney work product

## Implementation Timeline Summary

| Week | Phase | Deliverables |
|------|-------|-------------|
| 1-2 | Core Models | Database schema, Pydantic models, API scaffolding |
| 2-3 | insurance-parse | Document AI integration, dec page parsing service |
| 3-4 | demand-drafter | Gemini 2.5 Pro integration, demand generation service |
| 4-5 | Core Integration | Insurance API routes, service layer, database operations |
| 5-6 | Frontend | React components for policies, demands, negotiation timeline |
| 6 | comms-ingest | Gmail/Graph integration, email parsing service |
| 6-7 | Testing | Unit tests, integration tests, gold set validation |
| 7-8 | Deployment | Cloud Run deployment, monitoring, production readiness |

## Conclusion

This implementation plan delivers a complete Insurance Battle Station as specified in the PRD, built on a microservices architecture for scalability and compliance. The phased approach ensures steady progress with testable milestones, while the focus on security and HIPAA compliance meets the stringent requirements of legal practice management.

The result will be a powerful tool that transforms personal injury practice by automating dec page analysis, generating compelling demand letters with proper citations, and providing data-driven negotiation insights—all while maintaining the highest standards of security and professional compliance.