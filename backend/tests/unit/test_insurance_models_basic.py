"""
Basic Unit Tests for Insurance Models

Simple tests for Pydantic models in the insurance module.
"""

import uuid
from datetime import date
from decimal import Decimal

import pytest
from pydantic import ValidationError

from backend.models.insurance import (
    InsurerCreate,
    InsurerResponse,
    InsurancePolicyCreate,
    InsurancePolicyResponse,
    CoverageLimitCreate,
    CoverageType,
)


class TestInsurerModels:
    """Test insurer models."""

    def test_insurer_create_valid(self):
        """Test valid insurer creation."""
        data = {
            "name": "State Farm Insurance",
            "naic_code": "25178",
        }
        insurer = InsurerCreate(**data)
        assert insurer.name == "State Farm Insurance"
        assert insurer.naic_code == "25178"

    def test_insurer_create_minimal(self):
        """Test insurer creation with minimal required fields."""
        data = {
            "name": "Test Insurance",
        }
        insurer = InsurerCreate(**data)
        assert insurer.name == "Test Insurance"
        assert insurer.naic_code is None


class TestInsurancePolicyModels:
    """Test insurance policy models."""

    def test_insurance_policy_create_valid(self):
        """Test valid insurance policy creation."""
        matter_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        data = {
            "matter_id": matter_id,
            "tenant_id": tenant_id,
            "policy_number": "SF-123456789",
            "named_insured": "John Doe",
            "effective_date": date(2024, 1, 1),
            "expiration_date": date(2024, 12, 31),
        }
        policy = InsurancePolicyCreate(**data)
        assert policy.matter_id == matter_id
        assert policy.tenant_id == tenant_id
        assert policy.policy_number == "SF-123456789"
        assert policy.named_insured == "John Doe"

    def test_insurance_policy_create_minimal(self):
        """Test insurance policy creation with minimal fields."""
        data = {
            "matter_id": uuid.uuid4(),
            "tenant_id": uuid.uuid4(),
        }
        policy = InsurancePolicyCreate(**data)
        assert policy.matter_id is not None
        assert policy.tenant_id is not None
        assert policy.policy_number is None


class TestCoverageLimitModels:
    """Test coverage limit models."""

    def test_coverage_limit_create_valid(self):
        """Test valid coverage limit creation."""
        data = {
            "coverage_type": CoverageType.BODILY_INJURY,
            "per_person_limit": Decimal("250000.00"),
            "per_occurrence_limit": Decimal("500000.00"),
        }
        coverage = CoverageLimitCreate(**data)
        assert coverage.coverage_type == CoverageType.BODILY_INJURY
        assert coverage.per_person_limit == Decimal("250000.00")
        assert coverage.per_occurrence_limit == Decimal("500000.00")

    def test_coverage_limit_create_minimal(self):
        """Test coverage limit creation with minimal fields."""
        data = {
            "coverage_type": CoverageType.PROPERTY_DAMAGE,
        }
        coverage = CoverageLimitCreate(**data)
        assert coverage.coverage_type == CoverageType.PROPERTY_DAMAGE
        assert coverage.per_person_limit is None
        assert coverage.per_occurrence_limit is None


class TestModelValidation:
    """Test basic model validation."""

    def test_insurer_name_validation(self):
        """Test insurer name length validation."""
        # Test minimum length
        with pytest.raises(ValidationError):
            InsurerCreate(name="")

        # Test maximum length (255 characters)
        long_name = "x" * 256
        with pytest.raises(ValidationError):
            InsurerCreate(name=long_name)

    def test_policy_date_validation(self):
        """Test policy date validation."""
        # Test expiration before effective date
        data = {
            "matter_id": uuid.uuid4(),
            "tenant_id": uuid.uuid4(),
            "effective_date": date(2024, 12, 31),
            "expiration_date": date(2024, 1, 1),  # Before effective date
        }
        
        # This should raise a validation error due to the validator
        with pytest.raises(ValidationError):
            InsurancePolicyCreate(**data)

    def test_coverage_limit_negative_amounts(self):
        """Test that coverage limits reject negative amounts."""
        with pytest.raises(ValidationError):
            CoverageLimitCreate(
                coverage_type=CoverageType.BODILY_INJURY,
                per_person_limit=Decimal("-1000.00"),
            )

        with pytest.raises(ValidationError):
            CoverageLimitCreate(
                coverage_type=CoverageType.BODILY_INJURY,
                per_occurrence_limit=Decimal("-5000.00"),
            )