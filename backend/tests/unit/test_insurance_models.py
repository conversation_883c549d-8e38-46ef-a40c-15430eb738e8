"""
Unit Tests for Insurance Models

Tests for Pydantic models and SQLAlchemy models in the insurance module.
"""

import uuid
from datetime import date, datetime
from decimal import Decimal

import pytest
from pydantic import ValidationError

from backend.models.insurance import (
    CoverageLimitORM,
    CoverageLimitCreate,
    CoverageLimitResponse,
    DemandLetterORM,
    DemandLetterCreate,
    DemandLetterResponse,
    InsurancePolicyORM,
    InsurancePolicyCreate,
    InsurancePolicyResponse,
    InsurerORM,
    InsurerCreate,
    InsurerResponse,
    NegotiationEventORM,
    NegotiationEventCreate,
    NegotiationEventType,
    CoverageType,
)


class TestInsurerModels:
    """Test insurer models."""

    def test_insurer_create_valid(self):
        """Test valid insurer creation."""
        data = {
            "name": "State Farm Insurance",
            "naic_code": "25178",
            "website": "https://www.statefarm.com",
            "phone": "1-800-STATE-FARM",
            "email": "<EMAIL>",
        }
        insurer = InsurerCreate(**data)
        assert insurer.name == "State Farm Insurance"
        assert insurer.naic_code == "25178"
        assert insurer.website == "https://www.statefarm.com"

    def test_insurer_create_minimal(self):
        """Test insurer creation with minimal required fields."""
        data = {
            "name": "Test Insurance",
            "naic_code": "12345",
        }
        insurer = InsurerCreate(**data)
        assert insurer.name == "Test Insurance"
        assert insurer.naic_code == "12345"
        assert insurer.website is None
        assert insurer.phone is None

    def test_insurer_create_invalid_naic(self):
        """Test insurer creation with invalid NAIC code."""
        data = {
            "name": "Test Insurance",
            "naic_code": "abc",  # Should be numeric
        }
        with pytest.raises(ValidationError):
            InsurerCreate(**data)

    def test_insurer_create_invalid_email(self):
        """Test insurer creation with invalid email."""
        data = {
            "name": "Test Insurance",
            "naic_code": "12345",
            "email": "not-an-email",
        }
        with pytest.raises(ValidationError):
            InsurerCreate(**data)

    def test_insurer_create_invalid_website(self):
        """Test insurer creation with invalid website URL."""
        data = {
            "name": "Test Insurance",
            "naic_code": "12345",
            "website": "not-a-url",
        }
        with pytest.raises(ValidationError):
            InsurerCreate(**data)

    def test_insurer_response_model(self):
        """Test insurer response model."""
        data = {
            "id": uuid.uuid4(),
            "name": "State Farm Insurance",
            "naic_code": "25178",
            "website": "https://www.statefarm.com",
            "phone": "1-800-STATE-FARM",
            "email": "<EMAIL>",
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
        }
        response = InsurerResponse(**data)
        assert response.id == data["id"]
        assert response.name == "State Farm Insurance"
        assert response.is_active is True


class TestInsurancePolicyModels:
    """Test insurance policy models."""

    def test_insurance_policy_create_valid(self):
        """Test valid insurance policy creation."""
        tenant_id = uuid.uuid4()
        insurer_id = uuid.uuid4()
        data = {
            "tenant_id": tenant_id,
            "insurer_id": insurer_id,
            "policy_number": "SF-123456789",
            "policy_holder_name": "John Doe",
            "policy_type": PolicyType.AUTO,
            "effective_date": datetime.utcnow(),
            "expiry_date": datetime.utcnow(),
            "premium_amount": Decimal("1200.00"),
            "deductible": Decimal("500.00"),
        }
        policy = InsurancePolicyCreate(**data)
        assert policy.tenant_id == tenant_id
        assert policy.policy_number == "SF-123456789"
        assert policy.policy_type == PolicyType.AUTO
        assert policy.premium_amount == Decimal("1200.00")

    def test_insurance_policy_create_invalid_amount(self):
        """Test insurance policy creation with invalid amount."""
        data = {
            "tenant_id": uuid.uuid4(),
            "insurer_id": uuid.uuid4(),
            "policy_number": "SF-123456789",
            "policy_holder_name": "John Doe",
            "policy_type": PolicyType.AUTO,
            "effective_date": datetime.utcnow(),
            "expiry_date": datetime.utcnow(),
            "premium_amount": Decimal("-100.00"),  # Negative amount
        }
        with pytest.raises(ValidationError):
            InsurancePolicyCreate(**data)

    def test_insurance_policy_create_invalid_policy_type(self):
        """Test insurance policy creation with invalid policy type."""
        data = {
            "tenant_id": uuid.uuid4(),
            "insurer_id": uuid.uuid4(),
            "policy_number": "SF-123456789",
            "policy_holder_name": "John Doe",
            "policy_type": "invalid_type",
            "effective_date": datetime.utcnow(),
            "expiry_date": datetime.utcnow(),
            "premium_amount": Decimal("1200.00"),
        }
        with pytest.raises(ValidationError):
            InsurancePolicyCreate(**data)


class TestCoverageLimitModels:
    """Test coverage limit models."""

    def test_coverage_limit_create_valid(self):
        """Test valid coverage limit creation."""
        policy_id = uuid.uuid4()
        data = {
            "policy_id": policy_id,
            "coverage_type": "bodily_injury",
            "limit_amount": Decimal("250000.00"),
            "per_person": True,
            "description": "Bodily injury liability per person",
        }
        coverage = CoverageLimitCreate(**data)
        assert coverage.policy_id == policy_id
        assert coverage.coverage_type == "bodily_injury"
        assert coverage.limit_amount == Decimal("250000.00")
        assert coverage.per_person is True

    def test_coverage_limit_create_invalid_amount(self):
        """Test coverage limit creation with invalid amount."""
        data = {
            "policy_id": uuid.uuid4(),
            "coverage_type": "bodily_injury",
            "limit_amount": Decimal("-1000.00"),  # Negative amount
        }
        with pytest.raises(ValidationError):
            CoverageLimitCreate(**data)


class TestDemandLetterModels:
    """Test demand letter models."""

    def test_demand_letter_create_valid(self):
        """Test valid demand letter creation."""
        tenant_id = uuid.uuid4()
        case_id = uuid.uuid4()
        data = {
            "tenant_id": tenant_id,
            "case_id": case_id,
            "title": "Demand Letter for Motor Vehicle Accident",
            "total_demand": Decimal("50000.00"),
            "settlement_breakdown": {
                "medical_expenses": 15000,
                "lost_wages": 10000,
                "pain_and_suffering": 25000,
            },
        }
        demand_letter = DemandLetterCreate(**data)
        assert demand_letter.tenant_id == tenant_id
        assert demand_letter.case_id == case_id
        assert demand_letter.total_demand == Decimal("50000.00")
        assert demand_letter.settlement_breakdown["medical_expenses"] == 15000

    def test_demand_letter_create_invalid_amount(self):
        """Test demand letter creation with invalid total demand."""
        data = {
            "tenant_id": uuid.uuid4(),
            "case_id": uuid.uuid4(),
            "title": "Test Demand Letter",
            "total_demand": Decimal("-1000.00"),  # Negative amount
        }
        with pytest.raises(ValidationError):
            DemandLetterCreate(**data)

    def test_demand_letter_response_model(self):
        """Test demand letter response model."""
        data = {
            "id": uuid.uuid4(),
            "tenant_id": uuid.uuid4(),
            "case_id": uuid.uuid4(),
            "title": "Demand Letter for Motor Vehicle Accident",
            "generated_content": "Sample content",
            "final_content": "Final content",
            "status": DemandLetterStatus.DRAFT,
            "total_demand": Decimal("50000.00"),
            "settlement_breakdown": {"medical": 15000},
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
        }
        response = DemandLetterResponse(**data)
        assert response.status == DemandLetterStatus.DRAFT
        assert response.total_demand == Decimal("50000.00")


class TestNegotiationEventModels:
    """Test negotiation event models."""

    def test_negotiation_event_create_valid(self):
        """Test valid negotiation event creation."""
        demand_letter_id = uuid.uuid4()
        data = {
            "demand_letter_id": demand_letter_id,
            "event_type": NegotiationEventType.INITIAL_OFFER,
            "amount": Decimal("25000.00"),
            "notes": "Initial offer from insurance company",
        }
        event = NegotiationEventCreate(**data)
        assert event.demand_letter_id == demand_letter_id
        assert event.event_type == NegotiationEventType.INITIAL_OFFER
        assert event.amount == Decimal("25000.00")

    def test_negotiation_event_create_invalid_amount(self):
        """Test negotiation event creation with invalid amount."""
        data = {
            "demand_letter_id": uuid.uuid4(),
            "event_type": NegotiationEventType.INITIAL_OFFER,
            "amount": Decimal("-5000.00"),  # Negative amount
        }
        with pytest.raises(ValidationError):
            NegotiationEventCreate(**data)

    def test_negotiation_event_create_invalid_type(self):
        """Test negotiation event creation with invalid event type."""
        data = {
            "demand_letter_id": uuid.uuid4(),
            "event_type": "invalid_type",
            "amount": Decimal("25000.00"),
        }
        with pytest.raises(ValidationError):
            NegotiationEventCreate(**data)


class TestSQLAlchemyModels:
    """Test SQLAlchemy models."""

    def test_insurer_model_repr(self):
        """Test Insurer model string representation."""
        insurer = Insurer(
            id=uuid.uuid4(),
            name="State Farm Insurance",
            naic_code="25178",
        )
        repr_str = repr(insurer)
        assert "State Farm Insurance" in repr_str
        assert "25178" in repr_str

    def test_insurance_policy_model_repr(self):
        """Test InsurancePolicy model string representation."""
        policy = InsurancePolicy(
            id=uuid.uuid4(),
            tenant_id=uuid.uuid4(),
            policy_number="SF-123456789",
            policy_holder_name="John Doe",
            policy_type=PolicyType.AUTO,
        )
        repr_str = repr(policy)
        assert "SF-123456789" in repr_str
        assert "John Doe" in repr_str

    def test_demand_letter_model_repr(self):
        """Test DemandLetter model string representation."""
        demand_letter = DemandLetter(
            id=uuid.uuid4(),
            tenant_id=uuid.uuid4(),
            title="Test Demand Letter",
            status=DemandLetterStatus.DRAFT,
        )
        repr_str = repr(demand_letter)
        assert "Test Demand Letter" in repr_str
        assert "draft" in repr_str

    def test_model_relationships(self):
        """Test model relationships are properly defined."""
        # Test that relationships exist
        assert hasattr(Insurer, "policies")
        assert hasattr(InsurancePolicy, "insurer")
        assert hasattr(InsurancePolicy, "coverage_limits")
        assert hasattr(CoverageLimit, "policy")
        assert hasattr(DemandLetter, "negotiation_events")
        assert hasattr(NegotiationEvent, "demand_letter")


class TestModelValidation:
    """Test advanced model validation."""

    def test_policy_expiry_after_effective_date(self):
        """Test that policy expiry date is after effective date."""
        effective_date = datetime(2024, 1, 1)
        expiry_date = datetime(2023, 12, 31)  # Before effective date

        data = {
            "tenant_id": uuid.uuid4(),
            "insurer_id": uuid.uuid4(),
            "policy_number": "SF-123456789",
            "policy_holder_name": "John Doe",
            "policy_type": PolicyType.AUTO,
            "effective_date": effective_date,
            "expiry_date": expiry_date,
            "premium_amount": Decimal("1200.00"),
        }

        # This should be validated in the service layer or database constraints
        # For now, we just test that the model accepts the data
        policy = InsurancePolicyCreate(**data)
        assert policy.effective_date == effective_date
        assert policy.expiry_date == expiry_date

    def test_settlement_breakdown_totals(self):
        """Test that settlement breakdown adds up to total demand."""
        breakdown = {
            "medical_expenses": 15000,
            "lost_wages": 10000,
            "pain_and_suffering": 20000,  # Total: 45000
        }

        data = {
            "tenant_id": uuid.uuid4(),
            "case_id": uuid.uuid4(),
            "title": "Test Demand Letter",
            "total_demand": Decimal("50000.00"),  # Doesn't match breakdown total
            "settlement_breakdown": breakdown,
        }

        # This validation should be in the service layer
        demand_letter = DemandLetterCreate(**data)
        assert demand_letter.total_demand == Decimal("50000.00")
        assert sum(breakdown.values()) == 45000

    def test_phone_number_format_validation(self):
        """Test phone number format validation."""
        valid_phones = [
            "1-800-STATE-FARM",
            "(*************",
            "************",
            "15551234567",
        ]

        invalid_phones = [
            "123",
            "phone",
            "555-123",
        ]

        base_data = {
            "name": "Test Insurance",
            "naic_code": "12345",
        }

        # Test valid phone numbers
        for phone in valid_phones:
            data = {**base_data, "phone": phone}
            insurer = InsurerCreate(**data)
            assert insurer.phone == phone

        # Invalid phone numbers should ideally be caught by validation
        # For now, we just test they're accepted (validation can be added later)
        for phone in invalid_phones:
            data = {**base_data, "phone": phone}
            insurer = InsurerCreate(**data)
            assert insurer.phone == phone
