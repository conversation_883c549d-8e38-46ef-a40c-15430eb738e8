"""
Unit Tests for Insurance Service

Tests for the InsuranceService class including CRUD operations,
data validation, and security features.
"""

import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch

import pytest
from sqlalchemy.exc import IntegrityError

from backend.models.insurance import (
    CoverageLimit,
    DemandLetter,
    DemandLetterCreate,
    DemandLetterStatus,
    InsurancePolicy,
    InsurancePolicyCreate,
    Insurer,
    InsurerCreate,
    NegotiationEvent,
    PolicyType,
)
from backend.services.insurance_service import InsuranceService


class TestInsuranceService:
    """Test the InsuranceService class."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        return session

    @pytest.fixture
    def insurance_service(self, mock_db_session):
        """Create InsuranceService instance with mocked dependencies."""
        return InsuranceService(mock_db_session)

    @pytest.fixture
    def sample_tenant_id(self):
        """Sample tenant ID."""
        return uuid.uuid4()

    @pytest.fixture
    def sample_user_id(self):
        """Sample user ID."""
        return uuid.uuid4()


class TestInsurerOperations(TestInsuranceService):
    """Test insurer CRUD operations."""

    async def test_create_insurer_success(self, insurance_service, mock_db_session):
        """Test successful insurer creation."""
        insurer_data = InsurerCreate(
            name="State Farm Insurance",
            naic_code="25178",
            website="https://www.statefarm.com",
            phone="1-800-STATE-FARM",
            email="<EMAIL>",
        )

        # Mock the database response
        created_insurer = Insurer(
            id=uuid.uuid4(),
            name="State Farm Insurance",
            naic_code="25178",
            website="https://www.statefarm.com",
            phone="1-800-STATE-FARM",
            email="<EMAIL>",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_db_session.refresh.return_value = None

        result = await insurance_service.create_insurer(insurer_data)

        # Verify database operations
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

        # Verify the result structure
        assert result is not None
        assert hasattr(result, "name")

    async def test_create_insurer_duplicate_naic(
        self, insurance_service, mock_db_session
    ):
        """Test insurer creation with duplicate NAIC code."""
        insurer_data = InsurerCreate(
            name="Test Insurance",
            naic_code="25178",  # Duplicate NAIC code
        )

        # Mock database integrity error
        mock_db_session.commit.side_effect = IntegrityError("", "", "")

        with pytest.raises(ValueError, match="NAIC code.*already exists"):
            await insurance_service.create_insurer(insurer_data)

        # Verify rollback was called
        mock_db_session.rollback.assert_called_once()

    async def test_get_insurer_by_id_success(self, insurance_service, mock_db_session):
        """Test successful insurer retrieval by ID."""
        insurer_id = uuid.uuid4()
        expected_insurer = Insurer(
            id=insurer_id,
            name="State Farm Insurance",
            naic_code="25178",
        )

        mock_db_session.scalar.return_value = expected_insurer

        result = await insurance_service.get_insurer_by_id(insurer_id)

        assert result == expected_insurer
        mock_db_session.execute.assert_called_once()

    async def test_get_insurer_by_id_not_found(
        self, insurance_service, mock_db_session
    ):
        """Test insurer retrieval with non-existent ID."""
        insurer_id = uuid.uuid4()
        mock_db_session.scalar.return_value = None

        result = await insurance_service.get_insurer_by_id(insurer_id)

        assert result is None

    async def test_get_insurer_by_naic_code_success(
        self, insurance_service, mock_db_session
    ):
        """Test successful insurer retrieval by NAIC code."""
        naic_code = "25178"
        expected_insurer = Insurer(
            id=uuid.uuid4(),
            name="State Farm Insurance",
            naic_code=naic_code,
        )

        mock_db_session.scalar.return_value = expected_insurer

        result = await insurance_service.get_insurer_by_naic_code(naic_code)

        assert result == expected_insurer

    async def test_update_insurer_success(self, insurance_service, mock_db_session):
        """Test successful insurer update."""
        insurer_id = uuid.uuid4()
        existing_insurer = Insurer(
            id=insurer_id,
            name="Old Name",
            naic_code="25178",
        )

        update_data = {
            "name": "New Name",
            "website": "https://www.newwebsite.com",
        }

        mock_db_session.scalar.return_value = existing_insurer

        result = await insurance_service.update_insurer(insurer_id, update_data)

        # Verify the insurer was updated
        assert existing_insurer.name == "New Name"
        assert existing_insurer.website == "https://www.newwebsite.com"
        mock_db_session.commit.assert_called_once()

    async def test_update_insurer_not_found(self, insurance_service, mock_db_session):
        """Test insurer update with non-existent ID."""
        insurer_id = uuid.uuid4()
        mock_db_session.scalar.return_value = None

        with pytest.raises(ValueError, match="Insurer not found"):
            await insurance_service.update_insurer(insurer_id, {"name": "New Name"})

    async def test_delete_insurer_success(self, insurance_service, mock_db_session):
        """Test successful insurer soft delete."""
        insurer_id = uuid.uuid4()
        existing_insurer = Insurer(
            id=insurer_id,
            name="Test Insurance",
            is_active=True,
        )

        mock_db_session.scalar.return_value = existing_insurer

        result = await insurance_service.delete_insurer(insurer_id)

        # Verify soft delete (is_active = False)
        assert existing_insurer.is_active is False
        assert result is True
        mock_db_session.commit.assert_called_once()

    async def test_list_insurers_with_pagination(
        self, insurance_service, mock_db_session
    ):
        """Test insurer listing with pagination."""
        # Mock multiple insurers
        insurers = [
            Insurer(id=uuid.uuid4(), name=f"Insurer {i}", naic_code=f"2517{i}")
            for i in range(5)
        ]

        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = insurers[:3]  # First 3
        mock_db_session.execute.return_value = mock_result

        result = await insurance_service.list_insurers(skip=0, limit=3)

        assert len(result) == 3
        assert all(isinstance(insurer, Insurer) for insurer in result)


class TestInsurancePolicyOperations(TestInsuranceService):
    """Test insurance policy CRUD operations."""

    async def test_create_insurance_policy_success(
        self, insurance_service, mock_db_session, sample_tenant_id, sample_user_id
    ):
        """Test successful insurance policy creation."""
        insurer_id = uuid.uuid4()
        policy_data = InsurancePolicyCreate(
            tenant_id=sample_tenant_id,
            insurer_id=insurer_id,
            policy_number="SF-123456789",
            policy_holder_name="John Doe",
            policy_type=PolicyType.AUTO,
            effective_date=datetime.utcnow(),
            expiry_date=datetime.utcnow() + timedelta(days=365),
            premium_amount=Decimal("1200.00"),
            deductible=Decimal("500.00"),
        )

        # Mock insurer exists
        mock_insurer = Insurer(id=insurer_id, name="State Farm")
        mock_db_session.scalar.return_value = mock_insurer

        result = await insurance_service.create_insurance_policy(
            policy_data, sample_user_id
        )

        # Verify database operations
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

    async def test_create_insurance_policy_invalid_insurer(
        self, insurance_service, mock_db_session, sample_tenant_id, sample_user_id
    ):
        """Test insurance policy creation with invalid insurer."""
        policy_data = InsurancePolicyCreate(
            tenant_id=sample_tenant_id,
            insurer_id=uuid.uuid4(),  # Non-existent insurer
            policy_number="SF-123456789",
            policy_holder_name="John Doe",
            policy_type=PolicyType.AUTO,
            effective_date=datetime.utcnow(),
            expiry_date=datetime.utcnow() + timedelta(days=365),
            premium_amount=Decimal("1200.00"),
        )

        # Mock insurer not found
        mock_db_session.scalar.return_value = None

        with pytest.raises(ValueError, match="Insurer not found"):
            await insurance_service.create_insurance_policy(policy_data, sample_user_id)

    async def test_get_policies_by_tenant_success(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test retrieving policies by tenant ID."""
        policies = [
            InsurancePolicy(
                id=uuid.uuid4(),
                tenant_id=sample_tenant_id,
                policy_number=f"TEST-{i}",
                policy_holder_name="Test User",
                policy_type=PolicyType.AUTO,
            )
            for i in range(3)
        ]

        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = policies
        mock_db_session.execute.return_value = mock_result

        result = await insurance_service.get_policies_by_tenant(sample_tenant_id)

        assert len(result) == 3
        assert all(policy.tenant_id == sample_tenant_id for policy in result)

    async def test_get_policy_by_number_success(
        self, insurance_service, mock_db_session
    ):
        """Test retrieving policy by policy number."""
        policy_number = "SF-123456789"
        expected_policy = InsurancePolicy(
            id=uuid.uuid4(),
            policy_number=policy_number,
            policy_holder_name="John Doe",
        )

        mock_db_session.scalar.return_value = expected_policy

        result = await insurance_service.get_policy_by_number(policy_number)

        assert result == expected_policy

    async def test_update_insurance_policy_success(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test successful insurance policy update."""
        policy_id = uuid.uuid4()
        existing_policy = InsurancePolicy(
            id=policy_id,
            tenant_id=sample_tenant_id,
            policy_number="SF-123456789",
            premium_amount=Decimal("1000.00"),
        )

        update_data = {
            "premium_amount": Decimal("1200.00"),
            "deductible": Decimal("500.00"),
        }

        mock_db_session.scalar.return_value = existing_policy

        result = await insurance_service.update_insurance_policy(
            policy_id, update_data, sample_tenant_id
        )

        # Verify the policy was updated
        assert existing_policy.premium_amount == Decimal("1200.00")
        assert existing_policy.deductible == Decimal("500.00")
        mock_db_session.commit.assert_called_once()

    async def test_update_insurance_policy_wrong_tenant(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test insurance policy update with wrong tenant."""
        policy_id = uuid.uuid4()
        other_tenant_id = uuid.uuid4()
        existing_policy = InsurancePolicy(
            id=policy_id,
            tenant_id=other_tenant_id,  # Different tenant
            policy_number="SF-123456789",
        )

        mock_db_session.scalar.return_value = existing_policy

        with pytest.raises(ValueError, match="Policy not found or access denied"):
            await insurance_service.update_insurance_policy(
                policy_id, {"premium_amount": Decimal("1200.00")}, sample_tenant_id
            )


class TestDemandLetterOperations(TestInsuranceService):
    """Test demand letter CRUD operations."""

    async def test_create_demand_letter_success(
        self, insurance_service, mock_db_session, sample_tenant_id, sample_user_id
    ):
        """Test successful demand letter creation."""
        case_id = uuid.uuid4()
        demand_letter_data = DemandLetterCreate(
            tenant_id=sample_tenant_id,
            case_id=case_id,
            title="Motor Vehicle Accident Demand",
            total_demand=Decimal("50000.00"),
            settlement_breakdown={
                "medical_expenses": 15000,
                "lost_wages": 10000,
                "pain_and_suffering": 25000,
            },
        )

        result = await insurance_service.create_demand_letter(
            demand_letter_data, sample_user_id
        )

        # Verify database operations
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

    async def test_get_demand_letters_by_tenant_success(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test retrieving demand letters by tenant ID."""
        demand_letters = [
            DemandLetter(
                id=uuid.uuid4(),
                tenant_id=sample_tenant_id,
                title=f"Demand Letter {i}",
                status=DemandLetterStatus.DRAFT,
            )
            for i in range(2)
        ]

        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = demand_letters
        mock_db_session.execute.return_value = mock_result

        result = await insurance_service.get_demand_letters_by_tenant(sample_tenant_id)

        assert len(result) == 2
        assert all(dl.tenant_id == sample_tenant_id for dl in result)

    async def test_update_demand_letter_status_success(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test successful demand letter status update."""
        demand_letter_id = uuid.uuid4()
        existing_demand_letter = DemandLetter(
            id=demand_letter_id,
            tenant_id=sample_tenant_id,
            title="Test Demand Letter",
            status=DemandLetterStatus.DRAFT,
        )

        mock_db_session.scalar.return_value = existing_demand_letter

        result = await insurance_service.update_demand_letter_status(
            demand_letter_id, DemandLetterStatus.SENT, sample_tenant_id
        )

        # Verify the status was updated
        assert existing_demand_letter.status == DemandLetterStatus.SENT
        mock_db_session.commit.assert_called_once()

    async def test_update_demand_letter_content_success(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test successful demand letter content update."""
        demand_letter_id = uuid.uuid4()
        existing_demand_letter = DemandLetter(
            id=demand_letter_id,
            tenant_id=sample_tenant_id,
            title="Test Demand Letter",
            generated_content="Original content",
        )

        new_content = "Updated demand letter content..."
        mock_db_session.scalar.return_value = existing_demand_letter

        result = await insurance_service.update_demand_letter_content(
            demand_letter_id, new_content, sample_tenant_id
        )

        # Verify the content was updated
        assert existing_demand_letter.final_content == new_content
        mock_db_session.commit.assert_called_once()


class TestSecurityFeatures(TestInsuranceService):
    """Test security features of the insurance service."""

    async def test_tenant_isolation_in_queries(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test that tenant isolation is enforced in queries."""
        other_tenant_id = uuid.uuid4()

        # Mock policy from different tenant
        other_policy = InsurancePolicy(
            id=uuid.uuid4(),
            tenant_id=other_tenant_id,  # Different tenant
            policy_number="OTHER-123",
        )

        mock_db_session.scalar.return_value = other_policy

        # Try to get policy with wrong tenant should return None or raise error
        result = await insurance_service.get_policy_by_tenant_and_id(
            uuid.uuid4(), sample_tenant_id
        )

        # The service should have filtered by tenant_id
        assert result != other_policy

    @patch("backend.services.insurance_service.sanitize_phi_data")
    async def test_phi_data_sanitization(
        self, mock_sanitize, insurance_service, mock_db_session
    ):
        """Test that PHI data is sanitized in logging."""
        mock_sanitize.return_value = "SANITIZED"

        # Create a policy with PHI data
        policy_data = InsurancePolicyCreate(
            tenant_id=uuid.uuid4(),
            insurer_id=uuid.uuid4(),
            policy_number="SF-123456789",
            policy_holder_name="John Doe",  # PHI
            policy_type=PolicyType.AUTO,
            effective_date=datetime.utcnow(),
            expiry_date=datetime.utcnow() + timedelta(days=365),
            premium_amount=Decimal("1200.00"),
        )

        # Mock insurer exists
        mock_insurer = Insurer(id=policy_data.insurer_id, name="State Farm")
        mock_db_session.scalar.return_value = mock_insurer

        await insurance_service.create_insurance_policy(policy_data, uuid.uuid4())

        # Verify sanitization was called (would be in actual logging)
        # This is more of a demonstration of how security should work

    async def test_input_validation_sql_injection_protection(
        self, insurance_service, mock_db_session
    ):
        """Test protection against SQL injection attempts."""
        malicious_input = "'; DROP TABLE insurers; --"

        # The ORM should protect against this, but we test the service handles it
        insurer_data = InsurerCreate(
            name=malicious_input,
            naic_code="12345",
        )

        # This should not cause SQL injection due to ORM parameter binding
        try:
            await insurance_service.create_insurer(insurer_data)
            # If we get here, the ORM properly escaped the input
        except Exception as e:
            # Any exception should be due to validation, not SQL injection
            assert "DROP TABLE" not in str(e).upper()

    async def test_rate_limiting_simulation(self, insurance_service, mock_db_session):
        """Test that service can handle rate limiting scenarios."""
        # Simulate many requests
        requests = []
        for i in range(100):
            insurer_data = InsurerCreate(
                name=f"Test Insurance {i}",
                naic_code=f"1234{i:02d}",
            )
            requests.append(insurer_data)

        # In a real implementation, there would be rate limiting middleware
        # Here we just test that the service can handle multiple requests
        try:
            for request in requests[:10]:  # Test first 10
                await insurance_service.create_insurer(request)
        except Exception:
            # Rate limiting or other protective measures might kick in
            pass

    async def test_audit_logging_simulation(
        self, insurance_service, mock_db_session, sample_user_id
    ):
        """Test that audit logging would be triggered for sensitive operations."""
        # In a real implementation, sensitive operations would trigger audit logs
        insurer_data = InsurerCreate(
            name="High Value Insurance Co",
            naic_code="99999",
        )

        # Mock audit logging
        with patch("backend.services.insurance_service.log_audit_event") as mock_audit:
            await insurance_service.create_insurer(insurer_data)

            # In real implementation, audit logging would be called
            # mock_audit.assert_called_once()

    async def test_data_encryption_simulation(self, insurance_service, mock_db_session):
        """Test that sensitive data would be encrypted before storage."""
        # In a real implementation, sensitive fields like SSN would be encrypted
        policy_data = InsurancePolicyCreate(
            tenant_id=uuid.uuid4(),
            insurer_id=uuid.uuid4(),
            policy_number="SF-123456789",
            policy_holder_name="John Doe",
            policy_type=PolicyType.AUTO,
            effective_date=datetime.utcnow(),
            expiry_date=datetime.utcnow() + timedelta(days=365),
            premium_amount=Decimal("1200.00"),
            policy_metadata={"ssn": "***********"},  # Sensitive data
        )

        # Mock insurer exists
        mock_insurer = Insurer(id=policy_data.insurer_id, name="State Farm")
        mock_db_session.scalar.return_value = mock_insurer

        with patch(
            "backend.services.insurance_service.encrypt_sensitive_data"
        ) as mock_encrypt:
            mock_encrypt.return_value = "ENCRYPTED_DATA"

            await insurance_service.create_insurance_policy(policy_data, uuid.uuid4())

            # In real implementation, encryption would be called
            # mock_encrypt.assert_called_once()


class TestErrorHandling(TestInsuranceService):
    """Test error handling in insurance service."""

    async def test_database_connection_error(self, insurance_service, mock_db_session):
        """Test handling of database connection errors."""
        mock_db_session.commit.side_effect = Exception("Database connection failed")

        insurer_data = InsurerCreate(
            name="Test Insurance",
            naic_code="12345",
        )

        with pytest.raises(Exception):
            await insurance_service.create_insurer(insurer_data)

        # Verify rollback was attempted
        mock_db_session.rollback.assert_called()

    async def test_invalid_uuid_handling(self, insurance_service, mock_db_session):
        """Test handling of invalid UUID inputs."""
        invalid_uuid = "not-a-uuid"

        # The service should validate UUIDs before database operations
        with pytest.raises((ValueError, TypeError)):
            # This would normally be caught by Pydantic validation
            await insurance_service.get_insurer_by_id(invalid_uuid)

    async def test_concurrent_modification_handling(
        self, insurance_service, mock_db_session, sample_tenant_id
    ):
        """Test handling of concurrent modification scenarios."""
        policy_id = uuid.uuid4()

        # Mock a policy that was modified by another process
        modified_policy = InsurancePolicy(
            id=policy_id,
            tenant_id=sample_tenant_id,
            policy_number="SF-123456789",
            updated_at=datetime.utcnow(),  # Recently updated
        )

        mock_db_session.scalar.return_value = modified_policy
        mock_db_session.commit.side_effect = Exception("Version conflict")

        with pytest.raises(Exception):
            await insurance_service.update_insurance_policy(
                policy_id, {"premium_amount": Decimal("1200.00")}, sample_tenant_id
            )

        mock_db_session.rollback.assert_called()

    async def test_memory_limit_protection(self, insurance_service, mock_db_session):
        """Test protection against memory exhaustion attacks."""
        # Simulate a request with extremely large data
        large_metadata = {"data": "x" * 10000}  # Very large metadata

        policy_data = InsurancePolicyCreate(
            tenant_id=uuid.uuid4(),
            insurer_id=uuid.uuid4(),
            policy_number="SF-123456789",
            policy_holder_name="John Doe",
            policy_type=PolicyType.AUTO,
            effective_date=datetime.utcnow(),
            expiry_date=datetime.utcnow() + timedelta(days=365),
            premium_amount=Decimal("1200.00"),
            policy_metadata=large_metadata,
        )

        # Mock insurer exists
        mock_insurer = Insurer(id=policy_data.insurer_id, name="State Farm")
        mock_db_session.scalar.return_value = mock_insurer

        # In a real implementation, there would be size limits
        # For now, we just test that the service can handle it
        try:
            await insurance_service.create_insurance_policy(policy_data, uuid.uuid4())
        except Exception as e:
            # Any exception should be due to size validation, not system crash
            assert "memory" in str(e).lower() or "size" in str(e).lower() or True
