"""
Basic Unit Tests for Insurance Service

Simple tests for the InsuranceService class methods.
"""

import uuid
from datetime import date
from decimal import Decimal
from unittest.mock import AsyncMock, Mock

import pytest

from backend.models.insurance import (
    InsurerORM,
    InsurerCreate,
    InsurancePolicyORM, 
    InsurancePolicyCreate,
)
from backend.services.insurance_service import InsuranceService


class TestInsuranceServiceBasic:
    """Test the InsuranceService class basic functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        return session

    @pytest.fixture
    def insurance_service(self, mock_db_session):
        """Create InsuranceService instance with mocked dependencies."""
        return InsuranceService(mock_db_session)

    async def test_create_insurer_success(self, insurance_service, mock_db_session):
        """Test successful insurer creation."""
        insurer_data = InsurerCreate(
            name="State Farm Insurance",
            naic_code="25178",
        )

        # Mock the database operations
        mock_db_session.refresh.return_value = None

        result = await insurance_service.create_insurer(insurer_data)

        # Verify database operations were called
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

        # Result should not be None
        assert result is not None

    async def test_get_insurer_by_naic_success(self, insurance_service, mock_db_session):
        """Test successful insurer retrieval by NAIC code."""
        naic_code = "25178"
        expected_insurer = InsurerORM(
            id=uuid.uuid4(),
            name="State Farm Insurance",
            naic_code=naic_code,
        )

        mock_db_session.scalar.return_value = expected_insurer

        result = await insurance_service.get_insurer_by_naic(naic_code)

        # Result should match expected insurer data
        assert result is not None
        mock_db_session.execute.assert_called_once()

    async def test_get_insurer_by_naic_not_found(self, insurance_service, mock_db_session):
        """Test insurer retrieval with non-existent NAIC code."""
        naic_code = "99999"
        mock_db_session.scalar.return_value = None

        result = await insurance_service.get_insurer_by_naic(naic_code)

        assert result is None

    async def test_create_insurance_policy_success(self, insurance_service, mock_db_session):
        """Test successful insurance policy creation."""
        policy_data = InsurancePolicyCreate(
            matter_id=uuid.uuid4(),
            tenant_id=uuid.uuid4(),
            policy_number="SF-123456789",
            named_insured="John Doe",
            effective_date=date(2024, 1, 1),
            expiration_date=date(2024, 12, 31),
        )

        # Mock database operations
        mock_db_session.refresh.return_value = None

        result = await insurance_service.create_insurance_policy(policy_data)

        # Verify database operations were called
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

        # Result should not be None
        assert result is not None

    async def test_get_insurance_policy_success(self, insurance_service, mock_db_session):
        """Test successful insurance policy retrieval."""
        policy_id = uuid.uuid4()
        expected_policy = InsurancePolicyORM(
            id=policy_id,
            matter_id=uuid.uuid4(),
            tenant_id=uuid.uuid4(),
            policy_number="SF-123456789",
        )

        mock_db_session.scalar.return_value = expected_policy

        result = await insurance_service.get_insurance_policy(policy_id)

        # Result should not be None
        assert result is not None
        mock_db_session.execute.assert_called_once()

    async def test_get_insurance_policy_not_found(self, insurance_service, mock_db_session):
        """Test insurance policy retrieval with non-existent ID."""
        policy_id = uuid.uuid4()
        mock_db_session.scalar.return_value = None

        result = await insurance_service.get_insurance_policy(policy_id)

        assert result is None

    async def test_get_policies_by_matter_success(self, insurance_service, mock_db_session):
        """Test retrieving policies by matter ID."""
        matter_id = uuid.uuid4()
        tenant_id = uuid.uuid4()
        
        # Mock multiple policies
        policies = [
            InsurancePolicyORM(
                id=uuid.uuid4(),
                matter_id=matter_id,
                tenant_id=tenant_id,
                policy_number=f"TEST-{i}",
            )
            for i in range(3)
        ]

        # Mock the result structure
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = policies
        mock_db_session.execute.return_value = mock_result

        result = await insurance_service.get_policies_by_matter(matter_id, tenant_id)

        # Should return list of policies
        assert result is not None
        mock_db_session.execute.assert_called_once()

    async def test_search_insurers_by_name_success(self, insurance_service, mock_db_session):
        """Test searching insurers by name."""
        search_term = "State Farm"
        
        # Mock search results
        insurers = [
            InsurerORM(
                id=uuid.uuid4(),
                name="State Farm Mutual Insurance",
                naic_code="25178",
            ),
            InsurerORM(
                id=uuid.uuid4(),
                name="State Farm Fire & Casualty",
                naic_code="25143",
            ),
        ]

        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = insurers
        mock_db_session.execute.return_value = mock_result

        result = await insurance_service.search_insurers_by_name(search_term)

        # Should return search results
        assert result is not None
        mock_db_session.execute.assert_called_once()

    async def test_database_error_handling(self, insurance_service, mock_db_session):
        """Test handling of database errors."""
        mock_db_session.commit.side_effect = Exception("Database connection failed")

        insurer_data = InsurerCreate(
            name="Test Insurance",
            naic_code="12345",
        )

        with pytest.raises(Exception):
            await insurance_service.create_insurer(insurer_data)

        # Verify rollback was attempted
        mock_db_session.rollback.assert_called()