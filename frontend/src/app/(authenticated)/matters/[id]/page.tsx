'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Calendar, 
  CheckSquare, 
  Stethoscope,
  User,
  Building,
  Phone,
  Mail,
  MapPin,
  Briefcase
} from 'lucide-react'
import { useMattersApi } from '@/hooks/useMattersApi'
import type { Matter } from '@/types/domain/tenants/Matter'
import { getMatterDisplayLabel, getPracticeAreaDisplayName } from '@/types/domain/tenants/Matter'
import MedicalTab from '@/components/matters/medical-tab'

export default function MatterDetailPage() {
  const params = useParams()
  const matterId = params.id as string
  const [matter, setMatter] = useState<Matter | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const { getMatterById } = useMattersApi()

  useEffect(() => {
    const loadMatter = async () => {
      try {
        setLoading(true)
        const matterData = await getMatterById(matterId)
        setMatter(matterData)
      } catch (error) {
        console.error('Failed to load matter:', error)
      } finally {
        setLoading(false)
      }
    }

    if (matterId) {
      loadMatter()
    }
  }, [matterId, getMatterById])

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-64" />
          <div className="h-4 bg-gray-200 rounded w-96" />
          <div className="h-12 bg-gray-200 rounded" />
          <div className="h-64 bg-gray-200 rounded" />
        </div>
      </div>
    )
  }

  if (!matter) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <Briefcase className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Matter not found</h3>
          <p className="text-gray-500">The requested matter could not be found.</p>
        </div>
      </div>
    )
  }

  const matterLabel = getMatterDisplayLabel(matter.workType)

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{matter.title}</h1>
            <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
              {matter.caseNumber && (
                <span>Case #: {matter.caseNumber}</span>
              )}
              <span>•</span>
              <span>{getPracticeAreaDisplayName(matter.practiceArea)}</span>
              <span>•</span>
              <span>{matterLabel}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="capitalize">
              {matter.status.replace('_', ' ')}
            </Badge>
            {matter.priorityLevel && (
              <Badge 
                variant={
                  matter.priorityLevel === 'urgent' ? 'destructive' :
                  matter.priorityLevel === 'high' ? 'secondary' : 'outline'
                }
              >
                {matter.priorityLevel}
              </Badge>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Client{matter.clients && matter.clients.length > 1 ? 's' : ''}</p>
                  <p className="text-lg font-bold">
                    {matter.clients ? matter.clients.length : 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Documents</p>
                  <p className="text-lg font-bold">-</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <Stethoscope className="h-4 w-4 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Medical Records</p>
                  <p className="text-lg font-bold">-</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Deadlines</p>
                  <p className="text-lg font-bold">-</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Tabbed Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="medical" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Medical Records
          </TabsTrigger>
          <TabsTrigger value="deadlines" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Deadlines
          </TabsTrigger>
          <TabsTrigger value="tasks" className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4" />
            Tasks
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Matter Details */}
            <Card>
              <CardHeader>
                <CardTitle>Matter Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-500">Filing Date</p>
                    <p>{matter.filingDate ? new Date(matter.filingDate).toLocaleDateString() : 'Not set'}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-500">Trial Date</p>
                    <p>{matter.trialDate ? new Date(matter.trialDate).toLocaleDateString() : 'Not set'}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-500">Statute of Limitations</p>
                    <p>{matter.statuteOfLimitations ? new Date(matter.statuteOfLimitations).toLocaleDateString() : 'Not set'}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-500">Settlement Authority</p>
                    <p>{matter.settlementAuthority ? `$${matter.settlementAuthority.toLocaleString()}` : 'Not set'}</p>
                  </div>
                </div>
                
                {matter.description && (
                  <div>
                    <p className="font-medium text-gray-500 mb-2">Description</p>
                    <p className="text-sm">{matter.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Client Information */}
            <Card>
              <CardHeader>
                <CardTitle>Client Information</CardTitle>
              </CardHeader>
              <CardContent>
                {matter.clients && matter.clients.length > 0 ? (
                  <div className="space-y-4">
                    {matter.clients.map((client, index) => (
                      <div key={index} className="pb-4 border-b last:border-b-0 last:pb-0">
                        <h4 className="font-semibold">{client.fullName}</h4>
                        <div className="mt-2 space-y-1 text-sm text-gray-600">
                          {client.email && (
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              {client.email}
                            </div>
                          )}
                          {client.phone && (
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              {client.phone}
                            </div>
                          )}
                          {client.address && (
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              {client.address}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <User className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                    <p>No clients assigned to this matter</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <FileText className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                  <p>No recent activity</p>
                  <p className="text-sm">Activity will appear here as you work on this matter</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="documents" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <FileText className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <p>No documents uploaded yet</p>
                <Button className="mt-4">Upload Documents</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medical" className="mt-6">
          <MedicalTab matterId={matterId} />
        </TabsContent>

        <TabsContent value="deadlines" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Deadlines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Calendar className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <p>No deadlines set</p>
                <Button className="mt-4">Add Deadline</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <CheckSquare className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <p>No tasks created</p>
                <Button className="mt-4">Create Task</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}