/**
 * @jest-environment jsdom
 */
import { jest } from '@jest/globals'
import { 
  containsPotentialPHI,
  isValidHIPAAAuditEntry,
  validateAndSanitizeString
} from '@/lib/utils/type-guards'
import { 
  HIPAAViolationError,
  AuditLogError,
  TenantIsolationError,
  MedicalErrorFactory
} from '@/types/medical-errors'

// Mock audit logging service
const mockAuditService = {
  logAccess: jest.fn(),
  logPHIAccess: jest.fn(),
  logDataExport: jest.fn(),
  logAuthenticationEvent: jest.fn(),
  validateAuditIntegrity: jest.fn()
}

// Mock encryption service
const mockEncryptionService = {
  encryptPHI: jest.fn(),
  decryptPHI: jest.fn(),
  hashSensitiveData: jest.fn(),
  validateEncryption: jest.fn()
}

// Mock tenant isolation service
const mockTenantService = {
  validateTenantAccess: jest.fn(),
  isolateUserData: jest.fn(),
  checkCrossOriginAccess: jest.fn()
}

jest.mock('@/lib/services/security/audit-service', () => ({ auditService: mockAuditService }))
jest.mock('@/lib/services/security/encryption-service', () => ({ encryptionService: mockEncryptionService }))
jest.mock('@/lib/services/security/tenant-service', () => ({ tenantService: mockTenantService }))

describe('HIPAA Compliance Test Suite', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Set up default mock behaviors
    mockAuditService.logAccess.mockResolvedValue({ success: true, auditId: 'audit-123' })
    mockAuditService.logPHIAccess.mockResolvedValue({ success: true, auditId: 'phi-audit-456' })
    mockEncryptionService.encryptPHI.mockResolvedValue('encrypted-data-hash')
    mockTenantService.validateTenantAccess.mockResolvedValue({ valid: true })
  })

  describe('PHI Detection and Protection', () => {
    describe('PHI Pattern Recognition', () => {
      it('should detect Social Security Numbers', () => {
        const textWithSSN = 'Patient SSN: ***********'
        expect(containsPotentialPHI(textWithSSN)).toBe(true)
        
        const textWithoutSSN = 'Patient has hypertension'
        expect(containsPotentialPHI(textWithoutSSN)).toBe(false)
      })

      it('should detect phone numbers', () => {
        const textWithPhone = 'Contact: ************'
        expect(containsPotentialPHI(textWithPhone)).toBe(true)
        
        const textWithoutPhone = 'Blood pressure: 120/80'
        expect(containsPotentialPHI(textWithoutPhone)).toBe(false)
      })

      it('should detect email addresses', () => {
        const textWithEmail = 'Patient email: <EMAIL>'
        expect(containsPotentialPHI(textWithEmail)).toBe(true)
        
        const textWithoutEmail = 'Patient diagnosed with diabetes'
        expect(containsPotentialPHI(textWithoutEmail)).toBe(false)
      })

      it('should detect potential names with titles', () => {
        const textWithName = 'Dr. Smith examined the patient'
        expect(containsPotentialPHI(textWithName)).toBe(true)
        
        const textWithPatientName = 'Patient Johnson was seen today'
        expect(containsPotentialPHI(textWithPatientName)).toBe(true)
        
        const textGeneric = 'The patient was examined'
        expect(containsPotentialPHI(textGeneric)).toBe(false)
      })

      it('should detect date patterns', () => {
        const textWithDate = 'Date of birth: 01/15/1985'
        expect(containsPotentialPHI(textWithDate)).toBe(true)
        
        const textWithoutSpecificDate = 'Patient seen in January'
        expect(containsPotentialPHI(textWithoutSpecificDate)).toBe(false)
      })

      it('should handle multiple PHI elements', () => {
        const textMultiplePHI = 'Patient: John Doe, DOB: 01/15/1985, Phone: ************'
        expect(containsPotentialPHI(textMultiplePHI)).toBe(true)
      })

      it('should handle edge cases and false positives', () => {
        const medicalTerms = 'Patient has T2DM, HTN, and COPD with FEV1 of 2.5'
        expect(containsPotentialPHI(medicalTerms)).toBe(false)
        
        const numbersNotPHI = 'Dosage: 500mg twice daily for 10 days'
        expect(containsPotentialPHI(numbersNotPHI)).toBe(false)
      })
    })

    describe('Data Sanitization', () => {
      it('should sanitize input strings', () => {
        const validInput = 'Patient has diabetes type 2'
        const sanitized = validateAndSanitizeString(validInput)
        expect(sanitized).toBe(validInput)
      })

      it('should reject malicious input', () => {
        const maliciousInput = '<script>alert("xss")</script>'
        const sanitized = validateAndSanitizeString(maliciousInput)
        expect(sanitized).toBe(null)
      })

      it('should enforce length limits', () => {
        const tooLongInput = 'a'.repeat(2000)
        const sanitized = validateAndSanitizeString(tooLongInput, 100)
        expect(sanitized).toBe(null)
      })

      it('should handle empty and whitespace input', () => {
        expect(validateAndSanitizeString('')).toBe(null)
        expect(validateAndSanitizeString('   ')).toBe(null)
        expect(validateAndSanitizeString(null)).toBe(null)
        expect(validateAndSanitizeString(undefined)).toBe(null)
      })

      it('should allow medical terminology', () => {
        const medicalText = 'Patient diagnosed with chronic obstructive pulmonary disease (COPD)'
        const sanitized = validateAndSanitizeString(medicalText)
        expect(sanitized).toBe(medicalText)
      })
    })

    describe('Data Encryption Requirements', () => {
      it('should encrypt PHI data before storage', async () => {
        const phiData = 'Patient John Doe, SSN: ***********'
        
        // Simulate encryption call
        mockEncryptionService.encryptPHI.mockResolvedValue('encrypted-hash-abc123')
        
        const encrypted = await mockEncryptionService.encryptPHI(phiData)
        
        expect(mockEncryptionService.encryptPHI).toHaveBeenCalledWith(phiData)
        expect(encrypted).toBe('encrypted-hash-abc123')
        expect(encrypted).not.toContain('John Doe')
        expect(encrypted).not.toContain('***********')
      })

      it('should validate encryption integrity', async () => {
        const encryptedData = 'encrypted-hash-abc123'
        
        mockEncryptionService.validateEncryption.mockResolvedValue({
          valid: true,
          algorithm: 'AES-256-GCM',
          keyId: 'key-id-123'
        })
        
        const validation = await mockEncryptionService.validateEncryption(encryptedData)
        
        expect(validation.valid).toBe(true)
        expect(validation.algorithm).toBe('AES-256-GCM')
      })

      it('should handle encryption failures', async () => {
        const phiData = 'sensitive-data'
        
        mockEncryptionService.encryptPHI.mockRejectedValue(new Error('Encryption failed'))
        
        await expect(mockEncryptionService.encryptPHI(phiData)).rejects.toThrow('Encryption failed')
      })
    })
  })

  describe('Audit Logging Requirements', () => {
    describe('Access Logging', () => {
      it('should log all PHI access events', async () => {
        const accessEvent = {
          userId: 'user-123',
          tenantId: 'tenant-456',
          action: 'read_medical_record',
          resourceId: 'medical-record-789',
          phiElements: ['patient_name', 'diagnosis'],
          timestamp: Date.now(),
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0'
        }
        
        await mockAuditService.logPHIAccess(accessEvent)
        
        expect(mockAuditService.logPHIAccess).toHaveBeenCalledWith(accessEvent)
      })

      it('should log data export events', async () => {
        const exportEvent = {
          userId: 'user-123',
          tenantId: 'tenant-456',
          action: 'export_timeline',
          exportFormat: 'csv',
          recordCount: 25,
          phiIncluded: true,
          timestamp: Date.now()
        }
        
        await mockAuditService.logDataExport(exportEvent)
        
        expect(mockAuditService.logDataExport).toHaveBeenCalledWith(exportEvent)
      })

      it('should log authentication events', async () => {
        const authEvent = {
          userId: 'user-123',
          action: 'login',
          success: true,
          timestamp: Date.now(),
          ipAddress: '***********',
          method: 'oauth'
        }
        
        await mockAuditService.logAuthenticationEvent(authEvent)
        
        expect(mockAuditService.logAuthenticationEvent).toHaveBeenCalledWith(authEvent)
      })

      it('should handle audit logging failures', async () => {
        const accessEvent = { userId: 'user-123', action: 'read' }
        
        mockAuditService.logAccess.mockRejectedValue(new AuditLogError(
          'Audit logging failed',
          'user-123',
          'read'
        ))
        
        await expect(mockAuditService.logAccess(accessEvent)).rejects.toThrow(AuditLogError)
      })
    })

    describe('Audit Trail Integrity', () => {
      it('should validate audit entry structure', () => {
        const validEntry = {
          userId: 'user-123',
          tenantId: 'tenant-456',
          action: 'read_medical_data',
          timestamp: Date.now(),
          phiElements: ['patient_name']
        }
        
        expect(isValidHIPAAAuditEntry(validEntry)).toBe(true)
      })

      it('should reject invalid audit entries', () => {
        const invalidEntries = [
          {}, // missing required fields
          { userId: 'user-123' }, // missing other required fields
          { userId: 123, tenantId: 'tenant' }, // wrong types
          { 
            userId: 'user-123',
            tenantId: 'tenant-456',
            action: 'read',
            timestamp: 'invalid-timestamp', // wrong type
            phiElements: 'not-an-array' // wrong type
          }
        ]
        
        invalidEntries.forEach(entry => {
          expect(isValidHIPaAAuditEntry(entry)).toBe(false)
        })
      })

      it('should maintain audit log immutability', async () => {
        const auditId = 'audit-123'
        
        mockAuditService.validateAuditIntegrity.mockResolvedValue({
          valid: true,
          tamperEvident: true,
          lastModified: null
        })
        
        const integrity = await mockAuditService.validateAuditIntegrity(auditId)
        
        expect(integrity.valid).toBe(true)
        expect(integrity.tamperEvident).toBe(true)
        expect(integrity.lastModified).toBe(null)
      })

      it('should detect audit tampering', async () => {
        const auditId = 'audit-456'
        
        mockAuditService.validateAuditIntegrity.mockResolvedValue({
          valid: false,
          tamperEvident: false,
          lastModified: Date.now(),
          suspiciousActivity: true
        })
        
        const integrity = await mockAuditService.validateAuditIntegrity(auditId)
        
        expect(integrity.valid).toBe(false)
        expect(integrity.suspiciousActivity).toBe(true)
      })
    })
  })

  describe('Tenant Isolation and Access Controls', () => {
    describe('Multi-Tenant Data Isolation', () => {
      it('should enforce tenant boundaries', async () => {
        const accessRequest = {
          userId: 'user-123',
          tenantId: 'tenant-a',
          resourceId: 'medical-record-789',
          action: 'read'
        }
        
        mockTenantService.validateTenantAccess.mockResolvedValue({
          valid: true,
          tenantId: 'tenant-a'
        })
        
        const validation = await mockTenantService.validateTenantAccess(accessRequest)
        
        expect(validation.valid).toBe(true)
        expect(validation.tenantId).toBe('tenant-a')
      })

      it('should prevent cross-tenant access', async () => {
        const invalidAccessRequest = {
          userId: 'user-123',
          tenantId: 'tenant-a',
          resourceId: 'medical-record-from-tenant-b', // Different tenant resource
          action: 'read'
        }
        
        mockTenantService.validateTenantAccess.mockRejectedValue(
          new TenantIsolationError(
            'Cross-tenant access denied',
            'tenant-a',
            'medical-record-from-tenant-b'
          )
        )
        
        await expect(mockTenantService.validateTenantAccess(invalidAccessRequest))
          .rejects.toThrow(TenantIsolationError)
      })

      it('should isolate user data by tenant', async () => {
        const userData = {
          userId: 'user-123',
          tenantId: 'tenant-a',
          medicalRecords: ['record-1', 'record-2']
        }
        
        mockTenantService.isolateUserData.mockResolvedValue({
          isolatedData: userData,
          tenantValidated: true
        })
        
        const isolated = await mockTenantService.isolateUserData(userData)
        
        expect(isolated.tenantValidated).toBe(true)
        expect(isolated.isolatedData.tenantId).toBe('tenant-a')
      })

      it('should detect cross-origin access attempts', async () => {
        const suspiciousRequest = {
          userId: 'user-123',
          origin: 'https://malicious-site.com',
          targetTenant: 'tenant-a'
        }
        
        mockTenantService.checkCrossOriginAccess.mockResolvedValue({
          allowed: false,
          reason: 'Origin not in whitelist',
          suspiciousActivity: true
        })
        
        const originCheck = await mockTenantService.checkCrossOriginAccess(suspiciousRequest)
        
        expect(originCheck.allowed).toBe(false)
        expect(originCheck.suspiciousActivity).toBe(true)
      })
    })

    describe('Role-Based Access Control', () => {
      it('should enforce minimum necessary access', async () => {
        const accessControl = {
          userRole: 'paralegal',
          requestedAction: 'read_medical_summary',
          resourceType: 'medical_timeline'
        }
        
        // Mock RBAC service
        const mockRBACService = {
          checkPermission: jest.fn().mockResolvedValue({
            allowed: true,
            permissions: ['read_medical_summary'],
            restrictions: ['no_phi_export']
          })
        }
        
        const permission = await mockRBACService.checkPermission(accessControl)
        
        expect(permission.allowed).toBe(true)
        expect(permission.restrictions).toContain('no_phi_export')
      })

      it('should deny access for insufficient permissions', async () => {
        const accessControl = {
          userRole: 'staff',
          requestedAction: 'export_full_medical_records',
          resourceType: 'medical_records'
        }
        
        const mockRBACService = {
          checkPermission: jest.fn().mockResolvedValue({
            allowed: false,
            reason: 'Insufficient privileges for PHI export',
            requiredRole: 'attorney'
          })
        }
        
        const permission = await mockRBACService.checkPermission(accessControl)
        
        expect(permission.allowed).toBe(false)
        expect(permission.requiredRole).toBe('attorney')
      })
    })
  })

  describe('Data Minimization and Retention', () => {
    describe('Zero Data Retention Policy', () => {
      it('should enforce zero retention for AI processing', async () => {
        const aiProcessingRequest = {
          data: 'Patient medical text for analysis',
          modelId: 'gemini-2.5-flash',
          zeroRetention: true
        }
        
        const mockAIService = {
          processWithZeroRetention: jest.fn().mockResolvedValue({
            result: 'Analysis complete',
            dataRetained: false,
            processingComplete: true,
            deletionConfirmed: true
          })
        }
        
        const result = await mockAIService.processWithZeroRetention(aiProcessingRequest)
        
        expect(result.dataRetained).toBe(false)
        expect(result.deletionConfirmed).toBe(true)
      })

      it('should validate data deletion', async () => {
        const deletionRequest = {
          sessionId: 'ai-session-123',
          dataTypes: ['chat_messages', 'processing_context'],
          timestamp: Date.now()
        }
        
        const mockDeletionService = {
          validateDeletion: jest.fn().mockResolvedValue({
            deleted: true,
            remainingData: 0,
            deletionTimestamp: Date.now(),
            auditTrail: 'deletion-audit-456'
          })
        }
        
        const deletion = await mockDeletionService.validateDeletion(deletionRequest)
        
        expect(deletion.deleted).toBe(true)
        expect(deletion.remainingData).toBe(0)
      })
    })

    describe('Data Minimization', () => {
      it('should minimize data exposure in API responses', () => {
        const fullMedicalRecord = {
          id: 'record-123',
          patientName: 'John Doe', // PHI
          ssn: '***********', // PHI
          diagnosis: 'Type 2 Diabetes', // Medical data
          medications: ['Metformin'], // Medical data
          lastVisit: '2023-01-15', // Medical data
          internalNotes: 'Staff notes', // Internal only
          billingInfo: 'Insurance data' // Not relevant for medical analysis
        }
        
        const minimizedData = {
          id: 'record-123',
          diagnosis: 'Type 2 Diabetes',
          medications: ['Metformin'],
          lastVisit: '2023-01-15'
          // PHI and irrelevant data excluded
        }
        
        const mockDataMinimizer = {
          minimizeForMedicalAnalysis: jest.fn().mockReturnValue(minimizedData)
        }
        
        const result = mockDataMinimizer.minimizeForMedicalAnalysis(fullMedicalRecord)
        
        expect(result).not.toHaveProperty('patientName')
        expect(result).not.toHaveProperty('ssn')
        expect(result).not.toHaveProperty('internalNotes')
        expect(result).toHaveProperty('diagnosis')
        expect(result).toHaveProperty('medications')
      })
    })
  })

  describe('Session Security and Timeouts', () => {
    describe('Session Management', () => {
      it('should enforce session timeouts', async () => {
        const session = {
          id: 'session-123',
          userId: 'user-456',
          createdAt: Date.now() - (31 * 60 * 1000), // 31 minutes ago
          lastActivity: Date.now() - (31 * 60 * 1000),
          timeoutMinutes: 30
        }
        
        const mockSessionService = {
          validateSession: jest.fn().mockResolvedValue({
            valid: false,
            reason: 'Session expired',
            expired: true
          })
        }
        
        const validation = await mockSessionService.validateSession(session)
        
        expect(validation.valid).toBe(false)
        expect(validation.expired).toBe(true)
      })

      it('should maintain active sessions', async () => {
        const session = {
          id: 'session-123',
          userId: 'user-456',
          createdAt: Date.now() - (15 * 60 * 1000), // 15 minutes ago
          lastActivity: Date.now() - (5 * 60 * 1000), // 5 minutes ago
          timeoutMinutes: 30
        }
        
        const mockSessionService = {
          validateSession: jest.fn().mockResolvedValue({
            valid: true,
            remainingTime: 25 * 60 * 1000, // 25 minutes remaining
            refreshed: true
          })
        }
        
        const validation = await mockSessionService.validateSession(session)
        
        expect(validation.valid).toBe(true)
        expect(validation.remainingTime).toBeGreaterThan(0)
      })

      it('should handle concurrent session access', async () => {
        const sessionId = 'session-123'
        
        const mockSessionService = {
          handleConcurrentAccess: jest.fn().mockResolvedValue({
            allowed: false,
            reason: 'Session already active elsewhere',
            securityAlert: true
          })
        }
        
        const result = await mockSessionService.handleConcurrentAccess(sessionId)
        
        expect(result.allowed).toBe(false)
        expect(result.securityAlert).toBe(true)
      })
    })
  })

  describe('HIPAA Violation Detection and Response', () => {
    describe('Violation Detection', () => {
      it('should detect unauthorized PHI access', () => {
        const unauthorizedAccess = {
          userId: 'user-123',
          attemptedResource: 'medical-record-456',
          userRole: 'staff',
          requiredRole: 'attorney',
          timestamp: Date.now()
        }
        
        const violation = new HIPAAViolationError(
          'Unauthorized access to PHI',
          'unauthorized_access',
          ['patient_name', 'diagnosis']
        )
        
        expect(violation.code).toBe('HIPAA_VIOLATION_ERROR')
        expect(violation.severity).toBe('critical')
        expect(violation.violationType).toBe('unauthorized_access')
        expect(violation.phiElements).toContain('patient_name')
      })

      it('should detect data exposure incidents', () => {
        const dataExposure = new HIPAAViolationError(
          'PHI exposed in application logs',
          'data_exposure',
          ['patient_ssn', 'phone_number']
        )
        
        expect(dataExposure.violationType).toBe('data_exposure')
        expect(dataExposure.phiElements).toContain('patient_ssn')
        expect(dataExposure.phiElements).toContain('phone_number')
        expect(dataExposure.severity).toBe('critical')
      })

      it('should detect audit logging failures', () => {
        const auditFailure = new AuditLogError(
          'Failed to log PHI access event',
          'user-123',
          'read_medical_record'
        )
        
        expect(auditFailure.code).toBe('AUDIT_LOG_ERROR')
        expect(auditFailure.userId).toBe('user-123')
        expect(auditFailure.action).toBe('read_medical_record')
        expect(auditFailure.violationType).toBe('audit_failure')
      })

      it('should detect encryption failures', () => {
        const encryptionFailure = new HIPAAViolationError(
          'Failed to encrypt PHI before transmission',
          'encryption_failure',
          ['medical_record_data']
        )
        
        expect(encryptionFailure.violationType).toBe('encryption_failure')
        expect(encryptionFailure.severity).toBe('critical')
      })
    })

    describe('Incident Response', () => {
      it('should create proper error reports', () => {
        const violation = new HIPAAViolationError(
          'Unauthorized data access detected',
          'unauthorized_access',
          ['patient_name', 'diagnosis']
        )
        
        const errorReport = violation.toJSON()
        
        expect(errorReport).toEqual(
          expect.objectContaining({
            name: 'HIPAAViolationError',
            code: 'HIPAA_VIOLATION_ERROR',
            severity: 'critical',
            message: 'Unauthorized data access detected',
            timestamp: expect.any(Number),
            reportable: true
          })
        )
      })

      it('should escalate critical violations', async () => {
        const criticalViolation = new HIPAAViolationError(
          'Mass PHI exposure detected',
          'data_exposure',
          ['multiple_patient_records']
        )
        
        const mockIncidentService = {
          escalateViolation: jest.fn().mockResolvedValue({
            escalated: true,
            incidentId: 'incident-789',
            notificationsSent: ['security_team', 'compliance_officer'],
            immediateActions: ['disable_user', 'isolate_system']
          })
        }
        
        const escalation = await mockIncidentService.escalateViolation(criticalViolation)
        
        expect(escalation.escalated).toBe(true)
        expect(escalation.immediateActions).toContain('disable_user')
        expect(escalation.notificationsSent).toContain('security_team')
      })
    })
  })

  describe('Compliance Monitoring and Reporting', () => {
    describe('Real-time Monitoring', () => {
      it('should monitor access patterns', async () => {
        const accessPattern = {
          userId: 'user-123',
          accessTimes: [
            Date.now() - 1000,
            Date.now() - 500,
            Date.now() - 100
          ],
          resourcesAccessed: 10,
          timeSpan: 1000 // 1 second
        }
        
        const mockMonitoringService = {
          analyzeAccessPattern: jest.fn().mockResolvedValue({
            suspicious: true,
            reason: 'Rapid sequential access to multiple records',
            riskLevel: 'high',
            recommendedAction: 'temporary_lockout'
          })
        }
        
        const analysis = await mockMonitoringService.analyzeAccessPattern(accessPattern)
        
        expect(analysis.suspicious).toBe(true)
        expect(analysis.riskLevel).toBe('high')
      })

      it('should generate compliance reports', async () => {
        const reportRequest = {
          dateRange: {
            start: Date.now() - (30 * 24 * 60 * 60 * 1000), // 30 days ago
            end: Date.now()
          },
          includeMetrics: ['phi_access_count', 'violations_detected', 'audit_completeness']
        }
        
        const mockReportingService = {
          generateComplianceReport: jest.fn().mockResolvedValue({
            phi_access_count: 1250,
            violations_detected: 0,
            audit_completeness: 100,
            encryption_coverage: 100,
            session_timeouts_enforced: 45,
            data_retention_violations: 0
          })
        }
        
        const report = await mockReportingService.generateComplianceReport(reportRequest)
        
        expect(report.violations_detected).toBe(0)
        expect(report.audit_completeness).toBe(100)
        expect(report.encryption_coverage).toBe(100)
      })
    })
  })

  describe('Error Factory Integration', () => {
    it('should create HIPAA errors from generic errors', () => {
      const genericError = new Error('Database access denied')
      const medicalError = MedicalErrorFactory.fromError(genericError, {
        userId: 'user-123',
        attemptedAction: 'read_phi'
      })
      
      expect(medicalError).toBeInstanceOf(Error)
      expect(medicalError.message).toContain('Database access denied')
    })

    it('should preserve HIPAA error details', () => {
      const hipaaError = new HIPAAViolationError(
        'Critical violation',
        'unauthorized_access',
        ['patient_data']
      )
      
      const preservedError = MedicalErrorFactory.fromError(hipaaError)
      
      expect(preservedError).toBe(hipaaError) // Should return original
      expect(preservedError.severity).toBe('critical')
    })
  })
})

// Helper function name fix
function isValidHIPaAAuditEntry(data: unknown): boolean {
  return isValidHIPAAAuditEntry(data)
}