/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'
import MedicalTimelineAgent from '../medical-timeline-agent'
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'
import { getFeatureFlags } from '@/lib/features/ag-ui'
import type { 
  TimelineEvent, 
  TimelineGenerationResult, 
  TimelineStatistics 
} from '../medical-timeline-agent'
import type { MedicationRow, RadiologyRow, ProblemRow } from '@/types/medical-records'

// Mock dependencies
jest.mock('@/lib/services/medical/structured-extraction-service')
jest.mock('@/lib/features/ag-ui')

const mockStructuredExtractionService = structuredExtractionService as jest.Mocked<typeof structuredExtractionService>
const mockGetFeatureFlags = getFeatureFlags as jest.MockedFunction<typeof getFeatureFlags>

// Mock Progress component
jest.mock('@/components/ui/progress', () => {
  return function MockProgress({ value, className }: { value: number; className?: string }) {
    return (
      <div 
        data-testid="timeline-progress" 
        data-value={value}
        className={className}
        role="progressbar"
        aria-valuenow={value}
      >
        Timeline Generation: {value}%
      </div>
    )
  }
})

describe('MedicalTimelineAgent', () => {
  const defaultProps = {
    matterId: 'test-matter-123'
  }

  const mockExtractions = {
    medications: [
      {
        drug: 'Ibuprofen',
        dose: '400mg',
        route: 'Oral',
        frequency: 'TID',
        startDate: '2023-01-15',
        endDate: null,
        evidence: [{
          quote: 'Patient prescribed ibuprofen 400mg TID',
          documentId: 'doc-med-1',
          page: 1,
          confidence: 0.9
        }]
      } as MedicationRow,
      {
        drug: 'Metformin',
        dose: '500mg',
        route: 'Oral',
        frequency: 'BID',
        startDate: '2023-02-10',
        endDate: null,
        evidence: [{
          quote: 'Continue metformin 500mg BID',
          documentId: 'doc-med-2',
          page: 2,
          confidence: 0.85
        }]
      } as MedicationRow
    ],
    radiology: [
      {
        study: 'Chest X-Ray',
        date: '2023-03-01',
        bodyPart: 'Chest',
        finding: 'Normal cardiac silhouette',
        impression: 'No acute findings',
        evidence: [{
          quote: 'Chest X-ray shows normal cardiac silhouette',
          documentId: 'doc-rad-1',
          page: 1,
          confidence: 0.95
        }]
      } as RadiologyRow,
      {
        study: 'CT Abdomen',
        date: '2023-03-15',
        bodyPart: 'Abdomen',
        finding: 'Mild hepatic steatosis',
        impression: 'Fatty liver changes',
        evidence: [{
          quote: 'CT abdomen reveals mild hepatic steatosis',
          documentId: 'doc-rad-2',
          page: 3,
          confidence: 0.88
        }]
      } as RadiologyRow
    ],
    problems: [
      {
        term: 'Type 2 Diabetes Mellitus',
        evidence: [{
          quote: 'Patient has well-controlled Type 2 diabetes',
          documentId: 'doc-prob-1',
          page: 1,
          confidence: 0.92
        }]
      } as ProblemRow,
      {
        term: 'Hypertension',
        evidence: [{
          quote: 'Hypertension managed with ACE inhibitor',
          documentId: 'doc-prob-2',
          page: 2,
          confidence: 0.87
        }]
      } as ProblemRow
    ],
    followups: [
      {
        cue: 'Follow up in 3 months',
        target: 'Endocrinology',
        dueDateGuess: '2023-06-15',
        evidence: [{
          quote: 'Schedule follow-up with endocrinology in 3 months',
          documentId: 'doc-fu-1',
          page: 1,
          confidence: 0.8
        }]
      }
    ]
  }

  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
    mockGetFeatureFlags.mockReturnValue({
      aguiEnabled: true,
      aguiVersion: '2.0.0'
    })
    mockStructuredExtractionService.getExtractionsForMatter.mockResolvedValue(mockExtractions)
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Feature Flag Integration', () => {
    it('should render disabled state when AG-UI is disabled', () => {
      mockGetFeatureFlags.mockReturnValue({
        aguiEnabled: false,
        aguiVersion: '2.0.0'
      })

      render(<MedicalTimelineAgent {...defaultProps} />)

      expect(screen.getByText(/Timeline generation requires AG-UI features/)).toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /Generate Timeline/ })).not.toBeInTheDocument()
    })

    it('should render enabled state when AG-UI is enabled', () => {
      render(<MedicalTimelineAgent {...defaultProps} />)

      expect(screen.getByText('AI-Generated Medical Timeline')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Generate Timeline/ })).toBeInTheDocument()
      expect(screen.getByText('AI Powered')).toBeInTheDocument()
    })
  })

  describe('Timeline Generation', () => {
    it('should start timeline generation when button is clicked', async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)

      const generateButton = screen.getByRole('button', { name: /Generate Timeline/ })
      fireEvent.click(generateButton)

      expect(screen.getByRole('button', { name: /Generating.../ })).toBeInTheDocument()
      expect(screen.getByText('Loading medical data...')).toBeInTheDocument()

      await waitFor(() => {
        expect(screen.getByTestId('timeline-progress')).toBeInTheDocument()
      })
    })

    it('should show progress through generation steps', async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      const expectedSteps = [
        'Loading medical data...',
        'Analyzing medical extractions...',
        'Processing medications chronologically...',
        'Processing radiology studies...',
        'Processing diagnoses and problems...',
        'Generating contextual medical events...',
        'Timeline generation complete!'
      ]

      for (const step of expectedSteps) {
        await waitFor(() => {
          expect(screen.getByText(step)).toBeInTheDocument()
        }, { timeout: 2000 })
        
        jest.advanceTimersByTime(100) // Allow step to process
      }
    })

    it('should call structured extraction service during generation', async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        expect(mockStructuredExtractionService.getExtractionsForMatter).toHaveBeenCalledWith('test-matter-123')
      })
    })

    it('should generate timeline events from medical data', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        expect(mockOnTimelineGenerated).toHaveBeenCalledWith(
          expect.objectContaining({
            events: expect.arrayContaining([
              expect.objectContaining({
                type: 'medication',
                title: 'Started Ibuprofen'
              }),
              expect.objectContaining({
                type: 'test',
                title: 'Chest X-Ray'
              }),
              expect.objectContaining({
                type: 'diagnosis',
                title: 'Diagnosis: Type 2 Diabetes Mellitus'
              })
            ]),
            statistics: expect.objectContaining({
              totalEvents: expect.any(Number),
              eventsByType: expect.any(Object),
              averageConfidence: expect.any(Number),
              processingTime: expect.any(Number)
            }),
            errors: expect.any(Array)
          })
        )
      }, { timeout: 5000 })
    })

    it('should handle generation errors gracefully', async () => {
      const mockOnError = jest.fn()
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()
      
      mockStructuredExtractionService.getExtractionsForMatter.mockRejectedValue(
        new Error('Extraction service failed')
      )

      render(<MedicalTimelineAgent {...defaultProps} onError={mockOnError} />)

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        expect(screen.getByText('Generation failed')).toBeInTheDocument()
        expect(consoleErrorSpy).toHaveBeenCalledWith('Timeline generation failed:', expect.any(Error))
        expect(mockOnError).toHaveBeenCalledWith(expect.any(Error))
      })

      consoleErrorSpy.mockRestore()
    })
  })

  describe('Timeline Event Processing', () => {
    it('should create medication events with proper typing', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const medicationEvents = result.events.filter(e => e.type === 'medication')
          
          expect(medicationEvents).toHaveLength(2)
          expect(medicationEvents[0]).toEqual(
            expect.objectContaining({
              title: 'Started Ibuprofen',
              description: expect.stringContaining('Dose: 400mg'),
              type: 'medication',
              severity: 'moderate',
              source: expect.objectContaining({
                documentId: 'doc-med-1',
                page: 1
              }),
              metadata: expect.objectContaining({
                originalType: 'medication'
              })
            })
          )
        }
      })
    })

    it('should create radiology events with proper severity detection', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const radiologyEvents = result.events.filter(e => e.type === 'test')
          
          expect(radiologyEvents).toHaveLength(2)
          
          // Normal finding should have moderate severity
          const chestXray = radiologyEvents.find(e => e.title === 'Chest X-Ray')
          expect(chestXray?.severity).toBe('moderate')
          
          // Check description formatting
          expect(chestXray?.description).toContain('Body part: Chest')
          expect(chestXray?.description).toContain('Finding: Normal cardiac silhouette')
        }
      })
    })

    it('should create problem events with severity assessment', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const problemEvents = result.events.filter(e => e.type === 'diagnosis')
          
          expect(problemEvents).toHaveLength(2)
          
          // Diabetes should be high severity due to "chronic" nature
          const diabetes = problemEvents.find(e => e.title.includes('Diabetes'))
          expect(diabetes?.severity).toBe('high')
          
          // Hypertension should be high severity
          const hypertension = problemEvents.find(e => e.title.includes('Hypertension'))
          expect(hypertension?.severity).toBe('high')
        }
      })
    })

    it('should calculate confidence scores correctly', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const events = result.events
          
          // All events should have confidence scores
          events.forEach(event => {
            expect(event.confidence).toBeGreaterThanOrEqual(0.5)
            expect(event.confidence).toBeLessThanOrEqual(0.95)
          })
          
          // Events with good evidence should have higher confidence
          const highConfidenceEvent = events.find(e => 
            e.source.quote.includes('Chest X-ray shows normal')
          )
          expect(highConfidenceEvent?.confidence).toBeGreaterThan(0.85)
        }
      })
    })

    it('should generate synthetic contextual events', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const syntheticEvents = result.events.filter(e => 
            e.source.documentId === 'synthetic'
          )
          
          expect(syntheticEvents.length).toBeGreaterThan(0)
          expect(syntheticEvents).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                type: 'visit',
                title: 'Initial Medical Consultation'
              }),
              expect.objectContaining({
                type: 'admission',
                title: 'Hospital Admission'
              })
            ])
          )
        }
      })
    })

    it('should sort events chronologically', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const events = result.events
          
          // Check chronological ordering
          for (let i = 1; i < events.length; i++) {
            const prevDate = new Date(events[i - 1].date)
            const currDate = new Date(events[i].date)
            expect(prevDate.getTime()).toBeLessThanOrEqual(currDate.getTime())
          }
        }
      })
    })
  })

  describe('Timeline Filtering', () => {
    beforeEach(async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)
      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))
      
      await waitFor(() => {
        expect(screen.getByText('Timeline Filters')).toBeInTheDocument()
      })
    })

    it('should display filter controls after generation', async () => {
      expect(screen.getByText('Event Types')).toBeInTheDocument()
      expect(screen.getByText('Severity')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Verified Only/ })).toBeInTheDocument()
    })

    it('should filter by event type', async () => {
      // Click medication filter
      fireEvent.click(screen.getByRole('button', { name: /Medication/ }))

      await waitFor(() => {
        // Should only show medication events
        const medicationEvents = screen.getAllByText(/Started/)
        expect(medicationEvents.length).toBeGreaterThan(0)
        
        // Should not show radiology events
        expect(screen.queryByText('Chest X-Ray')).not.toBeInTheDocument()
      })
    })

    it('should filter by severity', async () => {
      // Click high severity filter
      fireEvent.click(screen.getByRole('button', { name: /High/ }))

      await waitFor(() => {
        // Should show high severity events
        const highSeverityBadges = screen.getAllByText('High')
        expect(highSeverityBadges.length).toBeGreaterThan(0)
      })
    })

    it('should filter by verification status', async () => {
      fireEvent.click(screen.getByRole('button', { name: /Verified Only/ }))

      await waitFor(() => {
        // Should only show verified events
        const verifiedBadges = screen.queryAllByText(/Verified/)
        // Some events should be verified (based on random generation)
        expect(screen.getByText(/of \d+ events/)).toBeInTheDocument()
      })
    })

    it('should clear all filters', async () => {
      // Apply some filters
      fireEvent.click(screen.getByRole('button', { name: /Medication/ }))
      fireEvent.click(screen.getByRole('button', { name: /High/ }))

      // Clear filters
      fireEvent.click(screen.getByRole('button', { name: /Clear Filters/ }))

      await waitFor(() => {
        // Should show all events again
        expect(screen.getByText(/of \d+ events/)).toBeInTheDocument()
      })
    })

    it('should show no results message when filters exclude all events', async () => {
      // Apply very restrictive filters
      fireEvent.click(screen.getByRole('button', { name: /Verified Only/ }))
      fireEvent.click(screen.getByRole('button', { name: /Critical/ }))

      await waitFor(() => {
        if (screen.queryByText(/No events match your current filters/)) {
          expect(screen.getByText(/No events match your current filters/)).toBeInTheDocument()
          expect(screen.getByRole('button', { name: /Clear Filters/ })).toBeInTheDocument()
        }
      })
    })
  })

  describe('Timeline Display', () => {
    beforeEach(async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)
      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))
      
      await waitFor(() => {
        expect(screen.getByText('Timeline generation complete!')).toBeInTheDocument()
      })
    })

    it('should display timeline events in cards', async () => {
      await waitFor(() => {
        const eventCards = screen.getAllByText(/Started|Chest X-Ray|Diagnosis:/)
        expect(eventCards.length).toBeGreaterThan(0)
      })
    })

    it('should show event details on expansion', async () => {
      const expandButton = screen.getAllByLabelText(/expand/i)[0] || 
                          screen.getAllByRole('button').find(btn => 
                            btn.querySelector('svg') && btn.getAttribute('aria-label')?.includes('expand')
                          )
      
      if (expandButton) {
        fireEvent.click(expandButton)

        await waitFor(() => {
          expect(screen.getByText(/Source:/)).toBeInTheDocument()
          expect(screen.getByText(/Quote:/)).toBeInTheDocument()
          expect(screen.getByText(/AI Confidence:/)).toBeInTheDocument()
        })
      }
    })

    it('should display confidence badges', async () => {
      await waitFor(() => {
        const confidenceBadges = screen.getAllByText(/% confident/)
        expect(confidenceBadges.length).toBeGreaterThan(0)
      })
    })

    it('should display AI generated badges', async () => {
      await waitFor(() => {
        const aiBadges = screen.getAllByText(/AI Generated/)
        expect(aiBadges.length).toBeGreaterThan(0)
      })
    })

    it('should show timeline connector lines', async () => {
      // Check for timeline visual elements (this would depend on CSS classes)
      const timelineElements = screen.getByRole('main') || screen.getByTestId('timeline-container')
      expect(timelineElements).toBeInTheDocument()
    })
  })

  describe('Export Functionality', () => {
    beforeEach(async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)
      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Export CSV/ })).toBeInTheDocument()
      })
    })

    it('should show export button after generation', () => {
      expect(screen.getByRole('button', { name: /Export CSV/ })).toBeInTheDocument()
    })

    it('should create CSV download on export', () => {
      // Mock document.createElement and URL.createObjectURL
      const mockAnchor = {
        href: '',
        download: '',
        click: jest.fn()
      }
      const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as any)
      const createObjectURLSpy = jest.spyOn(URL, 'createObjectURL').mockReturnValue('blob:mock-url')
      const revokeObjectURLSpy = jest.spyOn(URL, 'revokeObjectURL').mockImplementation()

      fireEvent.click(screen.getByRole('button', { name: /Export CSV/ }))

      expect(createElementSpy).toHaveBeenCalledWith('a')
      expect(createObjectURLSpy).toHaveBeenCalled()
      expect(mockAnchor.download).toBe(`medical-timeline-${defaultProps.matterId}.csv`)
      expect(mockAnchor.click).toHaveBeenCalled()
      expect(revokeObjectURLSpy).toHaveBeenCalled()

      createElementSpy.mockRestore()
      createObjectURLSpy.mockRestore()
      revokeObjectURLSpy.mockRestore()
    })
  })

  describe('Statistics Generation', () => {
    it('should generate accurate statistics', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const stats: TimelineStatistics = result.statistics
          
          expect(stats.totalEvents).toBeGreaterThan(0)
          expect(stats.eventsByType).toHaveProperty('medication')
          expect(stats.eventsByType).toHaveProperty('test')
          expect(stats.eventsByType).toHaveProperty('diagnosis')
          expect(stats.averageConfidence).toBeGreaterThan(0)
          expect(stats.averageConfidence).toBeLessThanOrEqual(1)
          expect(stats.processingTime).toBeGreaterThan(0)
          expect(stats.verifiedEvents).toBeGreaterThanOrEqual(0)
        }
      })
    })
  })

  describe('Helper Functions', () => {
    it('should create proper medication descriptions', () => {
      // These are internal helper functions, but we can test them indirectly
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          const medicationEvent = result.events.find(e => e.type === 'medication')
          
          expect(medicationEvent?.description).toContain('Dose:')
          expect(medicationEvent?.description).toContain('Route:')
          expect(medicationEvent?.description).toContain('Frequency:')
        }
      })
    })

    it('should determine correct severity levels', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          
          // Check that severe conditions get high severity
          const diabetesEvent = result.events.find(e => 
            e.title.includes('Diabetes') || e.title.includes('diabetes')
          )
          if (diabetesEvent) {
            expect(diabetesEvent.severity).toBe('high')
          }
          
          // Check that normal findings get moderate severity
          const normalEvent = result.events.find(e => 
            e.description?.includes('Normal') || e.description?.includes('normal')
          )
          if (normalEvent) {
            expect(['moderate', 'low']).toContain(normalEvent.severity)
          }
        }
      })
    })

    it('should validate timeline events correctly', async () => {
      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const call = mockOnTimelineGenerated.mock.calls[0]
        if (call) {
          const result: TimelineGenerationResult = call[0]
          
          // All events should pass validation
          result.events.forEach(event => {
            expect(event.id).toBeTruthy()
            expect(event.date).toBeTruthy()
            expect(event.title).toBeTruthy()
            expect(event.type).toBeTruthy()
            expect(event.severity).toBeTruthy()
            expect(typeof event.confidence).toBe('number')
            expect(event.source).toBeTruthy()
            expect(event.source.documentId).toBeTruthy()
          })
        }
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for progress bar', async () => {
      render(<MedicalTimelineAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        const progressBar = screen.getByRole('progressbar')
        expect(progressBar).toHaveAttribute('aria-valuenow')
      })
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      render(<MedicalTimelineAgent {...defaultProps} />)

      await user.tab()
      expect(screen.getByRole('button', { name: /Generate Timeline/ })).toHaveFocus()

      await user.keyboard('{Enter}')
      expect(screen.getByRole('button', { name: /Generating.../ })).toBeInTheDocument()
    })

    it('should have proper heading structure', () => {
      render(<MedicalTimelineAgent {...defaultProps} />)

      expect(screen.getByRole('heading', { name: /AI-Generated Medical Timeline/ })).toBeInTheDocument()
    })
  })

  describe('Error Edge Cases', () => {
    it('should handle empty extraction data', async () => {
      mockStructuredExtractionService.getExtractionsForMatter.mockResolvedValue({
        medications: [],
        radiology: [],
        problems: [],
        followups: []
      })

      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        expect(mockOnTimelineGenerated).toHaveBeenCalledWith(
          expect.objectContaining({
            events: expect.arrayContaining([
              expect.objectContaining({
                source: expect.objectContaining({
                  documentId: 'synthetic' // Should still generate synthetic events
                })
              })
            ])
          })
        )
      })
    })

    it('should handle malformed evidence data', async () => {
      mockStructuredExtractionService.getExtractionsForMatter.mockResolvedValue({
        medications: [{
          drug: 'Test Drug',
          evidence: [] // Empty evidence
        } as any],
        radiology: [],
        problems: [],
        followups: []
      })

      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        expect(mockOnTimelineGenerated).toHaveBeenCalled()
        // Should handle gracefully without crashing
      })
    })
  })

  describe('Performance', () => {
    it('should not cause memory leaks with async operations', async () => {
      const { unmount } = render(<MedicalTimelineAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))
      
      // Unmount while processing
      unmount()

      // Should not cause memory leaks or errors
      await waitFor(() => {
        expect(mockStructuredExtractionService.getExtractionsForMatter).toHaveBeenCalled()
      })
    })

    it('should handle large datasets efficiently', async () => {
      // Create large mock dataset
      const largeMockData = {
        medications: Array(100).fill(null).map((_, i) => ({
          drug: `Drug ${i}`,
          evidence: [{ quote: `Evidence ${i}`, documentId: `doc-${i}`, page: 1 }]
        })),
        radiology: Array(50).fill(null).map((_, i) => ({
          study: `Study ${i}`,
          evidence: [{ quote: `Finding ${i}`, documentId: `rad-${i}`, page: 1 }]
        })),
        problems: Array(25).fill(null).map((_, i) => ({
          term: `Problem ${i}`,
          evidence: [{ quote: `Problem ${i}`, documentId: `prob-${i}`, page: 1 }]
        })),
        followups: []
      }

      mockStructuredExtractionService.getExtractionsForMatter.mockResolvedValue(largeMockData)

      const mockOnTimelineGenerated = jest.fn()
      render(
        <MedicalTimelineAgent 
          {...defaultProps} 
          onTimelineGenerated={mockOnTimelineGenerated}
        />
      )

      const startTime = performance.now()
      fireEvent.click(screen.getByRole('button', { name: /Generate Timeline/ }))

      await waitFor(() => {
        expect(mockOnTimelineGenerated).toHaveBeenCalled()
        const endTime = performance.now()
        const processingTime = endTime - startTime
        
        // Should complete within reasonable time (adjust threshold as needed)
        expect(processingTime).toBeLessThan(5000) // 5 seconds
      }, { timeout: 10000 })
    })
  })
})