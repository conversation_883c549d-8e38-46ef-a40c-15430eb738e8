/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'
import MedicalProcessingAgent from '../medical-processing-agent'
import { getFeatureFlags } from '@/lib/features/ag-ui'
import type { ProcessingResults, HumanReviewItem } from '../medical-processing-agent'

// Mock dependencies
jest.mock('@/lib/features/ag-ui')
jest.mock('@/lib/services/medical/structured-extraction-service')

const mockGetFeatureFlags = getFeatureFlags as jest.MockedFunction<typeof getFeatureFlags>

// Mock Progress component
jest.mock('@/components/ui/progress', () => {
  return function MockProgress({ value, className }: { value: number; className?: string }) {
    return (
      <div 
        data-testid="progress-bar" 
        data-value={value}
        className={className}
        role="progressbar"
        aria-valuenow={value}
      >
        Progress: {value}%
      </div>
    )
  }
})

describe('MedicalProcessingAgent', () => {
  const defaultProps = {
    matterId: 'test-matter-123'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
    mockGetFeatureFlags.mockReturnValue({
      aguiEnabled: true,
      aguiVersion: '2.0.0'
    })
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Feature Flag Integration', () => {
    it('should render disabled state when AG-UI is disabled', () => {
      mockGetFeatureFlags.mockReturnValue({
        aguiEnabled: false,
        aguiVersion: '2.0.0'
      })

      render(<MedicalProcessingAgent {...defaultProps} />)

      expect(screen.getByText(/Agentic processing requires AG-UI features/)).toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /Start Processing/ })).not.toBeInTheDocument()
    })

    it('should render enabled state when AG-UI is enabled', () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      expect(screen.getByText('Agentic Medical Processing')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Start Processing/ })).toBeInTheDocument()
      expect(screen.getByText('AG-UI Powered')).toBeInTheDocument()
    })
  })

  describe('Processing Control', () => {
    it('should start processing when start button is clicked', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      const startButton = screen.getByRole('button', { name: /Start Processing/ })
      fireEvent.click(startButton)

      expect(screen.getByRole('button', { name: /Pause/ })).toBeInTheDocument()
      expect(screen.getByText(/Processing:/)).toBeInTheDocument()

      // Fast-forward timers to simulate processing
      jest.advanceTimersByTime(1000)

      await waitFor(() => {
        expect(screen.getByTestId('progress-bar')).toBeInTheDocument()
      })
    })

    it('should pause processing when pause button is clicked', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      // Start processing
      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Pause/ })).toBeInTheDocument()
      })

      // Pause processing
      fireEvent.click(screen.getByRole('button', { name: /Pause/ }))

      expect(screen.getByRole('button', { name: /Resume/ })).toBeInTheDocument()
    })

    it('should resume processing when resume button is clicked', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      // Start and pause processing
      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      
      await waitFor(() => {
        fireEvent.click(screen.getByRole('button', { name: /Pause/ }))
      })

      expect(screen.getByRole('button', { name: /Resume/ })).toBeInTheDocument()

      // Resume processing
      fireEvent.click(screen.getByRole('button', { name: /Resume/ }))

      expect(screen.getByRole('button', { name: /Pause/ })).toBeInTheDocument()
    })

    it('should show/hide processing details', () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      const detailsButton = screen.getByRole('button', { name: /Show Details/ })
      fireEvent.click(detailsButton)

      expect(screen.getByText('Processing Steps')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Hide Details/ })).toBeInTheDocument()

      fireEvent.click(screen.getByRole('button', { name: /Hide Details/ }))

      expect(screen.queryByText('Processing Steps')).not.toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Show Details/ })).toBeInTheDocument()
    })
  })

  describe('Processing Steps', () => {
    it('should display all processing steps with correct initial state', () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      // Show details to see steps
      fireEvent.click(screen.getByRole('button', { name: /Show Details/ }))

      const expectedSteps = [
        'Document Upload & Validation',
        'OCR Text Extraction',
        'Medical Entity Recognition',
        'Medication Extraction',
        'Radiology Processing',
        'Medical Problems',
        'Follow-up Analysis',
        'Quality Review'
      ]

      expectedSteps.forEach(step => {
        expect(screen.getByText(step)).toBeInTheDocument()
      })
    })

    it('should update step status during processing', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      // Show details
      fireEvent.click(screen.getByRole('button', { name: /Show Details/ }))

      // Start processing
      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      // Fast-forward through processing
      jest.advanceTimersByTime(500)

      await waitFor(() => {
        const progressBars = screen.getAllByTestId('progress-bar')
        expect(progressBars.length).toBeGreaterThan(0)
      })
    })

    it('should show confidence scores for completed steps', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Show Details/ }))
      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      // Fast-forward to complete first step
      jest.advanceTimersByTime(2000)

      await waitFor(() => {
        const confidenceBadges = screen.getAllByText(/% confident/)
        expect(confidenceBadges.length).toBeGreaterThan(0)
      })
    })
  })

  describe('Processing Statistics', () => {
    it('should initialize with random document count', () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      // Should show document count (randomly generated between 3-7)
      const stats = screen.getByText(/\/\d Documents/)
      expect(stats).toBeInTheDocument()
    })

    it('should update statistics during processing', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      // Fast-forward processing
      jest.advanceTimersByTime(1000)

      await waitFor(() => {
        expect(screen.getByText(/Extractions/)).toBeInTheDocument()
        expect(screen.getByText(/Confidence/)).toBeInTheDocument()
        expect(screen.getByText(/Time/)).toBeInTheDocument()
      })
    })

    it('should show completion summary when processing finishes', async () => {
      const mockOnProcessingComplete = jest.fn()
      render(
        <MedicalProcessingAgent 
          {...defaultProps} 
          onProcessingComplete={mockOnProcessingComplete}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      // Fast-forward to completion
      jest.advanceTimersByTime(20000) // Complete all steps

      await waitFor(() => {
        expect(screen.getByText('Processing Complete')).toBeInTheDocument()
      }, { timeout: 5000 })

      expect(mockOnProcessingComplete).toHaveBeenCalledWith(
        expect.objectContaining({
          medications: expect.any(Number),
          radiology: expect.any(Number),
          problems: expect.any(Number),
          followups: expect.any(Number),
          confidence: expect.any(Number),
          processingTime: expect.any(Number),
          documentsProcessed: expect.any(Number)
        })
      )
    })
  })

  describe('Human Review Workflow', () => {
    it('should trigger human review when needed', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      // Fast-forward to quality review step where human review might be triggered
      jest.advanceTimersByTime(15000)

      // Human review is triggered randomly, so we need to check if it appears
      await waitFor(() => {
        if (screen.queryByText('Human Review Required')) {
          expect(screen.getByText('Human Review Required')).toBeInTheDocument()
          expect(screen.getByRole('button', { name: /Approve & Continue/ })).toBeInTheDocument()
          expect(screen.getByRole('button', { name: /Review & Correct/ })).toBeInTheDocument()
        }
      }, { timeout: 1000 })
    })

    it('should handle human review approval', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(15000)

      await waitFor(() => {
        const reviewSection = screen.queryByText('Human Review Required')
        if (reviewSection) {
          fireEvent.click(screen.getByRole('button', { name: /Approve & Continue/ }))
          expect(screen.queryByText('Human Review Required')).not.toBeInTheDocument()
        }
      }, { timeout: 1000 })
    })

    it('should handle human review rejection', async () => {
      const mockOnHumanReviewRequired = jest.fn()
      render(
        <MedicalProcessingAgent 
          {...defaultProps} 
          onHumanReviewRequired={mockOnHumanReviewRequired}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(15000)

      await waitFor(() => {
        const reviewSection = screen.queryByText('Human Review Required')
        if (reviewSection) {
          fireEvent.click(screen.getByRole('button', { name: /Review & Correct/ }))
          expect(mockOnHumanReviewRequired).toHaveBeenCalledWith(
            expect.arrayContaining([
              expect.objectContaining({
                type: expect.stringMatching(/medication|radiology|problem|followup/),
                item: expect.any(String),
                confidence: expect.any(Number),
                requiresApproval: true
              })
            ])
          )
        }
      }, { timeout: 1000 })
    })

    it('should display review item details', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(15000)

      await waitFor(() => {
        const reviewSection = screen.queryByText('Human Review Required')
        if (reviewSection) {
          expect(screen.getByText(/Ambiguous dosage/)).toBeInTheDocument()
          expect(screen.getByText(/Type: medication/)).toBeInTheDocument()
          expect(screen.getByText(/% confident/)).toBeInTheDocument()
        }
      }, { timeout: 1000 })
    })
  })

  describe('Error Handling', () => {
    it('should handle processing errors', async () => {
      const mockOnError = jest.fn()
      
      // Mock a step that will fail
      const originalRandom = Math.random
      Math.random = jest.fn().mockReturnValue(0.1) // This might trigger an error condition

      render(<MedicalProcessingAgent {...defaultProps} onError={mockOnError} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(20000)

      // Restore Math.random
      Math.random = originalRandom

      // Error handling would be triggered by the mock or simulation
      // In the actual implementation, errors would be handled appropriately
    })

    it('should mark failed steps with error status', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Show Details/ }))
      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      // In a real scenario, we would simulate a step failure
      // This would require more complex mocking of the step simulation
    })
  })

  describe('Progress Tracking', () => {
    it('should show overall progress bar', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      await waitFor(() => {
        const progressBar = screen.getByRole('progressbar')
        expect(progressBar).toBeInTheDocument()
        expect(progressBar).toHaveAttribute('aria-valuenow')
      })
    })

    it('should update progress percentage', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(2000)

      await waitFor(() => {
        const percentageText = screen.getByText(/\d+%/)
        expect(percentageText).toBeInTheDocument()
      })
    })

    it('should show current step information', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      await waitFor(() => {
        expect(screen.getByText(/Processing:/)).toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for progress bars', async () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))

      await waitFor(() => {
        const progressBars = screen.getAllByRole('progressbar')
        expect(progressBars.length).toBeGreaterThan(0)
        progressBars.forEach(bar => {
          expect(bar).toHaveAttribute('aria-valuenow')
        })
      })
    })

    it('should have proper button states and labels', () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      const startButton = screen.getByRole('button', { name: /Start Processing/ })
      expect(startButton).toBeEnabled()

      fireEvent.click(startButton)

      expect(screen.getByRole('button', { name: /Pause/ })).toBeInTheDocument()
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      render(<MedicalProcessingAgent {...defaultProps} />)

      await user.tab()
      expect(screen.getByRole('button', { name: /Start Processing/ })).toHaveFocus()

      await user.keyboard('{Enter}')
      expect(screen.getByRole('button', { name: /Pause/ })).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('should not cause memory leaks with timers', () => {
      const { unmount } = render(<MedicalProcessingAgent {...defaultProps} />)

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      
      // Unmount component while processing
      unmount()

      // Should not throw errors or cause memory leaks
      jest.advanceTimersByTime(10000)
    })

    it('should handle rapid button clicks gracefully', () => {
      render(<MedicalProcessingAgent {...defaultProps} />)

      const startButton = screen.getByRole('button', { name: /Start Processing/ })
      
      // Rapid clicks
      fireEvent.click(startButton)
      fireEvent.click(startButton)
      fireEvent.click(startButton)

      // Should only start processing once
      expect(screen.getByRole('button', { name: /Pause/ })).toBeInTheDocument()
    })
  })

  describe('Integration with Medical Types', () => {
    it('should use proper ProcessingResults interface', async () => {
      const mockOnProcessingComplete = jest.fn()
      render(
        <MedicalProcessingAgent 
          {...defaultProps} 
          onProcessingComplete={mockOnProcessingComplete}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(20000)

      await waitFor(() => {
        expect(mockOnProcessingComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            medications: expect.any(Number),
            radiology: expect.any(Number),
            problems: expect.any(Number),
            followups: expect.any(Number),
            confidence: expect.any(Number),
            processingTime: expect.any(Number),
            documentsProcessed: expect.any(Number)
          })
        )
      }, { timeout: 5000 })
    })

    it('should use proper HumanReviewItem interface', async () => {
      const mockOnHumanReviewRequired = jest.fn()
      render(
        <MedicalProcessingAgent 
          {...defaultProps} 
          onHumanReviewRequired={mockOnHumanReviewRequired}
        />
      )

      fireEvent.click(screen.getByRole('button', { name: /Start Processing/ }))
      jest.advanceTimersByTime(15000)

      await waitFor(() => {
        if (mockOnHumanReviewRequired.mock.calls.length > 0) {
          const reviewItems = mockOnHumanReviewRequired.mock.calls[0][0]
          expect(Array.isArray(reviewItems)).toBe(true)
          reviewItems.forEach((item: any) => {
            expect(item).toEqual(
              expect.objectContaining({
                type: expect.stringMatching(/medication|radiology|problem|followup/),
                item: expect.any(String),
                confidence: expect.any(Number),
                requiresApproval: expect.any(Boolean),
                documentId: expect.any(String),
                pageNumber: expect.any(Number)
              })
            )
          })
        }
      }, { timeout: 1000 })
    })
  })
})