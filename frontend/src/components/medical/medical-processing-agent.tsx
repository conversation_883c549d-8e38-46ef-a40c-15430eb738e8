'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Bot, 
  FileText, 
  Upload, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Eye,
  Zap,
  RefreshCw,
  Settings,
  User
} from 'lucide-react'
import { getFeatureFlags } from '@/lib/features/ag-ui'
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'
import {
  ProcessingError,
  MedicalErrorHandler,
  DataValidationError
} from '@/types/medical-errors'

interface ProcessingStep {
  id: string
  name: string
  status: 'pending' | 'in_progress' | 'completed' | 'error'
  progress: number
  message?: string
  timestamp?: Date
  confidence?: number
  itemsProcessed?: number
  totalItems?: number
  error?: ProcessingError
}

interface ProcessingResults {
  medications: number
  radiology: number
  problems: number
  followups: number
  confidence: number
  processingTime: number
  documentsProcessed: number
}

interface ProcessingStatistics {
  documentsProcessed: number
  totalDocuments: number
  extractionsGenerated: number
  confidenceScore: number
  processingTime: number
}

interface HumanReviewItem {
  type: 'medication' | 'radiology' | 'problem' | 'followup'
  item: string
  confidence: number
  documentId?: string
  pageNumber?: number
  requiresApproval: boolean
}

interface MedicalProcessingAgentProps {
  matterId: string
  onProcessingComplete?: (results: ProcessingResults) => void
  onError?: (error: ProcessingError) => void
  onHumanReviewRequired?: (items: HumanReviewItem[]) => void
}

const PROCESSING_STEPS: Omit<ProcessingStep, 'status' | 'progress' | 'timestamp'>[] = [
  {
    id: 'document_upload',
    name: 'Document Upload & Validation',
    message: 'Uploading and validating medical documents'
  },
  {
    id: 'ocr_extraction',
    name: 'OCR Text Extraction', 
    message: 'Extracting text using Google Document AI'
  },
  {
    id: 'medical_nlp',
    name: 'Medical Entity Recognition',
    message: 'Identifying medical entities with Healthcare NLP'
  },
  {
    id: 'medication_extraction',
    name: 'Medication Extraction',
    message: 'Extracting medications using LangExtract'
  },
  {
    id: 'radiology_extraction', 
    name: 'Radiology Processing',
    message: 'Processing radiology reports and findings'
  },
  {
    id: 'problem_extraction',
    name: 'Medical Problems',
    message: 'Identifying medical problems and diagnoses'
  },
  {
    id: 'followup_extraction',
    name: 'Follow-up Analysis',
    message: 'Detecting follow-up requirements and care gaps'
  },
  {
    id: 'quality_review',
    name: 'Quality Review',
    message: 'AI-powered quality assurance and confidence scoring'
  }
]

export default function MedicalProcessingAgent({ 
  matterId, 
  onProcessingComplete, 
  onError,
  onHumanReviewRequired
}: MedicalProcessingAgentProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [steps, setSteps] = useState<ProcessingStep[]>(
    PROCESSING_STEPS.map(step => ({
      ...step,
      status: 'pending' as const,
      progress: 0
    }))
  )
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [overallProgress, setOverallProgress] = useState(0)
  const [processingStats, setProcessingStats] = useState<ProcessingStatistics>({
    documentsProcessed: 0,
    totalDocuments: 0,
    extractionsGenerated: 0,
    confidenceScore: 0,
    processingTime: 0
  })
  const [showDetails, setShowDetails] = useState(false)
  const [humanReviewNeeded, setHumanReviewNeeded] = useState(false)
  const [reviewItems, setReviewItems] = useState<HumanReviewItem[]>([])

  const featureFlags = getFeatureFlags()

  // Simulate real-time processing with streaming updates
  const simulateProcessingStep = useCallback(async (stepIndex: number): Promise<void> => {
    return new Promise((resolve) => {
      const step = steps[stepIndex]
      let progress = 0
      
      const interval = setInterval(() => {
        progress += Math.random() * 15 + 5
        
        setSteps(prevSteps => 
          prevSteps.map((s, idx) => 
            idx === stepIndex 
              ? { 
                  ...s, 
                  status: progress >= 100 ? 'completed' : 'in_progress',
                  progress: Math.min(progress, 100),
                  timestamp: new Date(),
                  confidence: Math.random() * 0.3 + 0.7, // 70-100% confidence
                  itemsProcessed: Math.floor(progress / 10),
                  totalItems: 10
                }
              : s
          )
        )
        
        if (progress >= 100) {
          clearInterval(interval)
          
          // Simulate human review needed for certain steps
          if (step.id === 'quality_review' && Math.random() > 0.7) {
            const reviewItemsData: HumanReviewItem[] = [
              { 
                type: 'medication', 
                item: 'Ambiguous dosage: "Take as needed"', 
                confidence: 0.65,
                requiresApproval: true,
                documentId: `doc_${Math.random().toString(36).substr(2, 9)}`,
                pageNumber: 1
              },
              { 
                type: 'radiology', 
                item: 'Unclear finding interpretation', 
                confidence: 0.58,
                requiresApproval: true,
                documentId: `doc_${Math.random().toString(36).substr(2, 9)}`,
                pageNumber: 2
              }
            ]
            setHumanReviewNeeded(true)
            setReviewItems(reviewItemsData)
          }
          
          resolve()
        }
      }, 200)
    })
  }, [steps])

  const startProcessing = async () => {
    setIsProcessing(true)
    setCurrentStepIndex(0)
    setOverallProgress(0)
    setHumanReviewNeeded(false)
    setReviewItems([])
    
    const startTime = Date.now()
    
    // Reset all steps
    setSteps(PROCESSING_STEPS.map(step => ({
      ...step,
      status: 'pending' as const,
      progress: 0
    })))

    try {
      // Process each step sequentially
      for (let i = 0; i < PROCESSING_STEPS.length; i++) {
        setCurrentStepIndex(i)
        await simulateProcessingStep(i)
        setOverallProgress(((i + 1) / PROCESSING_STEPS.length) * 100)
        
        // Update processing stats
        setProcessingStats(prev => ({
          ...prev,
          documentsProcessed: Math.min(prev.totalDocuments, i + 1),
          extractionsGenerated: prev.extractionsGenerated + Math.floor(Math.random() * 5),
          processingTime: Date.now() - startTime
        }))
      }

      // Generate final processing results
      const finalStats = {
        ...processingStats,
        confidenceScore: Math.random() * 0.2 + 0.8, // 80-100%
        processingTime: Date.now() - startTime
      }

      const processingResults: ProcessingResults = {
        medications: Math.floor(Math.random() * 15) + 5,
        radiology: Math.floor(Math.random() * 8) + 2,
        problems: Math.floor(Math.random() * 10) + 3,
        followups: Math.floor(Math.random() * 6) + 1,
        confidence: finalStats.confidenceScore,
        processingTime: finalStats.processingTime,
        documentsProcessed: finalStats.documentsProcessed
      }

      setProcessingStats(finalStats)
      onProcessingComplete?.(processingResults)
      
    } catch (error) {
      console.error('Processing failed:', error)
      
      const processingError = error instanceof ProcessingError 
        ? error 
        : new ProcessingError(
            error instanceof Error ? error.message : 'Processing failed',
            PROCESSING_STEPS[currentStepIndex]?.id as any
          )
      
      onError?.(processingError)
      
      // Mark current step as error with proper error info
      setSteps(prevSteps => 
        prevSteps.map((s, idx) => 
          idx === currentStepIndex 
            ? { 
                ...s, 
                status: 'error' as const,
                error: processingError,
                message: MedicalErrorHandler.getUserMessage(processingError)
              }
            : s
        )
      )
    } finally {
      setIsProcessing(false)
    }
  }

  const pauseProcessing = () => {
    setIsProcessing(false)
  }

  const resumeProcessing = () => {
    setIsProcessing(true)
  }

  const handleHumanReview = (approved: boolean) => {
    setHumanReviewNeeded(false)
    
    if (approved) {
      console.log('Human review approved, continuing...')
      // Mark review items as approved
      const approvedItems = reviewItems.map(item => ({ ...item, requiresApproval: false }))
      setReviewItems(approvedItems)
    } else {
      console.log('Human review requested changes')
      // Trigger callback for parent component to handle review workflow
      onHumanReviewRequired?.(reviewItems)
    }
  }

  // Mock document count on component mount
  useEffect(() => {
    setProcessingStats(prev => ({
      ...prev,
      totalDocuments: Math.floor(Math.random() * 5) + 3
    }))
  }, [])

  if (!featureFlags.aguiEnabled) {
    return (
      <Card className="border-gray-200">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Bot className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500">Agentic processing requires AG-UI features to be enabled.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Processing Control Header */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <Zap className="h-5 w-5" />
              Agentic Medical Processing
              <Badge variant="secondary" className="ml-2">AG-UI Powered</Badge>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button 
                variant={isProcessing ? "destructive" : "default"}
                onClick={isProcessing ? pauseProcessing : startProcessing}
                disabled={overallProgress > 0 && overallProgress < 100 && !isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Pause
                  </>
                ) : overallProgress > 0 && overallProgress < 100 ? (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Resume
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Start Processing
                  </>
                )}
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                <Settings className="h-4 w-4 mr-2" />
                {showDetails ? 'Hide' : 'Show'} Details
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Overall Progress */}
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-medium text-blue-900">Overall Progress</span>
                <span className="text-blue-700">{Math.round(overallProgress)}%</span>
              </div>
              <Progress value={overallProgress} className="h-2" />
            </div>

            {/* Current Step */}
            {currentStepIndex < steps.length && (
              <div className="flex items-center gap-2 text-sm text-blue-800">
                <RefreshCw className={`h-4 w-4 ${isProcessing ? 'animate-spin' : ''}`} />
                <span>
                  {isProcessing ? 'Processing: ' : 'Next: '}
                  {steps[currentStepIndex]?.name}
                </span>
              </div>
            )}

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-blue-600">
                  {processingStats.documentsProcessed}/{processingStats.totalDocuments}
                </div>
                <div className="text-xs text-blue-700">Documents</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600">
                  {processingStats.extractionsGenerated}
                </div>
                <div className="text-xs text-green-700">Extractions</div>
              </div>
              <div>
                <div className="text-lg font-bold text-purple-600">
                  {Math.round(processingStats.confidenceScore * 100)}%
                </div>
                <div className="text-xs text-purple-700">Confidence</div>
              </div>
              <div>
                <div className="text-lg font-bold text-yellow-600">
                  {Math.round(processingStats.processingTime / 1000)}s
                </div>
                <div className="text-xs text-yellow-700">Time</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Human Review Required */}
      {humanReviewNeeded && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-900">
              <User className="h-5 w-5" />
              Human Review Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-yellow-800 mb-4">
              The AI has identified items that need human review for accuracy:
            </p>
            <div className="space-y-2 mb-4">
              {reviewItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
                  <div>
                    <div className="font-medium">{item.item}</div>
                    <div className="text-sm text-gray-600">
                      Type: {item.type}
                      {item.documentId && (` • Doc: ${item.documentId}`)}
                      {item.pageNumber && (` • Page: ${item.pageNumber}`)}
                    </div>
                  </div>
                  <Badge variant="outline">
                    {Math.round(item.confidence * 100)}% confident
                  </Badge>
                </div>
              ))}
            </div>
            <div className="flex space-x-2">
              <Button 
                onClick={() => handleHumanReview(true)}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve & Continue
              </Button>
              <Button 
                variant="outline"
                onClick={() => handleHumanReview(false)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Review & Correct
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Steps */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Processing Steps
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-4 p-3 rounded border">
                  <div className="flex-shrink-0">
                    {step.status === 'completed' ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : step.status === 'error' ? (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    ) : step.status === 'in_progress' ? (
                      <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium">{step.name}</h4>
                      {step.confidence && (
                        <Badge variant="outline" className="text-xs">
                          {Math.round(step.confidence * 100)}% confident
                        </Badge>
                      )}
                    </div>
                    
                    {step.message && (
                      <p className="text-sm text-gray-600 mb-2">{step.message}</p>
                    )}
                    
                    {step.status !== 'pending' && (
                      <div className="space-y-1">
                        <Progress value={step.progress} className="h-1" />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>
                            {step.itemsProcessed && step.totalItems 
                              ? `${step.itemsProcessed}/${step.totalItems} items`
                              : `${Math.round(step.progress)}%`
                            }
                          </span>
                          {step.timestamp && (
                            <span>{step.timestamp.toLocaleTimeString()}</span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Completion Summary */}
      {overallProgress === 100 && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-900">
              <CheckCircle className="h-5 w-5" />
              Processing Complete
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-green-800 mb-4">
              All medical records have been processed successfully with AI-powered extraction and quality assurance.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="bg-white p-3 rounded">
                <div className="text-lg font-bold text-blue-600">
                  {processingStats.documentsProcessed}
                </div>
                <div className="text-xs text-gray-600">Documents Processed</div>
              </div>
              <div className="bg-white p-3 rounded">
                <div className="text-lg font-bold text-green-600">
                  {processingStats.extractionsGenerated}
                </div>
                <div className="text-xs text-gray-600">Items Extracted</div>
              </div>
              <div className="bg-white p-3 rounded">
                <div className="text-lg font-bold text-purple-600">
                  {Math.round(processingStats.confidenceScore * 100)}%
                </div>
                <div className="text-xs text-gray-600">Avg Confidence</div>
              </div>
              <div className="bg-white p-3 rounded">
                <div className="text-lg font-bold text-yellow-600">
                  {Math.round(processingStats.processingTime / 1000)}s
                </div>
                <div className="text-xs text-gray-600">Total Time</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}