'use client'

import { useState, useC<PERSON>back, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Bot, MessageSquare, Lightbulb, FileText, AlertCircle, Sparkles } from 'lucide-react'
import { ResilientChat } from '@/components/copilot/resilient-chat'
import { getFeatureFlags } from '@/lib/features/ag-ui'
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'
import type {
  MedicalChatMessage,
  MedicalChatContext,
  MedicalDataSummary,
  createMedicalChatMessage
} from '@/types/medical-chat'
import { isMedicalChatMessage } from '@/types/medical-chat'

interface MedicalCopilotProps {
  matterId: string
  onInsightGenerated?: (insight: MedicalInsight) => void
}

interface MedicalInsight {
  content: string
  confidence: number
  sources: DocumentSource[]
  type: 'summary' | 'analysis' | 'recommendation' | 'gap_detection' | 'timeline'
  timestamp: number
  relatedEntities: string[]
}

interface DocumentSource {
  documentId: string
  page: number
  quote: string
  confidence: number
  sourceType: 'primary' | 'supporting' | 'contextual'
}

// Using proper medical context interface from types
interface LocalMedicalContext {
  matterId: string
  totalMedications: number
  totalRadiology: number
  totalProblems: number
  totalFollowups: number
  recentExtractions: string[]
}

export default function MedicalCopilot({ matterId, onInsightGenerated }: MedicalCopilotProps) {
  const [isActive, setIsActive] = useState(false)
  const [medicalContext, setMedicalContext] = useState<LocalMedicalContext | null>(null)
  const [suggestedQueries, setSuggestedQueries] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const featureFlags = getFeatureFlags()

  // Load medical context for the copilot
  useEffect(() => {
    const loadContext = async () => {
      try {
        setLoading(true)
        const extractions = await structuredExtractionService.getExtractionsForMatter(matterId)
        
        const context: LocalMedicalContext = {
          matterId,
          totalMedications: extractions.medications.length,
          totalRadiology: extractions.radiology.length,
          totalProblems: extractions.problems.length,
          totalFollowups: extractions.followups.length,
          recentExtractions: [
            ...extractions.medications.slice(0, 3).map(m => `Medication: ${m.drug}`),
            ...extractions.problems.slice(0, 3).map(p => `Problem: ${p.term}`),
            ...extractions.radiology.slice(0, 2).map(r => `Study: ${r.study}`)
          ].slice(0, 5)
        }
        
        setMedicalContext(context)
        
        // Generate contextual suggestions
        const suggestions = generateSuggestions(context)
        setSuggestedQueries(suggestions)
        
      } catch (error) {
        console.error('Failed to load medical context:', error)
      } finally {
        setLoading(false)
      }
    }

    if (matterId) {
      loadContext()
    }
  }, [matterId])

  const generateSuggestions = (context: LocalMedicalContext): string[] => {
    const suggestions = []
    
    if (context.totalMedications > 0) {
      suggestions.push('What medications was the patient taking?')
      suggestions.push('Are there any drug interactions to be concerned about?')
    }
    
    if (context.totalRadiology > 0) {
      suggestions.push('Summarize the radiology findings')
      suggestions.push('What imaging studies were performed?')
    }
    
    if (context.totalProblems > 0) {
      suggestions.push('What are the primary medical diagnoses?')
      suggestions.push('How did the conditions progress over time?')
    }
    
    if (context.totalFollowups > 0) {
      suggestions.push('What follow-up care was recommended?')
      suggestions.push('Were there any missed appointments or treatments?')
    }
    
    // Always include these general queries
    suggestions.push('Create a medical timeline')
    suggestions.push('Identify any treatment gaps')
    suggestions.push('Generate a medical summary for demand')
    
    return suggestions.slice(0, 6)
  }

  const handleChatMessage = useCallback(async (message: unknown) => {
    // Validate message structure
    if (!isMedicalChatMessage(message)) {
      console.warn('Invalid chat message format received:', message)
      return
    }

    const chatMessage = message
    
    // Extract insights from AI responses
    if (chatMessage.type === 'assistant' && chatMessage.content && chatMessage.content.length > 100) {
      const insight: MedicalInsight = {
        content: chatMessage.content,
        confidence: chatMessage.confidence || 0.7,
        sources: chatMessage.sources || [],
        type: determineInsightType(chatMessage.content),
        timestamp: chatMessage.timestamp || Date.now(),
        relatedEntities: extractRelatedEntities(chatMessage.metadata)
      }
      
      if (onInsightGenerated) {
        onInsightGenerated(insight)
      }
    }
  }, [onInsightGenerated])

  const handleStartChat = () => {
    setIsActive(true)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setIsActive(true)
    // The suggestion would be sent as the initial message
    // This would typically be handled by the ResilientChat component
  }

  if (!featureFlags.aguiEnabled) {
    return (
      <Card className="border-gray-200">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Bot className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Medical AI Assistant</h3>
            <p className="text-gray-500 mb-4">
              AI assistant is currently disabled. Enable AG-UI features to use the medical copilot.
            </p>
            <Button variant="outline" disabled>
              <Bot className="h-4 w-4 mr-2" />
              AI Assistant Disabled
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card className="border-blue-200">
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4" />
            <div className="h-4 bg-gray-200 rounded w-1/2" />
            <div className="h-32 bg-gray-200 rounded" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!isActive) {
    return (
      <div className="space-y-6">
        {/* Medical Context Overview */}
        {medicalContext && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-900">
                <Sparkles className="h-5 w-5" />
                Medical Case Context
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{medicalContext.totalMedications}</div>
                  <div className="text-sm text-blue-700">Medications</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{medicalContext.totalRadiology}</div>
                  <div className="text-sm text-green-700">Radiology Studies</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{medicalContext.totalProblems}</div>
                  <div className="text-sm text-red-700">Medical Problems</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{medicalContext.totalFollowups}</div>
                  <div className="text-sm text-yellow-700">Follow-ups</div>
                </div>
              </div>
              
              {medicalContext.recentExtractions.length > 0 && (
                <div>
                  <h4 className="font-semibold text-blue-900 mb-2">Recent Extractions</h4>
                  <div className="flex flex-wrap gap-2">
                    {medicalContext.recentExtractions.map((extraction, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {extraction}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Chat Interface Launcher */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-900">
              <Bot className="h-5 w-5" />
              Medical AI Assistant
              <Badge variant="secondary" className="ml-2">Powered by AG-UI</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-6">
              <Bot className="mx-auto h-16 w-16 text-green-600 mb-4" />
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                AI-Powered Medical Analysis
              </h3>
              <p className="text-green-700 mb-6">
                Ask questions about medical records, get insights, and receive AI-powered analysis 
                with source citations and confidence scores.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="flex items-center gap-2 text-sm text-green-800">
                  <FileText className="h-4 w-4" />
                  <span>Source Grounding</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-green-800">
                  <MessageSquare className="h-4 w-4" />
                  <span>Real-time Streaming</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-green-800">
                  <Lightbulb className="h-4 w-4" />
                  <span>Smart Insights</span>
                </div>
              </div>

              <Button onClick={handleStartChat} className="bg-green-600 hover:bg-green-700">
                <Bot className="h-4 w-4 mr-2" />
                Start AI Conversation
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Suggested Queries */}
        {suggestedQueries.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                Suggested Questions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {suggestedQueries.map((query, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="justify-start h-auto p-3 text-left"
                    onClick={() => handleSuggestionClick(query)}
                  >
                    <MessageSquare className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="text-sm">{query}</span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Compliance Notice */}
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-semibold mb-1">HIPAA Compliance Notice</p>
                <p>
                  All medical AI interactions are logged for audit purposes. 
                  Zero data retention is enabled for AI model processing.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Active Chat Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-blue-600" />
              Medical AI Assistant
              <Badge variant="secondary" className="text-xs">Active</Badge>
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsActive(false)}
            >
              Minimize
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-96">
            <ResilientChat
              className="h-full"
              title="Medical AI Assistant"
              placeholder="Ask me anything about the medical records..."
              context={createMedicalChatContext(matterId, medicalContext)}
              onChatMessage={handleChatMessage}
              instructions={createMedicalInstructions(matterId)}
              suggestions={suggestedQueries}
            />
          </div>
        </CardContent>
      </Card>

      {/* Context sidebar when active */}
      {medicalContext && (
        <Card className="border-gray-200">
          <CardHeader>
            <CardTitle className="text-sm">Case Context</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-xs">
              <div>Total Medical Items: {
                medicalContext.totalMedications + 
                medicalContext.totalRadiology + 
                medicalContext.totalProblems + 
                medicalContext.totalFollowups
              }</div>
              <div>Active Features: Source grounding, Streaming, HIPAA audit</div>
              <Badge variant="outline" className="text-xs">
                AG-UI v{featureFlags.aguiVersion}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Helper functions for proper typing
function determineInsightType(content: string): MedicalInsight['type'] {
  const lowerContent = content.toLowerCase()
  if (lowerContent.includes('timeline') || lowerContent.includes('chronological')) return 'timeline'
  if (lowerContent.includes('gap') || lowerContent.includes('missing')) return 'gap_detection'
  if (lowerContent.includes('recommend') || lowerContent.includes('should')) return 'recommendation'
  if (lowerContent.includes('summary') || lowerContent.includes('overview')) return 'summary'
  return 'analysis'
}

function extractRelatedEntities(metadata?: any): string[] {
  if (!metadata || typeof metadata !== 'object') return []
  
  const entities: string[] = []
  if (metadata.entityType) entities.push(metadata.entityType)
  if (metadata.matterId) entities.push(`matter:${metadata.matterId}`)
  
  return entities
}

function createMedicalChatContext(matterId: string, localContext: LocalMedicalContext | null): MedicalChatContext {
  const medicalSummary: MedicalDataSummary = {
    totalMedications: localContext?.totalMedications || 0,
    totalRadiology: localContext?.totalRadiology || 0,
    totalProblems: localContext?.totalProblems || 0,
    totalFollowups: localContext?.totalFollowups || 0,
    totalDocuments: 0, // Would need to be fetched separately
    dateRange: {
      earliest: null,
      latest: null
    },
    providers: [],
    documentTypes: []
  }

  return {
    matterId,
    medicalSummary,
    userRole: 'attorney', // Default role, should be passed as prop
    tenantId: '', // Should be fetched from auth context
    sessionConfig: {
      enableStreaming: true,
      enableSourceGrounding: true,
      enableConfidenceScoring: true,
      maxHistoryLength: 50,
      modelConfig: {
        modelId: 'gemini-2.5-flash',
        temperature: 0.7,
        maxTokens: 2048,
        systemPrompt: createMedicalInstructions(matterId),
        zeroDataRetention: true
      },
      hipaaConfig: {
        enableAuditLogging: true,
        enablePHIDetection: true,
        redactionConfig: {
          enabled: true,
          redactNames: false, // Names needed for legal context
          redactDates: false, // Dates needed for timeline
          redactAddresses: true
        },
        sessionTimeout: 30
      }
    },
    availableEntities: []
  }
}

function createMedicalInstructions(matterId: string): string {
  return `You are a medical AI assistant helping with legal case analysis. 
    You have access to extracted medical records for matter ${matterId}. 
    Always provide source citations and confidence scores for medical claims.
    Focus on facts relevant to legal proceedings.
    Follow HIPAA compliance guidelines for all interactions.`
}