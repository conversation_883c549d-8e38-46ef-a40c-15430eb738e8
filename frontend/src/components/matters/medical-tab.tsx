'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Stethoscope,
  Bot,
  Calendar,
  Search,
  AlertTriangle,
  Upload,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react'
import MedicalRecordsCommandCenter from '@/components/medical/MedicalRecordsCommandCenter'
import MedicalCopilot from '@/components/medical/medical-copilot'
import MedicalProcessingAgent from '@/components/medical/medical-processing-agent'
import MedicalTimelineAgent from '@/components/medical/medical-timeline-agent'
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'

interface MedicalTabProps {
  matterId: string
}

export default function MedicalTab({ matterId }: MedicalTabProps) {
  const [activeSubTab, setActiveSubTab] = useState('records')
  const [extractionStats, setExtractionStats] = useState({
    totalRecords: 0,
    processed: 0,
    medications: 0,
    radiology: 0,
    problems: 0,
    followups: 0
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const [demandContent, setDemandContent] = useState<string>('')

  // Load extraction statistics
  useEffect(() => {
    const loadStats = async () => {
      try {
        const extractions = await structuredExtractionService.getExtractionsForMatter(matterId)
        setExtractionStats({
          totalRecords: 1, // Mock value - would come from document count
          processed: 1,    // Mock value
          medications: extractions.medications.length,
          radiology: extractions.radiology.length,
          problems: extractions.problems.length,
          followups: extractions.followups.length
        })
      } catch (error) {
        console.error('Failed to load extraction stats:', error)
      }
    }

    loadStats()
  }, [matterId])

  const handleSendToDemand = (content: string, citations: string[]) => {
    const formattedContent = `${content}\n\n### Citations\n${citations.join('\n')}`
    setDemandContent(prev => prev + '\n\n' + formattedContent)
  }

  const totalExtractions = extractionStats.medications + extractionStats.radiology + 
                          extractionStats.problems + extractionStats.followups

  return (
    <div className="space-y-6">
      {/* Medical Records Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Stethoscope className="h-6 w-6 text-red-500" />
            Medical Records Command Center
          </h2>
          <p className="text-gray-600 mt-1">
            AI-powered medical record processing with precise source citations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Upload Records
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsProcessing(!isProcessing)}
          >
            {isProcessing ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Pause Processing
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Process Records
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-blue-900 font-medium">Processing medical records...</span>
              </div>
              <div className="text-blue-700 text-sm">
                {extractionStats.processed} of {extractionStats.totalRecords} records processed
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{extractionStats.totalRecords}</div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{extractionStats.medications}</div>
              <div className="text-sm text-gray-600">Medications</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{extractionStats.radiology}</div>
              <div className="text-sm text-gray-600">Radiology</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{extractionStats.problems}</div>
              <div className="text-sm text-gray-600">Problems</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{extractionStats.followups}</div>
              <div className="text-sm text-gray-600">Follow-ups</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Features Banner */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-semibold text-blue-900">AI Assistant</h3>
                <p className="text-sm text-blue-700">Ask questions about medical records</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-900">Smart Timeline</h3>
                <p className="text-sm text-green-700">Auto-generated medical chronology</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <h3 className="font-semibold text-yellow-900">Gap Detection</h3>
                <p className="text-sm text-yellow-700">AI-identified treatment gaps</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sub-tabs for Medical Features */}
      <Tabs value={activeSubTab} onValueChange={setActiveSubTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="records" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Records
            {totalExtractions > 0 && (
              <Badge variant="secondary" className="ml-1">
                {totalExtractions}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="processing" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Processing
          </TabsTrigger>
          <TabsTrigger value="assistant" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            AI Assistant
          </TabsTrigger>
          <TabsTrigger value="timeline" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Timeline
          </TabsTrigger>
          <TabsTrigger value="gaps" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Treatment Gaps
          </TabsTrigger>
        </TabsList>

        <TabsContent value="records" className="mt-6">
          <MedicalRecordsCommandCenter 
            matterId={matterId}
            onSendToDemand={handleSendToDemand}
          />
        </TabsContent>

        <TabsContent value="processing" className="mt-6">
          <MedicalProcessingAgent 
            matterId={matterId}
            onProcessingComplete={(results) => {
              console.log('Processing completed:', results)
              // Refresh extraction stats
              setExtractionStats(prev => ({
                ...prev,
                medications: results.medications || prev.medications,
                radiology: results.radiology || prev.radiology,
                problems: results.problems || prev.problems,
                followups: results.followups || prev.followups
              }))
            }}
            onError={(error) => {
              console.error('Processing failed:', error)
            }}
          />
        </TabsContent>

        <TabsContent value="assistant" className="mt-6">
          <MedicalCopilot 
            matterId={matterId}
            onInsightGenerated={(insight) => {
              console.log('Generated insight:', insight)
              // Could add to demand content or show notification
            }}
          />
        </TabsContent>

        <TabsContent value="timeline" className="mt-6">
          <MedicalTimelineAgent 
            matterId={matterId}
            onTimelineGenerated={(events) => {
              console.log('Timeline generated:', events.length, 'events')
            }}
          />
        </TabsContent>

        <TabsContent value="gaps" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Treatment Gap Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <AlertTriangle className="mx-auto h-16 w-16 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium mb-2">AI-Powered Gap Detection</h3>
                <p className="mb-4">
                  Identify missing treatments, follow-up delays, and documentation gaps.
                </p>
                <Button className="mt-6">
                  <Search className="h-4 w-4 mr-2" />
                  Analyze Treatment Gaps
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Demand Package Preview */}
      {demandContent && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="secondary">Updated</Badge>
              Demand Package Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-white p-4 rounded border max-h-96 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm">{demandContent}</pre>
            </div>
            <div className="mt-4 flex gap-2">
              <Button size="sm">
                Open in Demand Builder
              </Button>
              <Button size="sm" variant="outline" onClick={() => setDemandContent('')}>
                Clear Preview
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}