# Insurance Parse Microservice Dependencies

# FastAPI and core web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Google Cloud dependencies
google-cloud-documentai==2.21.0
google-cloud-storage==2.10.0
google-auth==2.23.4

# Database and ORM
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for API calls
httpx==0.25.2

# Date/time handling
python-dateutil==2.8.2

# Logging and monitoring
structlog==23.2.0

# Environment and config
python-dotenv==1.0.0

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2  # For testing HTTP calls

# Data processing
pandas==2.1.3
numpy==1.25.2

# Text processing and OCR improvements
pytesseract==0.3.10
pdf2image==1.16.3
pillow==10.1.0

# Regular expressions and text parsing
regex==2023.10.3