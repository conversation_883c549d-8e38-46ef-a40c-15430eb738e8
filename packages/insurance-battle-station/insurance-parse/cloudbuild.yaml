# Cloud Build configuration for insurance-parse microservice
steps:
  # Build Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/insurance-parse:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/insurance-parse:latest'
      - '.'
    dir: 'packages/insurance-battle-station/insurance-parse'

  # Push Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/insurance-parse:$BUILD_ID'

  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/insurance-parse:latest'

  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'insurance-parse'
      - '--image=gcr.io/$PROJECT_ID/insurance-parse:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated=false'  # Require authentication
      - '--service-account=insurance-parse@$PROJECT_ID.iam.gserviceaccount.com'
      - '--memory=2Gi'
      - '--cpu=2'
      - '--concurrency=100'
      - '--timeout=300'
      - '--max-instances=10'
      - '--min-instances=0'  # Scale to zero when not in use
      - '--port=8000'
      - '--set-env-vars=GCP_PROJECT=$PROJECT_ID'
      - '--set-env-vars=DOC_AI_PROCESSOR=projects/$PROJECT_ID/locations/us/processors/$_DOC_AI_PROCESSOR_ID'
      - '--set-env-vars=DATABASE_URL=$_DATABASE_URL'
      - '--set-env-vars=CORE_API_URL=$_CORE_API_URL'
      - '--set-env-vars=LOG_LEVEL=INFO'

# Substitution variables (set these in Cloud Build trigger or command line)
substitutions:
  _DOC_AI_PROCESSOR_ID: 'PROCESSOR_ID_PLACEHOLDER'
  _DATABASE_URL: 'DATABASE_URL_PLACEHOLDER'  
  _CORE_API_URL: 'CORE_API_URL_PLACEHOLDER'

# Build timeout
timeout: '1200s'

# Service account for Cloud Build
serviceAccount: 'projects/$PROJECT_ID/serviceAccounts/cloudbuild@$PROJECT_ID.iam.gserviceaccount.com'

# Build logs
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: E2_STANDARD_4