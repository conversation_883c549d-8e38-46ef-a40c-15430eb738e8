"""
Insurance Parse Microservice

FastAPI application for parsing insurance declaration pages using Google Document AI.
"""

import asyncio
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import List
from uuid import UUID

import structlog
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from google.cloud import storage
from sqlalchemy.ext.asyncio import AsyncSession

from .config.settings import get_settings
from .models.parse_models import (
    ParseDecPageRequest, ParseDecPageResponse, ParsedPolicyData,
    NAICLookupRequest, NAICLookupResponse, HealthCheckResponse,
    ErrorResponse, BatchParseRequest, BatchParseResponse,
    ValidationRequest, ValidationResponse, ServiceMetrics
)
from .services.doc_ai_client import DocumentAIClient, ExtractedPolicy
from .services.naic_mapper import NA<PERSON><PERSON>ap<PERSON>, NAICMatch
from .middleware.auth_middleware import (
    get_auth_context, get_service_auth_context, AuthContext,
    TenantIsolationMiddleware, RateLimitMiddleware
)

# Configure structured logging
structlog.configure(
    processors=[
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.ConsoleRenderer()
    ],
    wrapper_class=structlog.make_filtering_bound_logger(20),  # INFO level
    logger_factory=structlog.PrintLoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)
settings = get_settings()


# Global clients
doc_ai_client = None
storage_client = None
rate_limiter = RateLimitMiddleware(requests_per_minute=100)
security = HTTPBearer()

# Metrics tracking
service_metrics = {
    "total_requests": 0,
    "successful_parses": 0,
    "failed_parses": 0,
    "start_time": time.time(),
    "document_ai_calls": 0,
    "database_queries": 0
}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global doc_ai_client, storage_client
    
    logger.info("Starting insurance-parse service", version=settings.app_version)
    
    # Initialize clients
    doc_ai_client = DocumentAIClient(settings.doc_ai_processor)
    storage_client = storage.Client(project=settings.gcp_project)
    
    logger.info("Service initialized successfully")
    
    yield
    
    logger.info("Shutting down insurance-parse service")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Microservice for parsing insurance declaration pages using Document AI",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Dependency injection
async def get_db_session() -> AsyncSession:
    """Get database session."""
    # This would typically use a connection pool
    # For now, return a mock session
    pass


async def get_naic_mapper() -> NAICMapper:
    """Get NAIC mapper instance."""
    db_session = await get_db_session()
    return NAICMapper(db_session)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            error_code=f"HTTP_{exc.status_code}",
            details={"path": str(request.url.path)}
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error("Unhandled exception", error=str(exc), path=request.url.path)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            error_code="INTERNAL_ERROR",
            details={"path": str(request.url.path)}
        ).dict()
    )


# API Routes

@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint."""
    dependencies = {}
    
    # Check Document AI availability
    try:
        # Simple check - would normally ping the service
        dependencies["document_ai"] = "healthy"
    except Exception as e:
        dependencies["document_ai"] = f"unhealthy: {str(e)}"
    
    # Check storage availability
    try:
        dependencies["cloud_storage"] = "healthy"
    except Exception as e:
        dependencies["cloud_storage"] = f"unhealthy: {str(e)}"
    
    return HealthCheckResponse(
        status="healthy" if all(status == "healthy" for status in dependencies.values()) else "unhealthy",
        version=settings.app_version,
        dependencies=dependencies
    )


@app.post("/v1/insurance/decpage:parse", response_model=ParseDecPageResponse)
async def parse_dec_page(
    request: ParseDecPageRequest,
    background_tasks: BackgroundTasks,
    auth_context: AuthContext = Depends(get_auth_context)
):
    """
    Parse insurance declaration page and extract policy information.
    """
    start_time = time.time()
    service_metrics["total_requests"] += 1
    
    # Validate tenant access
    if request.tenant_id:
        TenantIsolationMiddleware.validate_tenant_access(auth_context, request.tenant_id)
    
    # Apply rate limiting
    client_ip = "unknown"  # Would get from request in production
    await rate_limiter.check_rate_limit(client_ip, str(auth_context.user_id))
    
    # Sanitized logging (no PHI)
    logger.info(
        "Starting dec page parse",
        matter_id=str(request.matter_id),
        document_id=str(request.document_id),
        tenant_id=str(auth_context.tenant_id),
        user_id=str(auth_context.user_id)
    )
    
    try:
        # Load document from Cloud Storage
        document_content = await load_document_from_storage(request.document_id)
        
        if not document_content:
            raise HTTPException(
                status_code=404,
                detail=f"Document {request.document_id} not found"
            )
        
        # Parse with Document AI
        service_metrics["document_ai_calls"] += 1
        extracted_policy = await doc_ai_client.parse_dec_page(document_content)
        
        # Map insurer to NAIC code if possible
        if extracted_policy.insurer_name:
            naic_mapper = await get_naic_mapper()
            naic_match = await naic_mapper.map_insurer_name(extracted_policy.insurer_name)
            if naic_match:
                extracted_policy.insurer_naic_code = naic_match.naic_code
        
        # Convert to response model
        policy_data = convert_extracted_to_response(extracted_policy)
        
        processing_time = int((time.time() - start_time) * 1000)
        service_metrics["successful_parses"] += 1
        
        # Background task to save policy to database
        background_tasks.add_task(
            save_policy_to_database,
            policy_data,
            request.matter_id,
            request.tenant_id
        )
        
        logger.info(
            "Dec page parse completed",
            matter_id=str(request.matter_id),
            confidence=policy_data.parse_confidence,
            processing_time_ms=processing_time
        )
        
        return ParseDecPageResponse(
            success=True,
            policy=policy_data,
            confidence=policy_data.parse_confidence,
            processing_time_ms=processing_time,
            errors=[],
            warnings=[]
        )
        
    except Exception as e:
        service_metrics["failed_parses"] += 1
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.error(
            "Dec page parse failed",
            matter_id=str(request.matter_id),
            error=str(e),
            processing_time_ms=processing_time
        )
        
        return ParseDecPageResponse(
            success=False,
            policy=None,
            confidence=0.0,
            processing_time_ms=processing_time,
            errors=[str(e)],
            warnings=[]
        )


@app.post("/v1/insurance/decpage:batch", response_model=BatchParseResponse)
async def batch_parse_dec_pages(
    request: BatchParseRequest,
    background_tasks: BackgroundTasks
):
    """
    Parse multiple declaration pages in batch.
    """
    start_time = time.time()
    
    logger.info("Starting batch parse", count=len(request.requests))
    
    results = []
    successful_count = 0
    
    # Process each request
    for parse_request in request.requests:
        try:
            response = await parse_dec_page(parse_request, background_tasks)
            results.append(response)
            if response.success:
                successful_count += 1
        except Exception as e:
            results.append(ParseDecPageResponse(
                success=False,
                policy=None,
                confidence=0.0,
                processing_time_ms=0,
                errors=[str(e)],
                warnings=[]
            ))
    
    processing_time = int((time.time() - start_time) * 1000)
    
    return BatchParseResponse(
        results=results,
        total_processed=len(request.requests),
        total_successful=successful_count,
        processing_time_ms=processing_time
    )


@app.post("/v1/insurance/naic:lookup", response_model=NAICLookupResponse)
async def lookup_naic_code(request: NAICLookupRequest):
    """
    Look up NAIC code for insurance company name.
    """
    start_time = time.time()
    
    logger.info("NAIC lookup request", insurer_name=request.insurer_name)
    
    try:
        naic_mapper = await get_naic_mapper()
        
        if request.fuzzy_match:
            # Get single best match
            match = await naic_mapper.map_insurer_name(request.insurer_name)
            matches = [match] if match else []
        else:
            # Get multiple possible matches
            matches = await naic_mapper.search_insurers(request.insurer_name, limit=5)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        return NAICLookupResponse(
            success=True,
            matches=[
                NAICMatch(
                    naic_code=match.naic_code,
                    company_name=match.company_name,
                    confidence=match.confidence,
                    match_type=match.match_type
                )
                for match in matches
            ],
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.error("NAIC lookup failed", error=str(e))
        
        return NAICLookupResponse(
            success=False,
            matches=[],
            processing_time_ms=processing_time
        )


@app.post("/v1/insurance/policy:validate", response_model=ValidationResponse)
async def validate_policy_data(request: ValidationRequest):
    """
    Validate parsed policy data for quality and completeness.
    """
    validation_results = []
    
    # Validate policy number
    if request.policy_data.policy_number:
        if len(request.policy_data.policy_number) >= 6:
            validation_results.append(ValidationResult(
                field="policy_number",
                is_valid=True,
                message="Valid policy number format",
                severity="info"
            ))
        else:
            validation_results.append(ValidationResult(
                field="policy_number",
                is_valid=False,
                message="Policy number too short",
                severity="warning"
            ))
    else:
        validation_results.append(ValidationResult(
            field="policy_number",
            is_valid=False,
            message="Policy number missing",
            severity="error"
        ))
    
    # Validate dates
    if request.policy_data.effective_date and request.policy_data.expiration_date:
        validation_results.append(ValidationResult(
            field="dates",
            is_valid=True,
            message="Policy dates present",
            severity="info"
        ))
    else:
        validation_results.append(ValidationResult(
            field="dates",
            is_valid=False,
            message="Missing policy dates",
            severity="warning"
        ))
    
    # Validate insurer
    if request.policy_data.insurer_name:
        validation_results.append(ValidationResult(
            field="insurer_name",
            is_valid=True,
            message="Insurer name present",
            severity="info"
        ))
    else:
        validation_results.append(ValidationResult(
            field="insurer_name",
            is_valid=False,
            message="Insurer name missing",
            severity="error"
        ))
    
    # Validate coverages
    if request.policy_data.coverages:
        validation_results.append(ValidationResult(
            field="coverages",
            is_valid=True,
            message=f"{len(request.policy_data.coverages)} coverages found",
            severity="info"
        ))
    else:
        validation_results.append(ValidationResult(
            field="coverages",
            is_valid=False,
            message="No coverage information found",
            severity="error"
        ))
    
    # Calculate overall validity and score
    valid_count = sum(1 for r in validation_results if r.is_valid)
    total_count = len(validation_results)
    score = valid_count / total_count if total_count > 0 else 0.0
    is_valid = all(r.severity != "error" or r.is_valid for r in validation_results)
    
    return ValidationResponse(
        is_valid=is_valid,
        results=validation_results,
        score=score
    )


@app.get("/metrics", response_model=ServiceMetrics)
async def get_service_metrics():
    """
    Get service metrics for monitoring.
    """
    uptime = int(time.time() - service_metrics["start_time"])
    
    parse_metrics = {
        "total_requests": service_metrics["total_requests"],
        "successful_parses": service_metrics["successful_parses"],
        "failed_parses": service_metrics["failed_parses"],
        "average_confidence": 0.85,  # Would calculate from actual data
        "average_processing_time_ms": 5000.0,  # Would calculate from actual data
        "coverage_extraction_rate": 0.75  # Would calculate from actual data
    }
    
    error_rate = (
        service_metrics["failed_parses"] / service_metrics["total_requests"]
        if service_metrics["total_requests"] > 0 else 0.0
    )
    
    return ServiceMetrics(
        uptime_seconds=uptime,
        parse_metrics=parse_metrics,
        document_ai_calls=service_metrics["document_ai_calls"],
        database_queries=service_metrics["database_queries"],
        error_rate=error_rate
    )


# Helper functions

async def load_document_from_storage(document_id: UUID) -> bytes:
    """Load document content from Cloud Storage."""
    try:
        bucket_name = settings.storage_bucket or f"{settings.gcp_project}-documents"
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(f"documents/{document_id}.pdf")
        
        if not blob.exists():
            return None
        
        return blob.download_as_bytes()
        
    except Exception as e:
        logger.error("Failed to load document", document_id=str(document_id), error=str(e))
        return None


def convert_extracted_to_response(extracted: ExtractedPolicy) -> ParsedPolicyData:
    """Convert extracted policy to response model."""
    coverages = []
    
    for coverage in extracted.coverages:
        coverages.append({
            "coverage_type": coverage.coverage_type,
            "per_person_limit": coverage.per_person_limit,
            "per_occurrence_limit": coverage.per_occurrence_limit,
            "deductible": coverage.deductible,
            "confidence": coverage.confidence
        })
    
    return ParsedPolicyData(
        policy_number=extracted.policy_number,
        effective_date=extracted.effective_date,
        expiration_date=extracted.expiration_date,
        insurer_name=extracted.insurer_name,
        named_insured=extracted.named_insured,
        insurer_naic_code=extracted.insurer_naic_code,
        coverages=coverages,
        parse_confidence=extracted.parse_confidence,
        parsing_metadata={
            "raw_text_length": len(extracted.raw_text),
            "extraction_timestamp": datetime.utcnow().isoformat()
        }
    )


async def save_policy_to_database(
    policy_data: ParsedPolicyData,
    matter_id: UUID,
    tenant_id: UUID
):
    """Background task to save policy to database."""
    try:
        # This would save to the main database via API call
        logger.info(
            "Policy saved to database",
            matter_id=str(matter_id),
            policy_number=policy_data.policy_number
        )
        service_metrics["database_queries"] += 1
    except Exception as e:
        logger.error("Failed to save policy", error=str(e))


if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        log_level=settings.log_level.lower(),
        access_log=True
    )