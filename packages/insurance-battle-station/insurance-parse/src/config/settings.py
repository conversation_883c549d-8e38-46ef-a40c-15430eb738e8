"""
Configuration settings for insurance-parse microservice.
"""

import os
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    app_name: str = "Insurance Parse Service"
    app_version: str = "1.0.0"
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Google Cloud settings
    gcp_project: str = Field(..., env="GCP_PROJECT")
    doc_ai_processor: str = Field(..., env="DOC_AI_PROCESSOR")
    storage_bucket: Optional[str] = Field(None, env="STORAGE_BUCKET")
    
    # Database settings
    database_url: str = Field(..., env="DATABASE_URL")
    
    # External service URLs
    core_api_url: str = Field(..., env="CORE_API_URL")
    
    # Service authentication
    service_account_path: Optional[str] = Field(None, env="GOOGLE_APPLICATION_CREDENTIALS")
    
    # API settings
    cors_origins: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="CORS_ORIGINS"
    )
    
    # Processing limits
    max_file_size_mb: int = Field(default=50, env="MAX_FILE_SIZE_MB")
    parse_timeout_seconds: int = Field(default=120, env="PARSE_TIMEOUT_SECONDS")
    
    # Document AI settings
    doc_ai_location: str = Field(default="us", env="DOC_AI_LOCATION")
    confidence_threshold: float = Field(default=0.7, env="CONFIDENCE_THRESHOLD")
    
    # NAIC data settings
    naic_data_update_interval_hours: int = Field(default=24, env="NAIC_UPDATE_INTERVAL")
    
    # Retry settings
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    retry_delay_seconds: int = Field(default=1, env="RETRY_DELAY")
    
    # Monitoring and observability
    enable_tracing: bool = Field(default=True, env="ENABLE_TRACING")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings