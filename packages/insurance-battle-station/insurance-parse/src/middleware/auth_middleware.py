"""
Authentication and Authorization Middleware for Insurance Parse Service.
"""

import jwt
from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

import structlog
from fastapi import HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

logger = structlog.get_logger(__name__)


class AuthContext(BaseModel):
    """Authentication context for requests."""
    user_id: UUID
    tenant_id: UUID
    email: str
    roles: list[str]
    is_service_account: bool = False


class AuthMiddleware:
    """Middleware for handling authentication and authorization."""
    
    def __init__(self, jwt_secret: str, jwt_algorithm: str = "HS256"):
        self.jwt_secret = jwt_secret
        self.jwt_algorithm = jwt_algorithm
        self.security = HTTPBearer()

    async def verify_token(self, credentials: HTTPAuthorizationCredentials) -> AuthContext:
        """
        Verify JW<PERSON> token and extract user information.
        
        Args:
            credentials: Bearer token credentials
            
        Returns:
            AuthContext with user information
            
        Raises:
            HTTPException: If token is invalid or expired
        """
        try:
            # Decode JWT token
            payload = jwt.decode(
                credentials.credentials,
                self.jwt_secret,
                algorithms=[self.jwt_algorithm],
                options={"verify_exp": True}
            )
            
            # Extract required fields
            user_id = payload.get("sub")
            tenant_id = payload.get("tenant_id")
            email = payload.get("email")
            roles = payload.get("roles", [])
            
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing user ID"
                )
            
            if not tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, 
                    detail="Invalid token: missing tenant ID"
                )
            
            # Check if token is expired
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token expired"
                )
            
            # Check if this is a service account token
            is_service_account = "service" in roles or payload.get("iss") == "insurance-service"
            
            return AuthContext(
                user_id=UUID(user_id),
                tenant_id=UUID(tenant_id),
                email=email or "",
                roles=roles,
                is_service_account=is_service_account
            )
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        except jwt.InvalidTokenError as e:
            logger.warning("Invalid JWT token", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except ValueError as e:
            logger.warning("Invalid UUID in token", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token format"
            )

    def verify_service_access(self, auth_context: AuthContext, required_permissions: list[str] = None) -> None:
        """
        Verify that the authenticated user has access to insurance service.
        
        Args:
            auth_context: Authentication context
            required_permissions: List of required permissions
            
        Raises:
            HTTPException: If user lacks required permissions
        """
        # Service accounts have full access
        if auth_context.is_service_account:
            return
        
        # Check for required roles
        required_roles = {"insurance_user", "admin"}
        user_roles = set(auth_context.roles)
        
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions for insurance operations"
            )
        
        # Check specific permissions if provided
        if required_permissions:
            # This would integrate with a more sophisticated RBAC system
            if "admin" not in user_roles and "insurance_admin" not in user_roles:
                for perm in required_permissions:
                    if perm not in user_roles:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail=f"Missing required permission: {perm}"
                        )

    async def authenticate_request(self, request: Request) -> AuthContext:
        """
        Authenticate incoming request and return auth context.
        
        Args:
            request: FastAPI request object
            
        Returns:
            AuthContext with user information
        """
        # Extract authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing authorization header",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Parse Bearer token
        try:
            scheme, token = auth_header.split(" ", 1)
            if scheme.lower() != "bearer":
                raise ValueError("Invalid scheme")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header format",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Create credentials object
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials=token
        )
        
        # Verify token and get auth context
        auth_context = await self.verify_token(credentials)
        
        # Log authentication for audit trail (without PHI)
        logger.info(
            "Request authenticated",
            user_id=str(auth_context.user_id),
            tenant_id=str(auth_context.tenant_id),
            roles=auth_context.roles,
            is_service_account=auth_context.is_service_account,
            endpoint=str(request.url.path),
            method=request.method,
            ip_address=request.client.host if request.client else None
        )
        
        return auth_context


# Dependency for FastAPI
async def get_auth_context(request: Request) -> AuthContext:
    """
    FastAPI dependency to get authentication context.
    
    Args:
        request: FastAPI request object
        
    Returns:
        AuthContext with user information
    """
    # This would be configured with the actual JWT secret
    auth_middleware = AuthMiddleware(jwt_secret="your-jwt-secret-here")
    return await auth_middleware.authenticate_request(request)


async def get_service_auth_context(request: Request) -> AuthContext:
    """
    FastAPI dependency for service-to-service authentication.
    
    Args:
        request: FastAPI request object
        
    Returns:
        AuthContext with service account information
    """
    auth_context = await get_auth_context(request)
    
    # Verify service account access
    if not auth_context.is_service_account:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Service account access required"
        )
    
    return auth_context


class TenantIsolationMiddleware:
    """Middleware to enforce tenant isolation."""
    
    @staticmethod
    def validate_tenant_access(auth_context: AuthContext, resource_tenant_id: UUID) -> None:
        """
        Validate that user has access to tenant resource.
        
        Args:
            auth_context: Authentication context
            resource_tenant_id: Tenant ID of the resource being accessed
            
        Raises:
            HTTPException: If tenant access is not allowed
        """
        # Service accounts can access all tenants
        if auth_context.is_service_account:
            return
        
        # Admins can access all tenants
        if "admin" in auth_context.roles:
            return
        
        # Users can only access their own tenant
        if auth_context.tenant_id != resource_tenant_id:
            logger.warning(
                "Tenant access violation attempted",
                user_id=str(auth_context.user_id),
                user_tenant=str(auth_context.tenant_id),
                requested_tenant=str(resource_tenant_id)
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: tenant mismatch"
            )


def require_permissions(*permissions: str):
    """
    Decorator to require specific permissions for an endpoint.
    
    Args:
        permissions: Required permissions
    """
    def decorator(func):
        async def wrapper(*args, auth_context: AuthContext = None, **kwargs):
            if auth_context:
                auth_middleware = AuthMiddleware(jwt_secret="your-jwt-secret-here")
                auth_middleware.verify_service_access(auth_context, list(permissions))
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# Rate limiting for security
class RateLimitMiddleware:
    """Simple rate limiting middleware."""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # In production, use Redis
    
    async def check_rate_limit(self, client_ip: str, user_id: str) -> None:
        """
        Check if request should be rate limited.
        
        Args:
            client_ip: Client IP address
            user_id: User ID for rate limiting
            
        Raises:
            HTTPException: If rate limit exceeded
        """
        # Simple in-memory rate limiting (use Redis in production)
        key = f"{client_ip}:{user_id}"
        current_time = datetime.now().minute
        
        if key not in self.request_counts:
            self.request_counts[key] = {"count": 0, "minute": current_time}
        
        request_data = self.request_counts[key]
        
        # Reset count if we're in a new minute
        if request_data["minute"] != current_time:
            request_data["count"] = 0
            request_data["minute"] = current_time
        
        request_data["count"] += 1
        
        if request_data["count"] > self.requests_per_minute:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                user_id=user_id,
                requests=request_data["count"]
            )
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )