"""
Pydantic models for the insurance-parse microservice.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field, validator


class CoverageLimitParsed(BaseModel):
    """Parsed coverage limit data."""
    coverage_type: str = Field(..., description="Coverage type (BI, UM, PD, etc.)")
    per_person_limit: Optional[Decimal] = Field(None, ge=0, description="Per person limit")
    per_occurrence_limit: Optional[Decimal] = Field(None, ge=0, description="Per occurrence limit")
    deductible: Optional[Decimal] = Field(None, ge=0, description="Deductible amount")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score for this coverage")


class ParseDecPageRequest(BaseModel):
    """Request to parse insurance declaration page."""
    matter_id: UUID = Field(..., description="Matter ID this policy belongs to")
    document_id: UUID = Field(..., description="Document ID in object storage")
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID for isolation")


class ParsedPolicyData(BaseModel):
    """Parsed insurance policy data from dec page."""
    policy_number: Optional[str] = Field(None, description="Policy number")
    effective_date: Optional[str] = Field(None, description="Policy effective date")
    expiration_date: Optional[str] = Field(None, description="Policy expiration date")
    insurer_name: Optional[str] = Field(None, description="Insurance company name")
    named_insured: Optional[str] = Field(None, description="Named insured")
    insurer_naic_code: Optional[str] = Field(None, description="NAIC code if found/mapped")
    coverages: List[CoverageLimitParsed] = Field(default_factory=list, description="Coverage limits")
    parse_confidence: float = Field(..., ge=0, le=1, description="Overall parse confidence")
    parsing_metadata: Dict[str, Any] = Field(default_factory=dict, description="Parsing metadata")


class ParseDecPageResponse(BaseModel):
    """Response from dec page parsing."""
    success: bool = Field(..., description="Whether parsing succeeded")
    policy: Optional[ParsedPolicyData] = Field(None, description="Parsed policy data")
    confidence: float = Field(..., ge=0, le=1, description="Overall confidence score")
    processing_time_ms: int = Field(..., description="Processing time in milliseconds")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")
    warnings: List[str] = Field(default_factory=list, description="Any warnings")


class NAICLookupRequest(BaseModel):
    """Request to lookup NAIC code for insurer."""
    insurer_name: str = Field(..., min_length=1, description="Insurance company name")
    fuzzy_match: bool = Field(default=True, description="Allow fuzzy matching")


class NAICMatch(BaseModel):
    """NAIC lookup match result."""
    naic_code: str = Field(..., description="NAIC code")
    company_name: str = Field(..., description="Official company name")
    confidence: float = Field(..., ge=0, le=1, description="Match confidence")
    match_type: str = Field(..., description="Type of match: exact, fuzzy, alias")


class NAICLookupResponse(BaseModel):
    """Response from NAIC lookup."""
    success: bool = Field(..., description="Whether lookup succeeded")
    matches: List[NAICMatch] = Field(default_factory=list, description="Matching insurers")
    processing_time_ms: int = Field(..., description="Processing time in milliseconds")


class HealthCheckResponse(BaseModel):
    """Health check response."""
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")
    version: str = Field(..., description="Service version")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="Dependency status")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


class BatchParseRequest(BaseModel):
    """Request to parse multiple dec pages in batch."""
    requests: List[ParseDecPageRequest] = Field(
        ..., 
        min_items=1, 
        max_items=10, 
        description="Batch of parse requests"
    )


class BatchParseResponse(BaseModel):
    """Response from batch parsing."""
    results: List[ParseDecPageResponse] = Field(..., description="Parse results")
    total_processed: int = Field(..., description="Number of documents processed")
    total_successful: int = Field(..., description="Number of successful parses")
    processing_time_ms: int = Field(..., description="Total processing time")


class ValidationRequest(BaseModel):
    """Request to validate parsed policy data."""
    policy_data: ParsedPolicyData = Field(..., description="Policy data to validate")


class ValidationResult(BaseModel):
    """Validation result for policy data."""
    field: str = Field(..., description="Field name")
    is_valid: bool = Field(..., description="Whether field is valid")
    message: Optional[str] = Field(None, description="Validation message")
    severity: str = Field(..., description="Severity: error, warning, info")


class ValidationResponse(BaseModel):
    """Response from policy validation."""
    is_valid: bool = Field(..., description="Overall validation status")
    results: List[ValidationResult] = Field(..., description="Field-level validation results")
    score: float = Field(..., ge=0, le=1, description="Overall quality score")


# Metrics and monitoring models

class ParseMetrics(BaseModel):
    """Parsing metrics for monitoring."""
    total_requests: int = Field(..., description="Total parse requests")
    successful_parses: int = Field(..., description="Successful parses")
    failed_parses: int = Field(..., description="Failed parses")
    average_confidence: float = Field(..., description="Average parse confidence")
    average_processing_time_ms: float = Field(..., description="Average processing time")
    coverage_extraction_rate: float = Field(..., description="Rate of coverage extraction")


class ServiceMetrics(BaseModel):
    """Overall service metrics."""
    uptime_seconds: int = Field(..., description="Service uptime")
    parse_metrics: ParseMetrics = Field(..., description="Parsing metrics")
    document_ai_calls: int = Field(..., description="Document AI API calls")
    database_queries: int = Field(..., description="Database queries made")
    error_rate: float = Field(..., description="Error rate percentage")


# Configuration models

class ProcessingConfig(BaseModel):
    """Configuration for document processing."""
    max_file_size_mb: int = Field(default=50, description="Maximum file size")
    timeout_seconds: int = Field(default=120, description="Processing timeout")
    confidence_threshold: float = Field(default=0.7, description="Minimum confidence threshold")
    enable_ocr: bool = Field(default=True, description="Enable OCR processing")
    enable_naic_mapping: bool = Field(default=True, description="Enable NAIC code mapping")


# Testing and development models

class TestParseRequest(BaseModel):
    """Test parsing request with sample data."""
    sample_type: str = Field(..., description="Type of sample document")
    expected_policy_number: Optional[str] = Field(None, description="Expected policy number")
    expected_insurer: Optional[str] = Field(None, description="Expected insurer name")


class TestParseResponse(BaseModel):
    """Test parsing response."""
    parse_result: ParseDecPageResponse = Field(..., description="Parse result")
    validation_result: ValidationResponse = Field(..., description="Validation result")
    matches_expected: bool = Field(..., description="Whether result matches expectations")
    test_score: float = Field(..., ge=0, le=1, description="Test score")