"""
NAIC Code Mapping Service

This service maps insurance company names to their NAIC codes using
fuzzy matching and a comprehensive database of insurance carriers.
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from difflib import SequenceMatcher
from dataclasses import dataclass

import structlog
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

logger = structlog.get_logger(__name__)


@dataclass
class NAICMatch:
    """NAIC mapping match result."""
    naic_code: str
    company_name: str
    confidence: float
    match_type: str  # 'exact', 'fuzzy', 'alias'


class NAICMapper:
    """Service for mapping insurer names to NAIC codes."""

    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.logger = logger.bind(service="naic_mapper")
        
        # Common aliases and variations
        self.aliases = {
            'state farm': ['state farm mutual automobile insurance company', 'state farm fire and casualty company'],
            'geico': ['geico general insurance company', 'government employees insurance company'],
            'progressive': ['progressive casualty insurance company', 'progressive specialty insurance company'],
            'allstate': ['allstate fire and casualty insurance company', 'allstate insurance company'],
            'farmers': ['farmers insurance exchange', 'farmers group incorporated'],
            'usaa': ['usaa casualty insurance company', 'united services automobile association'],
            'liberty mutual': ['liberty mutual fire insurance company', 'liberty mutual insurance company'],
            'nationwide': ['nationwide mutual insurance company', 'nationwide general insurance company'],
            'american family': ['american family mutual insurance company'],
            'travelers': ['travelers casualty and surety company', 'travelers indemnity company'],
        }
        
        # Common company suffixes to normalize
        self.company_suffixes = [
            'insurance company',
            'mutual insurance company', 
            'fire and casualty insurance company',
            'casualty insurance company',
            'general insurance company',
            'indemnity company',
            'insurance exchange',
            'mutual company',
            'inc',
            'incorporated',
            'llc',
            'limited liability company'
        ]

    async def map_insurer_name(self, insurer_name: str) -> Optional[NAICMatch]:
        """
        Map insurer name to NAIC code with confidence scoring.
        
        Args:
            insurer_name: Insurance company name to map
            
        Returns:
            NAICMatch with best match or None if no good match found
        """
        if not insurer_name or len(insurer_name.strip()) < 3:
            return None
        
        self.logger.info("Mapping insurer name", insurer_name=insurer_name)
        
        # Normalize the input name
        normalized_name = self._normalize_company_name(insurer_name)
        
        # Try exact match first
        exact_match = await self._find_exact_match(normalized_name)
        if exact_match:
            return exact_match
        
        # Try alias matching
        alias_match = await self._find_alias_match(normalized_name)
        if alias_match:
            return alias_match
        
        # Try fuzzy matching
        fuzzy_match = await self._find_fuzzy_match(normalized_name)
        if fuzzy_match and fuzzy_match.confidence >= 0.8:
            return fuzzy_match
        
        self.logger.warning("No good NAIC match found", insurer_name=insurer_name)
        return None

    async def _find_exact_match(self, normalized_name: str) -> Optional[NAICMatch]:
        """Find exact match in database."""
        from ...backend.models.insurance import InsurerORM
        
        query = select(InsurerORM).where(
            func.lower(InsurerORM.name) == normalized_name.lower()
        )
        
        result = await self.db.execute(query)
        insurer = result.scalar_one_or_none()
        
        if insurer and insurer.naic_code:
            return NAICMatch(
                naic_code=insurer.naic_code,
                company_name=insurer.name,
                confidence=1.0,
                match_type='exact'
            )
        
        return None

    async def _find_alias_match(self, normalized_name: str) -> Optional[NAICMatch]:
        """Find match using common aliases."""
        # Check if input matches any known aliases
        for short_name, full_names in self.aliases.items():
            if short_name in normalized_name.lower():
                # Look for the full company names in database
                for full_name in full_names:
                    exact_match = await self._find_exact_match(full_name)
                    if exact_match:
                        return NAICMatch(
                            naic_code=exact_match.naic_code,
                            company_name=exact_match.company_name,
                            confidence=0.95,
                            match_type='alias'
                        )
        
        return None

    async def _find_fuzzy_match(self, normalized_name: str) -> Optional[NAICMatch]:
        """Find best fuzzy match using string similarity."""
        from ...backend.models.insurance import InsurerORM
        
        # Get all insurers with NAIC codes
        query = select(InsurerORM).where(InsurerORM.naic_code.isnot(None))
        result = await self.db.execute(query)
        insurers = result.scalars().all()
        
        best_match = None
        best_confidence = 0.0
        
        for insurer in insurers:
            # Calculate similarity with main name
            confidence = self._calculate_similarity(normalized_name, insurer.name)
            
            if confidence > best_confidence:
                best_confidence = confidence
                best_match = insurer
        
        if best_match and best_confidence >= 0.7:
            return NAICMatch(
                naic_code=best_match.naic_code,
                company_name=best_match.name,
                confidence=best_confidence,
                match_type='fuzzy'
            )
        
        return None

    def _normalize_company_name(self, name: str) -> str:
        """Normalize company name for better matching."""
        if not name:
            return ""
        
        # Convert to lowercase
        normalized = name.lower().strip()
        
        # Remove common punctuation
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Remove common suffixes
        for suffix in self.company_suffixes:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
                break
        
        return normalized

    def _calculate_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two company names."""
        # Normalize both names
        norm1 = self._normalize_company_name(name1)
        norm2 = self._normalize_company_name(name2)
        
        # Use SequenceMatcher for basic similarity
        basic_similarity = SequenceMatcher(None, norm1, norm2).ratio()
        
        # Boost score for key word matches
        words1 = set(norm1.split())
        words2 = set(norm2.split())
        
        # Remove common words
        common_words = {'insurance', 'company', 'mutual', 'general', 'casualty', 'fire', 'auto'}
        words1 -= common_words
        words2 -= common_words
        
        if words1 and words2:
            word_overlap = len(words1 & words2) / max(len(words1), len(words2))
            # Weighted combination
            similarity = (basic_similarity * 0.6) + (word_overlap * 0.4)
        else:
            similarity = basic_similarity
        
        return min(similarity, 1.0)

    async def bulk_map_insurers(self, insurer_names: List[str]) -> Dict[str, Optional[NAICMatch]]:
        """
        Map multiple insurer names in bulk for efficiency.
        
        Args:
            insurer_names: List of insurer names to map
            
        Returns:
            Dictionary mapping input names to NAIC matches
        """
        results = {}
        
        for name in insurer_names:
            results[name] = await self.map_insurer_name(name)
        
        return results

    async def search_insurers(self, query: str, limit: int = 10) -> List[NAICMatch]:
        """
        Search for insurers by partial name match.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching insurers with scores
        """
        from ...backend.models.insurance import InsurerORM
        
        if len(query) < 2:
            return []
        
        # Use PostgreSQL similarity search
        query_obj = (
            select(InsurerORM)
            .where(
                func.similarity(InsurerORM.name, query) > 0.3
            )
            .order_by(func.similarity(InsurerORM.name, query).desc())
            .limit(limit)
        )
        
        result = await self.db.execute(query_obj)
        insurers = result.scalars().all()
        
        matches = []
        for insurer in insurers:
            if insurer.naic_code:
                confidence = self._calculate_similarity(query, insurer.name)
                matches.append(NAICMatch(
                    naic_code=insurer.naic_code,
                    company_name=insurer.name,
                    confidence=confidence,
                    match_type='search'
                ))
        
        return matches

    async def validate_naic_code(self, naic_code: str) -> bool:
        """
        Validate that a NAIC code exists in our database.
        
        Args:
            naic_code: NAIC code to validate
            
        Returns:
            True if valid, False otherwise
        """
        from ...backend.models.insurance import InsurerORM
        
        if not naic_code or not re.match(r'^\d{4,5}$', naic_code):
            return False
        
        query = select(InsurerORM).where(InsurerORM.naic_code == naic_code)
        result = await self.db.execute(query)
        
        return result.scalar_one_or_none() is not None

    async def get_insurer_by_naic(self, naic_code: str) -> Optional[Dict]:
        """
        Get insurer details by NAIC code.
        
        Args:
            naic_code: NAIC code to look up
            
        Returns:
            Insurer details or None if not found
        """
        from ...backend.models.insurance import InsurerORM
        
        query = select(InsurerORM).where(InsurerORM.naic_code == naic_code)
        result = await self.db.execute(query)
        insurer = result.scalar_one_or_none()
        
        if insurer:
            return {
                'id': str(insurer.id),
                'name': insurer.name,
                'naic_code': insurer.naic_code,
                'common_addresses': insurer.common_addresses or [],
                'escalation_contacts': insurer.escalation_contacts or {}
            }
        
        return None

    def get_company_variations(self, company_name: str) -> List[str]:
        """
        Get common variations of a company name for better matching.
        
        Args:
            company_name: Base company name
            
        Returns:
            List of name variations
        """
        variations = [company_name]
        normalized = self._normalize_company_name(company_name)
        
        # Add normalized version
        if normalized != company_name.lower():
            variations.append(normalized)
        
        # Add versions with different suffixes
        base_name = normalized
        for suffix in self.company_suffixes[:5]:  # Use most common suffixes
            variations.append(f"{base_name} {suffix}")
        
        # Add abbreviated versions for long names
        if len(normalized.split()) > 3:
            words = normalized.split()
            # Use first and last words
            if len(words) >= 2:
                variations.append(f"{words[0]} {words[-1]}")
        
        return list(set(variations))  # Remove duplicates


# Utility functions for NAIC data management

def load_naic_seed_data() -> List[Dict]:
    """
    Load seed data for common insurance companies.
    This would typically come from a NAIC public data file.
    """
    return [
        {
            'name': 'State Farm Mutual Automobile Insurance Company',
            'naic_code': '25178',
            'common_addresses': ['One State Farm Plaza, Bloomington, IL 61710'],
        },
        {
            'name': 'GEICO General Insurance Company',
            'naic_code': '41491',
            'common_addresses': ['5260 Western Avenue, Chevy Chase, MD 20815'],
        },
        {
            'name': 'Progressive Casualty Insurance Company',
            'naic_code': '24260',
            'common_addresses': ['6300 Wilson Mills Road, Mayfield Village, OH 44143'],
        },
        {
            'name': 'Allstate Fire and Casualty Insurance Company',
            'naic_code': '19232',
            'common_addresses': ['2775 Sanders Road, Northbrook, IL 60062'],
        },
        {
            'name': 'USAA Casualty Insurance Company',
            'naic_code': '25941',
            'common_addresses': ['9800 Fredericksburg Road, San Antonio, TX 78288'],
        },
        {
            'name': 'Farmers Insurance Exchange',
            'naic_code': '21652',
            'common_addresses': ['6301 Owensmouth Avenue, Woodland Hills, CA 91367'],
        },
        {
            'name': 'Liberty Mutual Fire Insurance Company',
            'naic_code': '23035',
            'common_addresses': ['175 Berkeley Street, Boston, MA 02116'],
        },
        {
            'name': 'Nationwide Mutual Insurance Company',
            'naic_code': '23787',
            'common_addresses': ['One Nationwide Plaza, Columbus, OH 43215'],
        },
        {
            'name': 'American Family Mutual Insurance Company',
            'naic_code': '19275',
            'common_addresses': ['6000 American Parkway, Madison, WI 53783'],
        },
        {
            'name': 'Travelers Casualty and Surety Company',
            'naic_code': '19038',
            'common_addresses': ['One Tower Square, Hartford, CT 06183'],
        },
    ]


async def seed_naic_data(db_session: AsyncSession) -> int:
    """
    Seed database with NAIC data.
    
    Args:
        db_session: Database session
        
    Returns:
        Number of records created
    """
    from ...backend.models.insurance import InsurerORM
    
    seed_data = load_naic_seed_data()
    created_count = 0
    
    for data in seed_data:
        # Check if already exists
        query = select(InsurerORM).where(InsurerORM.naic_code == data['naic_code'])
        result = await db_session.execute(query)
        existing = result.scalar_one_or_none()
        
        if not existing:
            insurer = InsurerORM(**data)
            db_session.add(insurer)
            created_count += 1
    
    await db_session.commit()
    return created_count