"""
Document AI Client for parsing insurance declaration pages.

This module provides functionality to extract structured data from insurance
declaration pages using Google Cloud Document AI.
"""

import re
import logging
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from google.cloud import documentai
from google.api_core import retry
import structlog

from ..config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


@dataclass
class ExtractedCoverage:
    """Extracted coverage information."""
    coverage_type: str
    per_person_limit: Optional[Decimal]
    per_occurrence_limit: Optional[Decimal]
    deductible: Optional[Decimal]
    confidence: float


@dataclass
class ExtractedPolicy:
    """Extracted insurance policy information."""
    policy_number: Optional[str]
    effective_date: Optional[str]
    expiration_date: Optional[str]
    insurer_name: Optional[str]
    named_insured: Optional[str]
    insurer_naic_code: Optional[str]
    coverages: List[ExtractedCoverage]
    parse_confidence: float
    raw_text: str


class DocumentAIClient:
    """Client for Google Cloud Document AI operations."""

    def __init__(self, processor_name: str):
        """
        Initialize Document AI client.
        
        Args:
            processor_name: Full processor resource name
        """
        self.client = documentai.DocumentProcessorServiceClient()
        self.processor_name = processor_name
        self.logger = logger.bind(processor=processor_name)

    @retry.Retry()
    async def parse_dec_page(
        self, 
        document_content: bytes, 
        mime_type: str = "application/pdf"
    ) -> ExtractedPolicy:
        """
        Parse insurance declaration page using Document AI.
        
        Args:
            document_content: PDF document content as bytes
            mime_type: MIME type of the document
            
        Returns:
            ExtractedPolicy with parsed information
            
        Raises:
            ValueError: If input validation fails
            Exception: If parsing fails
        """
        # Input validation
        if not document_content:
            raise ValueError("Document content cannot be empty")
        
        if len(document_content) > 50 * 1024 * 1024:  # 50MB limit
            raise ValueError("Document size exceeds maximum allowed (50MB)")
        
        if mime_type not in ["application/pdf", "image/png", "image/jpeg"]:
            raise ValueError(f"Unsupported MIME type: {mime_type}")
        
        # Check if document appears to be a valid PDF
        if mime_type == "application/pdf" and not document_content.startswith(b"%PDF"):
            raise ValueError("Invalid PDF file format")
        
        self.logger.info("Starting dec page parsing", 
                        content_size=len(document_content),
                        mime_type=mime_type)
        
        try:
            # Create Document AI request
            request = documentai.ProcessRequest(
                name=self.processor_name,
                raw_document=documentai.RawDocument(
                    content=document_content,
                    mime_type=mime_type
                ),
                process_options=documentai.ProcessOptions(
                    ocr_config=documentai.OcrConfig(
                        enable_native_pdf_parsing=True,
                        enable_image_quality_scores=True,
                        premium_features=documentai.OcrConfig.PremiumFeatures(
                            enable_selection_mark_detection=True,
                            compute_style_info=True
                        )
                    )
                )
            )
            
            # Process document
            self.logger.info("Sending document to Document AI")
            result = self.client.process_document(request=request)
            document = result.document
            
            # Extract structured data
            extracted_policy = self._extract_insurance_data(document)
            
            self.logger.info(
                "Dec page parsing completed",
                confidence=extracted_policy.parse_confidence,
                coverages_found=len(extracted_policy.coverages),
                policy_number=extracted_policy.policy_number
            )
            
            return extracted_policy
            
        except Exception as e:
            self.logger.error("Dec page parsing failed", error=str(e))
            raise

    def _extract_insurance_data(self, document: documentai.Document) -> ExtractedPolicy:
        """
        Extract structured insurance data from Document AI response.
        
        Args:
            document: Processed document from Document AI
            
        Returns:
            ExtractedPolicy with extracted data
        """
        text = document.text
        
        # Extract basic policy information
        policy_number = self._extract_policy_number(text)
        effective_date, expiration_date = self._extract_dates(text)
        insurer_name = self._extract_insurer_name(text)
        named_insured = self._extract_named_insured(text)
        
        # Extract coverage information
        coverages = self._extract_coverages(text, document)
        
        # Calculate overall confidence
        parse_confidence = self._calculate_confidence(
            policy_number, effective_date, insurer_name, coverages
        )
        
        return ExtractedPolicy(
            policy_number=policy_number,
            effective_date=effective_date,
            expiration_date=expiration_date,
            insurer_name=insurer_name,
            named_insured=named_insured,
            insurer_naic_code=None,  # Will be mapped separately
            coverages=coverages,
            parse_confidence=parse_confidence,
            raw_text=text[:5000]  # Store first 5000 chars for debugging
        )

    def _extract_policy_number(self, text: str) -> Optional[str]:
        """Extract policy number from text."""
        patterns = [
            r'POLICY\s*(?:NO\.?|NUMBER)\s*:?\s*([A-Z0-9\-\s]{6,20})',
            r'Policy\s*#?\s*:?\s*([A-Z0-9\-\s]{6,20})',
            r'POL\.?\s*NO\.?\s*:?\s*([A-Z0-9\-\s]{6,20})',
            r'POLICY\s*([A-Z0-9\-\s]{6,20})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                policy_num = match.group(1).strip()
                # Clean up policy number
                policy_num = re.sub(r'\s+', '', policy_num)
                if len(policy_num) >= 6:
                    return policy_num
                    
        return None

    def _extract_dates(self, text: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract effective and expiration dates."""
        # Date patterns
        date_patterns = [
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'(\d{2,4}[\/\-]\d{1,2}[\/\-]\d{1,2})',
            r'([A-Za-z]{3}\s+\d{1,2},?\s+\d{2,4})',  # Mar 15, 2024
            r'(\d{1,2}\s+[A-Za-z]{3}\s+\d{2,4})',     # 15 Mar 2024
        ]
        
        dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            dates.extend(matches)
        
        if not dates:
            return None, None
        
        # Try to identify effective vs expiration dates
        effective_date = None
        expiration_date = None
        
        # Look for dates near "effective" or "period" keywords
        effective_pattern = r'(?:effective|period\s+from|from)\s*:?\s*([^\s]+\s*[\/\-]\s*[^\s]+\s*[\/\-]\s*[^\s]+)'
        expiration_pattern = r'(?:expir|to|until|through)\s*:?\s*([^\s]+\s*[\/\-]\s*[^\s]+\s*[\/\-]\s*[^\s]+)'
        
        effective_match = re.search(effective_pattern, text, re.IGNORECASE)
        expiration_match = re.search(expiration_pattern, text, re.IGNORECASE)
        
        if effective_match:
            effective_date = self._clean_date(effective_match.group(1))
        elif dates:
            effective_date = self._clean_date(dates[0])
            
        if expiration_match:
            expiration_date = self._clean_date(expiration_match.group(1))
        elif len(dates) > 1:
            expiration_date = self._clean_date(dates[1])
        
        return effective_date, expiration_date

    def _clean_date(self, date_str: str) -> str:
        """Clean and normalize date string."""
        # Remove extra whitespace and normalize separators
        date_str = re.sub(r'\s+', ' ', date_str.strip())
        date_str = re.sub(r'[\/\-]', '/', date_str)
        return date_str

    def _extract_insurer_name(self, text: str) -> Optional[str]:
        """Extract insurance company name."""
        # Common patterns for insurer names
        patterns = [
            r'(?:INSURANCE\s+COMPANY|MUTUAL|CASUALTY|INDEMNITY|GENERAL)\s*:?\s*([A-Z][A-Za-z\s&]+(?:INSURANCE|MUTUAL|CASUALTY|INDEMNITY|COMPANY))',
            r'COMPANY\s*:?\s*([A-Z][A-Za-z\s&]+(?:INSURANCE|MUTUAL|CASUALTY|INDEMNITY|COMPANY))',
            r'^([A-Z][A-Za-z\s&]+(?:INSURANCE|MUTUAL|CASUALTY|INDEMNITY|COMPANY))',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                # Clean up name
                name = re.sub(r'\s+', ' ', name)
                if len(name) > 5 and len(name) < 100:
                    return name.title()
        
        # Look for company names in the first few lines
        lines = text.split('\n')[:10]
        for line in lines:
            line = line.strip()
            if any(keyword in line.upper() for keyword in ['INSURANCE', 'MUTUAL', 'CASUALTY', 'INDEMNITY']):
                if len(line) > 5 and len(line) < 100:
                    return line.title()
        
        return None

    def _extract_named_insured(self, text: str) -> Optional[str]:
        """Extract named insured from text."""
        patterns = [
            r'NAMED\s+INSURED\s*:?\s*([A-Za-z\s,\.]+)',
            r'INSURED\s*:?\s*([A-Za-z\s,\.]+)',
            r'POLICYHOLDER\s*:?\s*([A-Za-z\s,\.]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                # Clean up name and check validity
                name = re.sub(r'\s+', ' ', name)
                if len(name) > 2 and len(name) < 100:
                    return name.title()
        
        return None

    def _extract_coverages(self, text: str, document: documentai.Document) -> List[ExtractedCoverage]:
        """Extract coverage limits from text and document structure."""
        coverages = []
        
        # Extract different coverage types
        coverage_extractors = [
            self._extract_bodily_injury,
            self._extract_uninsured_motorist,
            self._extract_underinsured_motorist,
            self._extract_medical_payments,
            self._extract_pip,
            self._extract_property_damage,
            self._extract_comprehensive,
            self._extract_collision
        ]
        
        for extractor in coverage_extractors:
            coverage = extractor(text)
            if coverage:
                coverages.append(coverage)
        
        # Try to extract from Document AI structured data if available
        if hasattr(document, 'entities') and document.entities:
            structured_coverages = self._extract_structured_coverages(document.entities)
            coverages.extend(structured_coverages)
        
        # Remove duplicates and merge similar coverages
        return self._deduplicate_coverages(coverages)

    def _extract_bodily_injury(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract bodily injury coverage."""
        patterns = [
            r'BODILY\s*INJURY.*?(\$[\d,]+)\s*(?:\/|each)\s*(\$[\d,]+)',
            r'BI.*?(\$[\d,]+)\s*\/\s*(\$[\d,]+)',
            r'LIABILITY.*?(\$[\d,]+)\s*\/\s*(\$[\d,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                per_person = self._parse_amount(match.group(1))
                per_occurrence = self._parse_amount(match.group(2))
                
                if per_person and per_occurrence:
                    return ExtractedCoverage(
                        coverage_type="BI",
                        per_person_limit=per_person,
                        per_occurrence_limit=per_occurrence,
                        deductible=None,
                        confidence=0.8
                    )
        
        return None

    def _extract_uninsured_motorist(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract uninsured motorist coverage."""
        patterns = [
            r'UNINSURED\s*MOTORIST.*?(\$[\d,]+)(?:\s*\/\s*(\$[\d,]+))?',
            r'UM(?:\s|$).*?(\$[\d,]+)(?:\s*\/\s*(\$[\d,]+))?',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                per_person = self._parse_amount(match.group(1))
                per_occurrence = self._parse_amount(match.group(2)) if match.group(2) else None
                
                return ExtractedCoverage(
                    coverage_type="UM",
                    per_person_limit=per_person,
                    per_occurrence_limit=per_occurrence,
                    deductible=None,
                    confidence=0.7
                )
        
        return None

    def _extract_underinsured_motorist(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract underinsured motorist coverage."""
        patterns = [
            r'UNDERINSURED\s*MOTORIST.*?(\$[\d,]+)(?:\s*\/\s*(\$[\d,]+))?',
            r'UIM(?:\s|$).*?(\$[\d,]+)(?:\s*\/\s*(\$[\d,]+))?',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                per_person = self._parse_amount(match.group(1))
                per_occurrence = self._parse_amount(match.group(2)) if match.group(2) else None
                
                return ExtractedCoverage(
                    coverage_type="UIM",
                    per_person_limit=per_person,
                    per_occurrence_limit=per_occurrence,
                    deductible=None,
                    confidence=0.7
                )
        
        return None

    def _extract_medical_payments(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract medical payments coverage."""
        patterns = [
            r'MEDICAL\s*PAYMENTS.*?(\$[\d,]+)',
            r'MED\s*PAY.*?(\$[\d,]+)',
            r'MEDPAY.*?(\$[\d,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                limit = self._parse_amount(match.group(1))
                
                return ExtractedCoverage(
                    coverage_type="MedPay",
                    per_person_limit=limit,
                    per_occurrence_limit=None,
                    deductible=None,
                    confidence=0.8
                )
        
        return None

    def _extract_pip(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract Personal Injury Protection coverage."""
        patterns = [
            r'PERSONAL\s*INJURY\s*PROTECTION.*?(\$[\d,]+)',
            r'PIP.*?(\$[\d,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                limit = self._parse_amount(match.group(1))
                
                return ExtractedCoverage(
                    coverage_type="PIP",
                    per_person_limit=limit,
                    per_occurrence_limit=None,
                    deductible=None,
                    confidence=0.8
                )
        
        return None

    def _extract_property_damage(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract property damage coverage."""
        patterns = [
            r'PROPERTY\s*DAMAGE.*?(\$[\d,]+)',
            r'PD.*?(\$[\d,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                limit = self._parse_amount(match.group(1))
                
                return ExtractedCoverage(
                    coverage_type="PD",
                    per_person_limit=None,
                    per_occurrence_limit=limit,
                    deductible=None,
                    confidence=0.8
                )
        
        return None

    def _extract_comprehensive(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract comprehensive coverage."""
        patterns = [
            r'COMPREHENSIVE.*?(?:DEDUCTIBLE|DED).*?(\$[\d,]+)',
            r'COMP.*?(?:DEDUCTIBLE|DED).*?(\$[\d,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                deductible = self._parse_amount(match.group(1))
                
                return ExtractedCoverage(
                    coverage_type="COMP",
                    per_person_limit=None,
                    per_occurrence_limit=None,
                    deductible=deductible,
                    confidence=0.7
                )
        
        return None

    def _extract_collision(self, text: str) -> Optional[ExtractedCoverage]:
        """Extract collision coverage."""
        patterns = [
            r'COLLISION.*?(?:DEDUCTIBLE|DED).*?(\$[\d,]+)',
            r'COLL.*?(?:DEDUCTIBLE|DED).*?(\$[\d,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                deductible = self._parse_amount(match.group(1))
                
                return ExtractedCoverage(
                    coverage_type="COLL",
                    per_person_limit=None,
                    per_occurrence_limit=None,
                    deductible=deductible,
                    confidence=0.7
                )
        
        return None

    def _extract_structured_coverages(self, entities) -> List[ExtractedCoverage]:
        """Extract coverages from Document AI structured entities."""
        coverages = []
        
        for entity in entities:
            if hasattr(entity, 'type_') and 'coverage' in entity.type_.lower():
                # Try to extract structured coverage data
                # This would depend on the specific Document AI processor configuration
                pass
        
        return coverages

    def _parse_amount(self, amount_str: str) -> Optional[Decimal]:
        """Parse amount string to Decimal."""
        if not amount_str:
            return None
            
        try:
            # Remove currency symbols and commas
            cleaned = re.sub(r'[$,\s]', '', amount_str)
            
            # Handle 'K' and 'M' suffixes
            if cleaned.upper().endswith('K'):
                return Decimal(cleaned[:-1]) * 1000
            elif cleaned.upper().endswith('M'):
                return Decimal(cleaned[:-1]) * 1000000
            else:
                return Decimal(cleaned)
                
        except (ValueError, Exception):
            return None

    def _deduplicate_coverages(self, coverages: List[ExtractedCoverage]) -> List[ExtractedCoverage]:
        """Remove duplicate coverages and keep the one with highest confidence."""
        coverage_map = {}
        
        for coverage in coverages:
            key = coverage.coverage_type
            if key not in coverage_map or coverage.confidence > coverage_map[key].confidence:
                coverage_map[key] = coverage
        
        return list(coverage_map.values())

    def _calculate_confidence(
        self,
        policy_number: Optional[str],
        effective_date: Optional[str],
        insurer_name: Optional[str],
        coverages: List[ExtractedCoverage]
    ) -> float:
        """Calculate overall parsing confidence score."""
        confidence_factors = []
        
        # Policy number confidence
        if policy_number and len(policy_number) >= 6:
            confidence_factors.append(0.9)
        elif policy_number:
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.2)
        
        # Date confidence
        if effective_date:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.3)
        
        # Insurer name confidence
        if insurer_name and len(insurer_name) > 5:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.4)
        
        # Coverage confidence
        if coverages:
            avg_coverage_confidence = sum(c.confidence for c in coverages) / len(coverages)
            confidence_factors.append(avg_coverage_confidence)
        else:
            confidence_factors.append(0.3)
        
        # Calculate weighted average
        return sum(confidence_factors) / len(confidence_factors)