# Demand Drafter Microservice Dependencies

# FastAPI and core web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Google Cloud dependencies for Vertex AI
google-cloud-aiplatform==1.38.1
vertexai==1.38.1
google-auth==2.23.4
google-cloud-storage==2.10.0

# Database and ORM
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for API calls
httpx==0.25.2

# Text processing and HTML manipulation
beautifulsoup4==4.12.2
lxml==4.9.3
markupsafe==2.1.3

# Template rendering
jinja2==3.1.2

# Date/time handling
python-dateutil==2.8.2

# Logging and monitoring
structlog==23.2.0

# Environment and config
python-dotenv==1.0.0

# PDF generation for demand letters
weasyprint==60.2
reportlab==4.0.7

# Citation processing and validation
regex==2023.10.3

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0

# Performance monitoring
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-exporter-gcp-monitoring==1.6.0