"""
Configuration settings for demand-drafter microservice.
"""

import os
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    app_name: str = "Demand Drafter Service"
    app_version: str = "1.0.0"
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Google Cloud settings
    gcp_project: str = Field(..., env="GCP_PROJECT")
    vertex_location: str = Field(default="us-central1", env="VERTEX_LOCATION")
    storage_bucket: Optional[str] = Field(None, env="STORAGE_BUCKET")
    
    # Vertex AI settings
    gemini_model: str = Field(default="gemini-2.5-pro", env="GEMINI_MODEL")
    gemini_temperature: float = Field(default=0.2, env="GEMINI_TEMPERATURE")
    gemini_max_output_tokens: int = Field(default=8192, env="GEMINI_MAX_OUTPUT_TOKENS")
    enable_zero_data_retention: bool = Field(default=True, env="ENABLE_ZERO_DATA_RETENTION")
    
    # Database settings
    database_url: str = Field(..., env="DATABASE_URL")
    
    # External service URLs
    core_api_url: str = Field(..., env="CORE_API_URL")
    medical_records_url: Optional[str] = Field(None, env="MEDICAL_RECORDS_URL")
    damages_module_url: Optional[str] = Field(None, env="DAMAGES_MODULE_URL")
    
    # Service authentication
    service_account_path: Optional[str] = Field(None, env="GOOGLE_APPLICATION_CREDENTIALS")
    
    # API settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="CORS_ORIGINS"
    )
    
    # Processing limits
    max_demand_length_words: int = Field(default=5000, env="MAX_DEMAND_LENGTH_WORDS")
    generation_timeout_seconds: int = Field(default=180, env="GENERATION_TIMEOUT_SECONDS")
    min_citations_required: int = Field(default=3, env="MIN_CITATIONS_REQUIRED")
    
    # Citation settings
    citation_pattern: str = Field(
        default=r'\[Doc\s+\w+\s+p\.\d+\]',
        env="CITATION_PATTERN"
    )
    require_citations: bool = Field(default=True, env="REQUIRE_CITATIONS")
    
    # Demand letter settings
    default_firm_signature: str = Field(
        default="Sincerely,\n\n[Attorney Name]\n[Firm Name]\n[Contact Information]",
        env="DEFAULT_FIRM_SIGNATURE"
    )
    
    # Quality control
    min_confidence_score: float = Field(default=0.8, env="MIN_CONFIDENCE_SCORE")
    enable_content_filtering: bool = Field(default=True, env="ENABLE_CONTENT_FILTERING")
    
    # Retry settings
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    retry_delay_seconds: int = Field(default=2, env="RETRY_DELAY")
    
    # Monitoring and observability
    enable_tracing: bool = Field(default=True, env="ENABLE_TRACING")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    # PDF generation settings
    pdf_page_size: str = Field(default="letter", env="PDF_PAGE_SIZE")
    pdf_margins: str = Field(default="1in", env="PDF_MARGINS")
    
    # Template settings
    template_directory: str = Field(default="src/templates", env="TEMPLATE_DIRECTORY")
    carrier_styles_directory: str = Field(
        default="src/prompts/carrier_styles", 
        env="CARRIER_STYLES_DIRECTORY"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings