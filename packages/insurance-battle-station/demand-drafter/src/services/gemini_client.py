"""
Gemini 2.5 Pro Client for Demand Letter Generation

This service uses Google's Vertex AI Gemini 2.5 Pro model to generate
demand letters with proper citations and carrier-specific styling.
"""

import json
import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig, Part
import structlog

from ..config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


@dataclass
class DemandGenerationRequest:
    """Request for demand letter generation."""
    matter_facts: Dict[str, Any]
    carrier_profile: Dict[str, Any]
    ask_amount: Decimal
    tone: str = "neutral"
    include_sections: List[str] = None
    firm_signature: str = ""


@dataclass
class DemandGenerationResult:
    """Result from demand letter generation."""
    letter_html: str
    exhibits: List[Dict[str, Any]]
    citations_count: int
    generation_metadata: Dict[str, Any]
    confidence_score: float


class GeminiDemandDrafter:
    """Client for generating demand letters using Gemini 2.5 Pro."""

    def __init__(self, project_id: str, location: str):
        """
        Initialize Gemini demand drafter.
        
        Args:
            project_id: GCP project ID
            location: Vertex AI location
        """
        self.project_id = project_id
        self.location = location
        self.logger = logger.bind(service="gemini_demand_drafter")
        
        # Initialize Vertex AI
        vertexai.init(project=project_id, location=location)
        
        # Configure model with zero data retention
        self.model = GenerativeModel(
            model_name=settings.gemini_model,
            system_instruction=self._load_system_prompt()
        )
        
        # Configure generation parameters
        self.generation_config = GenerationConfig(
            temperature=settings.gemini_temperature,
            max_output_tokens=settings.gemini_max_output_tokens,
            top_p=0.9,
            top_k=40
        )

    async def draft_demand_letter(self, request: DemandGenerationRequest) -> DemandGenerationResult:
        """
        Generate demand letter using Gemini 2.5 Pro.
        
        Args:
            request: Demand generation request
            
        Returns:
            DemandGenerationResult with generated letter
            
        Raises:
            ValueError: If generation fails or doesn't meet requirements
        """
        start_time = datetime.utcnow()
        
        self.logger.info(
            "Starting demand letter generation",
            ask_amount=float(request.ask_amount),
            tone=request.tone,
            sections=request.include_sections,
            carrier_name=request.carrier_profile.get('name', 'unknown')
        )
        
        try:
            # Prepare the prompt with all context
            prompt_data = self._prepare_prompt_data(request)
            
            # Generate content with retry logic
            response = await self._generate_with_retry(prompt_data)
            
            # Parse and validate the response
            result = await self._parse_and_validate_response(response, request)
            
            generation_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Add generation metadata
            result.generation_metadata.update({
                "generation_time_seconds": generation_time,
                "model_name": settings.gemini_model,
                "temperature": settings.gemini_temperature,
                "prompt_tokens": len(str(prompt_data)) // 4,  # Rough estimate
                "generation_timestamp": start_time.isoformat()
            })
            
            self.logger.info(
                "Demand letter generation completed",
                citations_count=result.citations_count,
                confidence_score=result.confidence_score,
                generation_time=generation_time,
                exhibits_count=len(result.exhibits)
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Demand letter generation failed",
                error=str(e),
                ask_amount=float(request.ask_amount)
            )
            raise

    def _load_system_prompt(self) -> str:
        """Load the system prompt for demand letter generation."""
        return """You are an expert personal injury demand letter drafter for AiLex law firm software.

CRITICAL RULES:
1. Use ONLY the provided facts and medical records. Each factual statement MUST include a page citation like [Doc abc123 p.5].
2. Follow the carrier style profile exactly (tone, section order, phrase blacklist).
3. NEVER state policy limits unless they appear in the provided records.
4. Return ONLY valid JSON in this exact format: {"html": "...", "exhibits": [...]}
5. Every medical fact, injury detail, or damages claim MUST have a citation.

SECTIONS TO INCLUDE (in specified order):
- Liability: Establish fault with citations
- Medical: Injuries and treatment with citations  
- Damages: Economic and non-economic losses with citations
- Liens: Any liens or subrogation with citations
- Ask: Demand amount with supporting rationale
- Closing: Professional closing

TONE GUIDELINES:
- "neutral": Professional, factual, respectful
- "firm": Assertive, confident, emphasizing strength of case  
- "conciliatory": Collaborative, settlement-focused, understanding

CITATION REQUIREMENTS:
- Format: [Doc {documentId} p.{page}] 
- Required for ALL factual claims about: injuries, treatment, bills, lost wages, pain/suffering
- No citations needed for: legal standards, general statements, procedural requests

EXHIBITS:
- List all documents referenced in citations
- Format: {"title": "Medical Records", "documentId": "abc123", "pages": "3-5, 7-9"}

QUALITY STANDARDS:
- Professional legal writing
- Proper grammar and formatting
- Logical flow and organization
- Compelling but accurate presentation
- HTML formatting for structure"""

    def _prepare_prompt_data(self, request: DemandGenerationRequest) -> Dict[str, Any]:
        """Prepare structured prompt data for Gemini."""
        return {
            "matter_facts": {
                "incident_date": request.matter_facts.get("incident_date"),
                "incident_description": request.matter_facts.get("incident_description"),
                "client_info": {
                    "age": request.matter_facts.get("client_age"),
                    "occupation": request.matter_facts.get("client_occupation")
                    # Note: No PII like names included
                },
                "medical_chronology": request.matter_facts.get("medical_chronology", []),
                "medical_bills": request.matter_facts.get("medical_bills", []),
                "damages_summary": request.matter_facts.get("damages_summary", {}),
                "liens": request.matter_facts.get("liens", []),
                "document_references": request.matter_facts.get("document_references", [])
            },
            "carrier_profile": {
                "name": request.carrier_profile.get("name", "Insurance Company"),
                "tone": request.carrier_profile.get("tone", request.tone),
                "section_order": request.carrier_profile.get("section_order", request.include_sections),
                "required_exhibits": request.carrier_profile.get("required_exhibits", []),
                "phrase_blacklist": request.carrier_profile.get("phrase_blacklist", []),
                "email_subject_template": request.carrier_profile.get("email_subject_template")
            },
            "demand_parameters": {
                "ask_amount": str(request.ask_amount),
                "tone": request.tone,
                "include_sections": request.include_sections or [
                    "liability", "medical", "damages", "liens", "ask", "closing"
                ]
            },
            "firm_info": {
                "signature_block": request.firm_signature or settings.default_firm_signature
            },
            "instructions": {
                "require_citations": settings.require_citations,
                "min_citations": settings.min_citations_required,
                "max_length_words": settings.max_demand_length_words,
                "citation_pattern": settings.citation_pattern
            }
        }

    async def _generate_with_retry(self, prompt_data: Dict[str, Any]) -> str:
        """Generate content with retry logic for reliability."""
        max_retries = settings.max_retries
        retry_delay = settings.retry_delay_seconds
        
        for attempt in range(max_retries + 1):
            try:
                # Convert prompt data to structured string
                prompt_text = json.dumps(prompt_data, indent=2, default=str)
                
                # Generate content
                response = await asyncio.to_thread(
                    self.model.generate_content,
                    prompt_text,
                    generation_config=self.generation_config
                )
                
                if not response or not response.text:
                    raise ValueError("Empty response from Gemini")
                
                return response.text.strip()
                
            except Exception as e:
                if attempt < max_retries:
                    self.logger.warning(
                        "Demand generation attempt failed, retrying",
                        attempt=attempt + 1,
                        error=str(e)
                    )
                    await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    raise ValueError(f"Failed to generate demand after {max_retries + 1} attempts: {str(e)}")

    async def _parse_and_validate_response(
        self, 
        response_text: str, 
        request: DemandGenerationRequest
    ) -> DemandGenerationResult:
        """Parse and validate the Gemini response."""
        try:
            # Parse JSON response
            response_data = json.loads(response_text)
            
            # Validate required fields
            if "html" not in response_data:
                raise ValueError("Response missing required 'html' field")
            
            letter_html = response_data["html"]
            exhibits = response_data.get("exhibits", [])
            
            # Validate HTML content
            if not letter_html or len(letter_html.strip()) < 100:
                raise ValueError("Generated letter is too short")
            
            # Count and validate citations
            citations_count = self._count_citations(letter_html)
            
            if settings.require_citations and citations_count < settings.min_citations_required:
                raise ValueError(f"Generated letter has only {citations_count} citations, minimum {settings.min_citations_required} required")
            
            # Validate exhibits format
            validated_exhibits = self._validate_exhibits(exhibits)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                letter_html, citations_count, validated_exhibits, request
            )
            
            # Check for prohibited phrases if specified
            self._check_phrase_blacklist(letter_html, request.carrier_profile.get("phrase_blacklist", []))
            
            return DemandGenerationResult(
                letter_html=letter_html,
                exhibits=validated_exhibits,
                citations_count=citations_count,
                generation_metadata={
                    "word_count": len(letter_html.split()),
                    "html_length": len(letter_html),
                    "validation_passed": True
                },
                confidence_score=confidence_score
            )
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON response from Gemini: {str(e)}")
        except Exception as e:
            raise ValueError(f"Response validation failed: {str(e)}")

    def _count_citations(self, html_content: str) -> int:
        """Count citations in the generated content."""
        import re
        citation_pattern = settings.citation_pattern
        citations = re.findall(citation_pattern, html_content)
        return len(citations)

    def _validate_exhibits(self, exhibits: List[Dict]) -> List[Dict[str, Any]]:
        """Validate and clean exhibit data."""
        validated = []
        
        for exhibit in exhibits:
            if not isinstance(exhibit, dict):
                continue
                
            # Required fields
            if not all(field in exhibit for field in ["title", "documentId", "pages"]):
                continue
                
            # Validate field types
            if not isinstance(exhibit["title"], str) or not exhibit["title"].strip():
                continue
                
            if not isinstance(exhibit["documentId"], str) or not exhibit["documentId"].strip():
                continue
                
            if not isinstance(exhibit["pages"], str) or not exhibit["pages"].strip():
                continue
            
            validated.append({
                "title": exhibit["title"].strip(),
                "documentId": exhibit["documentId"].strip(), 
                "pages": exhibit["pages"].strip()
            })
        
        return validated

    def _calculate_confidence_score(
        self, 
        letter_html: str, 
        citations_count: int,
        exhibits: List[Dict],
        request: DemandGenerationRequest
    ) -> float:
        """Calculate confidence score for the generated demand letter."""
        score_factors = []
        
        # Citation adequacy (0-1)
        if citations_count >= settings.min_citations_required * 2:
            score_factors.append(1.0)
        elif citations_count >= settings.min_citations_required:
            score_factors.append(0.8)
        else:
            score_factors.append(0.3)
        
        # Length appropriateness (0-1)
        word_count = len(letter_html.split())
        if 1000 <= word_count <= 3000:
            score_factors.append(1.0)
        elif 500 <= word_count <= 5000:
            score_factors.append(0.8)
        else:
            score_factors.append(0.5)
        
        # Exhibits completeness (0-1)
        if len(exhibits) >= 3:
            score_factors.append(1.0)
        elif len(exhibits) >= 1:
            score_factors.append(0.7)
        else:
            score_factors.append(0.4)
        
        # HTML structure quality (basic check)
        if "<p>" in letter_html and "</p>" in letter_html:
            score_factors.append(0.9)
        else:
            score_factors.append(0.6)
        
        # Calculate weighted average
        return sum(score_factors) / len(score_factors)

    def _check_phrase_blacklist(self, html_content: str, blacklist: List[str]) -> None:
        """Check for prohibited phrases in the generated content."""
        if not blacklist:
            return
            
        content_lower = html_content.lower()
        
        for phrase in blacklist:
            if phrase.lower() in content_lower:
                self.logger.warning(
                    "Generated letter contains blacklisted phrase",
                    phrase=phrase[:20] + "..." if len(phrase) > 20 else phrase
                )
                raise ValueError(f"Generated content contains prohibited phrase: {phrase}")

    async def test_connection(self) -> bool:
        """Test connection to Vertex AI Gemini service."""
        try:
            test_prompt = "Respond with only the word 'connected' if you can process this request."
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                test_prompt,
                generation_config=GenerationConfig(
                    temperature=0,
                    max_output_tokens=10
                )
            )
            
            return response and "connected" in response.text.lower()
            
        except Exception as e:
            self.logger.error("Gemini connection test failed", error=str(e))
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the configured model."""
        return {
            "model_name": settings.gemini_model,
            "project_id": self.project_id,
            "location": self.location,
            "temperature": settings.gemini_temperature,
            "max_output_tokens": settings.gemini_max_output_tokens,
            "zero_data_retention": settings.enable_zero_data_retention
        }