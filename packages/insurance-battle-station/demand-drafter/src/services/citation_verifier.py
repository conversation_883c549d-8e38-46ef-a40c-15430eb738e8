"""
Citation Verification System

This service verifies that demand letters include proper citations for all
factual claims, ensuring compliance with legal requirements and quality standards.
"""

import re
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

import structlog
from bs4 import BeautifulSoup, NavigableString, Tag

logger = structlog.get_logger(__name__)


class CitationSeverity(Enum):
    """Severity levels for citation issues."""
    CRITICAL = "critical"  # Factual claim with no citation
    WARNING = "warning"   # Weak citation or formatting issue
    INFO = "info"         # Suggestion for improvement


@dataclass
class CitationIssue:
    """Represents a citation issue found in the document."""
    severity: CitationSeverity
    message: str
    paragraph_text: str
    suggested_fix: Optional[str] = None
    line_number: Optional[int] = None


@dataclass
class CitationAnalysis:
    """Results of citation verification analysis."""
    total_citations: int
    valid_citations: int
    invalid_citations: int
    missing_citations: int
    issues: List[CitationIssue]
    citation_coverage_score: float
    overall_compliance: bool
    document_references: Set[str]


class CitationVerifier:
    """Service for verifying citations in demand letters."""
    
    def __init__(self, citation_pattern: str = r'\[Doc\s+(\w+)\s+p\.(\d+)\]'):
        """
        Initialize citation verifier.
        
        Args:
            citation_pattern: Regex pattern for valid citations
        """
        self.citation_pattern = citation_pattern
        self.logger = logger.bind(service="citation_verifier")
        
        # Keywords that indicate factual claims requiring citations
        self.factual_keywords = {
            # Medical terms
            'injury', 'injured', 'diagnosis', 'diagnosed', 'treatment', 'treated',
            'surgery', 'surgical', 'procedure', 'medication', 'prescription',
            'therapy', 'physical therapy', 'doctor', 'physician', 'medical',
            'hospital', 'emergency room', 'clinic', 'mri', 'ct scan', 'x-ray',
            'pain', 'suffering', 'symptoms', 'condition', 'recovery',
            
            # Financial terms
            'bill', 'billed', 'cost', 'costs', 'expense', 'expenses', 'paid',
            'payment', 'charge', 'charged', 'fee', 'fees', 'wage', 'wages',
            'income', 'earnings', 'lost', 'loss', 'damage', 'damages',
            
            # Accident/incident terms
            'accident', 'incident', 'collision', 'crash', 'occurred', 'happened',
            'caused', 'result', 'resulted', 'due to', 'because of',
            
            # Time/date specifics
            'on', 'at', 'during', 'while', 'after', 'before', 'since'
        }
        
        # Phrases that typically don't need citations (legal standards, etc.)
        self.non_factual_phrases = {
            'it is well established', 'under the law', 'the law provides',
            'according to statute', 'legal precedent', 'case law',
            'respectfully request', 'we demand', 'we submit', 'we assert',
            'in conclusion', 'therefore', 'accordingly', 'furthermore',
            'sincerely', 'respectfully', 'thank you', 'please contact'
        }

    async def verify_citations(
        self, 
        html_content: str, 
        available_documents: List[Dict[str, str]] = None
    ) -> CitationAnalysis:
        """
        Verify citations in HTML content.
        
        Args:
            html_content: HTML content to analyze
            available_documents: List of available documents with IDs
            
        Returns:
            CitationAnalysis with verification results
        """
        self.logger.info("Starting citation verification", content_length=len(html_content))
        
        try:
            # Parse HTML content
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find all citations
            citations = self._find_all_citations(html_content)
            
            # Analyze paragraphs for citation requirements
            paragraphs = soup.find_all(['p', 'div', 'li'])
            issues = []
            
            for i, paragraph in enumerate(paragraphs):
                paragraph_text = paragraph.get_text()
                if len(paragraph_text.strip()) < 10:  # Skip very short paragraphs
                    continue
                
                paragraph_issues = await self._analyze_paragraph(
                    paragraph_text, 
                    line_number=i+1,
                    available_documents=available_documents
                )
                issues.extend(paragraph_issues)
            
            # Validate citation format
            valid_citations, invalid_citations = self._validate_citation_format(citations)
            
            # Calculate metrics
            total_citations = len(citations)
            factual_paragraphs = self._count_factual_paragraphs(paragraphs)
            missing_citations = len([issue for issue in issues if issue.severity == CitationSeverity.CRITICAL])
            
            citation_coverage_score = self._calculate_coverage_score(
                total_citations, factual_paragraphs, missing_citations
            )
            
            # Determine overall compliance
            overall_compliance = (
                citation_coverage_score >= 0.8 and
                missing_citations == 0 and
                len(invalid_citations) == 0
            )
            
            # Extract document references
            document_references = {citation[0] for citation in valid_citations}
            
            analysis = CitationAnalysis(
                total_citations=total_citations,
                valid_citations=len(valid_citations),
                invalid_citations=len(invalid_citations),
                missing_citations=missing_citations,
                issues=issues,
                citation_coverage_score=citation_coverage_score,
                overall_compliance=overall_compliance,
                document_references=document_references
            )
            
            self.logger.info(
                "Citation verification completed",
                total_citations=total_citations,
                valid_citations=len(valid_citations),
                missing_citations=missing_citations,
                coverage_score=citation_coverage_score,
                compliant=overall_compliance
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error("Citation verification failed", error=str(e))
            raise

    def _find_all_citations(self, html_content: str) -> List[Tuple[str, str]]:
        """Find all citations in the content."""
        citations = []
        matches = re.finditer(self.citation_pattern, html_content, re.IGNORECASE)
        
        for match in matches:
            document_id = match.group(1)
            page_num = match.group(2)
            citations.append((document_id, page_num))
        
        return citations

    async def _analyze_paragraph(
        self, 
        paragraph_text: str, 
        line_number: int,
        available_documents: List[Dict[str, str]] = None
    ) -> List[CitationIssue]:
        """Analyze a single paragraph for citation requirements."""
        issues = []
        text_lower = paragraph_text.lower()
        
        # Check if paragraph contains factual claims
        has_factual_content = any(keyword in text_lower for keyword in self.factual_keywords)
        has_non_factual_content = any(phrase in text_lower for phrase in self.non_factual_phrases)
        
        # Skip non-factual paragraphs
        if not has_factual_content or has_non_factual_content:
            return issues
        
        # Check for citations in this paragraph
        citations_in_paragraph = re.findall(self.citation_pattern, paragraph_text)
        
        if not citations_in_paragraph:
            # This is a factual paragraph without citations
            issues.append(CitationIssue(
                severity=CitationSeverity.CRITICAL,
                message="Factual claim requires citation",
                paragraph_text=paragraph_text[:100] + "..." if len(paragraph_text) > 100 else paragraph_text,
                suggested_fix="Add citation in format [Doc documentId p.pageNumber]",
                line_number=line_number
            ))
        else:
            # Validate citations in this paragraph
            for doc_id, page_num in citations_in_paragraph:
                # Check if document ID is available
                if available_documents:
                    doc_ids = {doc.get('id', doc.get('documentId', '')) for doc in available_documents}
                    if doc_id not in doc_ids:
                        issues.append(CitationIssue(
                            severity=CitationSeverity.WARNING,
                            message=f"Citation references unavailable document: {doc_id}",
                            paragraph_text=paragraph_text[:100] + "...",
                            suggested_fix=f"Verify document {doc_id} exists or use available document",
                            line_number=line_number
                        ))
                
                # Check page number validity
                try:
                    page_int = int(page_num)
                    if page_int <= 0:
                        issues.append(CitationIssue(
                            severity=CitationSeverity.WARNING,
                            message=f"Invalid page number: {page_num}",
                            paragraph_text=paragraph_text[:100] + "...",
                            suggested_fix="Use valid page number (1 or greater)",
                            line_number=line_number
                        ))
                except ValueError:
                    issues.append(CitationIssue(
                        severity=CitationSeverity.WARNING,
                        message=f"Non-numeric page number: {page_num}",
                        paragraph_text=paragraph_text[:100] + "...",
                        suggested_fix="Use numeric page number",
                        line_number=line_number
                    ))
        
        return issues

    def _validate_citation_format(self, citations: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], List[str]]:
        """Validate citation format and return valid/invalid citations."""
        valid_citations = []
        invalid_citations = []
        
        for doc_id, page_num in citations:
            # Validate document ID format (alphanumeric, reasonable length)
            if re.match(r'^[a-zA-Z0-9_-]{1,50}$', doc_id):
                # Validate page number
                try:
                    page_int = int(page_num)
                    if 1 <= page_int <= 9999:  # Reasonable page range
                        valid_citations.append((doc_id, page_num))
                    else:
                        invalid_citations.append(f"[Doc {doc_id} p.{page_num}] - invalid page number")
                except ValueError:
                    invalid_citations.append(f"[Doc {doc_id} p.{page_num}] - non-numeric page")
            else:
                invalid_citations.append(f"[Doc {doc_id} p.{page_num}] - invalid document ID")
        
        return valid_citations, invalid_citations

    def _count_factual_paragraphs(self, paragraphs: List) -> int:
        """Count paragraphs that likely contain factual claims."""
        factual_count = 0
        
        for paragraph in paragraphs:
            text = paragraph.get_text().lower()
            
            # Skip very short paragraphs
            if len(text.strip()) < 20:
                continue
            
            # Check for factual content
            has_factual = any(keyword in text for keyword in self.factual_keywords)
            has_non_factual = any(phrase in text for phrase in self.non_factual_phrases)
            
            if has_factual and not has_non_factual:
                factual_count += 1
        
        return factual_count

    def _calculate_coverage_score(self, total_citations: int, factual_paragraphs: int, missing_citations: int) -> float:
        """Calculate citation coverage score (0-1)."""
        if factual_paragraphs == 0:
            return 1.0  # No factual content, perfect score
        
        if total_citations == 0:
            return 0.0  # No citations at all
        
        # Calculate based on coverage and missing citations
        coverage_ratio = min(total_citations / factual_paragraphs, 1.0)
        penalty_ratio = missing_citations / factual_paragraphs if factual_paragraphs > 0 else 0
        
        score = coverage_ratio - (penalty_ratio * 0.5)
        return max(0.0, min(1.0, score))

    def fix_common_citation_issues(self, html_content: str) -> str:
        """Attempt to fix common citation formatting issues."""
        # Fix common spacing issues
        html_content = re.sub(r'\[\s*Doc\s+(\w+)\s+p\.\s*(\d+)\s*\]', r'[Doc \1 p.\2]', html_content)
        
        # Fix missing periods
        html_content = re.sub(r'\[Doc\s+(\w+)\s+p\s*(\d+)\]', r'[Doc \1 p.\2]', html_content)
        
        # Fix case issues
        html_content = re.sub(r'\[doc\s+(\w+)\s+p\.(\d+)\]', r'[Doc \1 p.\2]', html_content, flags=re.IGNORECASE)
        
        return html_content

    def extract_citation_summary(self, analysis: CitationAnalysis) -> Dict[str, Any]:
        """Extract a summary of citation analysis for reporting."""
        critical_issues = [issue for issue in analysis.issues if issue.severity == CitationSeverity.CRITICAL]
        warning_issues = [issue for issue in analysis.issues if issue.severity == CitationSeverity.WARNING]
        
        return {
            "citation_metrics": {
                "total_citations": analysis.total_citations,
                "valid_citations": analysis.valid_citations,
                "invalid_citations": analysis.invalid_citations,
                "missing_citations": analysis.missing_citations,
                "coverage_score": round(analysis.citation_coverage_score, 2),
                "overall_compliant": analysis.overall_compliance
            },
            "issues_summary": {
                "critical_count": len(critical_issues),
                "warning_count": len(warning_issues),
                "total_issues": len(analysis.issues)
            },
            "document_references": list(analysis.document_references),
            "recommendations": self._generate_recommendations(analysis)
        }

    def _generate_recommendations(self, analysis: CitationAnalysis) -> List[str]:
        """Generate recommendations based on analysis results."""
        recommendations = []
        
        if analysis.missing_citations > 0:
            recommendations.append(
                f"Add citations to {analysis.missing_citations} factual claims that lack proper documentation"
            )
        
        if analysis.invalid_citations > 0:
            recommendations.append(
                f"Fix {analysis.invalid_citations} citations with formatting or reference issues"
            )
        
        if analysis.citation_coverage_score < 0.8:
            recommendations.append(
                "Improve citation coverage to meet quality standards (target: 80%+)"
            )
        
        if len(analysis.document_references) < 3:
            recommendations.append(
                "Consider referencing additional supporting documents for stronger evidence"
            )
        
        if not recommendations:
            recommendations.append("Citation quality meets all requirements")
        
        return recommendations

    async def validate_document_availability(
        self, 
        citations: List[Tuple[str, str]], 
        available_documents: List[Dict[str, str]]
    ) -> List[str]:
        """
        Validate that all cited documents are available.
        
        Args:
            citations: List of (document_id, page_number) tuples
            available_documents: List of available document information
            
        Returns:
            List of missing document IDs
        """
        cited_doc_ids = {doc_id for doc_id, _ in citations}
        available_doc_ids = {
            doc.get('id', doc.get('documentId', '')) for doc in available_documents
        }
        
        missing_docs = cited_doc_ids - available_doc_ids
        return list(missing_docs)