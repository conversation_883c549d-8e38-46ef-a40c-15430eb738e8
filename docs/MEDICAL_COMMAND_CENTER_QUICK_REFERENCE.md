# Medical Command Center - Quick Reference Guide

## 🚀 Quick Start

### For Users
1. Navigate to any matter: `/matters/{id}`
2. Click "Medical Records" tab
3. Choose from 5 AI-powered features:
   - **Records**: View extracted data
   - **Processing**: Upload & process documents  
   - **AI Assistant**: Ask medical questions
   - **Timeline**: Generate chronological events
   - **Treatment Gaps**: Identify missing care

### For Developers
```bash
# Essential files
/app/(authenticated)/matters/[id]/page.tsx         # Main matter page
/components/matters/medical-tab.tsx                # Medical tab wrapper
/components/medical/medical-copilot.tsx           # AI assistant
/components/medical/medical-processing-agent.tsx  # Agentic processing
/components/medical/medical-timeline-agent.tsx    # Timeline generation
```

## 🏗️ Architecture Overview

```
Medical Command Center UI
├── Matter Detail Integration
│   └── Medical Records Tab (new)
├── AI-First Components
│   ├── Medical Copilot (ResilientChat)
│   ├── Processing Agent (streaming)
│   └── Timeline Agent (AI generation)
└── Backend Integration
    ├── LangExtract Service
    ├── AG-UI Protocol
    └── Structured Extractions
```

## 🤖 AI Features

### Medical Copilot
- **Technology**: ResilientChat + AG-UI protocol
- **Features**: Context-aware queries, confidence scoring, source grounding
- **Usage**: Ask questions about medical records with AI assistance

### Agentic Processing  
- **Technology**: AG-UI streaming + human-in-the-loop
- **Features**: 8-step processing pipeline, real-time progress, review workflows
- **Usage**: Upload and process medical documents with AI oversight

### Timeline Generation
- **Technology**: AI analysis + interactive filtering
- **Features**: Chronological organization, confidence scores, source citations
- **Usage**: Generate medical timelines with AI-powered event detection

## 🔧 Technical Implementation

### Component Pattern
```typescript
// All medical AI components follow this pattern
import { getFeatureFlags } from '@/lib/features/ag-ui'

export default function MedicalComponent({ matterId }: Props) {
  const featureFlags = getFeatureFlags()
  
  if (!featureFlags.aguiEnabled) {
    return <DisabledState />
  }
  
  // AI-powered functionality here
}
```

### Data Integration
```typescript
// Use existing structured extraction service
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'

const extractions = await structuredExtractionService.getExtractionsForMatter(matterId)
```

### AG-UI Integration
```typescript
// ResilientChat configuration for medical copilot
<ResilientChat
  context={{
    matterId,
    medicalContext,
    mode: 'medical_analysis',
    features: ['source_grounding', 'confidence_scoring', 'streaming']
  }}
  instructions="Medical AI assistant instructions..."
/>
```

## 📊 Key Features

| Feature | Technology | Status | Description |
|---------|------------|--------|-------------|
| Medical Records Display | Existing Component | ✅ Complete | Tabbed view of extractions |
| AI Assistant | ResilientChat + AG-UI | ✅ Complete | Medical query copilot |
| Agentic Processing | AG-UI Streaming | ✅ Complete | Real-time document processing |
| Timeline Generation | AI Analysis | ✅ Complete | Chronological event organization |
| Treatment Gap Detection | AI Analysis | 🔄 Placeholder | Missing care identification |
| Source Grounding | Citation System | ✅ Complete | Document references for all AI outputs |
| Human-in-the-Loop | Review Workflows | ✅ Complete | Confidence-based review triggers |
| HIPAA Compliance | Audit + Zero Retention | ✅ Complete | Full compliance maintained |

## 🔗 Integration Points

### Navigation
- **Matters List**: Added "Medical Records" dropdown option
- **Matter Detail**: New "Medical Records" tab with 5 sub-features
- **URL Structure**: `/matters/{id}#medical` for direct access

### Services
- **Structured Extractions**: Uses existing service layer
- **AG-UI Protocol**: Leverages existing agent infrastructure  
- **ResilientChat**: Uses existing chat components
- **Error Handling**: Integrates with existing error boundaries

### Database
- **Existing Tables**: `medical_records`, `medication_rows`, `radiology_rows`, etc.
- **New Data**: Timeline events, processing status, AI interactions
- **Audit Logs**: HIPAA-compliant interaction logging

## 🚨 Troubleshooting

### Common Issues
| Issue | Cause | Solution |
|-------|-------|----------|
| AI features disabled | AG-UI flags not set | Check `NEXT_PUBLIC_AGUI_ENABLED=true` |
| Chat not loading | ResilientChat config | Verify CopilotKit endpoint |
| Processing stuck | LangExtract issues | Check Google Cloud services |
| No timeline events | Missing extractions | Verify medical data exists |

### Debug Commands
```bash
# Check feature flags
localStorage.getItem('NEXT_PUBLIC_AGUI_ENABLED')

# Test medical service
curl /api/medical/matters/{matterId}/extractions

# Check processing status
curl /api/medical/matters/{matterId}/processing-status
```

## 📈 Performance

### Load Times
- **Medical Tab**: < 1s initial load
- **AI Assistant**: < 2s chat initialization  
- **Processing**: Real-time streaming updates
- **Timeline**: < 3s generation for typical case

### Scalability
- **Concurrent Users**: Supports existing platform limits
- **Document Size**: 25MB max per document
- **Processing Queue**: Handles 10+ concurrent jobs
- **Database**: Uses existing tenant isolation

## 🔒 Security & Compliance

### HIPAA Features
- **Zero Data Retention**: Vertex AI configured
- **Audit Logging**: All AI interactions logged
- **Access Controls**: Tenant-based isolation
- **Encryption**: End-to-end for all medical data

### Authentication
- **User Context**: Inherited from existing auth
- **Tenant Isolation**: Matter-based access control
- **API Security**: Existing JWT validation
- **Rate Limiting**: Standard platform limits

## 📝 Development Checklist

### Adding New Medical Features
- [ ] Check AG-UI feature flags
- [ ] Use existing medical services
- [ ] Include confidence scoring
- [ ] Add source citations
- [ ] Implement error boundaries
- [ ] Add HIPAA audit logging
- [ ] Test disabled state gracefully
- [ ] Update documentation

### Testing Requirements
- [ ] Test with AG-UI enabled/disabled
- [ ] Mock ResilientChat responses
- [ ] Test confidence score handling
- [ ] Verify HIPAA audit logs
- [ ] Test error boundaries
- [ ] Validate source citations
- [ ] Check tenant isolation

---

**Quick Links**:
- [Full Documentation](MEDICAL_COMMAND_CENTER_UI_DOCUMENTATION.md)
- [Deployment Guide](MEDICAL_RECORDS_DEPLOYMENT.md)
- [AG-UI Integration](migration/ag-ui-protocol-implementation-summary.md)
- [LangExtract Backend](../LANGEXTRACT_INTEGRATION_SUMMARY.md)